﻿local typeof = typeof
local setmetatable = setmetatable
local require = require
local Utility = CS.War.Script.Utility
local event = require "event"
local mq_common_pb = require "mq_common_pb"



local RectTransformType = typeof(CS.UnityEngine.RectTransform)
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem

local Common_Util = CS.Common_Util.UIUtil

local chat_mgr_pro = require "chat_mgr_pro"
local ui_window_mgr = require "ui_window_mgr"
local time_util             = require "time_util"
local lang = require "lang"
local game_config = require "game_config"
local utility = CS.War.Script.Utility
local string = string
local chat_mgr_new = require "chat_mgr_new"
local string_util = require "string_util"
module("chat_handle_AllianceNoticeObj")

local M = {}

function M:ctor()

end
function M:OnRender(item)
    chat_mgr_pro.InitUIData(self,item,"AllianceNoticeObj")
   -- self.item
   -- self.itemData
   -- self.msgData
   -- self.isMy
   -- self.itemTransform
    --self.parentTransform
  --  self.UI.transform=
    self.UI.pendant_fanyi.gameObject:SetActive(false)
end
function M:InitUI()
    local instance =self.UI.transform
    local item = instance:GetComponent(typeof(ScrollRectItem))
    self.UI.instance = instance


    self.UI.btn_turnUrgent = item:Get("btn_turnUrgent")
    self.UI.btn_close = item:Get("btn_close")
    self.UI.line = item:Get("line")
    self.UI.lineRect = item:Get("line"):GetComponent(RectTransformType)
    self.UI.content2 = item:Get("txt_content_translate")
    self.UI.content = item:Get("txt_content")
    self.UI.contentRect = item:Get("txt_content"):GetComponent(RectTransformType)
    self.UI.contentRect2 = item:Get("txt_content_translate"):GetComponent(RectTransformType)
    self.UI.btn_delete = item:Get("btn_delete")
    self.UI.btn_self = item:Get("btn_self")
    self.UI.txt_content_translate = item:Get("txt_content_translate")
    self.UI.txt_bottomInfo = item:Get("txt_bottomInfo")
    self.UI.txt_title = item:Get("txt_title")
    self.UI.topBG = item:Get("topBG")


    local btn_close = function()
        if self.UI.clickClose then
            self.UI.clickClose()
        end
    end
    self.UI.btn_close.onClick:AddListener(btn_close)
    local btn_turnUrgent = function()
        if self.UI.clickTurnUrgent then
            self.UI.clickTurnUrgent()
        end
    end
    self.UI.btn_turnUrgent.onClick:AddListener(btn_turnUrgent)

    local btn_delete = function()
        if self.UI.clickDelete then
            self.UI.clickDelete()
        end
    end
    self.UI.btn_delete.onClick:AddListener(btn_delete)

    local btn_selfClick = function()
        if self.UI.clickSelf then
            self.UI.clickSelf()
        end
    end
    self.UI.btn_self.onClick:AddListener(btn_selfClick)

self:InstantiateTranslateItem()


end


function M:InstantiateTranslateItem()
    local instance =self.UI.transform
    local item = instance:GetComponent(typeof(ScrollRectItem))
    self.UI.pendant_fanyi = item:Get("btn_translate")
    self.UI.translated=self.UI.pendant_fanyi.gameObject
    self.UI.onClickTranslation = function()
        local context = self.msgData.context
        local net_chat_module_new = require "net_chat_module_new"
        net_chat_module_new.Req_TRANSLATE(context)
        self.UI.translated:SetActive(false)
        self.UI.isTranslated = true
    end
    self.UI.pendant_fanyi.onClick:AddListener(self.UI.onClickTranslation)
    
    self.UI.onTranslate = function(evt, sStr, tStr, langIso)
        --  log.Error(sStr)
        if self.msgData.context  == sStr then
            self:RenderTranslation()
            chat_mgr_pro.GetContentPro():RefreshAllShownItem()
        end
    end
    event.Register(event.TRANSLATE_RSP, self.UI.onTranslate)
end

function M:RenderTranslation()

    if chat_mgr_pro.IsCanRenderTranslation(self.itemData) then
        if  not self.msgData.context or self.isMy then--
            return
        end
        --系统语言改为游戏内语言
        local tStr=chat_mgr_pro.GetTranslateContext(self.msgData.context)
        self.UI.pendant_fanyi:SetActive(not tStr and ((not game_config.Q1SDK_DOMESTIC) or Utility.IsInEditor()))
        if not tStr then
            self.UI.line:SetActive(false)
            self.UI.content:SetActive(true)
            self.UI.content2:SetActive(false)
            chat_mgr_pro.TryAutoTranslation(self.UI,self.itemData)
        else
            self.itemData.isTranslated = true --已执行翻译操作
            self.UI.content2:SetActive(true)
            local isShowContent = self.UI.showTranslateAndHideOriginal == nil or (self.UI.showTranslateAndHideOriginal ~= nil and self.UI.showTranslateAndHideOriginal)
            self.UI.content:SetActive(isShowContent)
            self.UI.line:SetActive(isShowContent)
            self.UI.content2.text = tStr
        end
   end
end



function M:RenderAllianceNotice()
    local ui=self.UI
    local msgData=self.msgData
    ui.transform.gameObject:SetActive(true)

    local alliance_mgr = require "alliance_mgr"
    ui.isShowDeleteBtn = alliance_mgr.IsSelfR4R5() and true or false

    ui.txt_content_translate.gameObject:SetActive(false)
    ui.line:SetActive(false)

    --正文内容
    local contentStr = msgData.context
    ui.content.text = contentStr

    ui.txt_bottomInfo.text = time_util.ConvertStamp2Time(msgData.chatTime) .. " " .. msgData.name--时间及归属


    if msgData.sType == mq_common_pb.enSpeak_Announcement then
        ui.txt_title.text = lang.Get(670018)--普通公告
        ui.topBG:Switch(0)
        --普通转紧急公告按钮（条件显示）
        local isSelfR4R5 = alliance_mgr.IsSelfR4R5()
        ui.btn_turnUrgent:SetActive(isSelfR4R5)
        if isSelfR4R5 then
            --二次确认界面扩展
            ui.clickTurnUrgent = nil
            ui.clickTurnUrgent = function()
                self:ShowTurnUrgentMessageBox(contentStr)
            end
        end
    elseif msgData.sType == mq_common_pb.enSpeak_UrgentAnnouncement then
        ui.btn_turnUrgent:SetActive(false)
        ui.txt_title.text = lang.Get(670019)--紧急公告
        ui.topBG:Switch(1)
    end

    --objData.selfContentSize.enabled = false
    --objData.maskContentSize.enabled = false

    --关闭按钮（在联盟聊天页签，且没有被关闭记录）
    local isShowCloseBtn = self:IsShowAllianceTopNotice() and not ui.hideCloseBtn
    ui.btn_close:SetActive(isShowCloseBtn)
    --外部传入关闭按钮事件

    --删除开关(公告频道条件)
    --local isShowDeleteBtn = chat_mgr_new.GetPageState() == chat_mgr_new.enum_pState.allianceNotice and alliance_mgr.IsSelfR4R5()
    local isShowDeleteBtn = ui.isShowDeleteBtn ~= nil and ui.isShowDeleteBtn or false
    ui.btn_delete:SetActive(isShowDeleteBtn)
    if isShowDeleteBtn then
        ui.clickDelete = function()
           self:DeleteFunc()
        end
    end

    ui.clickSelf = function()
        ui_window_mgr:ShowModule("ui_alliance_notice_detail_panel", nil, nil, msgData)
    end


    self:RenderTranslation()
end
function M:RenderAllianceUrgentNotice()
  self:RenderAllianceNotice()
end

function M:ShowTurnUrgentMessageBox(context)
    --二次确认
    local message_box = require "message_box"
    message_box.SetUISkin("ui/prefabs/uimessageboxyestips.prefab")
    --发布紧急公告提示确认
    local remainNum = string.format2(lang.Get(654024), chat_mgr_new.GetUrgentRemainingNum())
    message_box.OpenYesTipBox(remainNum, lang.Get(670029), message_box.STYLE_YESNO, function(d, r)
        if r == message_box.RESULT_YES then
            local ui_chat_mgr_gw = require "ui_chat_mgr_gw"
            ui_chat_mgr_gw.TryReleaseUrgentNotice(context)
           -- print("普通公告转紧急公告")
        end
    end, 0, lang.KEY_OK, lang.KEY_CANCEL, lang.KEY_SYSTEM_TIPS)
end

--是否展示联盟聊天界面置顶公告
function M:IsShowAllianceTopNotice()

    --当前最新置顶公告id是否已经被关闭
    local pState = chat_mgr_new.GetPageState()
    return pState == chat_mgr_new.enum_pState.guide
end
function M:DeleteFunc()
    --二次弹窗确认
    local message_box = require "message_box"
    message_box.Open(lang.Get(670030), message_box.STYLE_YESNO, function(d, r)
        if r == message_box.RESULT_YES then
            local net_chat_module_new = require "net_chat_module_new"
            local chat_pb = require "chat_pb"
            net_chat_module_new.Recv_CHAT_RECALL_MSG_REQ(chat_pb.Channel_Guild,self.msgData.szChatID)
            chat_mgr_pro.GetContentPro():RemoveListItem(self.item)
        end
    end, 0, lang.KEY_OK, lang.KEY_CANCEL, lang.KEY_SYSTEM_TIPS)
end

function M:Dispose()


    --if self.UI.btn_translate then
    --    self.UI.btn_translate:RemoveAllListeners()
    --end
    --if self.UI.btn_close then
    --    self.UI.btn_close:RemoveAllListeners()
    --end
    --if self.UI.btn_turnUrgent then
    --    self.UI.btn_turnUrgent:RemoveAllListeners()
    --end
end

local class = require "class"

local object = setmetatable({}, {
    __call = function()
        return {}
    end
})

local CM = class(object, nil, M)

--- 创建实例
---@return M
function New()
    return CM()
end