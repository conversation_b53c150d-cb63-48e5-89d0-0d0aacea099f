local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local event = require "event"
local event_task_define = require "event_task_define"
local gw_task_const = require "gw_task_const"
local reward_mgr = require "reward_mgr"
local log = require "log"
local gw_task_data = require "gw_task_data"
local game_scheme = require "game_scheme"
local land_revival_data = require "land_revival_data"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_land_revival_day_task_controller")
local controller = nil
local UIController = newClass("ui_land_revival_day_task_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)

    self.curDay = land_revival_data.GetCurDayNew()
    if self.curDay > 7 then
        self.curDay = 7
    end
   
    self:GetActivityEndTime()
    self:GetDayTaskData()
    local jumpDay = nil
    if data and data.param then
        jumpDay = data.param.jumpDay
        self:TriggerUIEvent("RefreshDayToggle", jumpDay, self.curDay)
    end
    self:SelectDayToggle(jumpDay or self.curDay)
  
    self:GetVipTaskProgress()
    self:GetVipBoxRewardData()
   
end

function UIController:OnShow()
    self.__base.OnShow(self)
    local jumpDay = land_revival_data.GetJumpDay()
    if jumpDay then
        self:TriggerUIEvent("RefreshDayToggle", jumpDay, self.curDay)
        self:SelectDayToggle(jumpDay or self.curDay)
        land_revival_data.SetMainJumpDay(nil)
    end
    self:RefreshCanReceiveBox()
end

function UIController:Close(data)
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    self.taskUpdate = function(eventName, taskData, moduleId, moduleList)
        if moduleList[gw_task_const.TaskModuleType.pioneer_target] then
            self:SelectDayToggle(self.curSelectDayIndex,self.hammerToggleIndex)
            self:GetVipBoxRewardData()
            self:RefreshCanReceiveBox()
        end
    end
    self:RegisterEvent(event_task_define.REFRESH_TASK, self.taskUpdate)

    --道具补充监听
    self.goodsChangeFun = function(e,id,sid,num)
        if id == land_revival_data.GetHammerItemID() then
            self:GetVipTaskProgress()
            self:RefreshCanReceiveBox()
        end
    end
    self:RegisterEvent(event.UPDATE_GOODS_NUM_CHANGE, self.goodsChangeFun)
end

function UIController:AutoUnsubscribeEvents()
end
--endregion

--region Controller Logic
function UIController:OnBtnHelpClickedProxy()
    ui_window_mgr:ShowModule("ui_land_revival_help")
end
function UIController:OnSliderLeftSliderValueChange(value)
end
function UIController:OnBtnBoxIconClickedProxy()
    ui_window_mgr:ShowModule("ui_land_revival_reward")
end

---@public function 获取活动结束时间
function UIController:GetActivityEndTime()
    local activityData = land_revival_data.GetActivityTaskIDData()
    if activityData then
        local endTime = activityData.endTimeStamp
        self:TriggerUIEvent("SetActivityTimer", endTime)
    end
end

---@public function 选择天页签
function UIController:SelectDayToggle(dayIndex, togIndex)
    self.curSelectDayIndex = dayIndex 
    self.hammerToggleIndex = togIndex or 1
    self:RefreshDayTaskScrollTable(self.curSelectDayIndex,self.hammerToggleIndex)
    self:RefreshHammerToggleData(self.curSelectDayIndex,self.hammerToggleIndex)
end

---@public function 刷新锤子toggle显示
function UIController:RefreshHammerToggleData(dayIndex,defaultIndex)
    local dayData = land_revival_data.GetHammerToggleData(self.curSelectDayIndex)
    if dayData then
        local togListData = {}
        for i, v in ipairs(dayData) do
            local isUnFinishHammer = self:IsHammerTaskAndUnFinish(v)
            local tempData = {}
            tempData.nameLang = v.titleLang
            tempData.togValueChange = function()
                self:SelectHammerToggle(i)
            end
            tempData.isHammer = isUnFinishHammer
            table.insert(togListData, tempData)
        end
        self:TriggerUIEvent("RefreshChildToggle", togListData,dayIndex,defaultIndex)
    end
end

---@public function 选择锤子页签
function UIController:SelectHammerToggle(togIndex)
    self.hammerToggleIndex = togIndex
    self:RefreshDayTaskScrollTable(self.curSelectDayIndex,togIndex)
end

---@public function 获取每日任务数据
function UIController:GetDayTaskData(defaultDay)
    local cfgArr = land_revival_data.GetDayTaskData()
  

    local togData = {}
    togData.dayArr = {}
    for _, v in ipairs(cfgArr) do
        table.insert(togData.dayArr, v.nDayNum)
    end
    togData.curDay = defaultDay or self.curDay
    self:TriggerUIEvent("InitDayToggle", togData)
    self:TriggerUIEvent("JumpDayIndex", self.curDay)
end
 
---@public function 刷新任务列表
function UIController:RefreshDayTaskScrollTable(dayIndex, pageIndex)
    local listData = self:GetOnePageTaskData(dayIndex, pageIndex)
    if listData then
        self:TriggerUIEvent("RefreshTaskList", listData.taskDataArr)
    end
end

---@public function 获取一个页签的数据 
function UIController:GetOnePageTaskData(dayIndex, pageIndex)
    local allDayList = land_revival_data.GetAllDayTaskListData()
    if not allDayList then
        return nil
    end
    local dayData = allDayList[dayIndex]
    if dayData then
        local pageData = dayData[pageIndex]
        local viewData = {
            titleLang = pageData.titleLang,
            taskDataArr = {},
        }
        local isLockJump = self.curDay < dayIndex
        for i, v in ipairs(pageData.taskIDList) do
            local taskCfg = game_scheme:TaskMain_0(v)
            local taskData = gw_task_data.GetTaskData(v)
            --log.Error("输出任务数据ID",v)
            if taskData and taskCfg then
                local tempData = {
                    taskID = v,
                    taskConditionType = taskCfg.ConditionType,
                    taskLang = taskCfg.TaskLang,
                    rewardID = taskCfg.TaskReward,
                    targetRate = taskCfg.ConditionValue1,
                    status = taskData.status,
                    rate = taskData.rate,
                    isLockJump = isLockJump
                }
                if self:IsHammerTask(v) then
                    tempData.isHammerTask = true
                end
                table.insert(viewData.taskDataArr, tempData)
            end
        end

        table.sort(viewData.taskDataArr, function(a, b)
            local isFinishA = a.rate >= a.targetRate
            local isFinishB = b.rate >= b.targetRate

            if not a.status and not b.status then
                if isFinishA ~= isFinishB then
                    return isFinishA
                end
            end

            if a.status ~= b.status then
                if not a.status then
                    return true
                else
                    return false
                end
            end

            return a.taskID < b.taskID
        end)
        
        return viewData
    end
    return nil
end

---@public function 判断是否是锤子任务
function UIController:IsHammerTask(taskID)
    local taskCfg = game_scheme:TaskMain_0(taskID)
    if taskCfg then
        local itemIdList = reward_mgr.GetRewardGoodsList(taskCfg.TaskReward)
        for _, itemData in ipairs(itemIdList) do
            if itemData.id == land_revival_data.GetHammerItemID() then
                return true
            end
        end
    end
    return false
end

---@public function 判断这一个子页签是否有未完成的锤子任务
function UIController:IsHammerTaskAndUnFinish(togData)
    if not togData or not togData.taskIDList then
        return false
    end
    for _, v in ipairs(togData.taskIDList) do
        local isHammer = self:IsHammerTask(v)
        if isHammer then
            local taskCfg = game_scheme:TaskMain_0(v)
            local taskData = gw_task_data.GetTaskData(v)
            if taskData and taskCfg then
                local status = taskData.status
                if not status then
                    return true
                end
            end
        end
    end
    
end

---@public function 获取vip任务总进度
function UIController:GetVipTaskProgress()
    local curProgress, totalProgressRate = land_revival_data.GetVipTaskTotalProgress()
    self:TriggerUIEvent("SetVipTaskProgress", curProgress, totalProgressRate)
end

---@public function 获取vip任务宝箱奖励
function UIController:GetVipBoxRewardData()
    if land_revival_data.IsJudgeAllTaskFinish()  then
        self:TriggerUIEvent("InitBoxRewardShow")
    else
        self:TriggerUIEvent("InitBoxRewardShow", land_revival_data.GetNextVipTaskRewardList())
    end
end

function UIController:RefreshCanReceiveBox()
    local isPlay = land_revival_data.IsJudgeAllTaskCanReceive()
    self:TriggerUIEvent("PlayBoxAnim",isPlay)
end
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
