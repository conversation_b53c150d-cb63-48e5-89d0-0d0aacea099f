local require = require
local typeof = typeof

local GameObject = CS.UnityEngine.GameObject
local RectTransform = CS.UnityEngine.RectTransform
local Animator = CS.UnityEngine.Animator


module("item_box_bubble_binding")

UIPath = "ui/prefabs/gw/gw_universalui/activity/item_boxbubble.prefab"

WidgetTable ={
	item_boxBubble = { path = "", type = GameObject, },
	rtf_rewardParent = { path = "rtf&ator_rewardParent", type = RectTransform, },
	ator_rewardParent = { path = "rtf&ator_rewardParent", type = Animator, },

}
