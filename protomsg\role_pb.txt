-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local error_code_pb=require("error_code_pb")
local common_new_pb=require("common_new_pb")
module('role_pb')


V1M=V(4,"enLikeSysType_NoneType",0,0)
V2M=V(4,"enLikeSysType_BattleDuel",1,1)
V3M=V(4,"enLikeSysType_AcornPub",2,2)
V4M=V(4,"enLikeSysType_Radar",3,3)
V5M=V(4,"enLikeSysType_RolePage",4,4)
V6M=V(4,"enLikeSysType_SxMainCity",5,5)
V7M=V(4,"enLikeSysType_SurpriseBox",6,6)
V8M=V(4,"enLikeSysType_PresidentialMail",7,7)
V9M=V(4,"enLikeSysType_AllianceMail",8,8)
V10M=V(4,"enLikeSysType_AllianceShip",9,9)
V11M=V(4,"enLikeSysType_TreasureMap",10,10)
V12M=V(4,"enLikeSysType_NoviceArena",11,11)
V13M=V(4,"enLikeSysType_PeakArena",12,12)
V14M=V(4,"enLikeSysType_3v3Arena",13,13)
V15M=V(4,"enLikeSysType_AllianceFire",14,14)
V16M=V(4,"enLikeSysType_ZombieDisaster",15,15)
V17M=V(4,"enLikeSysType_StormArena",16,16)
E1M=E(3,"enLikeSysType",".CSMsg.enLikeSysType")
V18M=V(4,"RobotPoolType_unknown",0,0)
V19M=V(4,"RobotPoolType_RebirthSpace",1,1)
V20M=V(4,"RobotPoolType_Carriage",2,2)
E2M=E(3,"RobotPoolType",".CSMsg.RobotPoolType")
F1D=F(2,"faceID",".CSMsg.TMSG_ZONE_ROLE_FACE_UPDATE_REQ.faceID",1,0,2,false,0,5,1)
M1G=D(1,"TMSG_ZONE_ROLE_FACE_UPDATE_REQ",".CSMsg.TMSG_ZONE_ROLE_FACE_UPDATE_REQ",false,{},{},nil,{})
F2D=F(2,"err",".CSMsg.TMSG_ZONE_ROLE_FACE_UPDATE_RSP.err",1,0,2,false,nil,14,8)
F3D=F(2,"faceID",".CSMsg.TMSG_ZONE_ROLE_FACE_UPDATE_RSP.faceID",2,1,1,false,0,5,1)
M2G=D(1,"TMSG_ZONE_ROLE_FACE_UPDATE_RSP",".CSMsg.TMSG_ZONE_ROLE_FACE_UPDATE_RSP",false,{},{},nil,{})
F4D=F(2,"faceID",".CSMsg.TMSG_ZONE_ROLE_FACE_PROP_UPDATE_REQ.faceID",1,0,2,false,0,5,1)
M4G=D(1,"TMSG_ZONE_ROLE_FACE_PROP_UPDATE_REQ",".CSMsg.TMSG_ZONE_ROLE_FACE_PROP_UPDATE_REQ",false,{},{},nil,{})
F5D=F(2,"err",".CSMsg.TMSG_ZONE_ROLE_FACE_PROP_UPDATE_RSP.err",1,0,2,false,nil,14,8)
M5G=D(1,"TMSG_ZONE_ROLE_FACE_PROP_UPDATE_RSP",".CSMsg.TMSG_ZONE_ROLE_FACE_PROP_UPDATE_RSP",false,{},{},nil,{})
F6D=F(2,"faceID",".CSMsg.TMSG_ZONE_ROLE_FACE_PROP_UNLOAD_REQ.faceID",1,0,2,false,0,5,1)
M6G=D(1,"TMSG_ZONE_ROLE_FACE_PROP_UNLOAD_REQ",".CSMsg.TMSG_ZONE_ROLE_FACE_PROP_UNLOAD_REQ",false,{},{},nil,{})
F7D=F(2,"err",".CSMsg.TMSG_ZONE_ROLE_FACE_PROP_UNLOAD_RSP.err",1,0,2,false,nil,14,8)
M7G=D(1,"TMSG_ZONE_ROLE_FACE_PROP_UNLOAD_RSP",".CSMsg.TMSG_ZONE_ROLE_FACE_PROP_UNLOAD_RSP",false,{},{},nil,{})
F8D=F(2,"propId",".CSMsg.TFaceProp.propId",1,0,2,false,0,5,1)
F9D=F(2,"propValue",".CSMsg.TFaceProp.propValue",2,1,2,false,0,5,1)
F10D=F(2,"occupation",".CSMsg.TFaceProp.occupation",3,2,2,false,0,5,1)
M8G=D(1,"TFaceProp",".CSMsg.TFaceProp",false,{},{},nil,{})
F11D=F(2,"faceID",".CSMsg.TMSG_ZONE_ROLE_FACE_PROP_UPDATE_NTF.faceID",1,0,2,false,0,5,1)
F12D=F(2,"propFaceID",".CSMsg.TMSG_ZONE_ROLE_FACE_PROP_UPDATE_NTF.propFaceID",2,1,1,false,0,5,1)
M9G=D(1,"TMSG_ZONE_ROLE_FACE_PROP_UPDATE_NTF",".CSMsg.TMSG_ZONE_ROLE_FACE_PROP_UPDATE_NTF",false,{},{},nil,{})
F13D=F(2,"faceID",".CSMsg.TMSG_ZONE_ROLE_NEW_FACE_NTF.faceID",1,0,2,false,0,5,1)
M10G=D(1,"TMSG_ZONE_ROLE_NEW_FACE_NTF",".CSMsg.TMSG_ZONE_ROLE_NEW_FACE_NTF",false,{},{},nil,{})
F14D=F(2,"name",".CSMsg.TMSG_ZONE_ROLE_UPDATE_NAME_REQ.name",1,0,2,false,"",9,9)
M11G=D(1,"TMSG_ZONE_ROLE_UPDATE_NAME_REQ",".CSMsg.TMSG_ZONE_ROLE_UPDATE_NAME_REQ",false,{},{},nil,{})
F15D=F(2,"err",".CSMsg.TMSG_ZONE_ROLE_UPDATE_NAME_RSP.err",1,0,2,false,nil,14,8)
F16D=F(2,"name",".CSMsg.TMSG_ZONE_ROLE_UPDATE_NAME_RSP.name",2,1,1,false,"",9,9)
F17D=F(2,"allowmodifytime",".CSMsg.TMSG_ZONE_ROLE_UPDATE_NAME_RSP.allowmodifytime",3,2,1,false,0,13,3)
M12G=D(1,"TMSG_ZONE_ROLE_UPDATE_NAME_RSP",".CSMsg.TMSG_ZONE_ROLE_UPDATE_NAME_RSP",false,{},{},nil,{})
F18D=F(2,"sexType",".CSMsg.TMSG_ZONE_ROLE_UPDATE_SEX_REQ.sexType",1,0,2,false,0,13,3)
M13G=D(1,"TMSG_ZONE_ROLE_UPDATE_SEX_REQ",".CSMsg.TMSG_ZONE_ROLE_UPDATE_SEX_REQ",false,{},{},nil,{})
F19D=F(2,"errorcode",".CSMsg.TMSG_ZONE_ROLE_UPDATE_SEX_RSP.errorcode",1,0,2,false,nil,14,8)
F20D=F(2,"finlsexType",".CSMsg.TMSG_ZONE_ROLE_UPDATE_SEX_RSP.finlsexType",2,1,1,false,0,13,3)
F21D=F(2,"allowmodifytime",".CSMsg.TMSG_ZONE_ROLE_UPDATE_SEX_RSP.allowmodifytime",3,2,1,false,0,13,3)
M14G=D(1,"TMSG_ZONE_ROLE_UPDATE_SEX_RSP",".CSMsg.TMSG_ZONE_ROLE_UPDATE_SEX_RSP",false,{},{},nil,{})
F22D=F(2,"playId",".CSMsg.TMSG_ZONE_ROLE_PRAISE_UPDATE_REQ.playId",1,0,2,false,0,13,3)
F23D=F(2,"nNums",".CSMsg.TMSG_ZONE_ROLE_PRAISE_UPDATE_REQ.nNums",2,1,1,false,0,13,3)
F24D=F(2,"nLikeSysType",".CSMsg.TMSG_ZONE_ROLE_PRAISE_UPDATE_REQ.nLikeSysType",3,2,1,false,0,13,3)
F25D=F(2,"exMailId",".CSMsg.TMSG_ZONE_ROLE_PRAISE_UPDATE_REQ.exMailId",4,3,1,false,0,4,4)
M15G=D(1,"TMSG_ZONE_ROLE_PRAISE_UPDATE_REQ",".CSMsg.TMSG_ZONE_ROLE_PRAISE_UPDATE_REQ",false,{},{},nil,{})
F26D=F(2,"errorcode",".CSMsg.TMSG_ZONE_ROLE_PRAISE_UPDATE_RSP.errorcode",1,0,2,false,nil,14,8)
F27D=F(2,"nNums",".CSMsg.TMSG_ZONE_ROLE_PRAISE_UPDATE_RSP.nNums",2,1,1,false,0,13,3)
F28D=F(2,"nLikeRoleNums",".CSMsg.TMSG_ZONE_ROLE_PRAISE_UPDATE_RSP.nLikeRoleNums",3,2,1,false,0,13,3)
M16G=D(1,"TMSG_ZONE_ROLE_PRAISE_UPDATE_RSP",".CSMsg.TMSG_ZONE_ROLE_PRAISE_UPDATE_RSP",false,{},{},nil,{})
F29D=F(2,"mailId",".CSMsg.TMSG_GET_MAIL_LIKE_REQ.mailId",1,0,2,false,0,4,4)
F30D=F(2,"exMailId",".CSMsg.TMSG_GET_MAIL_LIKE_REQ.exMailId",2,1,2,false,0,4,4)
M17G=D(1,"TMSG_GET_MAIL_LIKE_REQ",".CSMsg.TMSG_GET_MAIL_LIKE_REQ",false,{},{},nil,{})
F31D=F(2,"errorcode",".CSMsg.TMSG_GET_MAIL_LIKE_RSP.errorcode",1,0,2,false,nil,14,8)
F32D=F(2,"mailId",".CSMsg.TMSG_GET_MAIL_LIKE_RSP.mailId",2,1,2,false,0,4,4)
F33D=F(2,"exMailId",".CSMsg.TMSG_GET_MAIL_LIKE_RSP.exMailId",3,2,2,false,0,4,4)
F34D=F(2,"nLikeNum",".CSMsg.TMSG_GET_MAIL_LIKE_RSP.nLikeNum",4,3,2,false,0,13,3)
F35D=F(2,"bGiveLike",".CSMsg.TMSG_GET_MAIL_LIKE_RSP.bGiveLike",5,4,2,false,false,8,7)
M18G=D(1,"TMSG_GET_MAIL_LIKE_RSP",".CSMsg.TMSG_GET_MAIL_LIKE_RSP",false,{},{},nil,{})
F36D=F(2,"nLikeSysType",".CSMsg.TMSG_GET_SP_LIKENUM_REQ.nLikeSysType",1,0,2,false,0,13,3)
F37D=F(2,"dbids",".CSMsg.TMSG_GET_SP_LIKENUM_REQ.dbids",2,1,3,false,{},13,3)
M19G=D(1,"TMSG_GET_SP_LIKENUM_REQ",".CSMsg.TMSG_GET_SP_LIKENUM_REQ",false,{},{},nil,{})
F38D=F(2,"dbid",".CSMsg.SpLikeNum.dbid",1,0,2,false,0,13,3)
F39D=F(2,"likeNum",".CSMsg.SpLikeNum.likeNum",2,1,2,false,0,13,3)
M20G=D(1,"SpLikeNum",".CSMsg.SpLikeNum",false,{},{},nil,{})
F40D=F(2,"errorcode",".CSMsg.TMSG_GET_SP_LIKENUM_RSP.errorcode",1,0,2,false,nil,14,8)
F41D=F(2,"nLikeSysType",".CSMsg.TMSG_GET_SP_LIKENUM_RSP.nLikeSysType",2,1,2,false,0,13,3)
F42D=F(2,"spLikeNum",".CSMsg.TMSG_GET_SP_LIKENUM_RSP.spLikeNum",3,2,3,false,{},11,10)
M21G=D(1,"TMSG_GET_SP_LIKENUM_RSP",".CSMsg.TMSG_GET_SP_LIKENUM_RSP",false,{},{},nil,{})
M22G=D(1,"TMSG_LIKE_GET_RECORD_REQ",".CSMsg.TMSG_LIKE_GET_RECORD_REQ",false,{},{},{},{})
F43D=F(2,"errorcode",".CSMsg.TMSG_LIKE_GET_RECORD_RSP.errorcode",1,0,2,false,nil,14,8)
F44D=F(2,"likes",".CSMsg.TMSG_LIKE_GET_RECORD_RSP.likes",2,1,3,false,{},11,10)
M23G=D(1,"TMSG_LIKE_GET_RECORD_RSP",".CSMsg.TMSG_LIKE_GET_RECORD_RSP",false,{},{},nil,{})
M25G=D(1,"TMSG_LIKE_GET_ROLE_RECORD_REQ",".CSMsg.TMSG_LIKE_GET_ROLE_RECORD_REQ",false,{},{},{},{})
F45D=F(2,"errorcode",".CSMsg.TMSG_LIKE_GET_ROLE_RECORD_RSP.errorcode",1,0,2,false,nil,14,8)
F46D=F(2,"roleLikes",".CSMsg.TMSG_LIKE_GET_ROLE_RECORD_RSP.roleLikes",2,1,3,false,{},11,10)
F47D=F(2,"likeNum",".CSMsg.TMSG_LIKE_GET_ROLE_RECORD_RSP.likeNum",3,2,2,false,0,13,3)
M26G=D(1,"TMSG_LIKE_GET_ROLE_RECORD_RSP",".CSMsg.TMSG_LIKE_GET_ROLE_RECORD_RSP",false,{},{},nil,{})
F48D=F(2,"playId",".CSMsg.TMSG_PRIVATE_CHAT_LIKE_REQ.playId",1,0,2,false,0,5,1)
F49D=F(2,"szChatID",".CSMsg.TMSG_PRIVATE_CHAT_LIKE_REQ.szChatID",2,1,2,false,"",9,9)
M28G=D(1,"TMSG_PRIVATE_CHAT_LIKE_REQ",".CSMsg.TMSG_PRIVATE_CHAT_LIKE_REQ",false,{},{},nil,{})
F50D=F(2,"errorcode",".CSMsg.TMSG_PRIVATE_CHAT_LIKE_RSP.errorcode",1,0,2,false,nil,14,8)
F51D=F(2,"playId",".CSMsg.TMSG_PRIVATE_CHAT_LIKE_RSP.playId",2,1,2,false,0,5,1)
F52D=F(2,"szChatID",".CSMsg.TMSG_PRIVATE_CHAT_LIKE_RSP.szChatID",3,2,2,false,"",9,9)
M29G=D(1,"TMSG_PRIVATE_CHAT_LIKE_RSP",".CSMsg.TMSG_PRIVATE_CHAT_LIKE_RSP",false,{},{},nil,{})
F53D=F(2,"type",".CSMsg.TLikeItemData.type",1,0,2,false,0,13,3)
F54D=F(2,"dbid",".CSMsg.TLikeItemData.dbid",2,1,2,false,0,13,3)
F55D=F(2,"name",".CSMsg.TLikeItemData.name",3,2,2,false,"",9,9)
F56D=F(2,"faceStr",".CSMsg.TLikeItemData.faceStr",4,3,2,false,"",9,9)
F57D=F(2,"frameID",".CSMsg.TLikeItemData.frameID",5,4,2,false,0,5,1)
F58D=F(2,"occurTime",".CSMsg.TLikeItemData.occurTime",6,5,2,false,0,5,1)
F59D=F(2,"worldId",".CSMsg.TLikeItemData.worldId",7,6,1,false,0,5,1)
F60D=F(2,"leagueId",".CSMsg.TLikeItemData.leagueId",8,7,1,false,0,13,3)
F61D=F(2,"leagueName",".CSMsg.TLikeItemData.leagueName",9,8,1,false,"",9,9)
F62D=F(2,"leagueShortName",".CSMsg.TLikeItemData.leagueShortName",10,9,1,false,"",9,9)
M24G=D(1,"TLikeItemData",".CSMsg.TLikeItemData",false,{},{},nil,{})
F63D=F(2,"likes",".CSMsg.TDBLikeData.likes",1,0,3,false,{},11,10)
M30G=D(1,"TDBLikeData",".CSMsg.TDBLikeData",false,{},{},nil,{})
F64D=F(2,"dbid",".CSMsg.TRoleLikeItemData.dbid",1,0,2,false,0,13,3)
F65D=F(2,"faceStr",".CSMsg.TRoleLikeItemData.faceStr",2,1,2,false,"",9,9)
F66D=F(2,"frameID",".CSMsg.TRoleLikeItemData.frameID",3,2,2,false,0,5,1)
F67D=F(2,"occurTime",".CSMsg.TRoleLikeItemData.occurTime",4,3,2,false,0,5,1)
M27G=D(1,"TRoleLikeItemData",".CSMsg.TRoleLikeItemData",false,{},{},nil,{})
F68D=F(2,"roleLikes",".CSMsg.TDBRoleLikeData.roleLikes",1,0,3,false,{},11,10)
M31G=D(1,"TDBRoleLikeData",".CSMsg.TDBRoleLikeData",false,{},{},nil,{})
F69D=F(2,"roleId",".CSMsg.tRoleNewInfo.roleId",1,0,2,false,0,13,3)
F70D=F(2,"roleName",".CSMsg.tRoleNewInfo.roleName",2,1,2,false,"",9,9)
F71D=F(2,"roleSex",".CSMsg.tRoleNewInfo.roleSex",3,2,2,false,0,13,3)
F72D=F(2,"likeNums",".CSMsg.tRoleNewInfo.likeNums",4,3,1,false,0,13,3)
F73D=F(2,"worldID",".CSMsg.tRoleNewInfo.worldID",5,4,1,false,0,13,3)
F74D=F(2,"roleFace",".CSMsg.tRoleNewInfo.roleFace",6,5,1,false,0,13,3)
F75D=F(2,"level",".CSMsg.tRoleNewInfo.level",7,6,1,false,0,13,3)
F76D=F(2,"schlossid",".CSMsg.tRoleNewInfo.schlossid",8,7,1,false,0,13,3)
F77D=F(2,"totalice",".CSMsg.tRoleNewInfo.totalice",9,8,1,false,0,13,3)
F78D=F(2,"leagueid",".CSMsg.tRoleNewInfo.leagueid",10,9,1,false,0,13,3)
F79D=F(2,"leaguename",".CSMsg.tRoleNewInfo.leaguename",11,10,1,false,"",9,9)
F80D=F(2,"killnum",".CSMsg.tRoleNewInfo.killnum",12,11,1,false,0,13,3)
F81D=F(2,"frameid",".CSMsg.tRoleNewInfo.frameid",13,12,1,false,0,13,3)
F82D=F(2,"dbid",".CSMsg.tRoleNewInfo.dbid",14,13,1,false,0,13,3)
F83D=F(2,"adornId",".CSMsg.tRoleNewInfo.adornId",15,14,1,false,0,5,1)
F84D=F(2,"customFaceId",".CSMsg.tRoleNewInfo.customFaceId",16,15,1,false,"",9,9)
F85D=F(2,"customFaceUrl",".CSMsg.tRoleNewInfo.customFaceUrl",17,16,1,false,"",9,9)
F86D=F(2,"roleFaceStr",".CSMsg.tRoleNewInfo.roleFaceStr",18,17,1,false,"",9,9)
F87D=F(2,"schlosseffectid",".CSMsg.tRoleNewInfo.schlosseffectid",19,18,1,false,0,13,3)
F88D=F(2,"plateid",".CSMsg.tRoleNewInfo.plateid",20,19,1,false,0,13,3)
F89D=F(2,"nationalFlagID",".CSMsg.tRoleNewInfo.nationalFlagID",21,20,1,false,0,13,3)
F90D=F(2,"nationalFlagTime",".CSMsg.tRoleNewInfo.nationalFlagTime",22,21,1,false,0,13,3)
M32G=D(1,"tRoleNewInfo",".CSMsg.tRoleNewInfo",false,{},{},nil,{})
F91D=F(2,"roleId",".CSMsg.TMSG_ZONE_NEW_ROLEINFO_REQ.roleId",1,0,2,false,0,13,3)
M33G=D(1,"TMSG_ZONE_NEW_ROLEINFO_REQ",".CSMsg.TMSG_ZONE_NEW_ROLEINFO_REQ",false,{},{},nil,{})
F92D=F(2,"errorcode",".CSMsg.TMSG_ZONE_NEW_ROLEINFO_RSP.errorcode",1,0,2,false,nil,14,8)
F93D=F(2,"roleInfo",".CSMsg.TMSG_ZONE_NEW_ROLEINFO_RSP.roleInfo",2,1,1,false,nil,11,10)
M34G=D(1,"TMSG_ZONE_NEW_ROLEINFO_RSP",".CSMsg.TMSG_ZONE_NEW_ROLEINFO_RSP",false,{},{},nil,{})
F94D=F(2,"nowDecoration",".CSMsg.TMSG_ZONE_ROLE_CITY_UPDATE_REQ.nowDecoration",1,0,2,false,0,13,3)
F95D=F(2,"goalDecoration",".CSMsg.TMSG_ZONE_ROLE_CITY_UPDATE_REQ.goalDecoration",2,1,2,false,0,13,3)
M35G=D(1,"TMSG_ZONE_ROLE_CITY_UPDATE_REQ",".CSMsg.TMSG_ZONE_ROLE_CITY_UPDATE_REQ",false,{},{},nil,{})
F96D=F(2,"errorcode",".CSMsg.TMSG_ZONE_ROLE_CITY_UPDATE_RSP.errorcode",1,0,2,false,nil,14,8)
F97D=F(2,"finlDecoration",".CSMsg.TMSG_ZONE_ROLE_CITY_UPDATE_RSP.finlDecoration",2,1,1,false,0,13,3)
M36G=D(1,"TMSG_ZONE_ROLE_CITY_UPDATE_RSP",".CSMsg.TMSG_ZONE_ROLE_CITY_UPDATE_RSP",false,{},{},nil,{})
F98D=F(2,"schloss",".CSMsg.TMSG_ZONE_ROLE_NEW_SCHLOSS_NTF.schloss",1,0,2,false,0,5,1)
M37G=D(1,"TMSG_ZONE_ROLE_NEW_SCHLOSS_NTF",".CSMsg.TMSG_ZONE_ROLE_NEW_SCHLOSS_NTF",false,{},{},nil,{})
F99D=F(2,"noweffectid",".CSMsg.TMSG_ZONE_ROLE_CITY_EFFECT_UPDATE_REQ.noweffectid",1,0,2,false,0,13,3)
F100D=F(2,"goaleffectid",".CSMsg.TMSG_ZONE_ROLE_CITY_EFFECT_UPDATE_REQ.goaleffectid",2,1,2,false,0,13,3)
M38G=D(1,"TMSG_ZONE_ROLE_CITY_EFFECT_UPDATE_REQ",".CSMsg.TMSG_ZONE_ROLE_CITY_EFFECT_UPDATE_REQ",false,{},{},nil,{})
F101D=F(2,"errorcode",".CSMsg.TMSG_ZONE_ROLE_CITY_EFFECT_UPDATE_RSP.errorcode",1,0,2,false,nil,14,8)
F102D=F(2,"finaleffectID",".CSMsg.TMSG_ZONE_ROLE_CITY_EFFECT_UPDATE_RSP.finaleffectID",2,1,1,false,0,13,3)
M39G=D(1,"TMSG_ZONE_ROLE_CITY_EFFECT_UPDATE_RSP",".CSMsg.TMSG_ZONE_ROLE_CITY_EFFECT_UPDATE_RSP",false,{},{},nil,{})
F103D=F(2,"schlosseffectid",".CSMsg.TMSG_ZONE_ROLE_NEW_SCHLOSS_EFFECT_NTF.schlosseffectid",1,0,2,false,0,5,1)
M40G=D(1,"TMSG_ZONE_ROLE_NEW_SCHLOSS_EFFECT_NTF",".CSMsg.TMSG_ZONE_ROLE_NEW_SCHLOSS_EFFECT_NTF",false,{},{},nil,{})
F104D=F(2,"g2Sum",".CSMsg.tRoleZoneSource.g2Sum",1,0,2,false,0,13,3)
F105D=F(2,"g1Sum",".CSMsg.tRoleZoneSource.g1Sum",2,1,2,false,0,3,2)
F106D=F(2,"g3Sum",".CSMsg.tRoleZoneSource.g3Sum",3,2,2,false,0,3,2)
F107D=F(2,"g40009Sum",".CSMsg.tRoleZoneSource.g40009Sum",4,3,2,false,0,13,3)
F108D=F(2,"g40033Sum",".CSMsg.tRoleZoneSource.g40033Sum",5,4,2,false,0,13,3)
F109D=F(2,"g40026Sum",".CSMsg.tRoleZoneSource.g40026Sum",6,5,2,false,0,13,3)
F110D=F(2,"g40037Sum",".CSMsg.tRoleZoneSource.g40037Sum",7,6,2,false,0,3,2)
F111D=F(2,"g40038Sum",".CSMsg.tRoleZoneSource.g40038Sum",8,7,2,false,0,3,2)
F112D=F(2,"g40039Sum",".CSMsg.tRoleZoneSource.g40039Sum",9,8,2,false,0,3,2)
F113D=F(2,"T1HeroSum",".CSMsg.tRoleZoneSource.T1HeroSum",10,9,2,false,0,13,3)
F114D=F(2,"T2HeroSum",".CSMsg.tRoleZoneSource.T2HeroSum",11,10,2,false,0,13,3)
F115D=F(2,"T3HeroSum",".CSMsg.tRoleZoneSource.T3HeroSum",12,11,2,false,0,13,3)
F116D=F(2,"start4HeroSum",".CSMsg.tRoleZoneSource.start4HeroSum",13,12,2,false,0,13,3)
F117D=F(2,"start3HeroSum",".CSMsg.tRoleZoneSource.start3HeroSum",14,13,2,false,0,13,3)
F118D=F(2,"g40015Sum",".CSMsg.tRoleZoneSource.g40015Sum",15,14,2,false,0,13,3)
F119D=F(2,"g40018Sum",".CSMsg.tRoleZoneSource.g40018Sum",16,15,2,false,0,13,3)
F120D=F(2,"seniorCallSum",".CSMsg.tRoleZoneSource.seniorCallSum",17,16,2,false,0,13,3)
F121D=F(2,"normalCallSum",".CSMsg.tRoleZoneSource.normalCallSum",18,17,2,false,0,13,3)
F122D=F(2,"magicSquareCallSum",".CSMsg.tRoleZoneSource.magicSquareCallSum",19,18,2,false,0,13,3)
F123D=F(2,"normalLotterySum",".CSMsg.tRoleZoneSource.normalLotterySum",20,19,2,false,0,13,3)
F124D=F(2,"seniorLotterySum",".CSMsg.tRoleZoneSource.seniorLotterySum",21,20,2,false,0,13,3)
F125D=F(2,"g40068Sum",".CSMsg.tRoleZoneSource.g40068Sum",22,21,1,false,0,13,3)
F126D=F(2,"g9Sum",".CSMsg.tRoleZoneSource.g9Sum",23,22,1,false,0,13,3)
F127D=F(2,"g41009Sum",".CSMsg.tRoleZoneSource.g41009Sum",24,23,1,false,0,13,3)
F128D=F(2,"LightAndDarkMagicSquareCallSum",".CSMsg.tRoleZoneSource.LightAndDarkMagicSquareCallSum",25,24,1,false,0,13,3)
F129D=F(2,"EntertainmentCityNum",".CSMsg.tRoleZoneSource.EntertainmentCityNum",26,25,1,false,0,13,3)
F130D=F(2,"costChipSum",".CSMsg.tRoleZoneSource.costChipSum",27,26,1,false,0,13,3)
F131D=F(2,"gIronBlockSum",".CSMsg.tRoleZoneSource.gIronBlockSum",28,27,1,false,0,4,4)
F132D=F(2,"gWheatSum",".CSMsg.tRoleZoneSource.gWheatSum",29,28,1,false,0,4,4)
F133D=F(2,"gExpSum",".CSMsg.tRoleZoneSource.gExpSum",30,29,1,false,0,4,4)
F134D=F(2,"g4002Sum",".CSMsg.tRoleZoneSource.g4002Sum",31,30,1,false,0,13,3)
F135D=F(2,"g4000Sum",".CSMsg.tRoleZoneSource.g4000Sum",32,31,1,false,0,13,3)
F136D=F(2,"g3205Sum",".CSMsg.tRoleZoneSource.g3205Sum",33,32,1,false,0,13,3)
F137D=F(2,"g3301Sum",".CSMsg.tRoleZoneSource.g3301Sum",34,33,1,false,0,13,3)
F138D=F(2,"g009Sum",".CSMsg.tRoleZoneSource.g009Sum",35,34,1,false,0,13,3)
F139D=F(2,"g13Sum",".CSMsg.tRoleZoneSource.g13Sum",36,35,1,false,0,13,3)
F140D=F(2,"g2103Sum",".CSMsg.tRoleZoneSource.g2103Sum",37,36,1,false,0,13,3)
F141D=F(2,"gSAddHeroSum",".CSMsg.tRoleZoneSource.gSAddHeroSum",38,37,1,false,0,13,3)
F142D=F(2,"gOrangeSurvivorSum",".CSMsg.tRoleZoneSource.gOrangeSurvivorSum",39,38,1,false,0,13,3)
F143D=F(2,"gHeroRecruitSum",".CSMsg.tRoleZoneSource.gHeroRecruitSum",40,39,1,false,0,13,3)
F144D=F(2,"gSurvivalRecruitSum",".CSMsg.tRoleZoneSource.gSurvivalRecruitSum",41,40,1,false,0,13,3)
F145D=F(2,"gCommSpeedSum",".CSMsg.tRoleZoneSource.gCommSpeedSum",42,41,1,false,0,13,3)
F146D=F(2,"gCitySpeedSum",".CSMsg.tRoleZoneSource.gCitySpeedSum",43,42,1,false,0,13,3)
F147D=F(2,"gTrainSpeedSum",".CSMsg.tRoleZoneSource.gTrainSpeedSum",44,43,1,false,0,13,3)
F148D=F(2,"gCureSpeedSum",".CSMsg.tRoleZoneSource.gCureSpeedSum",45,44,1,false,0,13,3)
F149D=F(2,"gResearchSpeedSum",".CSMsg.tRoleZoneSource.gResearchSpeedSum",46,45,1,false,0,13,3)
M41G=D(1,"tRoleZoneSource",".CSMsg.tRoleZoneSource",false,{},{},nil,{})
F150D=F(2,"arenaRank",".CSMsg.tRoleLobbySource.arenaRank",1,0,1,false,0,13,3)
F151D=F(2,"hook_num",".CSMsg.tRoleLobbySource.hook_num",2,1,1,false,0,5,1)
F152D=F(2,"illusion_num",".CSMsg.tRoleLobbySource.illusion_num",3,2,1,false,0,5,1)
F153D=F(2,"ashdungeon_num",".CSMsg.tRoleLobbySource.ashdungeon_num",4,3,1,false,0,5,1)
F154D=F(2,"crownArenaRank",".CSMsg.tRoleLobbySource.crownArenaRank",5,4,1,false,0,13,3)
F155D=F(2,"weekendArenaRank",".CSMsg.tRoleLobbySource.weekendArenaRank",6,5,1,false,0,13,3)
F156D=F(2,"Arena3v3Rank",".CSMsg.tRoleLobbySource.Arena3v3Rank",7,6,1,false,0,13,3)
M42G=D(1,"tRoleLobbySource",".CSMsg.tRoleLobbySource",false,{},{},nil,{})
F157D=F(2,"zsrc",".CSMsg.TMSG_ROLE_RESOURCE_STAT_NTF.zsrc",1,0,1,false,nil,11,10)
F158D=F(2,"lsrc",".CSMsg.TMSG_ROLE_RESOURCE_STAT_NTF.lsrc",2,1,1,false,nil,11,10)
M43G=D(1,"TMSG_ROLE_RESOURCE_STAT_NTF",".CSMsg.TMSG_ROLE_RESOURCE_STAT_NTF",false,{},{},nil,{})
F159D=F(2,"flagstate",".CSMsg.TMSG_ROLE_NATIONALFLAG_STATE_NTF.flagstate",1,0,2,false,false,8,7)
F160D=F(2,"flagid",".CSMsg.TMSG_ROLE_NATIONALFLAG_STATE_NTF.flagid",2,1,2,false,0,5,1)
M44G=D(1,"TMSG_ROLE_NATIONALFLAG_STATE_NTF",".CSMsg.TMSG_ROLE_NATIONALFLAG_STATE_NTF",false,{},{},nil,{})
F161D=F(2,"flagid",".CSMsg.TMSG_ROLE_NATIONALFLAG_MDF_REQ.flagid",1,0,2,false,0,5,1)
M45G=D(1,"TMSG_ROLE_NATIONALFLAG_MDF_REQ",".CSMsg.TMSG_ROLE_NATIONALFLAG_MDF_REQ",false,{},{},nil,{})
F162D=F(2,"err",".CSMsg.TMSG_ROLE_NATIONALFLAG_MDF_RSP.err",1,0,2,false,nil,14,8)
F163D=F(2,"flagid",".CSMsg.TMSG_ROLE_NATIONALFLAG_MDF_RSP.flagid",2,1,2,false,0,5,1)
F164D=F(2,"nextsettime",".CSMsg.TMSG_ROLE_NATIONALFLAG_MDF_RSP.nextsettime",3,2,2,false,0,5,1)
M46G=D(1,"TMSG_ROLE_NATIONALFLAG_MDF_RSP",".CSMsg.TMSG_ROLE_NATIONALFLAG_MDF_RSP",false,{},{},nil,{})
F165D=F(2,"equipId",".CSMsg.TMSG_GET_MAKE_HERO_EQUIP_REQ.equipId",1,0,2,false,0,5,1)
M47G=D(1,"TMSG_GET_MAKE_HERO_EQUIP_REQ",".CSMsg.TMSG_GET_MAKE_HERO_EQUIP_REQ",false,{},{},nil,{})
F166D=F(2,"errorCode",".CSMsg.TMSG_GET_MAKE_HERO_EQUIP_RSP.errorCode",1,0,2,false,nil,14,8)
F167D=F(2,"equipId",".CSMsg.TMSG_GET_MAKE_HERO_EQUIP_RSP.equipId",2,1,1,false,0,5,1)
M48G=D(1,"TMSG_GET_MAKE_HERO_EQUIP_RSP",".CSMsg.TMSG_GET_MAKE_HERO_EQUIP_RSP",false,{},{},nil,{})
F168D=F(2,"pos",".CSMsg.TMSG_CUSTOM_FACE_UPLOAD_REQ.pos",1,0,1,false,0,5,1)
M49G=D(1,"TMSG_CUSTOM_FACE_UPLOAD_REQ",".CSMsg.TMSG_CUSTOM_FACE_UPLOAD_REQ",false,{},{},nil,{})
F169D=F(2,"errorCode",".CSMsg.TMSG_CUSTOM_FACE_UPLOAD_RSP.errorCode",1,0,1,false,0,5,1)
F170D=F(2,"pos",".CSMsg.TMSG_CUSTOM_FACE_UPLOAD_RSP.pos",2,1,1,false,0,5,1)
M50G=D(1,"TMSG_CUSTOM_FACE_UPLOAD_RSP",".CSMsg.TMSG_CUSTOM_FACE_UPLOAD_RSP",false,{},{},nil,{})
F171D=F(2,"imageId",".CSMsg.TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ.imageId",1,0,1,false,"",9,9)
F172D=F(2,"curl",".CSMsg.TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ.curl",2,1,1,false,"",9,9)
F173D=F(2,"pos",".CSMsg.TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ.pos",3,2,1,false,0,5,1)
M51G=D(1,"TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ",".CSMsg.TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ",false,{},{},nil,{})
F174D=F(2,"errorCode",".CSMsg.TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP.errorCode",1,0,1,false,0,5,1)
F175D=F(2,"data",".CSMsg.TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP.data",2,1,1,false,nil,11,10)
F176D=F(2,"customFaceTime",".CSMsg.TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP.customFaceTime",3,2,1,false,0,13,3)
M52G=D(1,"TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP",".CSMsg.TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP",false,{},{},nil,{})
F177D=F(2,"datas",".CSMsg.TMSG_CUSTOM_FACE_VERIFY_NTF.datas",1,0,3,false,{},11,10)
M54G=D(1,"TMSG_CUSTOM_FACE_VERIFY_NTF",".CSMsg.TMSG_CUSTOM_FACE_VERIFY_NTF",false,{},{},nil,{})
F178D=F(2,"customFaceFlag",".CSMsg.TMSG_CUSTOM_FACE_ACTIVATE_NTF.customFaceFlag",1,0,1,false,0,13,3)
M55G=D(1,"TMSG_CUSTOM_FACE_ACTIVATE_NTF",".CSMsg.TMSG_CUSTOM_FACE_ACTIVATE_NTF",false,{},{},nil,{})
F179D=F(2,"imageId",".CSMsg.TMSG_CUSTOM_FACE_USE_REQ.imageId",1,0,1,false,"",9,9)
F180D=F(2,"pos",".CSMsg.TMSG_CUSTOM_FACE_USE_REQ.pos",2,1,1,false,0,5,1)
M56G=D(1,"TMSG_CUSTOM_FACE_USE_REQ",".CSMsg.TMSG_CUSTOM_FACE_USE_REQ",false,{},{},nil,{})
F181D=F(2,"errorCode",".CSMsg.TMSG_CUSTOM_FACE_USE_RSP.errorCode",1,0,1,false,0,5,1)
F182D=F(2,"data",".CSMsg.TMSG_CUSTOM_FACE_USE_RSP.data",2,1,1,false,nil,11,10)
M57G=D(1,"TMSG_CUSTOM_FACE_USE_RSP",".CSMsg.TMSG_CUSTOM_FACE_USE_RSP",false,{},{},nil,{})
F183D=F(2,"imageId",".CSMsg.TMSG_CUSTOM_FACE_REMOVE_REQ.imageId",1,0,1,false,"",9,9)
F184D=F(2,"pos",".CSMsg.TMSG_CUSTOM_FACE_REMOVE_REQ.pos",2,1,1,false,0,5,1)
M58G=D(1,"TMSG_CUSTOM_FACE_REMOVE_REQ",".CSMsg.TMSG_CUSTOM_FACE_REMOVE_REQ",false,{},{},nil,{})
F185D=F(2,"errorCode",".CSMsg.TMSG_CUSTOM_FACE_REMOVE_RSP.errorCode",1,0,1,false,0,5,1)
F186D=F(2,"imageId",".CSMsg.TMSG_CUSTOM_FACE_REMOVE_RSP.imageId",2,1,1,false,"",9,9)
F187D=F(2,"pos",".CSMsg.TMSG_CUSTOM_FACE_REMOVE_RSP.pos",3,2,1,false,0,5,1)
M59G=D(1,"TMSG_CUSTOM_FACE_REMOVE_RSP",".CSMsg.TMSG_CUSTOM_FACE_REMOVE_RSP",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M}
E2M.values = {V18M,V19M,V20M}
M1G.fields={F1D}
F2D.enum_type=error_code_pb.E1M
M2G.fields={F2D, F3D}
M4G.fields={F4D}
F5D.enum_type=error_code_pb.E1M
M5G.fields={F5D}
M6G.fields={F6D}
F7D.enum_type=error_code_pb.E1M
M7G.fields={F7D}
M8G.fields={F8D, F9D, F10D}
M9G.fields={F11D, F12D}
M10G.fields={F13D}
M11G.fields={F14D}
F15D.enum_type=error_code_pb.E1M
M12G.fields={F15D, F16D, F17D}
M13G.fields={F18D}
F19D.enum_type=error_code_pb.E1M
M14G.fields={F19D, F20D, F21D}
M15G.fields={F22D, F23D, F24D, F25D}
F26D.enum_type=error_code_pb.E1M
M16G.fields={F26D, F27D, F28D}
M17G.fields={F29D, F30D}
F31D.enum_type=error_code_pb.E1M
M18G.fields={F31D, F32D, F33D, F34D, F35D}
M19G.fields={F36D, F37D}
M20G.fields={F38D, F39D}
F40D.enum_type=error_code_pb.E1M
F42D.message_type=M20G
M21G.fields={F40D, F41D, F42D}
F43D.enum_type=error_code_pb.E1M
F44D.message_type=M24G
M23G.fields={F43D, F44D}
F45D.enum_type=error_code_pb.E1M
F46D.message_type=M27G
M26G.fields={F45D, F46D, F47D}
M28G.fields={F48D, F49D}
F50D.enum_type=error_code_pb.E1M
M29G.fields={F50D, F51D, F52D}
M24G.fields={F53D, F54D, F55D, F56D, F57D, F58D, F59D, F60D, F61D, F62D}
F63D.message_type=M24G
M30G.fields={F63D}
M27G.fields={F64D, F65D, F66D, F67D}
F68D.message_type=M27G
M31G.fields={F68D}
M32G.fields={F69D, F70D, F71D, F72D, F73D, F74D, F75D, F76D, F77D, F78D, F79D, F80D, F81D, F82D, F83D, F84D, F85D, F86D, F87D, F88D, F89D, F90D}
M33G.fields={F91D}
F92D.enum_type=error_code_pb.E1M
F93D.message_type=M32G
M34G.fields={F92D, F93D}
M35G.fields={F94D, F95D}
F96D.enum_type=error_code_pb.E1M
M36G.fields={F96D, F97D}
M37G.fields={F98D}
M38G.fields={F99D, F100D}
F101D.enum_type=error_code_pb.E1M
M39G.fields={F101D, F102D}
M40G.fields={F103D}
M41G.fields={F104D, F105D, F106D, F107D, F108D, F109D, F110D, F111D, F112D, F113D, F114D, F115D, F116D, F117D, F118D, F119D, F120D, F121D, F122D, F123D, F124D, F125D, F126D, F127D, F128D, F129D, F130D, F131D, F132D, F133D, F134D, F135D, F136D, F137D, F138D, F139D, F140D, F141D, F142D, F143D, F144D, F145D, F146D, F147D, F148D, F149D}
M42G.fields={F150D, F151D, F152D, F153D, F154D, F155D, F156D}
F157D.message_type=M41G
F158D.message_type=M42G
M43G.fields={F157D, F158D}
M44G.fields={F159D, F160D}
M45G.fields={F161D}
F162D.enum_type=error_code_pb.E1M
M46G.fields={F162D, F163D, F164D}
M47G.fields={F165D}
F166D.enum_type=error_code_pb.E1M
M48G.fields={F166D, F167D}
M49G.fields={F168D}
M50G.fields={F169D, F170D}
M51G.fields={F171D, F172D, F173D}
F175D.message_type=common_new_pb.M11G
M52G.fields={F174D, F175D, F176D}
F177D.message_type=common_new_pb.M11G
M54G.fields={F177D}
M55G.fields={F178D}
M56G.fields={F179D, F180D}
F182D.message_type=common_new_pb.M11G
M57G.fields={F181D, F182D}
M58G.fields={F183D, F184D}
M59G.fields={F185D, F186D, F187D}

RobotPoolType_Carriage = 2
RobotPoolType_RebirthSpace = 1
RobotPoolType_unknown = 0
SpLikeNum =M(M20G)
TDBLikeData =M(M30G)
TDBRoleLikeData =M(M31G)
TFaceProp =M(M8G)
TLikeItemData =M(M24G)
TMSG_CUSTOM_FACE_ACTIVATE_NTF =M(M55G)
TMSG_CUSTOM_FACE_REMOVE_REQ =M(M58G)
TMSG_CUSTOM_FACE_REMOVE_RSP =M(M59G)
TMSG_CUSTOM_FACE_UPLOAD_REQ =M(M49G)
TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ =M(M51G)
TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP =M(M52G)
TMSG_CUSTOM_FACE_UPLOAD_RSP =M(M50G)
TMSG_CUSTOM_FACE_USE_REQ =M(M56G)
TMSG_CUSTOM_FACE_USE_RSP =M(M57G)
TMSG_CUSTOM_FACE_VERIFY_NTF =M(M54G)
TMSG_GET_MAIL_LIKE_REQ =M(M17G)
TMSG_GET_MAIL_LIKE_RSP =M(M18G)
TMSG_GET_MAKE_HERO_EQUIP_REQ =M(M47G)
TMSG_GET_MAKE_HERO_EQUIP_RSP =M(M48G)
TMSG_GET_SP_LIKENUM_REQ =M(M19G)
TMSG_GET_SP_LIKENUM_RSP =M(M21G)
TMSG_LIKE_GET_RECORD_REQ =M(M22G)
TMSG_LIKE_GET_RECORD_RSP =M(M23G)
TMSG_LIKE_GET_ROLE_RECORD_REQ =M(M25G)
TMSG_LIKE_GET_ROLE_RECORD_RSP =M(M26G)
TMSG_PRIVATE_CHAT_LIKE_REQ =M(M28G)
TMSG_PRIVATE_CHAT_LIKE_RSP =M(M29G)
TMSG_ROLE_NATIONALFLAG_MDF_REQ =M(M45G)
TMSG_ROLE_NATIONALFLAG_MDF_RSP =M(M46G)
TMSG_ROLE_NATIONALFLAG_STATE_NTF =M(M44G)
TMSG_ROLE_RESOURCE_STAT_NTF =M(M43G)
TMSG_ZONE_NEW_ROLEINFO_REQ =M(M33G)
TMSG_ZONE_NEW_ROLEINFO_RSP =M(M34G)
TMSG_ZONE_ROLE_CITY_EFFECT_UPDATE_REQ =M(M38G)
TMSG_ZONE_ROLE_CITY_EFFECT_UPDATE_RSP =M(M39G)
TMSG_ZONE_ROLE_CITY_UPDATE_REQ =M(M35G)
TMSG_ZONE_ROLE_CITY_UPDATE_RSP =M(M36G)
TMSG_ZONE_ROLE_FACE_PROP_UNLOAD_REQ =M(M6G)
TMSG_ZONE_ROLE_FACE_PROP_UNLOAD_RSP =M(M7G)
TMSG_ZONE_ROLE_FACE_PROP_UPDATE_NTF =M(M9G)
TMSG_ZONE_ROLE_FACE_PROP_UPDATE_REQ =M(M4G)
TMSG_ZONE_ROLE_FACE_PROP_UPDATE_RSP =M(M5G)
TMSG_ZONE_ROLE_FACE_UPDATE_REQ =M(M1G)
TMSG_ZONE_ROLE_FACE_UPDATE_RSP =M(M2G)
TMSG_ZONE_ROLE_NEW_FACE_NTF =M(M10G)
TMSG_ZONE_ROLE_NEW_SCHLOSS_EFFECT_NTF =M(M40G)
TMSG_ZONE_ROLE_NEW_SCHLOSS_NTF =M(M37G)
TMSG_ZONE_ROLE_PRAISE_UPDATE_REQ =M(M15G)
TMSG_ZONE_ROLE_PRAISE_UPDATE_RSP =M(M16G)
TMSG_ZONE_ROLE_UPDATE_NAME_REQ =M(M11G)
TMSG_ZONE_ROLE_UPDATE_NAME_RSP =M(M12G)
TMSG_ZONE_ROLE_UPDATE_SEX_REQ =M(M13G)
TMSG_ZONE_ROLE_UPDATE_SEX_RSP =M(M14G)
TRoleLikeItemData =M(M27G)
enLikeSysType_3v3Arena = 13
enLikeSysType_AcornPub = 2
enLikeSysType_AllianceFire = 14
enLikeSysType_AllianceMail = 8
enLikeSysType_AllianceShip = 9
enLikeSysType_BattleDuel = 1
enLikeSysType_NoneType = 0
enLikeSysType_NoviceArena = 11
enLikeSysType_PeakArena = 12
enLikeSysType_PresidentialMail = 7
enLikeSysType_Radar = 3
enLikeSysType_RolePage = 4
enLikeSysType_StormArena = 16
enLikeSysType_SurpriseBox = 6
enLikeSysType_SxMainCity = 5
enLikeSysType_TreasureMap = 10
enLikeSysType_ZombieDisaster = 15
tRoleLobbySource =M(M42G)
tRoleNewInfo =M(M32G)
tRoleZoneSource =M(M41G)

