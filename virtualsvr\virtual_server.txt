-- virtual_server.txt ----------------------------------------
-- author:  郑秀程
-- date:    2017.10.27
-- ver:     1.0
-- desc:    虚拟服务器
--------------------------------------------------------------

local require = require
local net = require "virtual_net"
local pairs = pairs

module("virtual_server")

--[[消息处理器模块]]
local MSG_HANDLER_MODULE = 
{
    ["virtual_novice_module"] = true,--新手模块
    ["virtual_home_build_module"] = true,--家园建筑模块
    ["virtual_player_module"] = true,--玩家模块
}

local bStarted = false

--[[是否已经启动]]
function IsStarted()
    return bStarted
end

--[[启动]]
function Start()
    if bStarted then
        return
    end

    net.Start()
    
    for m,_ in pairs(MSG_HANDLER_MODULE) do
        local mdl = require(m)
        mdl.OnVirtualServerStarted(net)
    end
    
    bStarted = true
end

--[[停止]]
function Stop()

    for m,_ in pairs(MSG_HANDLER_MODULE) do
        local mdl = require(m)
        mdl.OnVirtualServerStoped(net)
    end
    
    net.Stop()
    
    bStarted = false
     
end

--[[注册一个模块]]
function RegModule(m)
    if MSG_HANDLER_MODULE[m] == true then
        return
    end

    MSG_HANDLER_MODULE[m] = true
    if bStarted == true then
        local mdl = require(m)
        if mdl and mdl.OnVirtualServerStoped then
            mdl.OnVirtualServerStarted(net)
        end
    end
end

--[[注销一个模块]]
function UnregModule(m)
    if MSG_HANDLER_MODULE[m] == nil then
        return
    end
    
    local mdl = require(m)
    if mdl and mdl.OnVirtualServerStoped then
        mdl.OnVirtualServerStoped(net)
    end
    
    MSG_HANDLER_MODULE[m] = nil
end
