---
--- Generated by Emmy<PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by du<PERSON><PERSON>.
--- DateTime: 2025/1/9 20:19
---
local require = require
local string = string
local tonumber = tonumber
local newclass = newclass
local pcall = pcall

local string_util = require "string_util"
local ui_window_mgr = require "ui_window_mgr"
local event = require "event"
local game_config = require "game_config"
local Utility = CS.War.Script.Utility
local ui_setting_cfg = require "ui_setting_cfg"
local EnSubModel = ui_setting_attr_enum.EnSubModel
local EnBaseAttrKey = ui_setting_attr_enum.EnBaseAttrKey
local ui_setting_data = require "ui_setting_data"
local player_mgr = require "player_mgr"
local util = require "util"
local player_prefs = require "player_prefs"
local chat_mgr_new = require "chat_mgr_new"

module("ui_chat_allianceNoticeObj")
---@class ui_chat_allianceNoticeObj

local M = newclass("ui_chat_allianceNoticeObj")
M.isInit = false
--聊天中的公告item走的是聊天的一套，跟这里流程不一样

---构造函数
function M:ctor()

end

--初始化
function M:Init(obj)
    self.data = {
        instance = nil,
        clickClose = nil,
        clickTurnUrgent = nil,
        clickDelete = nil,
        clickTranslate = nil,
        clickSelfBtn_Jump2Detail = nil,
        msgData = nil,
    }
    self.allianceLocalData = nil
    self.localDataKey = "uiChatAllianceNotice"

    self.data.instance = obj
end

-- 1.
function M:SetAutoContent(isAutoContent)
    self.data.isAutoContent = isAutoContent
    return self
end

function M:SetHideCloseBtn()
    self.data.hideCloseBtn = true
    return self
end

function M:SetShowTranslateAndNormalContent()
    self.data.ShowTranslateAndNormalContent = true
end

--2.
function M:SetData(msgData)
    self.data.msgData = msgData
    local ui_chat_data_gw = require "ui_chat_data_gw"
    ui_chat_data_gw.SetAllianceNoticeItemData({ data = self.data }, msgData)
    self:RegisterEvt()
    self:RenderTranslate(msgData)
    self:CheckAutoContent()
end

function M:SetShowDeleteBtn()
    local alliance_mgr = require "alliance_mgr"
    self.data.isShowDeleteBtn =alliance_mgr.IsSelfR4R5() and true or false
end

function M:ForceRegisterEvt()
    self.forceRegisterEvt = true
end

function M:SetDeleteCB(deleteCB)
    self.data.deleteCB = deleteCB
end

function M:RegisterBtnSelfClick_Delete()
    self.data.btn_delete.onClick:RemoveAllListeners()
    self.data.btn_delete.onClick:AddListener(self.data.clickDelete)
end
---设置点击自身的按钮事件（跳转到详情界面）
function M:RegisterBtnSelfClick_Jump2Detail()
    self.data.btn_self.onClick:RemoveAllListeners()
    self.clickSelfBtn_Jump2Detail = function()
        self:ShowNoticeDetailPanel()
    end
    self.data.btn_self.onClick:AddListener(self.clickSelfBtn_Jump2Detail)
end

function M:ShowNoticeDetailPanel()
    ui_window_mgr:ShowModule("ui_alliance_notice_detail_panel", nil, nil, self.data.msgData)
end

--检测自适应开关
function M:CheckAutoContent()
    if not self.data.scrollItem then
        return
    end
    local scrollItem = self.data.scrollItem
    local selfContentSize = scrollItem:Get("selfContentSize")
    selfContentSize.enabled = self.data.isAutoContent ~= nil and self.data.isAutoContent
end

--监听翻译完成事件
function M:OnTranslateFinishHandler()
    if self.data.OnTranslateEvent then
        return
    end
    self.data.OnTranslateEvent = function(event, sStr, tStr, langIso)
        if not self.data.isTranslated and self.data.instance.gameObject.activeInHierarchy then
            if self.data.onTranslate then
                self.data.onTranslate(sStr, tStr, langIso)
            end
        end
    end
    event.Register(event.TRANSLATE_RSP, self.data.OnTranslateEvent)
end

function M:RegisterEvt()
    if self.isInit and not self.forceRegisterEvt then
        return
    end

    self.data.btn_turnUrgent.onClick:RemoveAllListeners()
    self.data.btn_turnUrgent.onClick:AddListener(self.data.clickTurnUrgent)
    self.clickClose = function()
        self:Close()
    end
    self.data.btn_close.onClick:RemoveAllListeners()
    self.data.btn_close.onClick:AddListener(self.clickClose)
    self.isInit = true

    self.data.clickTranslate = function()
        self.data.onClickTranslation()
    end
    self:AddTranslateHandler()
end

---添加翻译事件
function M:AddTranslateHandler()
    self.data.btn_translate.onClick:RemoveAllListeners()
    self.data.btn_translate.onClick:AddListener(self.data.clickTranslate)
    self:OnTranslateFinishHandler()
end

---渲染翻译
function M:RenderTranslate(data)
    local btn_translate = self.data.btn_translate
    local line = self.data.line
    local content = self.data.txt_content
    local contentTranslate = self.data.txt_content_translate
    if not data then
        line:SetActive(false)
        contentTranslate:SetActive(false)
        return
    end

    --if data.roleid ~= player_mgr.GetPlayerRoleID() and self.data.instance.activeInHierarchy then
    if self.data.instance.gameObject.activeInHierarchy then
        --系统语言改为游戏内语言 
        local langkey = ui_setting_data.GetAttrData(EnSubModel.En_Model_Base, EnBaseAttrKey.En_AttrKey_Lang)
        local LangIso = ui_setting_cfg.LangMap[tonumber(langkey)].iso
        local tStr, err = pcall(chat_mgr_new.GetTranslation, data.context, LangIso)
        if err then
            print("Error in GetTranslation:", err)  -- 记录错误日志
            return nil
        end

        contentTranslate:SetActive(tStr ~= nil) --翻译文本框
        btn_translate:SetActive(not self.data.isTranslated and tStr == nil and ((not game_config.Q1SDK_DOMESTIC) or Utility.IsInEditor())) --翻译按钮
        line:SetActive(tStr ~= nil and self.data.ShowTranslateAndNormalContent ~= nil) --分割线
        content:SetActive(tStr == nil or self.data.ShowTranslateAndNormalContent ~= nil) --原文本框
        content.text = data.context
        --TODO 已翻译图标展示，正在翻译图标展示

        if not tStr then
            if not self.data.onClickTranslation then
                self.data.isTranslated = false
                self.data.onClickTranslation = function()
                    local net_chat_module_new = require "net_chat_module_new"
                    net_chat_module_new.Req_TRANSLATE(data.context)

                    self.data.onTranslate = function(sStr, tStr, langIso)
                        if data.context == sStr then
                            self:RenderTranslate(data)
                        end
                    end
                end
            end
        else
            self.data.isTranslated = true
            util.DelayCallOnce(0.1, function()
                if self.data.instance.gameObject.activeInHierarchy then
                    contentTranslate.text = tStr
                end
            end)
        end
    end
end

--region 渲染翻译相关
function M:RenderTranslate(data)
    if not data then
        self:SetUIStateForTranslation(false, false, false, false)
        return
    end

    --if data.roleid ~= player_mgr.GetPlayerRoleID() and self.data.instance.activeInHierarchy then
    if not self.data.instance.gameObject.activeInHierarchy then
        return
    end

    local content = self.data.txt_content
    local contentTranslate = self.data.txt_content_translate
    -- 获取翻译内容
    local langkey = ui_setting_data.GetAttrData(EnSubModel.En_Model_Base, EnBaseAttrKey.En_AttrKey_Lang)
    local LangIso = ui_setting_cfg.LangMap[tonumber(langkey)].iso
    local tStr = self:HandleTranslation(data.context, LangIso)

    -- 更新 UI 状态
    self:SetUIStateForTranslation(
            tStr ~= nil,
            tStr == nil or self.data.ShowTranslateAndNormalContent ~= nil,
            tStr ~= nil and self.data.ShowTranslateAndNormalContent ~= nil,
            not self.data.isTranslated and tStr == nil and ((not game_config.Q1SDK_DOMESTIC) or Utility.IsInEditor())
    )
    content.text = data.context
    -- 注册翻译回调
    if not tStr then
        self:RegisterTranslationCallback(data)
    else
        self.data.isTranslated = true
        util.DelayCallOnce(0.1, function()
            if self.data and self.data.instance.gameObject.activeInHierarchy then
                contentTranslate.text = tStr
            end
        end)
    end
end

function M:HandleTranslation(context, langIso)
    local err, tStr = pcall(chat_mgr_new.GetTranslation, context, langIso)
    if not err then
        print("Error in GetTranslation:", err)
        return nil
    end
    return tStr
end

function M:SetUIStateForTranslation(showTranslate, showContent, showLine, showTrsBtn)
    local btn_translate = self.data.btn_translate
    local line = self.data.line
    local content = self.data.txt_content
    local contentTranslate = self.data.txt_content_translate
    contentTranslate:SetActive(showTranslate)
    line:SetActive(showLine)
    content:SetActive(showContent)
    btn_translate:SetActive(showTrsBtn)
end

function M:RegisterTranslationCallback(data)
    if not self.data.onClickTranslation then
        self.data.isTranslated = false
        self.data.onClickTranslation = function()
            local net_chat_module_new = require "net_chat_module_new"
            net_chat_module_new.Req_TRANSLATE(data.context)

            self.data.onTranslate = function(sStr, tStr, langIso)
                if data.context == sStr then
                    self:RenderTranslate(data)
                end
            end
        end
    end
end
--endregion

function M:UnRegisterEvt()
    if not self.data then
        return
    end
    if self.data.clickTurnUrgent and self.data.btn_turnUrgent then
        self.data.btn_turnUrgent.onClick:RemoveListener(self.data.clickTurnUrgent)
    end
    if self.data.btn_close then
        self.data.btn_close.onClick:RemoveListener(self.clickClose)
    end
    if self.data.clickTranslate and self.data.btn_translate then
        self.data.btn_translate.onClick:RemoveListener(self.data.clickTranslate)
    end
    if self.data.clickDelete and self.data.btn_delete then
        self.data.btn_delete.onClick:RemoveListener(self.data.clickDelete)
    end

    if self.data.clickSelfBtn_Jump2Detail and self.data.btn_self then
        self.data.btn_self.onClick:RemoveListener(self.clickSelfBtn_Jump2Detail)
    end

    if self.data.OnTranslateEvent then
        event.Unregister(event.TRANSLATE_RSP, self.data.OnTranslateEvent)
    end

    self.isInit = false
end

---检测置顶是否显示
function M:TryShowInTop()
    if not self.allianceLocalData then
        self:GetLocalData()
    end
    local newNotice = chat_mgr_new.GetNewAllianceNotice()

    local noticeIdCur = self.allianceLocalData.noticeIdCurrent

    ---检测本地存储的最新联盟公告id是否与最新公告id一致
    --一致检测是否已经存储为关闭状态，若是则不显示，若否则显示
    --不一致则显示

    if newNotice then
        if string.IsNullOrEmpty(noticeIdCur) or noticeIdCur ~= newNotice.szChatID then
            self.allianceLocalData.isClose = false
            self:SetLocalData(newNotice.szChatID)
        end
    end

    if newNotice and not self.allianceLocalData.isClose then
        self.data.instance:SetActive(not self.allianceLocalData.isClose)
        self.forceRegisterEvt = true
        self:SetData(newNotice)
        self:RegisterBtnSelfClick_Jump2Detail()
    end
end

---获取本地存储数据
function M:GetLocalData()
    local tmpData = player_prefs.GetCacheData(self.localDataKey)
    if tmpData then
        self.allianceLocalData = tmpData
    else
        self.allianceLocalData = {
            noticeIdCurrent = "",
            isClose = true,
        }
    end
end

function M:SetLocalData(sId)
    self.allianceLocalData.noticeIdCurrent = sId
    player_prefs.SetCacheData(self.localDataKey, self.allianceLocalData)
end

function M:Close()
    self.allianceLocalData.isClose = true
    player_prefs.SetCacheData(self.localDataKey, self.allianceLocalData)
    self.data.instance:SetActive(false)
end

function M:Dispose()
    self:UnRegisterEvt()
    self.data = nil
    self.allianceLocalData = nil
end

return M