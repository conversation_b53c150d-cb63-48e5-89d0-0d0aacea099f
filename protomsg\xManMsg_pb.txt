-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
module('xManMsg_pb')


V1M=V(4,"MSG_ALLIANCE_RECOMMEND_REQ",0,200001)
V2M=V(4,"MSG_ALLIANCE_RECOMMEND_RSP",1,200002)
V3M=V(4,"MSG_ALLIANCE_SEARCH_REQ",2,200003)
V4M=V(4,"MSG_ALLIANCE_SEARCH_RSP",3,200004)
V5M=V(4,"MSG_ALLIANCE_INFO_REQ",4,200005)
V6M=V(4,"MSG_ALLIANCE_INFO_RSP",5,200006)
V7M=V(4,"MSG_ALLIANCE_ROLE_INFO_REQ",6,200007)
V8M=V(4,"MSG_ALLIANCE_ROLE_INFO_RSP",7,200008)
V9M=V(4,"MSG_ALLIANCE_QUICK_ADD_REQ",8,200009)
V10M=V(4,"MSG_ALLIANCE_QUICK_ADD_RSP",9,200010)
V11M=V(4,"MSG_ALLIANCE_APPLY_REQ",10,200011)
V12M=V(4,"MSG_ALLIANCE_APPLY_RSP",11,200012)
V13M=V(4,"MSG_ALLIANCE_CHECKNAME_REQ",12,200013)
V14M=V(4,"MSG_ALLIANCE_CHECKNAME_RSP",13,200014)
V15M=V(4,"MSG_ALLIANCE_RANDOM_NAME_REQ",14,200015)
V16M=V(4,"MSG_ALLIANCE_RANDOM_NAME_RSP",15,200016)
V17M=V(4,"MSG_ALLIANCE_CREATE_REQ",16,200017)
V18M=V(4,"MSG_ALLIANCE_CREATE_RSP",17,200018)
V19M=V(4,"MSG_ALLIANCE_CHANGECEO_REQ",18,200019)
V20M=V(4,"MSG_ALLIANCE_CHANGECEO_RSP",19,200020)
V21M=V(4,"MSG_ALLIANCE_AUTHORITY_REQ",20,200021)
V22M=V(4,"MSG_ALLIANCE_AUTHORITY_RSP",21,200022)
V23M=V(4,"MSG_ALLIANCE_EXPEL_REQ",22,200023)
V24M=V(4,"MSG_ALLIANCE_EXPEL_RSP",23,200024)
V25M=V(4,"MSG_ALLIANCE_CHECKCONTENT_REQ",24,200025)
V26M=V(4,"MSG_ALLIANCE_CHECKCONTENT_RSP",25,200026)
V27M=V(4,"MSG_ALLIANCE_CHANGE_NTF",26,200027)
V28M=V(4,"MSG_ALLIANCE_GIFT_GET_REQ",27,200029)
V29M=V(4,"MSG_ALLIANCE_GIFT_GET_RSP",28,200030)
V30M=V(4,"MSG_ALLIANCE_GIFT_NTF",29,200032)
V31M=V(4,"MSG_ALLIANCE_ANONYMOUS_REQ",30,200033)
V32M=V(4,"MSG_ALLIANCE_ANONYMOUS_RSP",31,200034)
V33M=V(4,"MSG_ALLIANCE_TECHNOLOGY_REQ",32,200035)
V34M=V(4,"MSG_ALLIANCE_TECHNOLOGY_RSP",33,200036)
V35M=V(4,"MSG_ALLIANCE_TECHNOLOGY_RECOMMEND_REQ",34,200037)
V36M=V(4,"MSG_ALLIANCE_TECHNOLOGY_RECOMMEND_RSP",35,200038)
V37M=V(4,"MSG_ALLIANCE_TECHNOLOGY_STUDY_REQ",36,200039)
V38M=V(4,"MSG_ALLIANCE_TECHNOLOGY_STUDY_RSP",37,200040)
V39M=V(4,"MSG_ALLIANCE_TECHNOLOGY_DONATE_REQ",38,200041)
V40M=V(4,"MSG_ALLIANCE_TECHNOLOGY_DONATE_RSP",39,200042)
V41M=V(4,"MSG_ALLIANCE_MODIFY_ANNOUNCEMENT_REQ",40,200051)
V42M=V(4,"MSG_ALLIANCE_MODIFY_ANNOUNCEMENT_RSP",41,200052)
V43M=V(4,"MSG_ALLIANCE_APPLICATION_LIST_REQ",42,200053)
V44M=V(4,"MSG_ALLIANCE_APPLICATION_LIST_RSP",43,200054)
V45M=V(4,"MSG_ALLIANCE_HANDLE_APPLICATION_REQ",44,200055)
V46M=V(4,"MSG_ALLIANCE_HANDLE_APPLICATION_RSP",45,200056)
V47M=V(4,"MSG_ALLIANCE_MODIFY_FLAG_REQ",46,200057)
V48M=V(4,"MSG_ALLIANCE_MODIFY_FLAG_RSP",47,200058)
V49M=V(4,"MSG_ALLIANCE_MODIFY_NAME_REQ",48,200059)
V50M=V(4,"MSG_ALLIANCE_MODIFY_NAME_RSP",49,200060)
V51M=V(4,"MSG_ALLIANCE_MODIFY_LANGUAGE_REQ",50,200061)
V52M=V(4,"MSG_ALLIANCE_MODIFY_LANGUAGE_RSP",51,200062)
V53M=V(4,"MSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_REQ",52,200063)
V54M=V(4,"MSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP",53,200064)
V55M=V(4,"MSG_ALLIANCE_MAIL_SEND_REQ",54,200067)
V56M=V(4,"MSG_ALLIANCE_MAIL_SEND_RSP",55,200068)
V57M=V(4,"MSG_ALLIANCE_UPDATE_NTF",56,200069)
V58M=V(4,"MSG_ALLIANCE_EXIT_REQ",57,200070)
V59M=V(4,"MSG_ALLIANCE_EXIT_RSP",58,200071)
V60M=V(4,"MSG_ALLIANCE_POWERRANK_REQ",59,200072)
V61M=V(4,"MSG_ALLIANCE_POWERRANK_RSP",60,200073)
V62M=V(4,"MSG_ALLIANCE_ROLERANK_REQ",61,200074)
V63M=V(4,"MSG_ALLIANCE_ROLERANK_RSP",62,200075)
V64M=V(4,"MSG_ALLIANCE_TECHNOLOGY_DETAILS_REQ",63,200076)
V65M=V(4,"MSG_ALLIANCE_TECHNOLOGY_DETAILS_RSP",64,200077)
V66M=V(4,"MSG_ALLIANCE_KILLNUM_REQ",65,200078)
V67M=V(4,"MSG_ALLIANCE_KILLNUM_RSP",66,200079)
V68M=V(4,"MSG_ALLIANCE_NEEDPOPUP_NTF",67,200080)
V69M=V(4,"MSG_ALLIANCE_HELP_SELF_REQ",68,200081)
V70M=V(4,"MSG_ALLIANCE_HELP_SELF_RSP",69,200082)
V71M=V(4,"MSG_ALLIANCE_HELP_LIST_REQ",70,200083)
V72M=V(4,"MSG_ALLIANCE_HELP_LIST_RSP",71,200084)
V73M=V(4,"MSG_ALLIANCE_HELP_LIST_NTF",72,200085)
V74M=V(4,"MSG_ALLIANCE_HELP_START_REQ",73,200086)
V75M=V(4,"MSG_ALLIANCE_HELP_START_RSP",74,200087)
V76M=V(4,"MSG_ALLIANCE_HELP_CLICK_REQ",75,200088)
V77M=V(4,"MSG_ALLIANCE_HELP_CLICK_RSP",76,200089)
V78M=V(4,"MSG_ALLIANCE_HELP_CLICK_NTF",77,200090)
V79M=V(4,"MSG_ALLIANCE_FIRST_JOINTIME_NTF",78,200091)
V80M=V(4,"MSG_ALLIANCE_ONE_CLICK_JOIN_REQ",79,200092)
V81M=V(4,"MSG_ALLIANCE_ONE_CLICK_JOIN_RSP",80,200093)
V82M=V(4,"MSG_ALLIANCE_MARK_REQ",81,200094)
V83M=V(4,"MSG_ALLIANCE_MARK_RSP",82,200095)
V84M=V(4,"MSG_ALLIANCE_SHARE_REQ",83,200096)
V85M=V(4,"MSG_ALLIANCE_SHARE_RSP",84,200097)
V86M=V(4,"MSG_ALLIANCE_INVITATION_REWARD_NTF",85,200098)
V87M=V(4,"MSG_ALLIANCE_INVITATION_REQ",86,200099)
V88M=V(4,"MSG_ALLIANCE_INVITATION_RSP",87,200100)
V89M=V(4,"MSG_SANDBOX_GET_DETAIL_REQ",88,200101)
V90M=V(4,"MSG_SANDBOX_GET_DETAIL_RSP",89,200102)
V91M=V(4,"MSG_SANDBOX_ATTACK_REQ",90,200103)
V92M=V(4,"MSG_SANDBOX_ATTACK_RSP",91,200104)
V93M=V(4,"MSG_SANDBOX_WONDERPOS_REQ",92,200105)
V94M=V(4,"MSG_SANDBOX_WONDERPOS_RSP",93,200106)
V95M=V(4,"MSG_SANDBOX_BACK_REQ",94,200107)
V96M=V(4,"MSG_SANDBOX_BACK_RSP",95,200108)
V97M=V(4,"MSG_MOVE_CITY_REQ",96,200109)
V98M=V(4,"MSG_MOVE_CITY_RSP",97,200110)
V99M=V(4,"MSG_STAMINA_OPER_REQ",98,200111)
V100M=V(4,"MSG_STAMINA_OPER_RSP",99,200112)
V101M=V(4,"MSG_SANDBOX_BASE_INFO_REQ",100,200113)
V102M=V(4,"MSG_SANDBOX_BASE_INFO_RSP",101,200114)
V103M=V(4,"MSG_SANDBOX_ENTER_REQ",102,200115)
V104M=V(4,"MSG_SANDBOX_ENTER_RSP",103,200116)
V105M=V(4,"MSG_SANDBOX_DATA_NTF",104,200117)
V106M=V(4,"MSG_SANDBOX_MOVE_VIEW_REQ",105,200118)
V107M=V(4,"MSG_SANDBOX_LOOT_INFO_REQ",106,200120)
V108M=V(4,"MSG_SANDBOX_LOOT_INFO_RSP",107,200121)
V109M=V(4,"MSG_SANDBOX_SAVE_TEAM_REQ",108,200122)
V110M=V(4,"MSG_SANDBOX_SAVE_TEAM_RSP",109,200123)
V111M=V(4,"MSG_SANDBOX_LOOT_DATA_NTF",110,200124)
V112M=V(4,"MSG_SANDBOX_TEAMINFO_NTF",111,200125)
V113M=V(4,"MSG_SANDBOX_GET_LOOT_REQ",112,200126)
V114M=V(4,"MSG_SANDBOX_GET_LOOT_RSP",113,200127)
V115M=V(4,"MSG_SANDBOX_EXPEDITION_DETAIL_REQ",114,200128)
V116M=V(4,"MSG_SANDBOX_EXPEDITION_DETAIL_RSP",115,200129)
V117M=V(4,"MSG_SANDBOX_EXIT_REQ",116,200130)
V118M=V(4,"MSG_SANDBOX_EXIT_RSP",117,200131)
V119M=V(4,"MSG_SANDBOX_SEARCH_REQ",118,200132)
V120M=V(4,"MSG_SANDBOX_SEARCH_RSP",119,200133)
V121M=V(4,"MSG_SANDBOX_MARK_REQ",120,200134)
V122M=V(4,"MSG_SANDBOX_MARK_RSP",121,200135)
V123M=V(4,"MSG_SANDBOX_DETECT_RSP",122,200136)
V124M=V(4,"MSG_SANDBOX_MARKLIST_NTF",123,200137)
V125M=V(4,"MSG_SANDBOX_REINFORCELIST_REQ",124,200138)
V126M=V(4,"MSG_SANDBOX_REINFORCELIST_RSP",125,200139)
V127M=V(4,"MSG_SANDBOX_REINFORCE_REQ",126,200140)
V128M=V(4,"MSG_SANDBOX_REINFORCE_RSP",127,200141)
V129M=V(4,"MSG_SANDBOX_REINFORCEBACK_REQ",128,200142)
V130M=V(4,"MSG_SANDBOX_REINFORCEBACK_RSP",129,200143)
V131M=V(4,"MSG_SANDBOX_ALERT_NTF",130,200144)
V132M=V(4,"MSG_SANDBOX_DETECT_REQ",131,200145)
V133M=V(4,"MSG_SANDBOX_MONSTERFK_NTF",132,200146)
V134M=V(4,"MSG_SANDBOX_MARCH_NTF",133,200147)
V135M=V(4,"MSG_SANDBOX_ACCELERATE_REQ",134,200148)
V136M=V(4,"MSG_SANDBOX_ACCELERATE_RSP",135,200149)
V137M=V(4,"MSG_SCIENTIFICRESEARCH_OPENBUILD_REQ",136,200150)
V138M=V(4,"MSG_SCIENTIFICRESEARCH_OPENBUILD_RSP",137,200151)
V139M=V(4,"MSG_SCIENTIFICRESEARCH_DORESEARCH_REQ",138,200152)
V140M=V(4,"MSG_SCIENTIFICRESEARCH_DORESEARCH_RSP",139,200153)
V141M=V(4,"MSG_SCIENTIFICRESEARCH_GETRESEARCH_REQ",140,200154)
V142M=V(4,"MSG_SCIENTIFICRESEARCH_GETRESEARCH_RSP",141,200155)
V143M=V(4,"MSG_SCIENTIFICRESEARCH_BUILDUPDATE_NTF",142,200156)
V144M=V(4,"MSG_SCIENTIFICRESEARCH_SCIENTIFICUPDATE_NTF",143,200157)
V145M=V(4,"MSG_SPEEDUPUSEITEM_REQ",144,200170)
V146M=V(4,"MSG_SPEEDUPUSEITEM_RSP",145,200171)
V147M=V(4,"MSG_SPEEDUPUSEDIAMOND_REQ",146,200172)
V148M=V(4,"MSG_SPEEDUPUSEDIAMOND_RSP",147,200173)
V149M=V(4,"MSG_SPEEDUPONECLICKUSEITEM_REQ",148,200174)
V150M=V(4,"MSG_SPEEDUPONECLICKUSEITEM_RSP",149,200175)
V151M=V(4,"MSG_SPEEDUPONECLICKUSEDIAMOND_REQ",150,200176)
V152M=V(4,"MSG_SPEEDUPONECLICKUSEDIAMOND_RSP",151,200177)
V153M=V(4,"MSG_CITY_GET_ALLINFO_RSP",152,200202)
V154M=V(4,"MSG_CITY_BUILD_A_BUILDING_REQ",153,200203)
V155M=V(4,"MSG_CITY_BUILD_A_BUILDING_RSP",154,200204)
V156M=V(4,"MSG_CITY_REPAIR_A_BUILDING_REQ",155,200205)
V157M=V(4,"MSG_CITY_REPAIR_A_BUILDING_RSP",156,200206)
V158M=V(4,"MSG_CITY_GET_BUILDING_OPENGIFT_REQ",157,200207)
V159M=V(4,"MSG_CITY_GET_BUILDING_OPENGIFT_RSP",158,200208)
V160M=V(4,"MSG_CITY_UPGRADE_A_BUILDING_REQ",159,200209)
V161M=V(4,"MSG_CITY_UPGRADE_A_BUILDING_RSP",160,200210)
V162M=V(4,"MSG_CITY_GET_BUILDING_RESOURCE_REQ",161,200211)
V163M=V(4,"MSG_CITY_GET_BUILDING_RESOURCE_RSP",162,200212)
V164M=V(4,"MSG_CITY_CHANGE_BUILDING_LOCATION_REQ",163,200213)
V165M=V(4,"MSG_CITY_CHANGE_BUILDING_LOCATION_RSP",164,200214)
V166M=V(4,"MSG_CITY_MILITARY_RECRUIT_SOLDIER_REQ",165,200215)
V167M=V(4,"MSG_CITY_MILITARY_RECRUIT_SOLDIER_RSP",166,200216)
V168M=V(4,"MSG_CITY_MILITARY_GET_SOLDIER_REQ",167,200217)
V169M=V(4,"MSG_CITY_MILITARY_GET_SOLDIER_RSP",168,200218)
V170M=V(4,"MSG_CITY_MILITARY_UPGRADE_SOLDIER_REQ",169,200219)
V171M=V(4,"MSG_CITY_MILITARY_UPGRADE_SOLDIER_RSP",170,200220)
V172M=V(4,"MSG_CITY_HOSPITAL_CURE_SOLDIER_REQ",171,200221)
V173M=V(4,"MSG_CITY_HOSPITAL_CURE_SOLDIER_RSP",172,200222)
V174M=V(4,"MSG_CITY_HOSPITAL_GET_SOLDIER_REQ",173,200223)
V175M=V(4,"MSG_CITY_HOSPITAL_GET_SOLDIER_RSP",174,200224)
V176M=V(4,"MSG_CITY_BUILDING_UPDATE_NTF",175,200225)
V177M=V(4,"MSG_CITY_BUILDQUEUE_UPDATE_NTF",176,200226)
V178M=V(4,"MSG_CITY_AREA_UPDATE_NTF",177,200227)
V179M=V(4,"MSG_CITY_WORKER_LEVELUP_REQ",178,200228)
V180M=V(4,"MSG_CITY_WORKER_LEVELUP_RSP",179,200229)
V181M=V(4,"MSG_CITY_DISPATCH_WORKER_REQ",180,200230)
V182M=V(4,"MSG_CITY_DISPATCH_WORKER_RSP",181,200231)
V183M=V(4,"MSG_CITY_TRAINNINGCENTER_UPDATE_NTF",182,200232)
V184M=V(4,"MSG_CITY_HOSPITAL_UPDATE_NTF",183,200233)
V185M=V(4,"MSG_CITY_WALL_FIRE_FIGHTING_REQ",184,200234)
V186M=V(4,"MSG_CITY_WALL_FIRE_FIGHTING_RSP",185,200235)
V187M=V(4,"MSG_CITY_BUILD_UPGRADE_NOW_REQ",186,200236)
V188M=V(4,"MSG_CITY_BUILD_UPGRADE_NOW_RSP",187,200237)
V189M=V(4,"MSG_CITY_BUILD_DIAMOND_RENT_QUEUE_REQ",188,200238)
V190M=V(4,"MSG_CITY_BUILD_DIAMOND_RENT_QUEUE_RSP",189,200239)
V191M=V(4,"MSG_CITY_SAVE_TROOP_REQ",190,200240)
V192M=V(4,"MSG_CITY_SAVE_TROOP_RSP",191,200241)
V193M=V(4,"MSG_CITY_GET_TROOP_REQ",192,200242)
V194M=V(4,"MSG_CITY_GET_TROOP_RSP",193,200243)
V195M=V(4,"MSG_CITY_GET_ALL_TROOP_REQ",194,200244)
V196M=V(4,"MSG_CITY_GET_ALL_TROOP_RSP",195,200245)
V197M=V(4,"MSG_CITY_PROP_UPDATE_NTF",196,200246)
V198M=V(4,"MSG_CITY_WORKER_REPLACE_REQ",197,200247)
V199M=V(4,"MSG_CITY_WORKER_REPLACE_RSP",198,200248)
V200M=V(4,"MSG_CITY_WORKER_COMPOSITE_REQ",199,200249)
V201M=V(4,"MSG_CITY_WORKER_COMPOSITE_RSP",200,200250)
V202M=V(4,"MSG_CITY_GET_EVENT_REWARD_REQ",201,200251)
V203M=V(4,"MSG_CITY_GET_EVENT_REWARD_RSP",202,200252)
V204M=V(4,"MSG_CITY_CLICK_FULL_OPEN_REQ",203,200253)
V205M=V(4,"MSG_CITY_CLICK_FULL_OPEN_RSP",204,200254)
V206M=V(4,"MSG_CITY_AREA_STATE_CHANGE_NTF",205,200255)
V207M=V(4,"MSG_CITY_COMPLETE_BEGIN_EVENT_REQ",206,200256)
V208M=V(4,"MSG_CITY_COMPLETE_BEGIN_EVENT_RSP",207,200257)
V209M=V(4,"MSG_CITY_EXCHANGE_ORDER_REQ",208,200258)
V210M=V(4,"MSG_CITY_EXCHANGE_ORDER_RSP",209,200259)
V211M=V(4,"MSG_CITY_TEAMINFO_NTF",210,200260)
V212M=V(4,"MSG_CITY_WORKER_DISPATCH_ONECLICK_REQ",211,200261)
V213M=V(4,"MSG_CITY_WORKER_DISPATCH_ONECLICK_RSP",212,200262)
V214M=V(4,"MSG_CITY_WORKER_UPDATE_NTF",213,200263)
V215M=V(4,"MSG_CITY_REWARD_QUEUE_NTF",214,200264)
V216M=V(4,"MSG_CITY_GET_REWARD_QUEUE_REQ",215,200265)
V217M=V(4,"MSG_CITY_GET_REWARD_QUEUE_RSP",216,200266)
V218M=V(4,"MSG_CITY_CHECK_AREA_EVENT_REQ",217,200267)
V219M=V(4,"MSG_CITY_CHECK_AREA_EVENT_RSP",218,200268)
V220M=V(4,"MSG_CITY_WALL_BUY_CITYDEFENSE_REQ",219,200269)
V221M=V(4,"MSG_CITY_WALL_BUY_CITYDEFENSE_RSP",220,200270)
V222M=V(4,"MSG_CITY_TROOP_IS_IDLE_REQ",221,200271)
V223M=V(4,"MSG_CITY_TROOP_IS_IDLE_RSP",222,200272)
V224M=V(4,"MSG_CITY_GET_ALLINFO_REQ",223,200273)
V225M=V(4,"MSG_SANDBOX__MASS_OPER_REQ",224,200300)
V226M=V(4,"MSG_SANDBOX__MASS_OPER_RSP",225,200301)
V227M=V(4,"MSG_SANDBOX_MASS_TEAM_REQ",226,200302)
V228M=V(4,"MSG_SANDBOX_MASS_TEAM_RSP",227,200303)
V229M=V(4,"MSG_SANDBOX_MASS_TEAM_MEMBER_REQ",228,200304)
V230M=V(4,"MSG_SANDBOX_MASS_TEAM_MEMBER_RSP",229,200305)
V231M=V(4,"MSG_SANDBOX_MASS_MAIN_ICON_NTF",230,200306)
V232M=V(4,"MSG_SANDBOX_MASS_CHANGE_NTF",231,200307)
V233M=V(4,"MSG_SANDBOX_MASS_LEADER_DEL_NTF",232,200308)
V234M=V(4,"MSG_SANDBOX_MEMBER_REQ",233,200400)
V235M=V(4,"MSG_SANDBOX_MEMBER_RSP",234,200401)
V236M=V(4,"MSG_BUFFINFO_NTF",235,200420)
V237M=V(4,"MSG_ROLEPROP_UPDATE_NTF",236,200421)
V238M=V(4,"MSG_ROLEPROP_LOGIN_NTF",237,200422)
V239M=V(4,"MSG_HERO_UPGRADE_NEW_REQ",238,200430)
V240M=V(4,"MSG_HERO_UPGRADE_NEW_RSP",239,200431)
V241M=V(4,"MSG_HERO_DEBRIS_COMPOSE_NEW_REQ",240,200432)
V242M=V(4,"MSG_HERO_DEBRIS_COMPOSE_NEW_RSP",241,200433)
V243M=V(4,"MSG_HERO_AWAKEN_NEW_REQ",242,200434)
V244M=V(4,"MSG_HERO_AWAKEN_NEW_RSP",243,200435)
V245M=V(4,"MSG_HERO_SKILL_UPGRADE_NEW_REQ",244,200436)
V246M=V(4,"MSG_HERO_SKILL_UPGRADE_NEW_RSP",245,200437)
V247M=V(4,"MSG_HERO_HONORWALL_INFO_REQ",246,200438)
V248M=V(4,"MSG_HERO_HONORWALL_INFO_RSP",247,200439)
V249M=V(4,"MSG_HERO_HONORWALL_UPGRADE_REQ",248,200440)
V250M=V(4,"MSG_HERO_HONORWALL_UPGRADE_RSP",249,200441)
V251M=V(4,"MSG_HERO_HONORWALL_INFO_NTF",250,200442)
V252M=V(4,"MSG_GET_ALL_HERO_REQ",251,200443)
V253M=V(4,"MSG_GET_ALL_HERO_RSP",252,200444)
V254M=V(4,"MSG_RADAR_INFO_REQ",253,200460)
V255M=V(4,"MSG_RADAR_INFO_RSP",254,200461)
V256M=V(4,"MSG_RADAR_DO_MISSION_REQ",255,200462)
V257M=V(4,"MSG_RADAR_DO_MISSION_RSP",256,200463)
V258M=V(4,"MSG_RADAR_RECEIVE_REWARD_REQ",257,200464)
V259M=V(4,"MSG_RADAR_RECEIVE_REWARD_RSP",258,200465)
V260M=V(4,"MSG_RADAR_GENERATE_NEW_MISSION_NTF",259,200466)
V261M=V(4,"MSG_RADAR_UPDATE_MISSION_INFO_NTF",260,200467)
V262M=V(4,"MSG_RADAR_DESTROY_MISSION_NTF",261,200468)
V263M=V(4,"MSG_RADAR_INFO_UPDATE_NTF",262,200469)
V264M=V(4,"MSG_RADAR_MISSION_REQ",263,200470)
V265M=V(4,"MSG_RADAR_MISSION_RSP",264,200471)
V266M=V(4,"MSG_RADAR_DO_MISSION_RPT",265,200472)
V267M=V(4,"MSG_RADAR_LEVEL_MISSION_REQ",266,200473)
V268M=V(4,"MSG_RADAR_LEVEL_MISSION_RSP",267,200474)
V269M=V(4,"MSG_RADAR_INFO_NTF",268,200475)
V270M=V(4,"MSG_RADAR_GRATITUDE_REQ",269,200476)
V271M=V(4,"MSG_RADAR_GRATITUDE_RSP",270,200477)
V272M=V(4,"MSG_RADAR_ASSIST_NTF",271,200478)
V273M=V(4,"MSG_SANDBOX_BATTLE_RESULT_NTF",272,200500)
V274M=V(4,"MSG_SANDBOX_BASEVFX_NTF",273,200501)
V275M=V(4,"MSG_SANDBOX_DEFFAILD_NTF",274,200502)
V276M=V(4,"MSG_SANDBOX_UPVOTE_NTF",275,200503)
V277M=V(4,"MSG_SANDBOX_VISUAL_ENTER_REQ",276,200504)
V278M=V(4,"MSG_SANDBOX_VISUAL_ENTER_RSP",277,200505)
V279M=V(4,"MSG_SANDBOX_ROLECITY_NTF",278,200506)
V280M=V(4,"MSG_SANDBOX_CROSS_BACK_POS_REQ",279,200507)
V281M=V(4,"MSG_SANDBOX_CROSS_BACK_POS_RSP",280,200508)
V282M=V(4,"MSG_SANDBOX_KILLSOLDIER_NTF",281,200509)
V283M=V(4,"MSG_LOBBY_GET_RECRUIT_POOLID_REQ",282,200600)
V284M=V(4,"MSG_LOBBY_GET_RECRUIT_POOLID_RSP",283,200601)
V285M=V(4,"MSG_LOBBY_GET_RECRUIT_POOLINFO_REQ",284,200602)
V286M=V(4,"MSG_LOBBY_GET_RECRUIT_POOLINFO_RSP",285,200603)
V287M=V(4,"MSG_LOBBY_GET_RECRUIT_POOLINFO_NTF",286,200604)
V288M=V(4,"MSG_DRONECENTER_UPGRADE_REQ",287,200700)
V289M=V(4,"MSG_DRONECENTER_UPGRADE_RSP",288,200701)
V290M=V(4,"MSG_DRONECENTER_PARTUPGRADE_REQ",289,200702)
V291M=V(4,"MSG_DRONECENTER_PARTUPGRADE_RSP",290,200703)
V292M=V(4,"MSG_DRONECENTER_PARTEXCHANGE_REQ",291,200704)
V293M=V(4,"MSG_DRONECENTER_PARTEXCHANGE_RSP",292,200705)
V294M=V(4,"MSG_DRONECENTER_ALLDATA_NTF",293,200706)
V295M=V(4,"MSG_DRONECENTER_ADORN_REQ",294,200707)
V296M=V(4,"MSG_DRONECENTER_ADORN_RSP",295,200708)
V297M=V(4,"MSG_DRONECENTER_ADORN_NTF",296,200709)
V298M=V(4,"MSG_DRONEADVANDE_UPGRADE_REQ",297,200710)
V299M=V(4,"MSG_DRONEADVANDE_UPGRADE_RSP",298,200711)
V300M=V(4,"MSG_DRONESTAR_INFO_REQ",299,200712)
V301M=V(4,"MSG_DRONESTAR_INFO_RSP",300,200713)
V302M=V(4,"MSG_DRONESTAR_EQUIP_REQ",301,200714)
V303M=V(4,"MSG_DRONESTAR_EQUIP_RSP",302,200715)
V304M=V(4,"MSG_DRONESTAR_UPGRADE_REQ",303,200716)
V305M=V(4,"MSG_DRONESTAR_UPGRADE_RSP",304,200717)
V306M=V(4,"MSG_DRONESTAR_RESET_REQ",305,200718)
V307M=V(4,"MSG_DRONESTAR_RESET_RSP",306,200719)
V308M=V(4,"MSG_DRONESTAR_TROOP_REQ",307,200720)
V309M=V(4,"MSG_DRONESTAR_TROOP_RSP",308,200721)
V310M=V(4,"MSG_DRONESTAR_INFO_NTF",309,200722)
V311M=V(4,"MSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_REQ",310,200770)
V312M=V(4,"MSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_RSP",311,200771)
V313M=V(4,"MSG_SANDBOX_KASTENBOX_UPDATE_PERSON_INFO_NTF",312,200772)
V314M=V(4,"MSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_REQ",313,200773)
V315M=V(4,"MSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_RSP",314,200774)
V316M=V(4,"MSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_NTF",315,200775)
V317M=V(4,"MSG_SANDBOX_GET_KASTENBOX_INFO_REQ",316,200776)
V318M=V(4,"MSG_SANDBOX_GET_KASTENBOX_INFO_RSP",317,200777)
V319M=V(4,"MSG_SANDBOX_RESOURCE_EXPLORATE_REQ",318,200778)
V320M=V(4,"MSG_SANDBOX_RESOURCE_EXPLORATE_RSP",319,200779)
V321M=V(4,"MSG_SANDBOX_ALLIANCE_HELP_REQ",320,200780)
V322M=V(4,"MSG_SANDBOX_ALLIANCE_HELP_RSP",321,200781)
V323M=V(4,"MSG_SANDBOX_GAME_BATTLE_REQ",322,200782)
V324M=V(4,"MSG_SANDBOX_GAME_BATTLE_RSP",323,200783)
V325M=V(4,"MSG_SANDBOX_CARRIAGEPOS_REQ",324,200784)
V326M=V(4,"MSG_SANDBOX_CARRIAGEPOS_RSP",325,200785)
V327M=V(4,"MSG_SANDBOX_FAKE_MARCH_NTF",326,200786)
V328M=V(4,"MSG_SANDBOX_ALLIANCE_HELP_SUCCESS_NTF",327,200787)
V329M=V(4,"MSG_SANDBOX_CALLBACK_DETAIL_REQ",328,200788)
V330M=V(4,"MSG_SANDBOX_CALLBACK_DETAIL_RSP",329,200789)
V331M=V(4,"MSG_SANDBOX_ALERT_LIST_REQ",330,200790)
V332M=V(4,"MSG_SANDBOX_ALERT_LIST_RSP",331,200791)
V333M=V(4,"MSG_SANDBOX_ALERT_LIST_NTF",332,200792)
V334M=V(4,"MSG_SANDBOX_BATTLE_BROCAST_NTF",333,200793)
V335M=V(4,"MSG_SANDBOX_ALLIANCE_TRAIN_POS_REQ",334,200794)
V336M=V(4,"MSG_SANDBOX_ALLIANCE_TRAIN_POS_RSP",335,200795)
V337M=V(4,"MSG_SANDBOX_ALONE_MARCH_DATA_REQ",336,200796)
V338M=V(4,"MSG_SANDBOX_ALONE_MARCH_DATA_RSP",337,200797)
V339M=V(4,"MSG_SANDBOX_ALONE_ENTITY_DATA_REQ",338,200798)
V340M=V(4,"MSG_SANDBOX_ALONE_ENTITY_DATA_RSP",339,200799)
V341M=V(4,"MSG_ALLIANCEBOSS_GET_INFO_REQ",340,200800)
V342M=V(4,"MSG_ALLIANCEBOSS_GET_INFO_RSP",341,200801)
V343M=V(4,"MSG_ALLIANCEBOSS_DONATE_REQ",342,200802)
V344M=V(4,"MSG_ALLIANCEBOSS_DONATE_RSP",343,200803)
V345M=V(4,"MSG_ALLIANCEBOSS_GETREADY_REQ",344,200804)
V346M=V(4,"MSG_ALLIANCEBOSS_GETREADY_RSP",345,200805)
V347M=V(4,"MSG_ALLIANCEBOSS_START_REQ",346,200806)
V348M=V(4,"MSG_ALLIANCEBOSS_START_RSP",347,200807)
V349M=V(4,"MSG_ALLIANCEBOSS_INFO_NTF",348,200808)
V350M=V(4,"MSG_ALLIANCEBOSS_CHANGELOC_REQ",349,200809)
V351M=V(4,"MSG_ALLIANCEBOSS_CHANGELOC_RSP",350,200810)
V352M=V(4,"MSG_ALLIANCEBOSS_CHANGE_OFFLINE_MASS_REQ",351,200811)
V353M=V(4,"MSG_ALLIANCEBOSS_CHANGE_OFFLINE_MASS_RSP",352,200812)
V354M=V(4,"MSG_GATHERING_AWARD_REQ",353,200851)
V355M=V(4,"MSG_GATHERING_AWARD_RSP",354,200852)
V356M=V(4,"MSG_GATHERING_NTF",355,200853)
V357M=V(4,"MSG_ARMSRACE_INFO_REQ",356,200901)
V358M=V(4,"MSG_ARMSRACE_INFO_RSP",357,200902)
V359M=V(4,"MSG_ARMSRACE_TASKFINISH_RSP",358,200903)
V360M=V(4,"MSG_ARMSRACE_TEAM_REPLACE_RSP",359,200904)
V361M=V(4,"MSG_ARMSRACE_ROUND_REPLACE_RSP",360,200905)
V362M=V(4,"MSG_ARMSRACE_POIN_PROCESS_REQ",361,200906)
V363M=V(4,"MSG_ARMSRACE_POIN_PROCESS_RSP",362,200907)
V364M=V(4,"MSG_ALLIANCE_AUTO_RESEARCH_REQ",363,200950)
V365M=V(4,"MSG_ALLIANCE_AUTO_RESEARCH_RSP",364,200951)
V366M=V(4,"MSG_NEWPALYER_FREEMOVE_NTF",365,200952)
V367M=V(4,"MSG_NEWPALYER_FREEMOVE_REQ",366,200953)
V368M=V(4,"MSG_NEWPALYER_FREEMOVE_RSP",367,200954)
V369M=V(4,"MSG_ALLIANCE_INVITE_REQ",368,200955)
V370M=V(4,"MSG_ALLIANCE_INVITE_RSP",369,200956)
V371M=V(4,"MSG_ALLIANCE_INVITE_NTF",370,200957)
V372M=V(4,"MSG_ALLIANCE_ACHIEVEMENT_REQ",371,200958)
V373M=V(4,"MSG_ALLIANCE_ACHIEVEMENT_RSP",372,200959)
V374M=V(4,"MSG_ALLIANCE_ACHIEVEMENT_NTF",373,200960)
V375M=V(4,"MSG_ALLIANCE_ACHIEVEMENT_REWARD_REQ",374,200961)
V376M=V(4,"MSG_ALLIANCE_ACHIEVEMENT_REWARD_RSP",375,200962)
V377M=V(4,"MSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF",376,200963)
V378M=V(4,"MSG_ALLIANCE_ISACCEPT_INVITE_REQ",377,200964)
V379M=V(4,"MSG_ALLIANCE_ISACCEPT_INVITE_RSP",378,200965)
V380M=V(4,"MSG_ALLIANCE_FREE_MOVE_CITY_NTF",379,200966)
V381M=V(4,"MSG_ALLIANCE_INVITE_INFO_REQ",380,200967)
V382M=V(4,"MSG_ALLIANCE_INVITE_INFO_RSP",381,200968)
V383M=V(4,"MSG_ACT_TASK_GETREWARD_REQ",382,201000)
V384M=V(4,"MSG_ACT_TASK_GETREWARD_RSP",383,201001)
V385M=V(4,"MSG_ACT_TASK_DATA_NTF",384,201002)
V386M=V(4,"MSG_GENERALTRIAL_PERSONALDATA_REQ",385,201020)
V387M=V(4,"MSG_GENERALTRIAL_PERSONALDATA_RSP",386,201021)
V388M=V(4,"MSG_GENERALTRIAL_SELECTDIFFICULTY_REQ",387,201022)
V389M=V(4,"MSG_GENERALTRIAL_SELECTDIFFICULTY_RSP",388,201023)
V390M=V(4,"MSG_GENERALTRIAL_BEGAINCHALLENGE_REQ",389,201024)
V391M=V(4,"MSG_GENERALTRIAL_BEGAINCHALLENGE_RSP",390,201025)
V392M=V(4,"MSG_GENERALTRIAL_PERSONALGETPRIZE_REQ",391,201026)
V393M=V(4,"MSG_GENERALTRIAL_PERSONALGETPRIZE_RSP",392,201027)
V394M=V(4,"MSG_GENERALTRIAL_ALLIANCEDATA_REQ",393,201028)
V395M=V(4,"MSG_GENERALTRIAL_ALLIANCEDATA_RSP",394,201029)
V396M=V(4,"MSG_GENERALTRIAL_BEGINALLIANCECHALLENGE_REQ",395,201030)
V397M=V(4,"MSG_GENERALTRIAL_BEGINALLIANCECHALLENGE_RSP",396,201031)
V398M=V(4,"MSG_GENERALTRIAL_ALLIANCECHALLENGE_REQ",397,201032)
V399M=V(4,"MSG_GENERALTRIAL_ALLIANCECHALLENGE_RSP",398,201033)
V400M=V(4,"MSG_GENERALTRIAL_PERSONALDATA_NTF",399,201034)
V401M=V(4,"MSG_GENERALTRIAL_ALLIANCEDATA_NTF",400,201035)
V402M=V(4,"MSG_ZOMBIE_COMING_ACT_INFO_REQ",401,201040)
V403M=V(4,"MSG_ZOMBIE_COMING_ACT_INFO_RSP",402,201041)
V404M=V(4,"MSG_ZOMBIE_COMING_LEADER_NTF",403,201042)
V405M=V(4,"MSG_VIP_GETEXP_EVERDAY_REQ",404,201050)
V406M=V(4,"MSG_VIP_GETEXP_EVERDAY_RSP",405,201051)
V407M=V(4,"MSG_VIP_GETGIFT_EVERDAY_REQ",406,201052)
V408M=V(4,"MSG_VIP_GETGIFT_EVERDAY_RSP",407,201053)
V409M=V(4,"MSG_VIP_GETLIMITGIFTDETAIL_REQ",408,201054)
V410M=V(4,"MSG_VIP_GETLIMITGIFTDETAIL_RSP",409,201055)
V411M=V(4,"MSG_SANDBOX_NC_DECLAREWAR_REQ",410,201070)
V412M=V(4,"MSG_SANDBOX_NC_DECLAREWAR_RSP",411,201071)
V413M=V(4,"MSG_SANDBOX_NC_OFFLINEATTACK_REQ",412,201072)
V414M=V(4,"MSG_SANDBOX_NC_OFFLINEATTACK_RSP",413,201073)
V415M=V(4,"MSG_SANDBOX_NC_OFFLINEATTACK_NTF",414,201074)
V416M=V(4,"MSG_SANDBOX_NC_ATTACKHERO_NTF",415,201075)
V417M=V(4,"MSG_SANDBOX_NC_CONGRESSLIST_REQ",416,201076)
V418M=V(4,"MSG_SANDBOX_NC_CONGRESSLIST_RSP",417,201077)
V419M=V(4,"MSG_SANDBOX_NC_GETREWARD_REQ",418,201078)
V420M=V(4,"MSG_SANDBOX_NC_GETREWARD_RSP",419,201079)
V421M=V(4,"MSG_SANDBOX_NC_ABANDON_REQ",420,201080)
V422M=V(4,"MSG_SANDBOX_NC_ABANDON_RSP",421,201081)
V423M=V(4,"MSG_SANDBOX_NC_POPUPS_NTF",422,201082)
V424M=V(4,"MSG_SANDBOX_NC_LIST_REQ",423,201083)
V425M=V(4,"MSG_SANDBOX_NC_LIST_RSP",424,201084)
V426M=V(4,"MSG_SANDBOX_NC_ACTIVITY_REQ",425,201085)
V427M=V(4,"MSG_SANDBOX_NC_ACTIVITY_RSP",426,201086)
V428M=V(4,"MSG_SANDBOX_NC_REWARDINFO_REQ",427,201087)
V429M=V(4,"MSG_SANDBOX_NC_REWARDINFO_RSP",428,201088)
V430M=V(4,"MSG_SANDBOX_NC_TEAMBATTLE_REQ",429,201089)
V431M=V(4,"MSG_SANDBOX_NC_TEAMBATTLE_RSP",430,201090)
V432M=V(4,"MSG_SANDBOX_NC_CONGRESSBACK_REQ",431,201091)
V433M=V(4,"MSG_SANDBOX_NC_CONGRESSBACK_RSP",432,201092)
V434M=V(4,"MSG_SANDBOX_NC_GETREGIOINPOS_REQ",433,201093)
V435M=V(4,"MSG_SANDBOX_NC_GETREGIOINPOS_RSP",434,201094)
V436M=V(4,"MSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_REQ",435,201095)
V437M=V(4,"MSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_RSP",436,201096)
V438M=V(4,"MSG_SANDBOX_SIEGECAMP_REINFORCELIST_REQ",437,201097)
V439M=V(4,"MSG_SANDBOX_SIEGECAMP_REINFORCELIST_RSP",438,201098)
V440M=V(4,"MSG_NEWPLAYERACT_RESCUEHOSTAGE_REQ",439,201100)
V441M=V(4,"MSG_NEWPLAYERACT_RESCUEHOSTAGE_RSP",440,201101)
V442M=V(4,"MSG_NEWPLAYERACT_RESOURCEINFO_REQ",441,201102)
V443M=V(4,"MSG_NEWPLAYERACT_RESOURCEINFO_RSP",442,201103)
V444M=V(4,"MSG_NEWPLAYERACT_GETRESOURCE_REQ",443,201104)
V445M=V(4,"MSG_NEWPLAYERACT_GETRESOURCE_RSP",444,201105)
V446M=V(4,"MSG_CARRIAGE_CNETER_DATA_NTF",445,201120)
V447M=V(4,"MSG_CARRIAGE_LOOT_DATA_REQ",446,201121)
V448M=V(4,"MSG_CARRIAGE_LOOT_DATA_RSP",447,201122)
V449M=V(4,"MSG_CARRIAGE_SET_FILTER_REQ",448,201123)
V450M=V(4,"MSG_CARRIAGE_SET_FILTER_RSP",449,201124)
V451M=V(4,"MSG_CARRIAGE_MINE_DATA_REQ",450,201125)
V452M=V(4,"MSG_CARRIAGE_MINE_DATA_RSP",451,201126)
V453M=V(4,"MSG_CARRIAGE_PRE_TRADE_REQ",452,201127)
V454M=V(4,"MSG_CARRIAGE_PRE_TRADE_RSP",453,201128)
V455M=V(4,"MSG_CARRIAGE_SET_TROOP_REQ",454,201129)
V456M=V(4,"MSG_CARRIAGE_SET_TROOP_RSP",455,201130)
V457M=V(4,"MSG_CARRIAGE_REPART_REQ",456,201131)
V458M=V(4,"MSG_CARRIAGE_REPART_RSP",457,201132)
V459M=V(4,"MSG_CARRIAGE_TRADE_DETAIL_REQ",458,201133)
V460M=V(4,"MSG_CARRIAGE_TRADE_DETAIL_RSP",459,201134)
V461M=V(4,"MSG_CARRIAGE_TRADE_RECOR_REQ",460,201135)
V462M=V(4,"MSG_CARRIAGE_TRADE_RECOR_RSP",461,201136)
V463M=V(4,"MSG_CARRIAGE_TRADE_HISTORY_REQ",462,201137)
V464M=V(4,"MSG_CARRIAGE_TRADE_HISTORY_RSP",463,201138)
V465M=V(4,"MSG_CARRIAGE_WANTED_LOOT_REQ",464,201139)
V466M=V(4,"MSG_CARRIAGE_WANTED_LOOT_RSP",465,201140)
V467M=V(4,"MSG_CARRIAGE_TRADE_REWARD_REQ",466,201141)
V468M=V(4,"MSG_CARRIAGE_TRADE_REWARD_RSP",467,201142)
V469M=V(4,"MSG_CARRIAGE_ERR_NTF",468,201143)
V470M=V(4,"MSG_CARRIAGE_SAVE_TROOP_REQ",469,201144)
V471M=V(4,"MSG_CARRIAGE_SAVE_TROOP_RSP",470,201145)
V472M=V(4,"MSG_CARRIAGE_WANTLIST_NTF",471,201146)
V473M=V(4,"MSG_CARRIAGE_MAX",472,201159)
V474M=V(4,"MSG_CAMPTRIAL_ACTIVITY_REQ",473,201201)
V475M=V(4,"MSG_CAMPTRIAL_ACTIVITY_NTF",474,201202)
V476M=V(4,"MSG_CAMPTRIAL_PROGRESS_NTF",475,201211)
V477M=V(4,"MSG_CAMPTRIAL_HERO_RECORD_NTF",476,201212)
V478M=V(4,"MSG_CAMPTRIAL_RANK_FIRST_NTF",477,201213)
V479M=V(4,"MSG_CAMPTRIAL_SELECT_DIFF_REQ",478,201221)
V480M=V(4,"MSG_CAMPTRIAL_SELECT_DIFF_RSP",479,201222)
V481M=V(4,"MSG_CAMPTRIAL_CHALL_REQ",480,201231)
V482M=V(4,"MSG_CAMPTRIAL_CHALL_RSP",481,201232)
V483M=V(4,"MSG_CAMPTRIAL_QUICK_CHALL_REQ",482,201233)
V484M=V(4,"MSG_CAMPTRIAL_QUICK_CHALL_RSP",483,201234)
V485M=V(4,"MSG_ALLIANCEDUEL_INFO_REQ",484,201250)
V486M=V(4,"MSG_ALLIANCEDUEL_INFO_RSP",485,201251)
V487M=V(4,"MSG_ALLIANCEDUEL_INFO_NTF",486,201252)
V488M=V(4,"MSG_ALLIANCEDUEL_ALLIANCEPOINT_NTF",487,201253)
V489M=V(4,"MSG_ALLIANCEDUEL_THEMEINFO_REQ",488,201254)
V490M=V(4,"MSG_ALLIANCEDUEL_THEMEINFO_RSP",489,201255)
V491M=V(4,"MSG_ALLIANCEDUEL_DEFEAT_NTF",490,201256)
V492M=V(4,"MSG_ALLIANCEDUEL_DEFEATNOTICE_REQ",491,201257)
V493M=V(4,"MSG_ALLIANCEDUEL_DEFEATNOTICE_RSP",492,201258)
V494M=V(4,"MSG_ALLIANCE_THEMEDUEL_REQ",493,201259)
V495M=V(4,"MSG_ALLIANCE_THEMEDUEL_RSP",494,201260)
V496M=V(4,"MSG_ALLIANCE_THEMEDUEL_NTF",495,201261)
V497M=V(4,"MSG_ALLIANCEDUEL_MOVE_COORDINATE_REQ",496,201262)
V498M=V(4,"MSG_ALLIANCEDUEL_MOVE_COORDINATE_RSP",497,201263)
V499M=V(4,"MSG_ALLIANCEDUEL_BATTLE_CITY_MOVE_REQ",498,201264)
V500M=V(4,"MSG_ALLIANCEDUEL_BATTLE_CITY_MOVE_RSP",499,201265)
V501M=V(4,"MSG_ALLIANCEDUEL_BACK_COORDINATE_REQ",500,201266)
V502M=V(4,"MSG_ALLIANCEDUEL_BACK_COORDINATE_RSP",501,201267)
V503M=V(4,"MSG_ALLIANCEDUEL_BATTLE_CITY_BACK_REQ",502,201268)
V504M=V(4,"MSG_ALLIANCEDUEL_BATTLE_CITY_BACK_RSP",503,201269)
V505M=V(4,"MSG_ALLIANCEDUEL_SANDBOXID_NTF",504,201270)
V506M=V(4,"MSG_ALLIANCEDUEL_POINT_MVP_REQ",505,201271)
V507M=V(4,"MSG_ALLIANCEDUEL_POINT_MVP_RSP",506,201272)
V508M=V(4,"MSG_ALLIANCEDUEL_BATTLE_RECORD_REQ",507,201273)
V509M=V(4,"MSG_ALLIANCEDUEL_BATTLE_RECORD_RSP",508,201274)
V510M=V(4,"MSG_ALLIANCEDUEL_BATTLEINFO_REQ",509,201275)
V511M=V(4,"MSG_ALLIANCEDUEL_BATTLEINFO_RSP",510,201276)
V512M=V(4,"MSG_ALLIANCEDUEL_BATTLEINFO_NTF",511,201277)
V513M=V(4,"MSG_ALLIANCEDUEL_BATTLE_UP_DOWN_NTF",512,201278)
V514M=V(4,"MSG_ALLIANCEDUEL_BATTLE_UP_DOWN_REQ",513,201279)
V515M=V(4,"MSG_ALLIANCEDUEL_BATTLE_UP_DOWN_RSP",514,201280)
V516M=V(4,"MSG_ALLIANCEDUEL_BATTLE_RANK_SCORE_REQ",515,201281)
V517M=V(4,"MSG_ALLIANCEDUEL_BATTLE_RANK_SCORE_RSP",516,201282)
V518M=V(4,"MSG_FULLBATTLE_EXCHANGE_REQ",517,201300)
V519M=V(4,"MSG_FULLBATTLE_EXCHANGE_RSP",518,201301)
V520M=V(4,"MSG_EXCHANGE_SHOP_INFO_NTF",519,201302)
V521M=V(4,"MSG_FULLBATTLE_REWARD_NTF",520,201303)
V522M=V(4,"MSG_LUCKY_DRAW_LOTTERY_REQ",521,201305)
V523M=V(4,"MSG_LUCKY_DRAW_LOTTERY_RSP",522,201306)
V524M=V(4,"MSG_LUCKY_DRAW_DAILYGIFT_NTF",523,201307)
V525M=V(4,"MSG_OPEN_MIRACLE_BOX_REQ",524,201308)
V526M=V(4,"MSG_OPEN_MIRACLE_BOX_RSP",525,201309)
V527M=V(4,"MSG_DEL_MIRACLE_BOX_DATA_REQ",526,201310)
V528M=V(4,"MSG_DEL_MIRACLE_BOX_DATA_RSP",527,201311)
V529M=V(4,"MSG_STRONGEST_COMMANDER_REQ",528,201325)
V530M=V(4,"MSG_STRONGEST_COMMANDER_RSP",529,201326)
V531M=V(4,"MSG_STRONGEST_COMMANDER_NTF",530,201327)
V532M=V(4,"MSG_RECHARGE_GIFT_DATA_NTF",531,201330)
V533M=V(4,"MSG_RECHARGE_GIFT_GET_FREE_REQ",532,201331)
V534M=V(4,"MSG_RECHARGE_GIFT_GET_FREE_RSP",533,201332)
V535M=V(4,"MSG_RECHARGE_GIFT_SELECT_REQ",534,201333)
V536M=V(4,"MSG_RECHARGE_GIFT_SELECT_RSP",535,201334)
V537M=V(4,"MSG_RECHARGE_GIFT_GET_FOLLOWUP_REQ",536,201335)
V538M=V(4,"MSG_RECHARGE_GIFT_GET_FOLLOWUP_RSP",537,201336)
V539M=V(4,"MSG_RECHARGE_GIFT_BUY_NTF",538,201337)
V540M=V(4,"MSG_ACORNPUB_GET_INFO_REQ",539,201350)
V541M=V(4,"MSG_ACORNPUB_GET_INFO_RSP",540,201351)
V542M=V(4,"MSG_ACORNPUB_SENDTASK_REQ",541,201352)
V543M=V(4,"MSG_ACORNPUB_SENDTASK_RSP",542,201353)
V544M=V(4,"MSG_ACORNPUB_ROBTASK_REQ",543,201354)
V545M=V(4,"MSG_ACORNPUB_ROBTASK_RSP",544,201355)
V546M=V(4,"MSG_ACORNPUB_HELPTASK_REQ",545,201356)
V547M=V(4,"MSG_ACORNPUB_HELPTASK_RSP",546,201357)
V548M=V(4,"MSG_ACORNPUB_REFRESHTASK_REQ",547,201358)
V549M=V(4,"MSG_ACORNPUB_REFRESHTASK_RSP",548,201359)
V550M=V(4,"MSG_ACORNPUB_SUPERREFRESHTASK_REQ",549,201360)
V551M=V(4,"MSG_ACORNPUB_SUPERREFRESHTASK_RSP",550,201361)
V552M=V(4,"MSG_ACORNPUB_BATCHGETREWARD_REQ",551,201362)
V553M=V(4,"MSG_ACORNPUB_BATCHGETREWARD_RSP",552,201363)
V554M=V(4,"MSG_ACORNPUB_INFO_NTF",553,201364)
V555M=V(4,"MSG_ACORNPUB_GET_RECORD_REQ",554,201365)
V556M=V(4,"MSG_ACORNPUB_GET_RECORD_RSP",555,201366)
V557M=V(4,"MSG_ACORNPUB_SENDTASK_PRE_REQ",556,201367)
V558M=V(4,"MSG_ACORNPUB_SENDTASK_PRE_RSP",557,201368)
V559M=V(4,"MSG_ACORNPUB_GET_TASK_ROB_RECORD_REQ",558,201369)
V560M=V(4,"MSG_ACORNPUB_GET_TASK_ROB_RECORD_RSP",559,201370)
V561M=V(4,"MSG_ACORNPUB_HELP_RECORD_LIKED_REQ",560,201371)
V562M=V(4,"MSG_ACORNPUB_HELP_RECORD_LIKED_RSP",561,201372)
V563M=V(4,"MSG_CONGRESS_MAINDATA_REQ",562,201400)
V564M=V(4,"MSG_CONGRESS_MAINDATA_RSP",563,201401)
V565M=V(4,"MSG_CONGRESS_OFFICIAL_NTF",564,201402)
V566M=V(4,"MSG_CONGRESS_OFFICIAL_REQ",565,201403)
V567M=V(4,"MSG_CONGRESS_OFFICIAL_RSP",566,201404)
V568M=V(4,"MSG_CONGRESS_ADDITIONAL_OFFICIAL_NTF",567,201405)
V569M=V(4,"MSG_CONGRESS_PRESRECORD_REQ",568,201406)
V570M=V(4,"MSG_CONGRESS_PRESRECORD_RSP",569,201407)
V571M=V(4,"MSG_CONGRESS_MANIFESTO_NTF",570,201411)
V572M=V(4,"MSG_CONGRESS_MANIFESTO_EDIT_REQ",571,201412)
V573M=V(4,"MSG_CONGRESS_MANIFESTO_EDIT_RSP",572,201413)
V574M=V(4,"MSG_CONGRESS_MAIL_NTF",573,201416)
V575M=V(4,"MSG_CONGRESS_SENDMAIL_REQ",574,201417)
V576M=V(4,"MSG_CONGRESS_SENDMAIL_RSP",575,201418)
V577M=V(4,"MSG_CONGRESS_APPOINT_REQ",576,201421)
V578M=V(4,"MSG_CONGRESS_APPOINT_RSP",577,201422)
V579M=V(4,"MSG_CONGRESS_DISMISS_REQ",578,201423)
V580M=V(4,"MSG_CONGRESS_DISMISS_RSP",579,201424)
V581M=V(4,"MSG_CONGRESS_OFFICE_RECORD_REQ",580,201425)
V582M=V(4,"MSG_CONGRESS_OFFICE_RECORD_RSP",581,201426)
V583M=V(4,"MSG_CONGRESS_APPLYFOR_LIST_NTF",582,201431)
V584M=V(4,"MSG_CONGRESS_APPLYFOR_LIST_REQ",583,201432)
V585M=V(4,"MSG_CONGRESS_APPLYFOR_LIST_RSP",584,201433)
V586M=V(4,"MSG_CONGRESS_APPLYFOR_MANAGE_REQ",585,201434)
V587M=V(4,"MSG_CONGRESS_APPLYFOR_MANAGE_RSP",586,201435)
V588M=V(4,"MSG_CONGRESS_APPLYFOR_SELF_NTF",587,201436)
V589M=V(4,"MSG_CONGRESS_APPLYFOR_REQ",588,201437)
V590M=V(4,"MSG_CONGRESS_APPLYFOR_RSP",589,201438)
V591M=V(4,"MSG_CONGRESS_QUEUESETTING_NTF",590,201441)
V592M=V(4,"MSG_CONGRESS_SET_TIME_REQ",591,201442)
V593M=V(4,"MSG_CONGRESS_SET_TIME_RSP",592,201443)
V594M=V(4,"MSG_CONGRESS_QUEUEUP_NTF",593,201446)
V595M=V(4,"MSG_CONGRESS_QUEUEUP_LIST_REQ",594,201447)
V596M=V(4,"MSG_CONGRESS_QUEUEUP_LIST_RSP",595,201448)
V597M=V(4,"MSG_CONGRESS_QUEUEUP_CANCEL_REQ",596,201449)
V598M=V(4,"MSG_CONGRESS_QUEUEUP_CANCEL_RSP",597,201450)
V599M=V(4,"MSG_CONGRESS_AWARD_INFO_NTF",598,201451)
V600M=V(4,"MSG_CONGRESS_AWARD_INFO_REQ",599,201452)
V601M=V(4,"MSG_CONGRESS_AWARD_INFO_RSP",600,201453)
V602M=V(4,"MSG_CONGRESS_AWARD_ALLOCATE_REQ",601,201454)
V603M=V(4,"MSG_CONGRESS_AWARD_ALLOCATE_RSP",602,201455)
V604M=V(4,"MSG_CONGRESS_AWARDRECORD_REQ",603,201456)
V605M=V(4,"MSG_CONGRESS_AWARDRECORD_RSP",604,201457)
V606M=V(4,"MSG_SEARCH_PLAYER_REQ",605,201458)
V607M=V(4,"MSG_SEARCH_PLAYER_RSP",606,201459)
V608M=V(4,"MSG_GETREWARD_NTF",607,201500)
V609M=V(4,"MSG_ERROR_NTF",608,201501)
V610M=V(4,"MSG_DEBUG_NTF",609,201502)
V611M=V(4,"MSG_PLAYERINFO_RANK_REQ",610,201600)
V612M=V(4,"MSG_PLAYERINFO_RANK_RSP",611,201601)
V613M=V(4,"MSG_PLAYERINFO_TASK_INFO_REQ",612,201602)
V614M=V(4,"MSG_PLAYERINFO_TASK_INFO_RSP",613,201603)
V615M=V(4,"MSG_ALLIANCE_MEDAL_BASE_REQ",614,201701)
V616M=V(4,"MSG_ALLIANCE_MEDAL_BASE_RSP",615,201702)
V617M=V(4,"MSG_ALLIANCE_MEDAL_BASE_NTF",616,201703)
V618M=V(4,"MSG_ALLIANCE_MEDAL_DETAIL_REQ",617,201704)
V619M=V(4,"MSG_ALLIANCE_MEDAL_DETAIL_RSP",618,201705)
V620M=V(4,"MSG_ALLIANCE_MEDAL_REWARD_REQ",619,201706)
V621M=V(4,"MSG_ALLIANCE_MEDAL_REWARD_RSP",620,201707)
V622M=V(4,"MSG_ALLIANCE_MEDAL_WARNDEL_REQ",621,201708)
V623M=V(4,"MSG_ALLIANCE_MEDAL_WARNDEL_RSP",622,201709)
V624M=V(4,"MSG_ALLIANCE_MEDAL_WARNAUTO_REQ",623,201710)
V625M=V(4,"MSG_ALLIANCE_MEDAL_WARNAUTO_RSP",624,201711)
V626M=V(4,"MSG_ALLIANCE_MEDAL_WARN_REQ",625,201712)
V627M=V(4,"MSG_ALLIANCE_MEDAL_WARN_RSP",626,201713)
V628M=V(4,"MSG_FUNCTIONID_OPEN_NTF",627,201714)
V629M=V(4,"MSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ",628,201715)
V630M=V(4,"MSG_SET_HOOKLEVELCHALLENGE_RESULT_RSP",629,201716)
V631M=V(4,"MSG_ALLIANCE_RECORD_REQ",630,201717)
V632M=V(4,"MSG_ALLIANCE_RECORD_RSP",631,201718)
V633M=V(4,"MSG_ALLIANCE_MASS_REQ",632,201719)
V634M=V(4,"MSG_ALLIANCE_MASS_RSP",633,201720)
V635M=V(4,"MSG_ALLIANCE_MASS_NTF",634,201721)
V636M=V(4,"MSG_ALLIANCE_MASS_CNT_NTF",635,201722)
V637M=V(4,"MSG_ALLIANCE_INTELLIGENCE_REQ",636,201723)
V638M=V(4,"MSG_ALLIANCE_INTELLIGENCE_RSP",637,201724)
V639M=V(4,"MSG_ALLIANCE_INTELLIGENCE_NTF",638,201725)
V640M=V(4,"MSG_ALLIANCE_SURPASS_RALLYPOINT_NUMS_NTF",639,201726)
V641M=V(4,"MSG_ZONEBATTLEDUEL_GETINFO_REQ",640,201750)
V642M=V(4,"MSG_ZONEBATTLEDUEL_GETINFO_RSP",641,201751)
V643M=V(4,"MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_REQ",642,201752)
V644M=V(4,"MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP",643,201753)
V645M=V(4,"MSG_ZONEBATTLEDUEL_GET_ROUNDSCORE_REWARD_REQ",644,201754)
V646M=V(4,"MSG_ZONEBATTLEDUEL_GET_ROUNDSCORE_REWARD_RSP",645,201755)
V647M=V(4,"MSG_ZONEBATTLEDUEL_GET_VSINFO_REQ",646,201756)
V648M=V(4,"MSG_ZONEBATTLEDUEL_GET_VSINFO_RSP",647,201757)
V649M=V(4,"MSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_REQ",648,201758)
V650M=V(4,"MSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP",649,201759)
V651M=V(4,"MSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF",650,201760)
V652M=V(4,"MSG_ZONE_BATTLE_DUEL_CHANGE_NTF",651,201761)
V653M=V(4,"MSG_ZONEBATTLEDUEL_NOMATCH_REWARD_REQ",652,201762)
V654M=V(4,"MSG_ZONEBATTLEDUEL_NOMATCH_REWARD_RSP",653,201763)
V655M=V(4,"MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_REQ",654,201764)
V656M=V(4,"MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP",655,201765)
V657M=V(4,"MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_REQ",656,201766)
V658M=V(4,"MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP",657,201767)
V659M=V(4,"MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_REQ",658,201768)
V660M=V(4,"MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP",659,201769)
V661M=V(4,"MSG_SANDBOX_CANCLE_DETECT_REQ",660,201800)
V662M=V(4,"MSG_SANDBOX_CANCLE_DETECT_RSP",661,201801)
V663M=V(4,"MSG_SANDBOX_IS_NORMAL_REQ",662,201802)
V664M=V(4,"MSG_SANDBOX_IS_NORMAL_RSP",663,201803)
V665M=V(4,"MSG_ALLIANCE_TRAIN_UPDATE_INFO_NTF",664,201900)
V666M=V(4,"MSG_ALLIANCE_TRAIN_GET_INFO_REQ",665,201901)
V667M=V(4,"MSG_ALLIANCE_TRAIN_GET_INFO_RSP",666,201902)
V668M=V(4,"MSG_ALLIANCE_TRAIN_ENTER_CARRIAGE_REQ",667,201903)
V669M=V(4,"MSG_ALLIANCE_TRAIN_ENTER_CARRIAGE_RSP",668,201904)
V670M=V(4,"MSG_ALLIANCE_TRAIN_APPOINT_DRIVER_REQ",669,201905)
V671M=V(4,"MSG_ALLIANCE_TRAIN_APPOINT_DRIVER_RSP",670,201906)
V672M=V(4,"MSG_ALLIANCE_TRAIN_SET_DEFEND_LINEUP_REQ",671,201907)
V673M=V(4,"MSG_ALLIANCE_TRAIN_SET_DEFEND_LINEUP_RSP",672,201908)
V674M=V(4,"MSG_ALLIANCE_TRAIN_GET_DEFEND_LINEUP_REQ",673,201909)
V675M=V(4,"MSG_ALLIANCE_TRAIN_GET_DEFEND_LINEUP_RSP",674,201910)
V676M=V(4,"MSG_ALLIANCE_TRAIN_GIVE_CARD_REQ",675,201911)
V677M=V(4,"MSG_ALLIANCE_TRAIN_GIVE_CARD_RSP",676,201912)
V678M=V(4,"MSG_ALLIANCE_TRAIN_GET_CARD_REQ",677,201913)
V679M=V(4,"MSG_ALLIANCE_TRAIN_GET_CARD_RSP",678,201914)
V680M=V(4,"MSG_ALLIANCE_TRAIN_USE_CARD_REQ",679,201915)
V681M=V(4,"MSG_ALLIANCE_TRAIN_USE_CARD_RSP",680,201916)
V682M=V(4,"MSG_ALLIANCE_TRAIN_ATTACK_REQ",681,201917)
V683M=V(4,"MSG_ALLIANCE_TRAIN_ATTACK_RSP",682,201918)
V684M=V(4,"MSG_ALLIANCE_TRAIN_GET_ROLE_INFO_REQ",683,201919)
V685M=V(4,"MSG_ALLIANCE_TRAIN_GET_ROLE_INFO_RSP",684,201920)
V686M=V(4,"MSG_ALLIANCE_TRAIN_BATTLE_HISTORY_REQ",685,201921)
V687M=V(4,"MSG_ALLIANCE_TRAIN_BATTLE_HISTORY_RSP",686,201922)
V688M=V(4,"MSG_ALLIANCE_TRAIN_SWAP_LINEUP_REQ",687,201923)
V689M=V(4,"MSG_ALLIANCE_TRAIN_SWAP_LINEUP_RSP",688,201924)
V690M=V(4,"MSG_ALLIANCE_TRAIN_GM_NTF",689,201925)
V691M=V(4,"MSG_ALLIANCE_TRAIN_REFRESH_REQ",690,201926)
V692M=V(4,"MSG_ALLIANCE_TRAIN_REFRESH_RSP",691,201927)
V693M=V(4,"MSG_ALLIANCE_TRAIN_REFRESH_LOOT_NUM_NTF",692,201928)
V694M=V(4,"MSG_DESERTSTROM_ACTIVITY_NTF",693,202000)
V695M=V(4,"MSG_DESERTSTROM_SIGNUP_INFO_REQ",694,202011)
V696M=V(4,"MSG_DESERTSTROM_SIGNUP_INFO_RSP",695,202012)
V697M=V(4,"MSG_DESERTSTROM_SIGNUP_INFO_NTF",696,202013)
V698M=V(4,"MSG_DESERTSTROM_SIGNUP_ACTION_REQ",697,202014)
V699M=V(4,"MSG_DESERTSTROM_SIGNUP_ACTION_RSP",698,202015)
V700M=V(4,"MSG_DESERTSTROM_SIGNUP_SELECT_TIME_REQ",699,202016)
V701M=V(4,"MSG_DESERTSTROM_SIGNUP_SELECT_TIME_RSP",700,202017)
V702M=V(4,"MSG_DESERTSTROM_SIGNUP_SET_MEMBER_REQ",701,202018)
V703M=V(4,"MSG_DESERTSTROM_SIGNUP_SET_MEMBER_RSP",702,202019)
V704M=V(4,"MSG_DESERTSTROM_SIGNUP_TIME_HOPE_REQ",703,202020)
V705M=V(4,"MSG_DESERTSTROM_SIGNUP_TIME_HOPE_RSP",704,202021)
V706M=V(4,"MSG_DESERTSTROM_MATCH_INFO_REQ",705,202022)
V707M=V(4,"MSG_DESERTSTROM_MATCH_INFO_RSP",706,202023)
V708M=V(4,"MSG_DESERTSTROM_MATCH_INFO_NTF",707,202024)
V709M=V(4,"MSG_DESERTSTROM_BATTLE_SID_NTF",708,202031)
V710M=V(4,"MSG_DESERTSTROM_BATTLE_LEAVE_REQ",709,202032)
V711M=V(4,"MSG_DESERTSTROM_BATTLE_LEAVE_RSP",710,202033)
V712M=V(4,"MSG_DESERTSTROM_BATTLE_STATUS_NTF",711,202034)
V713M=V(4,"MSG_DESERTSTROM_RANK_PUBLIC_REQ",712,202035)
V714M=V(4,"MSG_DESERTSTROM_RANK_PUBLIC_RSP",713,202036)
V715M=V(4,"MSG_DESERTSTROM_RANK_SELF_NTF",714,202037)
V716M=V(4,"MSG_DESERTSTROM_BATTLE_RESULT_NTF",715,202038)
V717M=V(4,"MSG_DESERTSTROM_RANK_ALLIANCE_NTF",716,202039)
V718M=V(4,"MSG_DESERTSTROM_SCORE_ALLIANCE_REQ",717,202040)
V719M=V(4,"MSG_DESERTSTROM_SCORE_ALLIANCE_RSP",718,202041)
V720M=V(4,"MSG_DESERTSTROM_SCORE_SELF_REQ",719,202042)
V721M=V(4,"MSG_DESERTSTROM_SCORE_SELF_RSP",720,202043)
V722M=V(4,"MSG_DESERTSTROM_RANK_BUILDING_NTF",721,202044)
V723M=V(4,"MSG_DESERTSTROM_SCOREBOX_ANIMATION_NTF",722,202045)
V724M=V(4,"MSG_DESERT_STROM_BUFFINFO_NTF",723,202046)
V725M=V(4,"MSG_DESERTSTROM_CREATE_CITY_REQ",724,202047)
V726M=V(4,"MSG_DESERTSTROM_CREATE_CITY_RSP",725,202048)
V727M=V(4,"MSG_BATTLE_HOSPITAL_SOLDIER_REQ",726,202050)
V728M=V(4,"MSG_BATTLE_HOSPITAL_SOLDIER_RSP",727,202051)
V729M=V(4,"MSG_BATTLE_HOSPITAL_SOLDIER_GET_REQ",728,202052)
V730M=V(4,"MSG_BATTLE_HOSPITAL_SOLDIER_GET_RSP",729,202053)
V731M=V(4,"MSG_HOSPITAL_CANCURE_SOLDIER_COUNT_REQ",730,202054)
V732M=V(4,"MSG_HOSPITAL_CANCURE_SOLDIER_COUNT_RSP",731,202055)
V733M=V(4,"MSG_DESERTSTROM_MOVE_CD_REQ",732,202056)
V734M=V(4,"MSG_DESERTSTROM_MOVE_CD_RSP",733,202057)
V735M=V(4,"MSG_DESERTSTROM_SCOREBOX_NTF",734,202058)
V736M=V(4,"MSG_DESERTSTROM_ROLE_CITY_INFO_NTF",735,202060)
V737M=V(4,"MSG_DESERTSTROM_BUILDING_DEFEND_REQ",736,202061)
V738M=V(4,"MSG_DESERTSTROM_BUILDING_DEFEND_RSP",737,202062)
V739M=V(4,"MSG_DESERTSTROM_INFO_CUE_NTF",738,202063)
V740M=V(4,"MSG_DESERT_MAP_BUILDING_INFO_REQ",739,202064)
V741M=V(4,"MSG_DESERT_MAP_BUILDING_INFO_RSP",740,202065)
V742M=V(4,"MSG_DESERT_TEST_ROUTER_REQ",741,202066)
V743M=V(4,"MSG_DESERT_TEST_ROUTER_RSP",742,202067)
V744M=V(4,"MSG_DESERTSTROM_LOG_REQ",743,202070)
V745M=V(4,"MSG_DESERTSTROM_LOG_RSP",744,202071)
V746M=V(4,"MSG_DESERTSTROM_RECORD_REQ",745,202072)
V747M=V(4,"MSG_DESERTSTROM_RECORD_RSP",746,202073)
V748M=V(4,"MSG_ACORNPUB_TREASURE_BASE_NTF",747,202101)
V749M=V(4,"MSG_ACORNPUB_OPEN_TREASURE_REQ",748,202102)
V750M=V(4,"MSG_ACORNPUB_OPEN_TREASURE_RSP",749,202103)
V751M=V(4,"MSG_COMMONATY_VIEWORDER_REQ",750,202111)
V752M=V(4,"MSG_COMMONATY_VIEWORDER_RSP",751,202112)
V753M=V(4,"MSG_COMMONATY_SHELF_GOODS_REQ",752,202113)
V754M=V(4,"MSG_COMMONATY_SHELF_GOODS_RSP",753,202114)
V755M=V(4,"MSG_COMMONATY_EXCHANGE_GOODS_REQ",754,202115)
V756M=V(4,"MSG_COMMONATY_EXCHANGE_GOODS_RSP",755,202116)
V757M=V(4,"MSG_COMMONATY_CANCLE_EXCHANGE_REQ",756,202117)
V758M=V(4,"MSG_COMMONATY_CANCLE_EXCHANGE_RSP",757,202118)
V759M=V(4,"MSG_COMMONATY_EXCHANGE_ORDER_LIST_REQ",758,202119)
V760M=V(4,"MSG_COMMONATY_EXCHANGE_ORDER_LIST_RSP",759,202120)
V761M=V(4,"MSG_COMMONATY_VIEW_ORDER_RECORD_REQ",760,202121)
V762M=V(4,"MSG_COMMONATY_VIEW_ORDER_RECORD_RSP",761,202122)
V763M=V(4,"MSG_ACORNPUB_TREASURE_PARASE_REQ",762,202123)
V764M=V(4,"MSG_ACORNPUB_TREASURE_PARASE_RSP",763,202124)
V765M=V(4,"MSG_SCHEDULE_LIST_REQ",764,202131)
V766M=V(4,"MSG_SCHEDULE_LIST_RSP",765,202132)
V767M=V(4,"MSG_GEAR_SUPPLY_GETACTIVITYDATA_REQ",766,202136)
V768M=V(4,"MSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP",767,202137)
V769M=V(4,"MSG_GEAR_SUPPLY_RECEIVEAWARD_REQ",768,202138)
V770M=V(4,"MSG_GEAR_SUPPLY_RECEIVEAWARD_RSP",769,202139)
V771M=V(4,"MSG_GEAR_SUPPLY_DRAWAWARD_REQ",770,202140)
V772M=V(4,"MSG_GEAR_SUPPLY_DRAWAWARD_RSP",771,202141)
V773M=V(4,"MSG_GEAR_SUPPLY_SETREWARD_REQ",772,202142)
V774M=V(4,"MSG_GEAR_SUPPLY_SETREWARD_RSP",773,202143)
V775M=V(4,"MSG_PAYMENT_ORDER_EVENT_TRACKING_NTF",774,202145)
V776M=V(4,"MSG_SKY_FALL_THREAT_MONSTER_REQ",775,202151)
V777M=V(4,"MSG_SKY_FALL_THREAT_MONSTER_RSP",776,202152)
V778M=V(4,"MSG_SKY_FALL_TASK_PRO_NTF",777,202153)
V779M=V(4,"MSG_SKY_FALL_DOOMSDAY_NTF",778,202154)
V780M=V(4,"MSG_MONSTER_COMING_UPDATE_NTF",779,202160)
V781M=V(4,"MSG_MONSTER_COMING_GET_DATA_REQ",780,202161)
V782M=V(4,"MSG_MONSTER_COMING_GET_DATA_RSP",781,202162)
V783M=V(4,"MSG_MONSTER_COMING_RECEIVE_REWARD_REQ",782,202163)
V784M=V(4,"MSG_MONSTER_COMING_RECEIVE_REWARD_RSP",783,202164)
V785M=V(4,"MSG_MONSTER_COMING_NEW_UPDATE_NTF",784,202165)
V786M=V(4,"MSG_MONSTER_COMING_NEW_RECEIVE_REWARD_REQ",785,202166)
V787M=V(4,"MSG_MONSTER_COMING_NEW_RECEIVE_REWARD_RSP",786,202167)
V788M=V(4,"MSG_SANDBOX_GETBOX_NTF",787,202170)
V789M=V(4,"MSG_PUSHMESSAGE_LOGIN_REQ",788,202180)
V790M=V(4,"MSG_PUSHMESSAGE_LOGIN_RSP",789,202181)
V791M=V(4,"MSG_PUSHMESSAGE_BUTTON_REQ",790,202182)
V792M=V(4,"MSG_PUSHMESSAGE_BUTTON_RSP",791,202183)
V793M=V(4,"MSG_PUSHMESSAGE_STATE_REQ",792,202184)
V794M=V(4,"MSG_PUSHMESSAGE_STATE_RSP",793,202185)
V795M=V(4,"MSG_PUSHMESSAGE_STATE_NTF",794,202186)
V796M=V(4,"MSG_COMMUNITYGIFT_REWARDSTATUS_REQ",795,202191)
V797M=V(4,"MSG_COMMUNITYGIFT_REWARDSTATUS_RSP",796,202192)
V798M=V(4,"MSG_COMMUNITYGIFT_JUMP_REQ",797,202193)
V799M=V(4,"MSG_COMMUNITYGIFT_JUMP_RSP",798,202194)
V800M=V(4,"MSG_COMMUNITYGIFT_GETREWARD_REQ",799,202195)
V801M=V(4,"MSG_COMMUNITYGIFT_GETREWARD_RSP",800,202196)
V802M=V(4,"MSG_All_ATY_CHOOSE_WEEK_CARD_NTF",801,202200)
V803M=V(4,"MSG_ONE_ATY_CHOOSE_WEEK_CARD_NTF",802,202201)
V804M=V(4,"MSG_BUY_CHOOSE_WEEK_CARD_CHECK_REQ",803,202202)
V805M=V(4,"MSG_BUY_CHOOSE_WEEK_CARD_CHECK_RSP",804,202203)
V806M=V(4,"MSG_ACTIVE_CHOOSE_WEEK_CARD_NTF",805,202204)
V807M=V(4,"MSG_GET_CHOOSE_WEEK_CARD_REWARD_REQ",806,202205)
V808M=V(4,"MSG_GET_CHOOSE_WEEK_CARD_REWARD_RSP",807,202206)
V809M=V(4,"MSG_GOLDENEGGS_CHAT_NTF",808,202211)
V810M=V(4,"MSG_GOLDENEGGS_RECEIVE_REQ",809,202212)
V811M=V(4,"MSG_GOLDENEGGS_RECEIVE_RSP",810,202213)
V812M=V(4,"MSG_GOLDENEGGS_BACKPACK_REQ",811,202214)
V813M=V(4,"MSG_GOLDENEGGS_BACKPACK_RSP",812,202215)
V814M=V(4,"MSG_GOLDENEGGS_LOOT_REQ",813,202216)
V815M=V(4,"MSG_GOLDENEGGS_LOOT_RSP",814,202217)
V816M=V(4,"MSG_GOLDENEGGS_LIKE_REQ",815,202218)
V817M=V(4,"MSG_GOLDENEGGS_LIKE_RSP",816,202219)
V818M=V(4,"MSG_GOLDENEGGS_BACKPACK_LIKE_NTF",817,202220)
V819M=V(4,"MSG_GOLDENEGGS_REDPACKET_REQ",818,202221)
V820M=V(4,"MSG_GOLDENEGGS_REDPACKET_RSP",819,202222)
V821M=V(4,"MSG_SEVENDAY_LOGIN_RECEIVEAWARD_REQ",820,202251)
V822M=V(4,"MSG_SEVENDAY_LOGIN_RECEIVEAWARD_RSP",821,202252)
V823M=V(4,"MSG_LIKE_GET_RECORD_REQ",822,202261)
V824M=V(4,"MSG_LIKE_GET_RECORD_RSP",823,202262)
V825M=V(4,"MSG_LIKE_GET_ROLE_RECORD_REQ",824,202263)
V826M=V(4,"MSG_LIKE_GET_ROLE_RECORD_RSP",825,202264)
V827M=V(4,"MSG_AICUSTOMERSERVICE_NTF",826,202281)
V828M=V(4,"MSG_AICUSTOMERSERVICE_REQ",827,202282)
V829M=V(4,"MSG_AICUSTOMERSERVICE_RSP",828,202283)
V830M=V(4,"MSG_BUILDING_VISITOR_NTF",829,202290)
V831M=V(4,"MSG_BUILDINGVISITOR_DEL_REQ",830,202291)
V832M=V(4,"MSG_BUILDINGVISITOR_DEL_RSP",831,202292)
V833M=V(4,"MSG_MINIGAME_ACTIVITY_JION_REQ",832,202301)
V834M=V(4,"MSG_MINIGAME_ACTIVITY_JION_RSP",833,202302)
V835M=V(4,"MSG_ZOMBIEAPOCALYPSE_ACTIVITY_DATA_NTF",834,202350)
V836M=V(4,"MSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_REQ",835,202351)
V837M=V(4,"MSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_RSP",836,202352)
V838M=V(4,"MSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_REQ",837,202353)
V839M=V(4,"MSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_RSP",838,202354)
V840M=V(4,"MSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_REQ",839,202355)
V841M=V(4,"MSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_RSP",840,202356)
V842M=V(4,"MSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_REQ",841,202357)
V843M=V(4,"MSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_RSP",842,202358)
V844M=V(4,"MSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_REQ",843,202359)
V845M=V(4,"MSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_RSP",844,202360)
V846M=V(4,"MSG_ZOMBIEAPOCALYPSE_CHAT_CHANGE_NTF",845,202361)
V847M=V(4,"MSG_ZOMBIEAPOCALYPSE_POISONEDINFO_REQ",846,202362)
V848M=V(4,"MSG_ZOMBIEAPOCALYPSE_POISONEDINFO_RSP",847,202363)
V849M=V(4,"MSG_ZOMBIEAPOCALYPSE_POISONEDINFO_NTF",848,202364)
V850M=V(4,"MSG_ZOMBIEAPOCALYPSE_JUMP_REQ",849,202365)
V851M=V(4,"MSG_ZOMBIEAPOCALYPSE_JUMP_RSP",850,202366)
V852M=V(4,"MSG_BATTLE_PASS_GET_ALL_REWARD_REQ",851,202400)
V853M=V(4,"MSG_BATTLE_PASS_GET_ALL_REWARD_RSP",852,202401)
V854M=V(4,"MSG_CHANGEBAG_GIFT_REQ",853,202411)
V855M=V(4,"MSG_CHANGEBAG_GIFT_RSP",854,202412)
V856M=V(4,"MSG_CHANGEBAG_GIFT_NTF",855,202413)
V857M=V(4,"MSG_GETBINDEMAIL_REWARD_REQ",856,202421)
V858M=V(4,"MSG_GETBINDEMAIL_REWARD_RSP",857,202422)
V859M=V(4,"MSG_ACTIVITY_NOTICEPERIOD_NTF",858,202426)
V860M=V(4,"MSG_ALLIANCE_OUTFIRE_REQ",859,202431)
V861M=V(4,"MSG_ALLIANCE_OUTFIRE_RSP",860,202432)
V862M=V(4,"MSG_ALLIANCE_OUTFIRE_NTF",861,202433)
V863M=V(4,"MSG_SANDBOX_SPECIAL_EFFECT_NTF",862,202434)
V864M=V(4,"MSG_GOOGLE_COMMENT_SET_REQ",863,202436)
V865M=V(4,"MSG_GOOGLE_COMMENT_SET_RSP",864,202437)
V866M=V(4,"MSG_CHAT_MSG_SAVE_BATTLE_REPORT_REQ",865,202450)
V867M=V(4,"MSG_CHAT_MSG_SAVE_BATTLE_REPORT_RSP",866,202451)
V868M=V(4,"MSG_CHAT_MSG_GET_BATTLE_REPORT_REQ",867,202452)
V869M=V(4,"MSG_CHAT_MSG_GET_BATTLE_REPORT_RSP",868,202453)
V870M=V(4,"MSG_ALLIANCE_R4R5TODO_NTF",869,202501)
V871M=V(4,"MSG_ALLIANCE_R4R5TODO_REWARD_REQ",870,202502)
V872M=V(4,"MSG_ALLIANCE_R4R5TODO_REWARD_RSP",871,202503)
V873M=V(4,"MSG_ALLIANCELOG_RECORD_REQ",872,202506)
V874M=V(4,"MSG_ALLIANCELOG_RECORD_RSP",873,202507)
V875M=V(4,"MSG_NEW_ARENA_SET_DEFENCE_LINEUP_REQ",874,202510)
V876M=V(4,"MSG_NEW_ARENA_SET_DEFENCE_LINEUP_RSP",875,202511)
V877M=V(4,"MSG_NEW_ARENA_GET_DEFENCE_LINEUP_REQ",876,202512)
V878M=V(4,"MSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP",877,202513)
V879M=V(4,"MSG_NEW_ARENA_GET_RANK_INFO_REQ",878,202514)
V880M=V(4,"MSG_NEW_ARENA_GET_RANK_INFO_RSP",879,202515)
V881M=V(4,"MSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_REQ",880,202516)
V882M=V(4,"MSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP",881,202517)
V883M=V(4,"MSG_NEW_ARENA_ENTER_BATTLE_REQ",882,202518)
V884M=V(4,"MSG_NEW_ARENA_ENTER_BATTLE_RSP",883,202519)
V885M=V(4,"MSG_NEW_ARENA_GET_BATTLE_RECORDS_REQ",884,202520)
V886M=V(4,"MSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP",885,202521)
V887M=V(4,"MSG_NEW_ARENA_CHANGE_ARENA_REQ",886,202522)
V888M=V(4,"MSG_NEW_ARENA_CHANGE_ARENA_RSP",887,202523)
V889M=V(4,"MSG_NEW_ARENA_ENTER_ARENA_NTF",888,202524)
V890M=V(4,"MSG_NEW_ARENA_RANK_UPDATE_NTF",889,202525)
V891M=V(4,"MSG_STRAY_DOG_GETINFO_REQ",890,202531)
V892M=V(4,"MSG_STRAY_DOG_GETINFO_RSP",891,202532)
V893M=V(4,"MSG_STRAY_DOG_RECEIVEAWARD_REQ",892,202533)
V894M=V(4,"MSG_STRAY_DOG_RECEIVEAWARD_RSP",893,202534)
V895M=V(4,"MSG_LOGIN_ACTOR_CREATE_QUEUE_NTF",894,202541)
V896M=V(4,"MSG_ACTOR_CREATE_DATA_SYNC_REQ",895,202542)
V897M=V(4,"MSG_ACTOR_CREATE_DATA_SYNC_RSP",896,202543)
V898M=V(4,"MSG_SLOT_MACHINE_DRAW_REQ",897,202551)
V899M=V(4,"MSG_SLOT_MACHINE_DRAW_RSP",898,202552)
V900M=V(4,"MSG_GHOST_PARTY_HOLD_REQ",899,202553)
V901M=V(4,"MSG_GHOST_PARTY_HOLD_RSP",900,202554)
V902M=V(4,"MSG_GHOST_PARTY_HOLD_LIST_REQ",901,202555)
V903M=V(4,"MSG_GHOST_PARTY_HOLD_LIST_RSP",902,202556)
V904M=V(4,"MSG_HALLOWEEN_PARTY_GETINFO_REQ",903,202557)
V905M=V(4,"MSG_HALLOWEEN_PARTY_GETINFO_RSP",904,202558)
V906M=V(4,"MSG_HALLOWEEN_PARTY_SUBMIT_REQ",905,202559)
V907M=V(4,"MSG_HALLOWEEN_PARTY_SUBMIT_RSP",906,202560)
V908M=V(4,"MSG_HALLOWEEN_PARTY_GET_REWARD_REQ",907,202561)
V909M=V(4,"MSG_HALLOWEEN_PARTY_GET_REWARD_RSP",908,202562)
V910M=V(4,"MSG_LOGIN_NATIONAL_FLAG_REQ",909,202600)
V911M=V(4,"MSG_LOGIN_NATIONAL_FLAG_RSP",910,202601)
V912M=V(4,"MSG_NATIONAL_FLAG_CHANGE_REQ",911,202602)
V913M=V(4,"MSG_NATIONAL_FLAG_CHANGE_RSP",912,202603)
V914M=V(4,"MSG_ROLD_NATIONAL_FLAG_NTF",913,202604)
V915M=V(4,"MSG_VIOLENT_ZOMBIE_DATA_REQ",914,202621)
V916M=V(4,"MSG_VIOLENT_ZOMBIE_DATA_RSP",915,202622)
E1M=E(3,"ActionxManKey",".CSMsg.ActionxManKey")

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M,V24M,V25M,V26M,V27M,V28M,V29M,V30M,V31M,V32M,V33M,V34M,V35M,V36M,V37M,V38M,V39M,V40M,V41M,V42M,V43M,V44M,V45M,V46M,V47M,V48M,V49M,V50M,V51M,V52M,V53M,V54M,V55M,V56M,V57M,V58M,V59M,V60M,V61M,V62M,V63M,V64M,V65M,V66M,V67M,V68M,V69M,V70M,V71M,V72M,V73M,V74M,V75M,V76M,V77M,V78M,V79M,V80M,V81M,V82M,V83M,V84M,V85M,V86M,V87M,V88M,V89M,V90M,V91M,V92M,V93M,V94M,V95M,V96M,V97M,V98M,V99M,V100M,V101M,V102M,V103M,V104M,V105M,V106M,V107M,V108M,V109M,V110M,V111M,V112M,V113M,V114M,V115M,V116M,V117M,V118M,V119M,V120M,V121M,V122M,V123M,V124M,V125M,V126M,V127M,V128M,V129M,V130M,V131M,V132M,V133M,V134M,V135M,V136M,V137M,V138M,V139M,V140M,V141M,V142M,V143M,V144M,V145M,V146M,V147M,V148M,V149M,V150M,V151M,V152M,V153M,V154M,V155M,V156M,V157M,V158M,V159M,V160M,V161M,V162M,V163M,V164M,V165M,V166M,V167M,V168M,V169M,V170M,V171M,V172M,V173M,V174M,V175M,V176M,V177M,V178M,V179M,V180M,V181M,V182M,V183M,V184M,V185M,V186M,V187M,V188M,V189M,V190M,V191M,V192M,V193M,V194M,V195M,V196M,V197M,V198M,V199M,V200M,V201M,V202M,V203M,V204M,V205M,V206M,V207M,V208M,V209M,V210M,V211M,V212M,V213M,V214M,V215M,V216M,V217M,V218M,V219M,V220M,V221M,V222M,V223M,V224M,V225M,V226M,V227M,V228M,V229M,V230M,V231M,V232M,V233M,V234M,V235M,V236M,V237M,V238M,V239M,V240M,V241M,V242M,V243M,V244M,V245M,V246M,V247M,V248M,V249M,V250M,V251M,V252M,V253M,V254M,V255M,V256M,V257M,V258M,V259M,V260M,V261M,V262M,V263M,V264M,V265M,V266M,V267M,V268M,V269M,V270M,V271M,V272M,V273M,V274M,V275M,V276M,V277M,V278M,V279M,V280M,V281M,V282M,V283M,V284M,V285M,V286M,V287M,V288M,V289M,V290M,V291M,V292M,V293M,V294M,V295M,V296M,V297M,V298M,V299M,V300M,V301M,V302M,V303M,V304M,V305M,V306M,V307M,V308M,V309M,V310M,V311M,V312M,V313M,V314M,V315M,V316M,V317M,V318M,V319M,V320M,V321M,V322M,V323M,V324M,V325M,V326M,V327M,V328M,V329M,V330M,V331M,V332M,V333M,V334M,V335M,V336M,V337M,V338M,V339M,V340M,V341M,V342M,V343M,V344M,V345M,V346M,V347M,V348M,V349M,V350M,V351M,V352M,V353M,V354M,V355M,V356M,V357M,V358M,V359M,V360M,V361M,V362M,V363M,V364M,V365M,V366M,V367M,V368M,V369M,V370M,V371M,V372M,V373M,V374M,V375M,V376M,V377M,V378M,V379M,V380M,V381M,V382M,V383M,V384M,V385M,V386M,V387M,V388M,V389M,V390M,V391M,V392M,V393M,V394M,V395M,V396M,V397M,V398M,V399M,V400M,V401M,V402M,V403M,V404M,V405M,V406M,V407M,V408M,V409M,V410M,V411M,V412M,V413M,V414M,V415M,V416M,V417M,V418M,V419M,V420M,V421M,V422M,V423M,V424M,V425M,V426M,V427M,V428M,V429M,V430M,V431M,V432M,V433M,V434M,V435M,V436M,V437M,V438M,V439M,V440M,V441M,V442M,V443M,V444M,V445M,V446M,V447M,V448M,V449M,V450M,V451M,V452M,V453M,V454M,V455M,V456M,V457M,V458M,V459M,V460M,V461M,V462M,V463M,V464M,V465M,V466M,V467M,V468M,V469M,V470M,V471M,V472M,V473M,V474M,V475M,V476M,V477M,V478M,V479M,V480M,V481M,V482M,V483M,V484M,V485M,V486M,V487M,V488M,V489M,V490M,V491M,V492M,V493M,V494M,V495M,V496M,V497M,V498M,V499M,V500M,V501M,V502M,V503M,V504M,V505M,V506M,V507M,V508M,V509M,V510M,V511M,V512M,V513M,V514M,V515M,V516M,V517M,V518M,V519M,V520M,V521M,V522M,V523M,V524M,V525M,V526M,V527M,V528M,V529M,V530M,V531M,V532M,V533M,V534M,V535M,V536M,V537M,V538M,V539M,V540M,V541M,V542M,V543M,V544M,V545M,V546M,V547M,V548M,V549M,V550M,V551M,V552M,V553M,V554M,V555M,V556M,V557M,V558M,V559M,V560M,V561M,V562M,V563M,V564M,V565M,V566M,V567M,V568M,V569M,V570M,V571M,V572M,V573M,V574M,V575M,V576M,V577M,V578M,V579M,V580M,V581M,V582M,V583M,V584M,V585M,V586M,V587M,V588M,V589M,V590M,V591M,V592M,V593M,V594M,V595M,V596M,V597M,V598M,V599M,V600M,V601M,V602M,V603M,V604M,V605M,V606M,V607M,V608M,V609M,V610M,V611M,V612M,V613M,V614M,V615M,V616M,V617M,V618M,V619M,V620M,V621M,V622M,V623M,V624M,V625M,V626M,V627M,V628M,V629M,V630M,V631M,V632M,V633M,V634M,V635M,V636M,V637M,V638M,V639M,V640M,V641M,V642M,V643M,V644M,V645M,V646M,V647M,V648M,V649M,V650M,V651M,V652M,V653M,V654M,V655M,V656M,V657M,V658M,V659M,V660M,V661M,V662M,V663M,V664M,V665M,V666M,V667M,V668M,V669M,V670M,V671M,V672M,V673M,V674M,V675M,V676M,V677M,V678M,V679M,V680M,V681M,V682M,V683M,V684M,V685M,V686M,V687M,V688M,V689M,V690M,V691M,V692M,V693M,V694M,V695M,V696M,V697M,V698M,V699M,V700M,V701M,V702M,V703M,V704M,V705M,V706M,V707M,V708M,V709M,V710M,V711M,V712M,V713M,V714M,V715M,V716M,V717M,V718M,V719M,V720M,V721M,V722M,V723M,V724M,V725M,V726M,V727M,V728M,V729M,V730M,V731M,V732M,V733M,V734M,V735M,V736M,V737M,V738M,V739M,V740M,V741M,V742M,V743M,V744M,V745M,V746M,V747M,V748M,V749M,V750M,V751M,V752M,V753M,V754M,V755M,V756M,V757M,V758M,V759M,V760M,V761M,V762M,V763M,V764M,V765M,V766M,V767M,V768M,V769M,V770M,V771M,V772M,V773M,V774M,V775M,V776M,V777M,V778M,V779M,V780M,V781M,V782M,V783M,V784M,V785M,V786M,V787M,V788M,V789M,V790M,V791M,V792M,V793M,V794M,V795M,V796M,V797M,V798M,V799M,V800M,V801M,V802M,V803M,V804M,V805M,V806M,V807M,V808M,V809M,V810M,V811M,V812M,V813M,V814M,V815M,V816M,V817M,V818M,V819M,V820M,V821M,V822M,V823M,V824M,V825M,V826M,V827M,V828M,V829M,V830M,V831M,V832M,V833M,V834M,V835M,V836M,V837M,V838M,V839M,V840M,V841M,V842M,V843M,V844M,V845M,V846M,V847M,V848M,V849M,V850M,V851M,V852M,V853M,V854M,V855M,V856M,V857M,V858M,V859M,V860M,V861M,V862M,V863M,V864M,V865M,V866M,V867M,V868M,V869M,V870M,V871M,V872M,V873M,V874M,V875M,V876M,V877M,V878M,V879M,V880M,V881M,V882M,V883M,V884M,V885M,V886M,V887M,V888M,V889M,V890M,V891M,V892M,V893M,V894M,V895M,V896M,V897M,V898M,V899M,V900M,V901M,V902M,V903M,V904M,V905M,V906M,V907M,V908M,V909M,V910M,V911M,V912M,V913M,V914M,V915M,V916M}

MSG_ACORNPUB_BATCHGETREWARD_REQ = 201362
MSG_ACORNPUB_BATCHGETREWARD_RSP = 201363
MSG_ACORNPUB_GET_INFO_REQ = 201350
MSG_ACORNPUB_GET_INFO_RSP = 201351
MSG_ACORNPUB_GET_RECORD_REQ = 201365
MSG_ACORNPUB_GET_RECORD_RSP = 201366
MSG_ACORNPUB_GET_TASK_ROB_RECORD_REQ = 201369
MSG_ACORNPUB_GET_TASK_ROB_RECORD_RSP = 201370
MSG_ACORNPUB_HELPTASK_REQ = 201356
MSG_ACORNPUB_HELPTASK_RSP = 201357
MSG_ACORNPUB_HELP_RECORD_LIKED_REQ = 201371
MSG_ACORNPUB_HELP_RECORD_LIKED_RSP = 201372
MSG_ACORNPUB_INFO_NTF = 201364
MSG_ACORNPUB_OPEN_TREASURE_REQ = 202102
MSG_ACORNPUB_OPEN_TREASURE_RSP = 202103
MSG_ACORNPUB_REFRESHTASK_REQ = 201358
MSG_ACORNPUB_REFRESHTASK_RSP = 201359
MSG_ACORNPUB_ROBTASK_REQ = 201354
MSG_ACORNPUB_ROBTASK_RSP = 201355
MSG_ACORNPUB_SENDTASK_PRE_REQ = 201367
MSG_ACORNPUB_SENDTASK_PRE_RSP = 201368
MSG_ACORNPUB_SENDTASK_REQ = 201352
MSG_ACORNPUB_SENDTASK_RSP = 201353
MSG_ACORNPUB_SUPERREFRESHTASK_REQ = 201360
MSG_ACORNPUB_SUPERREFRESHTASK_RSP = 201361
MSG_ACORNPUB_TREASURE_BASE_NTF = 202101
MSG_ACORNPUB_TREASURE_PARASE_REQ = 202123
MSG_ACORNPUB_TREASURE_PARASE_RSP = 202124
MSG_ACTIVE_CHOOSE_WEEK_CARD_NTF = 202204
MSG_ACTIVITY_NOTICEPERIOD_NTF = 202426
MSG_ACTOR_CREATE_DATA_SYNC_REQ = 202542
MSG_ACTOR_CREATE_DATA_SYNC_RSP = 202543
MSG_ACT_TASK_DATA_NTF = 201002
MSG_ACT_TASK_GETREWARD_REQ = 201000
MSG_ACT_TASK_GETREWARD_RSP = 201001
MSG_AICUSTOMERSERVICE_NTF = 202281
MSG_AICUSTOMERSERVICE_REQ = 202282
MSG_AICUSTOMERSERVICE_RSP = 202283
MSG_ALLIANCEBOSS_CHANGELOC_REQ = 200809
MSG_ALLIANCEBOSS_CHANGELOC_RSP = 200810
MSG_ALLIANCEBOSS_CHANGE_OFFLINE_MASS_REQ = 200811
MSG_ALLIANCEBOSS_CHANGE_OFFLINE_MASS_RSP = 200812
MSG_ALLIANCEBOSS_DONATE_REQ = 200802
MSG_ALLIANCEBOSS_DONATE_RSP = 200803
MSG_ALLIANCEBOSS_GETREADY_REQ = 200804
MSG_ALLIANCEBOSS_GETREADY_RSP = 200805
MSG_ALLIANCEBOSS_GET_INFO_REQ = 200800
MSG_ALLIANCEBOSS_GET_INFO_RSP = 200801
MSG_ALLIANCEBOSS_INFO_NTF = 200808
MSG_ALLIANCEBOSS_START_REQ = 200806
MSG_ALLIANCEBOSS_START_RSP = 200807
MSG_ALLIANCEDUEL_ALLIANCEPOINT_NTF = 201253
MSG_ALLIANCEDUEL_BACK_COORDINATE_REQ = 201266
MSG_ALLIANCEDUEL_BACK_COORDINATE_RSP = 201267
MSG_ALLIANCEDUEL_BATTLEINFO_NTF = 201277
MSG_ALLIANCEDUEL_BATTLEINFO_REQ = 201275
MSG_ALLIANCEDUEL_BATTLEINFO_RSP = 201276
MSG_ALLIANCEDUEL_BATTLE_CITY_BACK_REQ = 201268
MSG_ALLIANCEDUEL_BATTLE_CITY_BACK_RSP = 201269
MSG_ALLIANCEDUEL_BATTLE_CITY_MOVE_REQ = 201264
MSG_ALLIANCEDUEL_BATTLE_CITY_MOVE_RSP = 201265
MSG_ALLIANCEDUEL_BATTLE_RANK_SCORE_REQ = 201281
MSG_ALLIANCEDUEL_BATTLE_RANK_SCORE_RSP = 201282
MSG_ALLIANCEDUEL_BATTLE_RECORD_REQ = 201273
MSG_ALLIANCEDUEL_BATTLE_RECORD_RSP = 201274
MSG_ALLIANCEDUEL_BATTLE_UP_DOWN_NTF = 201278
MSG_ALLIANCEDUEL_BATTLE_UP_DOWN_REQ = 201279
MSG_ALLIANCEDUEL_BATTLE_UP_DOWN_RSP = 201280
MSG_ALLIANCEDUEL_DEFEATNOTICE_REQ = 201257
MSG_ALLIANCEDUEL_DEFEATNOTICE_RSP = 201258
MSG_ALLIANCEDUEL_DEFEAT_NTF = 201256
MSG_ALLIANCEDUEL_INFO_NTF = 201252
MSG_ALLIANCEDUEL_INFO_REQ = 201250
MSG_ALLIANCEDUEL_INFO_RSP = 201251
MSG_ALLIANCEDUEL_MOVE_COORDINATE_REQ = 201262
MSG_ALLIANCEDUEL_MOVE_COORDINATE_RSP = 201263
MSG_ALLIANCEDUEL_POINT_MVP_REQ = 201271
MSG_ALLIANCEDUEL_POINT_MVP_RSP = 201272
MSG_ALLIANCEDUEL_SANDBOXID_NTF = 201270
MSG_ALLIANCEDUEL_THEMEINFO_REQ = 201254
MSG_ALLIANCEDUEL_THEMEINFO_RSP = 201255
MSG_ALLIANCELOG_RECORD_REQ = 202506
MSG_ALLIANCELOG_RECORD_RSP = 202507
MSG_ALLIANCE_ACHIEVEMENT_NTF = 200960
MSG_ALLIANCE_ACHIEVEMENT_REQ = 200958
MSG_ALLIANCE_ACHIEVEMENT_REWARD_REQ = 200961
MSG_ALLIANCE_ACHIEVEMENT_REWARD_RSP = 200962
MSG_ALLIANCE_ACHIEVEMENT_RSP = 200959
MSG_ALLIANCE_ANONYMOUS_REQ = 200033
MSG_ALLIANCE_ANONYMOUS_RSP = 200034
MSG_ALLIANCE_APPLICATION_LIST_REQ = 200053
MSG_ALLIANCE_APPLICATION_LIST_RSP = 200054
MSG_ALLIANCE_APPLY_REQ = 200011
MSG_ALLIANCE_APPLY_RSP = 200012
MSG_ALLIANCE_AUTHORITY_REQ = 200021
MSG_ALLIANCE_AUTHORITY_RSP = 200022
MSG_ALLIANCE_AUTO_RESEARCH_REQ = 200950
MSG_ALLIANCE_AUTO_RESEARCH_RSP = 200951
MSG_ALLIANCE_CHANGECEO_REQ = 200019
MSG_ALLIANCE_CHANGECEO_RSP = 200020
MSG_ALLIANCE_CHANGE_NTF = 200027
MSG_ALLIANCE_CHECKCONTENT_REQ = 200025
MSG_ALLIANCE_CHECKCONTENT_RSP = 200026
MSG_ALLIANCE_CHECKNAME_REQ = 200013
MSG_ALLIANCE_CHECKNAME_RSP = 200014
MSG_ALLIANCE_CREATE_REQ = 200017
MSG_ALLIANCE_CREATE_RSP = 200018
MSG_ALLIANCE_EXIT_REQ = 200070
MSG_ALLIANCE_EXIT_RSP = 200071
MSG_ALLIANCE_EXPEL_REQ = 200023
MSG_ALLIANCE_EXPEL_RSP = 200024
MSG_ALLIANCE_FIRST_JOINTIME_NTF = 200091
MSG_ALLIANCE_FREE_MOVE_CITY_NTF = 200966
MSG_ALLIANCE_GIFT_GET_REQ = 200029
MSG_ALLIANCE_GIFT_GET_RSP = 200030
MSG_ALLIANCE_GIFT_NTF = 200032
MSG_ALLIANCE_HANDLE_APPLICATION_REQ = 200055
MSG_ALLIANCE_HANDLE_APPLICATION_RSP = 200056
MSG_ALLIANCE_HELP_CLICK_NTF = 200090
MSG_ALLIANCE_HELP_CLICK_REQ = 200088
MSG_ALLIANCE_HELP_CLICK_RSP = 200089
MSG_ALLIANCE_HELP_LIST_NTF = 200085
MSG_ALLIANCE_HELP_LIST_REQ = 200083
MSG_ALLIANCE_HELP_LIST_RSP = 200084
MSG_ALLIANCE_HELP_SELF_REQ = 200081
MSG_ALLIANCE_HELP_SELF_RSP = 200082
MSG_ALLIANCE_HELP_START_REQ = 200086
MSG_ALLIANCE_HELP_START_RSP = 200087
MSG_ALLIANCE_INFO_REQ = 200005
MSG_ALLIANCE_INFO_RSP = 200006
MSG_ALLIANCE_INTELLIGENCE_NTF = 201725
MSG_ALLIANCE_INTELLIGENCE_REQ = 201723
MSG_ALLIANCE_INTELLIGENCE_RSP = 201724
MSG_ALLIANCE_INVITATION_REQ = 200099
MSG_ALLIANCE_INVITATION_REWARD_NTF = 200098
MSG_ALLIANCE_INVITATION_RSP = 200100
MSG_ALLIANCE_INVITE_INFO_REQ = 200967
MSG_ALLIANCE_INVITE_INFO_RSP = 200968
MSG_ALLIANCE_INVITE_NTF = 200957
MSG_ALLIANCE_INVITE_REQ = 200955
MSG_ALLIANCE_INVITE_RSP = 200956
MSG_ALLIANCE_ISACCEPT_INVITE_REQ = 200964
MSG_ALLIANCE_ISACCEPT_INVITE_RSP = 200965
MSG_ALLIANCE_KILLNUM_REQ = 200078
MSG_ALLIANCE_KILLNUM_RSP = 200079
MSG_ALLIANCE_MAIL_SEND_REQ = 200067
MSG_ALLIANCE_MAIL_SEND_RSP = 200068
MSG_ALLIANCE_MARK_REQ = 200094
MSG_ALLIANCE_MARK_RSP = 200095
MSG_ALLIANCE_MASS_CNT_NTF = 201722
MSG_ALLIANCE_MASS_NTF = 201721
MSG_ALLIANCE_MASS_REQ = 201719
MSG_ALLIANCE_MASS_RSP = 201720
MSG_ALLIANCE_MEDAL_BASE_NTF = 201703
MSG_ALLIANCE_MEDAL_BASE_REQ = 201701
MSG_ALLIANCE_MEDAL_BASE_RSP = 201702
MSG_ALLIANCE_MEDAL_DETAIL_REQ = 201704
MSG_ALLIANCE_MEDAL_DETAIL_RSP = 201705
MSG_ALLIANCE_MEDAL_REWARD_REQ = 201706
MSG_ALLIANCE_MEDAL_REWARD_RSP = 201707
MSG_ALLIANCE_MEDAL_WARNAUTO_REQ = 201710
MSG_ALLIANCE_MEDAL_WARNAUTO_RSP = 201711
MSG_ALLIANCE_MEDAL_WARNDEL_REQ = 201708
MSG_ALLIANCE_MEDAL_WARNDEL_RSP = 201709
MSG_ALLIANCE_MEDAL_WARN_REQ = 201712
MSG_ALLIANCE_MEDAL_WARN_RSP = 201713
MSG_ALLIANCE_MODIFY_ANNOUNCEMENT_REQ = 200051
MSG_ALLIANCE_MODIFY_ANNOUNCEMENT_RSP = 200052
MSG_ALLIANCE_MODIFY_FLAG_REQ = 200057
MSG_ALLIANCE_MODIFY_FLAG_RSP = 200058
MSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_REQ = 200063
MSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP = 200064
MSG_ALLIANCE_MODIFY_LANGUAGE_REQ = 200061
MSG_ALLIANCE_MODIFY_LANGUAGE_RSP = 200062
MSG_ALLIANCE_MODIFY_NAME_REQ = 200059
MSG_ALLIANCE_MODIFY_NAME_RSP = 200060
MSG_ALLIANCE_NEEDPOPUP_NTF = 200080
MSG_ALLIANCE_ONE_CLICK_JOIN_REQ = 200092
MSG_ALLIANCE_ONE_CLICK_JOIN_RSP = 200093
MSG_ALLIANCE_OUTFIRE_NTF = 202433
MSG_ALLIANCE_OUTFIRE_REQ = 202431
MSG_ALLIANCE_OUTFIRE_RSP = 202432
MSG_ALLIANCE_POWERRANK_REQ = 200072
MSG_ALLIANCE_POWERRANK_RSP = 200073
MSG_ALLIANCE_QUICK_ADD_REQ = 200009
MSG_ALLIANCE_QUICK_ADD_RSP = 200010
MSG_ALLIANCE_R4R5TODO_NTF = 202501
MSG_ALLIANCE_R4R5TODO_REWARD_REQ = 202502
MSG_ALLIANCE_R4R5TODO_REWARD_RSP = 202503
MSG_ALLIANCE_RANDOM_NAME_REQ = 200015
MSG_ALLIANCE_RANDOM_NAME_RSP = 200016
MSG_ALLIANCE_RECOMMEND_REQ = 200001
MSG_ALLIANCE_RECOMMEND_RSP = 200002
MSG_ALLIANCE_RECORD_REQ = 201717
MSG_ALLIANCE_RECORD_RSP = 201718
MSG_ALLIANCE_ROLERANK_REQ = 200074
MSG_ALLIANCE_ROLERANK_RSP = 200075
MSG_ALLIANCE_ROLE_INFO_REQ = 200007
MSG_ALLIANCE_ROLE_INFO_RSP = 200008
MSG_ALLIANCE_SEARCH_REQ = 200003
MSG_ALLIANCE_SEARCH_RSP = 200004
MSG_ALLIANCE_SHARE_REQ = 200096
MSG_ALLIANCE_SHARE_RSP = 200097
MSG_ALLIANCE_SURPASS_RALLYPOINT_NUMS_NTF = 201726
MSG_ALLIANCE_TECHNOLOGY_DETAILS_REQ = 200076
MSG_ALLIANCE_TECHNOLOGY_DETAILS_RSP = 200077
MSG_ALLIANCE_TECHNOLOGY_DONATE_REQ = 200041
MSG_ALLIANCE_TECHNOLOGY_DONATE_RSP = 200042
MSG_ALLIANCE_TECHNOLOGY_RECOMMEND_REQ = 200037
MSG_ALLIANCE_TECHNOLOGY_RECOMMEND_RSP = 200038
MSG_ALLIANCE_TECHNOLOGY_REQ = 200035
MSG_ALLIANCE_TECHNOLOGY_RSP = 200036
MSG_ALLIANCE_TECHNOLOGY_STUDY_REQ = 200039
MSG_ALLIANCE_TECHNOLOGY_STUDY_RSP = 200040
MSG_ALLIANCE_THEMEDUEL_NTF = 201261
MSG_ALLIANCE_THEMEDUEL_REQ = 201259
MSG_ALLIANCE_THEMEDUEL_RSP = 201260
MSG_ALLIANCE_TRAIN_APPOINT_DRIVER_REQ = 201905
MSG_ALLIANCE_TRAIN_APPOINT_DRIVER_RSP = 201906
MSG_ALLIANCE_TRAIN_ATTACK_REQ = 201917
MSG_ALLIANCE_TRAIN_ATTACK_RSP = 201918
MSG_ALLIANCE_TRAIN_BATTLE_HISTORY_REQ = 201921
MSG_ALLIANCE_TRAIN_BATTLE_HISTORY_RSP = 201922
MSG_ALLIANCE_TRAIN_ENTER_CARRIAGE_REQ = 201903
MSG_ALLIANCE_TRAIN_ENTER_CARRIAGE_RSP = 201904
MSG_ALLIANCE_TRAIN_GET_CARD_REQ = 201913
MSG_ALLIANCE_TRAIN_GET_CARD_RSP = 201914
MSG_ALLIANCE_TRAIN_GET_DEFEND_LINEUP_REQ = 201909
MSG_ALLIANCE_TRAIN_GET_DEFEND_LINEUP_RSP = 201910
MSG_ALLIANCE_TRAIN_GET_INFO_REQ = 201901
MSG_ALLIANCE_TRAIN_GET_INFO_RSP = 201902
MSG_ALLIANCE_TRAIN_GET_ROLE_INFO_REQ = 201919
MSG_ALLIANCE_TRAIN_GET_ROLE_INFO_RSP = 201920
MSG_ALLIANCE_TRAIN_GIVE_CARD_REQ = 201911
MSG_ALLIANCE_TRAIN_GIVE_CARD_RSP = 201912
MSG_ALLIANCE_TRAIN_GM_NTF = 201925
MSG_ALLIANCE_TRAIN_REFRESH_LOOT_NUM_NTF = 201928
MSG_ALLIANCE_TRAIN_REFRESH_REQ = 201926
MSG_ALLIANCE_TRAIN_REFRESH_RSP = 201927
MSG_ALLIANCE_TRAIN_SET_DEFEND_LINEUP_REQ = 201907
MSG_ALLIANCE_TRAIN_SET_DEFEND_LINEUP_RSP = 201908
MSG_ALLIANCE_TRAIN_SWAP_LINEUP_REQ = 201923
MSG_ALLIANCE_TRAIN_SWAP_LINEUP_RSP = 201924
MSG_ALLIANCE_TRAIN_UPDATE_INFO_NTF = 201900
MSG_ALLIANCE_TRAIN_USE_CARD_REQ = 201915
MSG_ALLIANCE_TRAIN_USE_CARD_RSP = 201916
MSG_ALLIANCE_UPDATE_NTF = 200069
MSG_ARMSRACE_INFO_REQ = 200901
MSG_ARMSRACE_INFO_RSP = 200902
MSG_ARMSRACE_POIN_PROCESS_REQ = 200906
MSG_ARMSRACE_POIN_PROCESS_RSP = 200907
MSG_ARMSRACE_ROUND_REPLACE_RSP = 200905
MSG_ARMSRACE_TASKFINISH_RSP = 200903
MSG_ARMSRACE_TEAM_REPLACE_RSP = 200904
MSG_All_ATY_CHOOSE_WEEK_CARD_NTF = 202200
MSG_BATTLE_HOSPITAL_SOLDIER_GET_REQ = 202052
MSG_BATTLE_HOSPITAL_SOLDIER_GET_RSP = 202053
MSG_BATTLE_HOSPITAL_SOLDIER_REQ = 202050
MSG_BATTLE_HOSPITAL_SOLDIER_RSP = 202051
MSG_BATTLE_PASS_GET_ALL_REWARD_REQ = 202400
MSG_BATTLE_PASS_GET_ALL_REWARD_RSP = 202401
MSG_BUFFINFO_NTF = 200420
MSG_BUILDINGVISITOR_DEL_REQ = 202291
MSG_BUILDINGVISITOR_DEL_RSP = 202292
MSG_BUILDING_VISITOR_NTF = 202290
MSG_BUY_CHOOSE_WEEK_CARD_CHECK_REQ = 202202
MSG_BUY_CHOOSE_WEEK_CARD_CHECK_RSP = 202203
MSG_CAMPTRIAL_ACTIVITY_NTF = 201202
MSG_CAMPTRIAL_ACTIVITY_REQ = 201201
MSG_CAMPTRIAL_CHALL_REQ = 201231
MSG_CAMPTRIAL_CHALL_RSP = 201232
MSG_CAMPTRIAL_HERO_RECORD_NTF = 201212
MSG_CAMPTRIAL_PROGRESS_NTF = 201211
MSG_CAMPTRIAL_QUICK_CHALL_REQ = 201233
MSG_CAMPTRIAL_QUICK_CHALL_RSP = 201234
MSG_CAMPTRIAL_RANK_FIRST_NTF = 201213
MSG_CAMPTRIAL_SELECT_DIFF_REQ = 201221
MSG_CAMPTRIAL_SELECT_DIFF_RSP = 201222
MSG_CARRIAGE_CNETER_DATA_NTF = 201120
MSG_CARRIAGE_ERR_NTF = 201143
MSG_CARRIAGE_LOOT_DATA_REQ = 201121
MSG_CARRIAGE_LOOT_DATA_RSP = 201122
MSG_CARRIAGE_MAX = 201159
MSG_CARRIAGE_MINE_DATA_REQ = 201125
MSG_CARRIAGE_MINE_DATA_RSP = 201126
MSG_CARRIAGE_PRE_TRADE_REQ = 201127
MSG_CARRIAGE_PRE_TRADE_RSP = 201128
MSG_CARRIAGE_REPART_REQ = 201131
MSG_CARRIAGE_REPART_RSP = 201132
MSG_CARRIAGE_SAVE_TROOP_REQ = 201144
MSG_CARRIAGE_SAVE_TROOP_RSP = 201145
MSG_CARRIAGE_SET_FILTER_REQ = 201123
MSG_CARRIAGE_SET_FILTER_RSP = 201124
MSG_CARRIAGE_SET_TROOP_REQ = 201129
MSG_CARRIAGE_SET_TROOP_RSP = 201130
MSG_CARRIAGE_TRADE_DETAIL_REQ = 201133
MSG_CARRIAGE_TRADE_DETAIL_RSP = 201134
MSG_CARRIAGE_TRADE_HISTORY_REQ = 201137
MSG_CARRIAGE_TRADE_HISTORY_RSP = 201138
MSG_CARRIAGE_TRADE_RECOR_REQ = 201135
MSG_CARRIAGE_TRADE_RECOR_RSP = 201136
MSG_CARRIAGE_TRADE_REWARD_REQ = 201141
MSG_CARRIAGE_TRADE_REWARD_RSP = 201142
MSG_CARRIAGE_WANTED_LOOT_REQ = 201139
MSG_CARRIAGE_WANTED_LOOT_RSP = 201140
MSG_CARRIAGE_WANTLIST_NTF = 201146
MSG_CHANGEBAG_GIFT_NTF = 202413
MSG_CHANGEBAG_GIFT_REQ = 202411
MSG_CHANGEBAG_GIFT_RSP = 202412
MSG_CHAT_MSG_GET_BATTLE_REPORT_REQ = 202452
MSG_CHAT_MSG_GET_BATTLE_REPORT_RSP = 202453
MSG_CHAT_MSG_SAVE_BATTLE_REPORT_REQ = 202450
MSG_CHAT_MSG_SAVE_BATTLE_REPORT_RSP = 202451
MSG_CITY_AREA_STATE_CHANGE_NTF = 200255
MSG_CITY_AREA_UPDATE_NTF = 200227
MSG_CITY_BUILDING_UPDATE_NTF = 200225
MSG_CITY_BUILDQUEUE_UPDATE_NTF = 200226
MSG_CITY_BUILD_A_BUILDING_REQ = 200203
MSG_CITY_BUILD_A_BUILDING_RSP = 200204
MSG_CITY_BUILD_DIAMOND_RENT_QUEUE_REQ = 200238
MSG_CITY_BUILD_DIAMOND_RENT_QUEUE_RSP = 200239
MSG_CITY_BUILD_UPGRADE_NOW_REQ = 200236
MSG_CITY_BUILD_UPGRADE_NOW_RSP = 200237
MSG_CITY_CHANGE_BUILDING_LOCATION_REQ = 200213
MSG_CITY_CHANGE_BUILDING_LOCATION_RSP = 200214
MSG_CITY_CHECK_AREA_EVENT_REQ = 200267
MSG_CITY_CHECK_AREA_EVENT_RSP = 200268
MSG_CITY_CLICK_FULL_OPEN_REQ = 200253
MSG_CITY_CLICK_FULL_OPEN_RSP = 200254
MSG_CITY_COMPLETE_BEGIN_EVENT_REQ = 200256
MSG_CITY_COMPLETE_BEGIN_EVENT_RSP = 200257
MSG_CITY_DISPATCH_WORKER_REQ = 200230
MSG_CITY_DISPATCH_WORKER_RSP = 200231
MSG_CITY_EXCHANGE_ORDER_REQ = 200258
MSG_CITY_EXCHANGE_ORDER_RSP = 200259
MSG_CITY_GET_ALLINFO_REQ = 200273
MSG_CITY_GET_ALLINFO_RSP = 200202
MSG_CITY_GET_ALL_TROOP_REQ = 200244
MSG_CITY_GET_ALL_TROOP_RSP = 200245
MSG_CITY_GET_BUILDING_OPENGIFT_REQ = 200207
MSG_CITY_GET_BUILDING_OPENGIFT_RSP = 200208
MSG_CITY_GET_BUILDING_RESOURCE_REQ = 200211
MSG_CITY_GET_BUILDING_RESOURCE_RSP = 200212
MSG_CITY_GET_EVENT_REWARD_REQ = 200251
MSG_CITY_GET_EVENT_REWARD_RSP = 200252
MSG_CITY_GET_REWARD_QUEUE_REQ = 200265
MSG_CITY_GET_REWARD_QUEUE_RSP = 200266
MSG_CITY_GET_TROOP_REQ = 200242
MSG_CITY_GET_TROOP_RSP = 200243
MSG_CITY_HOSPITAL_CURE_SOLDIER_REQ = 200221
MSG_CITY_HOSPITAL_CURE_SOLDIER_RSP = 200222
MSG_CITY_HOSPITAL_GET_SOLDIER_REQ = 200223
MSG_CITY_HOSPITAL_GET_SOLDIER_RSP = 200224
MSG_CITY_HOSPITAL_UPDATE_NTF = 200233
MSG_CITY_MILITARY_GET_SOLDIER_REQ = 200217
MSG_CITY_MILITARY_GET_SOLDIER_RSP = 200218
MSG_CITY_MILITARY_RECRUIT_SOLDIER_REQ = 200215
MSG_CITY_MILITARY_RECRUIT_SOLDIER_RSP = 200216
MSG_CITY_MILITARY_UPGRADE_SOLDIER_REQ = 200219
MSG_CITY_MILITARY_UPGRADE_SOLDIER_RSP = 200220
MSG_CITY_PROP_UPDATE_NTF = 200246
MSG_CITY_REPAIR_A_BUILDING_REQ = 200205
MSG_CITY_REPAIR_A_BUILDING_RSP = 200206
MSG_CITY_REWARD_QUEUE_NTF = 200264
MSG_CITY_SAVE_TROOP_REQ = 200240
MSG_CITY_SAVE_TROOP_RSP = 200241
MSG_CITY_TEAMINFO_NTF = 200260
MSG_CITY_TRAINNINGCENTER_UPDATE_NTF = 200232
MSG_CITY_TROOP_IS_IDLE_REQ = 200271
MSG_CITY_TROOP_IS_IDLE_RSP = 200272
MSG_CITY_UPGRADE_A_BUILDING_REQ = 200209
MSG_CITY_UPGRADE_A_BUILDING_RSP = 200210
MSG_CITY_WALL_BUY_CITYDEFENSE_REQ = 200269
MSG_CITY_WALL_BUY_CITYDEFENSE_RSP = 200270
MSG_CITY_WALL_FIRE_FIGHTING_REQ = 200234
MSG_CITY_WALL_FIRE_FIGHTING_RSP = 200235
MSG_CITY_WORKER_COMPOSITE_REQ = 200249
MSG_CITY_WORKER_COMPOSITE_RSP = 200250
MSG_CITY_WORKER_DISPATCH_ONECLICK_REQ = 200261
MSG_CITY_WORKER_DISPATCH_ONECLICK_RSP = 200262
MSG_CITY_WORKER_LEVELUP_REQ = 200228
MSG_CITY_WORKER_LEVELUP_RSP = 200229
MSG_CITY_WORKER_REPLACE_REQ = 200247
MSG_CITY_WORKER_REPLACE_RSP = 200248
MSG_CITY_WORKER_UPDATE_NTF = 200263
MSG_COMMONATY_CANCLE_EXCHANGE_REQ = 202117
MSG_COMMONATY_CANCLE_EXCHANGE_RSP = 202118
MSG_COMMONATY_EXCHANGE_GOODS_REQ = 202115
MSG_COMMONATY_EXCHANGE_GOODS_RSP = 202116
MSG_COMMONATY_EXCHANGE_ORDER_LIST_REQ = 202119
MSG_COMMONATY_EXCHANGE_ORDER_LIST_RSP = 202120
MSG_COMMONATY_SHELF_GOODS_REQ = 202113
MSG_COMMONATY_SHELF_GOODS_RSP = 202114
MSG_COMMONATY_VIEWORDER_REQ = 202111
MSG_COMMONATY_VIEWORDER_RSP = 202112
MSG_COMMONATY_VIEW_ORDER_RECORD_REQ = 202121
MSG_COMMONATY_VIEW_ORDER_RECORD_RSP = 202122
MSG_COMMUNITYGIFT_GETREWARD_REQ = 202195
MSG_COMMUNITYGIFT_GETREWARD_RSP = 202196
MSG_COMMUNITYGIFT_JUMP_REQ = 202193
MSG_COMMUNITYGIFT_JUMP_RSP = 202194
MSG_COMMUNITYGIFT_REWARDSTATUS_REQ = 202191
MSG_COMMUNITYGIFT_REWARDSTATUS_RSP = 202192
MSG_CONGRESS_ADDITIONAL_OFFICIAL_NTF = 201405
MSG_CONGRESS_APPLYFOR_LIST_NTF = 201431
MSG_CONGRESS_APPLYFOR_LIST_REQ = 201432
MSG_CONGRESS_APPLYFOR_LIST_RSP = 201433
MSG_CONGRESS_APPLYFOR_MANAGE_REQ = 201434
MSG_CONGRESS_APPLYFOR_MANAGE_RSP = 201435
MSG_CONGRESS_APPLYFOR_REQ = 201437
MSG_CONGRESS_APPLYFOR_RSP = 201438
MSG_CONGRESS_APPLYFOR_SELF_NTF = 201436
MSG_CONGRESS_APPOINT_REQ = 201421
MSG_CONGRESS_APPOINT_RSP = 201422
MSG_CONGRESS_AWARDRECORD_REQ = 201456
MSG_CONGRESS_AWARDRECORD_RSP = 201457
MSG_CONGRESS_AWARD_ALLOCATE_REQ = 201454
MSG_CONGRESS_AWARD_ALLOCATE_RSP = 201455
MSG_CONGRESS_AWARD_INFO_NTF = 201451
MSG_CONGRESS_AWARD_INFO_REQ = 201452
MSG_CONGRESS_AWARD_INFO_RSP = 201453
MSG_CONGRESS_DISMISS_REQ = 201423
MSG_CONGRESS_DISMISS_RSP = 201424
MSG_CONGRESS_MAIL_NTF = 201416
MSG_CONGRESS_MAINDATA_REQ = 201400
MSG_CONGRESS_MAINDATA_RSP = 201401
MSG_CONGRESS_MANIFESTO_EDIT_REQ = 201412
MSG_CONGRESS_MANIFESTO_EDIT_RSP = 201413
MSG_CONGRESS_MANIFESTO_NTF = 201411
MSG_CONGRESS_OFFICE_RECORD_REQ = 201425
MSG_CONGRESS_OFFICE_RECORD_RSP = 201426
MSG_CONGRESS_OFFICIAL_NTF = 201402
MSG_CONGRESS_OFFICIAL_REQ = 201403
MSG_CONGRESS_OFFICIAL_RSP = 201404
MSG_CONGRESS_PRESRECORD_REQ = 201406
MSG_CONGRESS_PRESRECORD_RSP = 201407
MSG_CONGRESS_QUEUESETTING_NTF = 201441
MSG_CONGRESS_QUEUEUP_CANCEL_REQ = 201449
MSG_CONGRESS_QUEUEUP_CANCEL_RSP = 201450
MSG_CONGRESS_QUEUEUP_LIST_REQ = 201447
MSG_CONGRESS_QUEUEUP_LIST_RSP = 201448
MSG_CONGRESS_QUEUEUP_NTF = 201446
MSG_CONGRESS_SENDMAIL_REQ = 201417
MSG_CONGRESS_SENDMAIL_RSP = 201418
MSG_CONGRESS_SET_TIME_REQ = 201442
MSG_CONGRESS_SET_TIME_RSP = 201443
MSG_DEBUG_NTF = 201502
MSG_DEL_MIRACLE_BOX_DATA_REQ = 201310
MSG_DEL_MIRACLE_BOX_DATA_RSP = 201311
MSG_DESERTSTROM_ACTIVITY_NTF = 202000
MSG_DESERTSTROM_BATTLE_LEAVE_REQ = 202032
MSG_DESERTSTROM_BATTLE_LEAVE_RSP = 202033
MSG_DESERTSTROM_BATTLE_RESULT_NTF = 202038
MSG_DESERTSTROM_BATTLE_SID_NTF = 202031
MSG_DESERTSTROM_BATTLE_STATUS_NTF = 202034
MSG_DESERTSTROM_BUILDING_DEFEND_REQ = 202061
MSG_DESERTSTROM_BUILDING_DEFEND_RSP = 202062
MSG_DESERTSTROM_CREATE_CITY_REQ = 202047
MSG_DESERTSTROM_CREATE_CITY_RSP = 202048
MSG_DESERTSTROM_INFO_CUE_NTF = 202063
MSG_DESERTSTROM_LOG_REQ = 202070
MSG_DESERTSTROM_LOG_RSP = 202071
MSG_DESERTSTROM_MATCH_INFO_NTF = 202024
MSG_DESERTSTROM_MATCH_INFO_REQ = 202022
MSG_DESERTSTROM_MATCH_INFO_RSP = 202023
MSG_DESERTSTROM_MOVE_CD_REQ = 202056
MSG_DESERTSTROM_MOVE_CD_RSP = 202057
MSG_DESERTSTROM_RANK_ALLIANCE_NTF = 202039
MSG_DESERTSTROM_RANK_BUILDING_NTF = 202044
MSG_DESERTSTROM_RANK_PUBLIC_REQ = 202035
MSG_DESERTSTROM_RANK_PUBLIC_RSP = 202036
MSG_DESERTSTROM_RANK_SELF_NTF = 202037
MSG_DESERTSTROM_RECORD_REQ = 202072
MSG_DESERTSTROM_RECORD_RSP = 202073
MSG_DESERTSTROM_ROLE_CITY_INFO_NTF = 202060
MSG_DESERTSTROM_SCOREBOX_ANIMATION_NTF = 202045
MSG_DESERTSTROM_SCOREBOX_NTF = 202058
MSG_DESERTSTROM_SCORE_ALLIANCE_REQ = 202040
MSG_DESERTSTROM_SCORE_ALLIANCE_RSP = 202041
MSG_DESERTSTROM_SCORE_SELF_REQ = 202042
MSG_DESERTSTROM_SCORE_SELF_RSP = 202043
MSG_DESERTSTROM_SIGNUP_ACTION_REQ = 202014
MSG_DESERTSTROM_SIGNUP_ACTION_RSP = 202015
MSG_DESERTSTROM_SIGNUP_INFO_NTF = 202013
MSG_DESERTSTROM_SIGNUP_INFO_REQ = 202011
MSG_DESERTSTROM_SIGNUP_INFO_RSP = 202012
MSG_DESERTSTROM_SIGNUP_SELECT_TIME_REQ = 202016
MSG_DESERTSTROM_SIGNUP_SELECT_TIME_RSP = 202017
MSG_DESERTSTROM_SIGNUP_SET_MEMBER_REQ = 202018
MSG_DESERTSTROM_SIGNUP_SET_MEMBER_RSP = 202019
MSG_DESERTSTROM_SIGNUP_TIME_HOPE_REQ = 202020
MSG_DESERTSTROM_SIGNUP_TIME_HOPE_RSP = 202021
MSG_DESERT_MAP_BUILDING_INFO_REQ = 202064
MSG_DESERT_MAP_BUILDING_INFO_RSP = 202065
MSG_DESERT_STROM_BUFFINFO_NTF = 202046
MSG_DESERT_TEST_ROUTER_REQ = 202066
MSG_DESERT_TEST_ROUTER_RSP = 202067
MSG_DRONEADVANDE_UPGRADE_REQ = 200710
MSG_DRONEADVANDE_UPGRADE_RSP = 200711
MSG_DRONECENTER_ADORN_NTF = 200709
MSG_DRONECENTER_ADORN_REQ = 200707
MSG_DRONECENTER_ADORN_RSP = 200708
MSG_DRONECENTER_ALLDATA_NTF = 200706
MSG_DRONECENTER_PARTEXCHANGE_REQ = 200704
MSG_DRONECENTER_PARTEXCHANGE_RSP = 200705
MSG_DRONECENTER_PARTUPGRADE_REQ = 200702
MSG_DRONECENTER_PARTUPGRADE_RSP = 200703
MSG_DRONECENTER_UPGRADE_REQ = 200700
MSG_DRONECENTER_UPGRADE_RSP = 200701
MSG_DRONESTAR_EQUIP_REQ = 200714
MSG_DRONESTAR_EQUIP_RSP = 200715
MSG_DRONESTAR_INFO_NTF = 200722
MSG_DRONESTAR_INFO_REQ = 200712
MSG_DRONESTAR_INFO_RSP = 200713
MSG_DRONESTAR_RESET_REQ = 200718
MSG_DRONESTAR_RESET_RSP = 200719
MSG_DRONESTAR_TROOP_REQ = 200720
MSG_DRONESTAR_TROOP_RSP = 200721
MSG_DRONESTAR_UPGRADE_REQ = 200716
MSG_DRONESTAR_UPGRADE_RSP = 200717
MSG_ERROR_NTF = 201501
MSG_EXCHANGE_SHOP_INFO_NTF = 201302
MSG_FULLBATTLE_EXCHANGE_REQ = 201300
MSG_FULLBATTLE_EXCHANGE_RSP = 201301
MSG_FULLBATTLE_REWARD_NTF = 201303
MSG_FUNCTIONID_OPEN_NTF = 201714
MSG_GATHERING_AWARD_REQ = 200851
MSG_GATHERING_AWARD_RSP = 200852
MSG_GATHERING_NTF = 200853
MSG_GEAR_SUPPLY_DRAWAWARD_REQ = 202140
MSG_GEAR_SUPPLY_DRAWAWARD_RSP = 202141
MSG_GEAR_SUPPLY_GETACTIVITYDATA_REQ = 202136
MSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP = 202137
MSG_GEAR_SUPPLY_RECEIVEAWARD_REQ = 202138
MSG_GEAR_SUPPLY_RECEIVEAWARD_RSP = 202139
MSG_GEAR_SUPPLY_SETREWARD_REQ = 202142
MSG_GEAR_SUPPLY_SETREWARD_RSP = 202143
MSG_GENERALTRIAL_ALLIANCECHALLENGE_REQ = 201032
MSG_GENERALTRIAL_ALLIANCECHALLENGE_RSP = 201033
MSG_GENERALTRIAL_ALLIANCEDATA_NTF = 201035
MSG_GENERALTRIAL_ALLIANCEDATA_REQ = 201028
MSG_GENERALTRIAL_ALLIANCEDATA_RSP = 201029
MSG_GENERALTRIAL_BEGAINCHALLENGE_REQ = 201024
MSG_GENERALTRIAL_BEGAINCHALLENGE_RSP = 201025
MSG_GENERALTRIAL_BEGINALLIANCECHALLENGE_REQ = 201030
MSG_GENERALTRIAL_BEGINALLIANCECHALLENGE_RSP = 201031
MSG_GENERALTRIAL_PERSONALDATA_NTF = 201034
MSG_GENERALTRIAL_PERSONALDATA_REQ = 201020
MSG_GENERALTRIAL_PERSONALDATA_RSP = 201021
MSG_GENERALTRIAL_PERSONALGETPRIZE_REQ = 201026
MSG_GENERALTRIAL_PERSONALGETPRIZE_RSP = 201027
MSG_GENERALTRIAL_SELECTDIFFICULTY_REQ = 201022
MSG_GENERALTRIAL_SELECTDIFFICULTY_RSP = 201023
MSG_GETBINDEMAIL_REWARD_REQ = 202421
MSG_GETBINDEMAIL_REWARD_RSP = 202422
MSG_GETREWARD_NTF = 201500
MSG_GET_ALL_HERO_REQ = 200443
MSG_GET_ALL_HERO_RSP = 200444
MSG_GET_CHOOSE_WEEK_CARD_REWARD_REQ = 202205
MSG_GET_CHOOSE_WEEK_CARD_REWARD_RSP = 202206
MSG_GHOST_PARTY_HOLD_LIST_REQ = 202555
MSG_GHOST_PARTY_HOLD_LIST_RSP = 202556
MSG_GHOST_PARTY_HOLD_REQ = 202553
MSG_GHOST_PARTY_HOLD_RSP = 202554
MSG_GOLDENEGGS_BACKPACK_LIKE_NTF = 202220
MSG_GOLDENEGGS_BACKPACK_REQ = 202214
MSG_GOLDENEGGS_BACKPACK_RSP = 202215
MSG_GOLDENEGGS_CHAT_NTF = 202211
MSG_GOLDENEGGS_LIKE_REQ = 202218
MSG_GOLDENEGGS_LIKE_RSP = 202219
MSG_GOLDENEGGS_LOOT_REQ = 202216
MSG_GOLDENEGGS_LOOT_RSP = 202217
MSG_GOLDENEGGS_RECEIVE_REQ = 202212
MSG_GOLDENEGGS_RECEIVE_RSP = 202213
MSG_GOLDENEGGS_REDPACKET_REQ = 202221
MSG_GOLDENEGGS_REDPACKET_RSP = 202222
MSG_GOOGLE_COMMENT_SET_REQ = 202436
MSG_GOOGLE_COMMENT_SET_RSP = 202437
MSG_HALLOWEEN_PARTY_GETINFO_REQ = 202557
MSG_HALLOWEEN_PARTY_GETINFO_RSP = 202558
MSG_HALLOWEEN_PARTY_GET_REWARD_REQ = 202561
MSG_HALLOWEEN_PARTY_GET_REWARD_RSP = 202562
MSG_HALLOWEEN_PARTY_SUBMIT_REQ = 202559
MSG_HALLOWEEN_PARTY_SUBMIT_RSP = 202560
MSG_HERO_AWAKEN_NEW_REQ = 200434
MSG_HERO_AWAKEN_NEW_RSP = 200435
MSG_HERO_DEBRIS_COMPOSE_NEW_REQ = 200432
MSG_HERO_DEBRIS_COMPOSE_NEW_RSP = 200433
MSG_HERO_HONORWALL_INFO_NTF = 200442
MSG_HERO_HONORWALL_INFO_REQ = 200438
MSG_HERO_HONORWALL_INFO_RSP = 200439
MSG_HERO_HONORWALL_UPGRADE_REQ = 200440
MSG_HERO_HONORWALL_UPGRADE_RSP = 200441
MSG_HERO_SKILL_UPGRADE_NEW_REQ = 200436
MSG_HERO_SKILL_UPGRADE_NEW_RSP = 200437
MSG_HERO_UPGRADE_NEW_REQ = 200430
MSG_HERO_UPGRADE_NEW_RSP = 200431
MSG_HOSPITAL_CANCURE_SOLDIER_COUNT_REQ = 202054
MSG_HOSPITAL_CANCURE_SOLDIER_COUNT_RSP = 202055
MSG_LIKE_GET_RECORD_REQ = 202261
MSG_LIKE_GET_RECORD_RSP = 202262
MSG_LIKE_GET_ROLE_RECORD_REQ = 202263
MSG_LIKE_GET_ROLE_RECORD_RSP = 202264
MSG_LOBBY_GET_RECRUIT_POOLID_REQ = 200600
MSG_LOBBY_GET_RECRUIT_POOLID_RSP = 200601
MSG_LOBBY_GET_RECRUIT_POOLINFO_NTF = 200604
MSG_LOBBY_GET_RECRUIT_POOLINFO_REQ = 200602
MSG_LOBBY_GET_RECRUIT_POOLINFO_RSP = 200603
MSG_LOGIN_ACTOR_CREATE_QUEUE_NTF = 202541
MSG_LOGIN_NATIONAL_FLAG_REQ = 202600
MSG_LOGIN_NATIONAL_FLAG_RSP = 202601
MSG_LUCKY_DRAW_DAILYGIFT_NTF = 201307
MSG_LUCKY_DRAW_LOTTERY_REQ = 201305
MSG_LUCKY_DRAW_LOTTERY_RSP = 201306
MSG_MINIGAME_ACTIVITY_JION_REQ = 202301
MSG_MINIGAME_ACTIVITY_JION_RSP = 202302
MSG_MONSTER_COMING_GET_DATA_REQ = 202161
MSG_MONSTER_COMING_GET_DATA_RSP = 202162
MSG_MONSTER_COMING_NEW_RECEIVE_REWARD_REQ = 202166
MSG_MONSTER_COMING_NEW_RECEIVE_REWARD_RSP = 202167
MSG_MONSTER_COMING_NEW_UPDATE_NTF = 202165
MSG_MONSTER_COMING_RECEIVE_REWARD_REQ = 202163
MSG_MONSTER_COMING_RECEIVE_REWARD_RSP = 202164
MSG_MONSTER_COMING_UPDATE_NTF = 202160
MSG_MOVE_CITY_REQ = 200109
MSG_MOVE_CITY_RSP = 200110
MSG_NATIONAL_FLAG_CHANGE_REQ = 202602
MSG_NATIONAL_FLAG_CHANGE_RSP = 202603
MSG_NEWPALYER_FREEMOVE_NTF = 200952
MSG_NEWPALYER_FREEMOVE_REQ = 200953
MSG_NEWPALYER_FREEMOVE_RSP = 200954
MSG_NEWPLAYERACT_GETRESOURCE_REQ = 201104
MSG_NEWPLAYERACT_GETRESOURCE_RSP = 201105
MSG_NEWPLAYERACT_RESCUEHOSTAGE_REQ = 201100
MSG_NEWPLAYERACT_RESCUEHOSTAGE_RSP = 201101
MSG_NEWPLAYERACT_RESOURCEINFO_REQ = 201102
MSG_NEWPLAYERACT_RESOURCEINFO_RSP = 201103
MSG_NEW_ARENA_CHANGE_ARENA_REQ = 202522
MSG_NEW_ARENA_CHANGE_ARENA_RSP = 202523
MSG_NEW_ARENA_ENTER_ARENA_NTF = 202524
MSG_NEW_ARENA_ENTER_BATTLE_REQ = 202518
MSG_NEW_ARENA_ENTER_BATTLE_RSP = 202519
MSG_NEW_ARENA_GET_BATTLE_RECORDS_REQ = 202520
MSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP = 202521
MSG_NEW_ARENA_GET_DEFENCE_LINEUP_REQ = 202512
MSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP = 202513
MSG_NEW_ARENA_GET_RANK_INFO_REQ = 202514
MSG_NEW_ARENA_GET_RANK_INFO_RSP = 202515
MSG_NEW_ARENA_RANK_UPDATE_NTF = 202525
MSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_REQ = 202516
MSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP = 202517
MSG_NEW_ARENA_SET_DEFENCE_LINEUP_REQ = 202510
MSG_NEW_ARENA_SET_DEFENCE_LINEUP_RSP = 202511
MSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF = 200963
MSG_ONE_ATY_CHOOSE_WEEK_CARD_NTF = 202201
MSG_OPEN_MIRACLE_BOX_REQ = 201308
MSG_OPEN_MIRACLE_BOX_RSP = 201309
MSG_PAYMENT_ORDER_EVENT_TRACKING_NTF = 202145
MSG_PLAYERINFO_RANK_REQ = 201600
MSG_PLAYERINFO_RANK_RSP = 201601
MSG_PLAYERINFO_TASK_INFO_REQ = 201602
MSG_PLAYERINFO_TASK_INFO_RSP = 201603
MSG_PUSHMESSAGE_BUTTON_REQ = 202182
MSG_PUSHMESSAGE_BUTTON_RSP = 202183
MSG_PUSHMESSAGE_LOGIN_REQ = 202180
MSG_PUSHMESSAGE_LOGIN_RSP = 202181
MSG_PUSHMESSAGE_STATE_NTF = 202186
MSG_PUSHMESSAGE_STATE_REQ = 202184
MSG_PUSHMESSAGE_STATE_RSP = 202185
MSG_RADAR_ASSIST_NTF = 200478
MSG_RADAR_DESTROY_MISSION_NTF = 200468
MSG_RADAR_DO_MISSION_REQ = 200462
MSG_RADAR_DO_MISSION_RPT = 200472
MSG_RADAR_DO_MISSION_RSP = 200463
MSG_RADAR_GENERATE_NEW_MISSION_NTF = 200466
MSG_RADAR_GRATITUDE_REQ = 200476
MSG_RADAR_GRATITUDE_RSP = 200477
MSG_RADAR_INFO_NTF = 200475
MSG_RADAR_INFO_REQ = 200460
MSG_RADAR_INFO_RSP = 200461
MSG_RADAR_INFO_UPDATE_NTF = 200469
MSG_RADAR_LEVEL_MISSION_REQ = 200473
MSG_RADAR_LEVEL_MISSION_RSP = 200474
MSG_RADAR_MISSION_REQ = 200470
MSG_RADAR_MISSION_RSP = 200471
MSG_RADAR_RECEIVE_REWARD_REQ = 200464
MSG_RADAR_RECEIVE_REWARD_RSP = 200465
MSG_RADAR_UPDATE_MISSION_INFO_NTF = 200467
MSG_RECHARGE_GIFT_BUY_NTF = 201337
MSG_RECHARGE_GIFT_DATA_NTF = 201330
MSG_RECHARGE_GIFT_GET_FOLLOWUP_REQ = 201335
MSG_RECHARGE_GIFT_GET_FOLLOWUP_RSP = 201336
MSG_RECHARGE_GIFT_GET_FREE_REQ = 201331
MSG_RECHARGE_GIFT_GET_FREE_RSP = 201332
MSG_RECHARGE_GIFT_SELECT_REQ = 201333
MSG_RECHARGE_GIFT_SELECT_RSP = 201334
MSG_ROLD_NATIONAL_FLAG_NTF = 202604
MSG_ROLEPROP_LOGIN_NTF = 200422
MSG_ROLEPROP_UPDATE_NTF = 200421
MSG_SANDBOX_ACCELERATE_REQ = 200148
MSG_SANDBOX_ACCELERATE_RSP = 200149
MSG_SANDBOX_ALERT_LIST_NTF = 200792
MSG_SANDBOX_ALERT_LIST_REQ = 200790
MSG_SANDBOX_ALERT_LIST_RSP = 200791
MSG_SANDBOX_ALERT_NTF = 200144
MSG_SANDBOX_ALLIANCE_HELP_REQ = 200780
MSG_SANDBOX_ALLIANCE_HELP_RSP = 200781
MSG_SANDBOX_ALLIANCE_HELP_SUCCESS_NTF = 200787
MSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_REQ = 201095
MSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_RSP = 201096
MSG_SANDBOX_ALLIANCE_TRAIN_POS_REQ = 200794
MSG_SANDBOX_ALLIANCE_TRAIN_POS_RSP = 200795
MSG_SANDBOX_ALONE_ENTITY_DATA_REQ = 200798
MSG_SANDBOX_ALONE_ENTITY_DATA_RSP = 200799
MSG_SANDBOX_ALONE_MARCH_DATA_REQ = 200796
MSG_SANDBOX_ALONE_MARCH_DATA_RSP = 200797
MSG_SANDBOX_ATTACK_REQ = 200103
MSG_SANDBOX_ATTACK_RSP = 200104
MSG_SANDBOX_BACK_REQ = 200107
MSG_SANDBOX_BACK_RSP = 200108
MSG_SANDBOX_BASEVFX_NTF = 200501
MSG_SANDBOX_BASE_INFO_REQ = 200113
MSG_SANDBOX_BASE_INFO_RSP = 200114
MSG_SANDBOX_BATTLE_BROCAST_NTF = 200793
MSG_SANDBOX_BATTLE_RESULT_NTF = 200500
MSG_SANDBOX_CALLBACK_DETAIL_REQ = 200788
MSG_SANDBOX_CALLBACK_DETAIL_RSP = 200789
MSG_SANDBOX_CANCLE_DETECT_REQ = 201800
MSG_SANDBOX_CANCLE_DETECT_RSP = 201801
MSG_SANDBOX_CARRIAGEPOS_REQ = 200784
MSG_SANDBOX_CARRIAGEPOS_RSP = 200785
MSG_SANDBOX_CROSS_BACK_POS_REQ = 200507
MSG_SANDBOX_CROSS_BACK_POS_RSP = 200508
MSG_SANDBOX_DATA_NTF = 200117
MSG_SANDBOX_DEFFAILD_NTF = 200502
MSG_SANDBOX_DETECT_REQ = 200145
MSG_SANDBOX_DETECT_RSP = 200136
MSG_SANDBOX_ENTER_REQ = 200115
MSG_SANDBOX_ENTER_RSP = 200116
MSG_SANDBOX_EXIT_REQ = 200130
MSG_SANDBOX_EXIT_RSP = 200131
MSG_SANDBOX_EXPEDITION_DETAIL_REQ = 200128
MSG_SANDBOX_EXPEDITION_DETAIL_RSP = 200129
MSG_SANDBOX_FAKE_MARCH_NTF = 200786
MSG_SANDBOX_GAME_BATTLE_REQ = 200782
MSG_SANDBOX_GAME_BATTLE_RSP = 200783
MSG_SANDBOX_GETBOX_NTF = 202170
MSG_SANDBOX_GET_DETAIL_REQ = 200101
MSG_SANDBOX_GET_DETAIL_RSP = 200102
MSG_SANDBOX_GET_KASTENBOX_INFO_REQ = 200776
MSG_SANDBOX_GET_KASTENBOX_INFO_RSP = 200777
MSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_REQ = 200770
MSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_RSP = 200771
MSG_SANDBOX_GET_LOOT_REQ = 200126
MSG_SANDBOX_GET_LOOT_RSP = 200127
MSG_SANDBOX_IS_NORMAL_REQ = 201802
MSG_SANDBOX_IS_NORMAL_RSP = 201803
MSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_NTF = 200775
MSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_REQ = 200773
MSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_RSP = 200774
MSG_SANDBOX_KASTENBOX_UPDATE_PERSON_INFO_NTF = 200772
MSG_SANDBOX_KILLSOLDIER_NTF = 200509
MSG_SANDBOX_LOOT_DATA_NTF = 200124
MSG_SANDBOX_LOOT_INFO_REQ = 200120
MSG_SANDBOX_LOOT_INFO_RSP = 200121
MSG_SANDBOX_MARCH_NTF = 200147
MSG_SANDBOX_MARKLIST_NTF = 200137
MSG_SANDBOX_MARK_REQ = 200134
MSG_SANDBOX_MARK_RSP = 200135
MSG_SANDBOX_MASS_CHANGE_NTF = 200307
MSG_SANDBOX_MASS_LEADER_DEL_NTF = 200308
MSG_SANDBOX_MASS_MAIN_ICON_NTF = 200306
MSG_SANDBOX_MASS_TEAM_MEMBER_REQ = 200304
MSG_SANDBOX_MASS_TEAM_MEMBER_RSP = 200305
MSG_SANDBOX_MASS_TEAM_REQ = 200302
MSG_SANDBOX_MASS_TEAM_RSP = 200303
MSG_SANDBOX_MEMBER_REQ = 200400
MSG_SANDBOX_MEMBER_RSP = 200401
MSG_SANDBOX_MONSTERFK_NTF = 200146
MSG_SANDBOX_MOVE_VIEW_REQ = 200118
MSG_SANDBOX_NC_ABANDON_REQ = 201080
MSG_SANDBOX_NC_ABANDON_RSP = 201081
MSG_SANDBOX_NC_ACTIVITY_REQ = 201085
MSG_SANDBOX_NC_ACTIVITY_RSP = 201086
MSG_SANDBOX_NC_ATTACKHERO_NTF = 201075
MSG_SANDBOX_NC_CONGRESSBACK_REQ = 201091
MSG_SANDBOX_NC_CONGRESSBACK_RSP = 201092
MSG_SANDBOX_NC_CONGRESSLIST_REQ = 201076
MSG_SANDBOX_NC_CONGRESSLIST_RSP = 201077
MSG_SANDBOX_NC_DECLAREWAR_REQ = 201070
MSG_SANDBOX_NC_DECLAREWAR_RSP = 201071
MSG_SANDBOX_NC_GETREGIOINPOS_REQ = 201093
MSG_SANDBOX_NC_GETREGIOINPOS_RSP = 201094
MSG_SANDBOX_NC_GETREWARD_REQ = 201078
MSG_SANDBOX_NC_GETREWARD_RSP = 201079
MSG_SANDBOX_NC_LIST_REQ = 201083
MSG_SANDBOX_NC_LIST_RSP = 201084
MSG_SANDBOX_NC_OFFLINEATTACK_NTF = 201074
MSG_SANDBOX_NC_OFFLINEATTACK_REQ = 201072
MSG_SANDBOX_NC_OFFLINEATTACK_RSP = 201073
MSG_SANDBOX_NC_POPUPS_NTF = 201082
MSG_SANDBOX_NC_REWARDINFO_REQ = 201087
MSG_SANDBOX_NC_REWARDINFO_RSP = 201088
MSG_SANDBOX_NC_TEAMBATTLE_REQ = 201089
MSG_SANDBOX_NC_TEAMBATTLE_RSP = 201090
MSG_SANDBOX_REINFORCEBACK_REQ = 200142
MSG_SANDBOX_REINFORCEBACK_RSP = 200143
MSG_SANDBOX_REINFORCELIST_REQ = 200138
MSG_SANDBOX_REINFORCELIST_RSP = 200139
MSG_SANDBOX_REINFORCE_REQ = 200140
MSG_SANDBOX_REINFORCE_RSP = 200141
MSG_SANDBOX_RESOURCE_EXPLORATE_REQ = 200778
MSG_SANDBOX_RESOURCE_EXPLORATE_RSP = 200779
MSG_SANDBOX_ROLECITY_NTF = 200506
MSG_SANDBOX_SAVE_TEAM_REQ = 200122
MSG_SANDBOX_SAVE_TEAM_RSP = 200123
MSG_SANDBOX_SEARCH_REQ = 200132
MSG_SANDBOX_SEARCH_RSP = 200133
MSG_SANDBOX_SIEGECAMP_REINFORCELIST_REQ = 201097
MSG_SANDBOX_SIEGECAMP_REINFORCELIST_RSP = 201098
MSG_SANDBOX_SPECIAL_EFFECT_NTF = 202434
MSG_SANDBOX_TEAMINFO_NTF = 200125
MSG_SANDBOX_UPVOTE_NTF = 200503
MSG_SANDBOX_VISUAL_ENTER_REQ = 200504
MSG_SANDBOX_VISUAL_ENTER_RSP = 200505
MSG_SANDBOX_WONDERPOS_REQ = 200105
MSG_SANDBOX_WONDERPOS_RSP = 200106
MSG_SANDBOX__MASS_OPER_REQ = 200300
MSG_SANDBOX__MASS_OPER_RSP = 200301
MSG_SCHEDULE_LIST_REQ = 202131
MSG_SCHEDULE_LIST_RSP = 202132
MSG_SCIENTIFICRESEARCH_BUILDUPDATE_NTF = 200156
MSG_SCIENTIFICRESEARCH_DORESEARCH_REQ = 200152
MSG_SCIENTIFICRESEARCH_DORESEARCH_RSP = 200153
MSG_SCIENTIFICRESEARCH_GETRESEARCH_REQ = 200154
MSG_SCIENTIFICRESEARCH_GETRESEARCH_RSP = 200155
MSG_SCIENTIFICRESEARCH_OPENBUILD_REQ = 200150
MSG_SCIENTIFICRESEARCH_OPENBUILD_RSP = 200151
MSG_SCIENTIFICRESEARCH_SCIENTIFICUPDATE_NTF = 200157
MSG_SEARCH_PLAYER_REQ = 201458
MSG_SEARCH_PLAYER_RSP = 201459
MSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ = 201715
MSG_SET_HOOKLEVELCHALLENGE_RESULT_RSP = 201716
MSG_SEVENDAY_LOGIN_RECEIVEAWARD_REQ = 202251
MSG_SEVENDAY_LOGIN_RECEIVEAWARD_RSP = 202252
MSG_SKY_FALL_DOOMSDAY_NTF = 202154
MSG_SKY_FALL_TASK_PRO_NTF = 202153
MSG_SKY_FALL_THREAT_MONSTER_REQ = 202151
MSG_SKY_FALL_THREAT_MONSTER_RSP = 202152
MSG_SLOT_MACHINE_DRAW_REQ = 202551
MSG_SLOT_MACHINE_DRAW_RSP = 202552
MSG_SPEEDUPONECLICKUSEDIAMOND_REQ = 200176
MSG_SPEEDUPONECLICKUSEDIAMOND_RSP = 200177
MSG_SPEEDUPONECLICKUSEITEM_REQ = 200174
MSG_SPEEDUPONECLICKUSEITEM_RSP = 200175
MSG_SPEEDUPUSEDIAMOND_REQ = 200172
MSG_SPEEDUPUSEDIAMOND_RSP = 200173
MSG_SPEEDUPUSEITEM_REQ = 200170
MSG_SPEEDUPUSEITEM_RSP = 200171
MSG_STAMINA_OPER_REQ = 200111
MSG_STAMINA_OPER_RSP = 200112
MSG_STRAY_DOG_GETINFO_REQ = 202531
MSG_STRAY_DOG_GETINFO_RSP = 202532
MSG_STRAY_DOG_RECEIVEAWARD_REQ = 202533
MSG_STRAY_DOG_RECEIVEAWARD_RSP = 202534
MSG_STRONGEST_COMMANDER_NTF = 201327
MSG_STRONGEST_COMMANDER_REQ = 201325
MSG_STRONGEST_COMMANDER_RSP = 201326
MSG_VIOLENT_ZOMBIE_DATA_REQ = 202621
MSG_VIOLENT_ZOMBIE_DATA_RSP = 202622
MSG_VIP_GETEXP_EVERDAY_REQ = 201050
MSG_VIP_GETEXP_EVERDAY_RSP = 201051
MSG_VIP_GETGIFT_EVERDAY_REQ = 201052
MSG_VIP_GETGIFT_EVERDAY_RSP = 201053
MSG_VIP_GETLIMITGIFTDETAIL_REQ = 201054
MSG_VIP_GETLIMITGIFTDETAIL_RSP = 201055
MSG_ZOMBIEAPOCALYPSE_ACTIVITY_DATA_NTF = 202350
MSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_REQ = 202359
MSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_RSP = 202360
MSG_ZOMBIEAPOCALYPSE_CHAT_CHANGE_NTF = 202361
MSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_REQ = 202351
MSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_RSP = 202352
MSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_REQ = 202355
MSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_RSP = 202356
MSG_ZOMBIEAPOCALYPSE_JUMP_REQ = 202365
MSG_ZOMBIEAPOCALYPSE_JUMP_RSP = 202366
MSG_ZOMBIEAPOCALYPSE_POISONEDINFO_NTF = 202364
MSG_ZOMBIEAPOCALYPSE_POISONEDINFO_REQ = 202362
MSG_ZOMBIEAPOCALYPSE_POISONEDINFO_RSP = 202363
MSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_REQ = 202357
MSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_RSP = 202358
MSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_REQ = 202353
MSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_RSP = 202354
MSG_ZOMBIE_COMING_ACT_INFO_REQ = 201040
MSG_ZOMBIE_COMING_ACT_INFO_RSP = 201041
MSG_ZOMBIE_COMING_LEADER_NTF = 201042
MSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF = 201760
MSG_ZONEBATTLEDUEL_GETINFO_REQ = 201750
MSG_ZONEBATTLEDUEL_GETINFO_RSP = 201751
MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_REQ = 201764
MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP = 201765
MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_REQ = 201768
MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP = 201769
MSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_REQ = 201758
MSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP = 201759
MSG_ZONEBATTLEDUEL_GET_ROUNDSCORE_REWARD_REQ = 201754
MSG_ZONEBATTLEDUEL_GET_ROUNDSCORE_REWARD_RSP = 201755
MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_REQ = 201752
MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP = 201753
MSG_ZONEBATTLEDUEL_GET_VSINFO_REQ = 201756
MSG_ZONEBATTLEDUEL_GET_VSINFO_RSP = 201757
MSG_ZONEBATTLEDUEL_NOMATCH_REWARD_REQ = 201762
MSG_ZONEBATTLEDUEL_NOMATCH_REWARD_RSP = 201763
MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_REQ = 201766
MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP = 201767
MSG_ZONE_BATTLE_DUEL_CHANGE_NTF = 201761

