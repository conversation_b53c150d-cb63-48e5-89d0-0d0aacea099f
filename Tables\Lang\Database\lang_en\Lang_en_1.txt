return {
[0]='',
[1]='Confirm',
[2]='Cancel',
[3]='Gold',
[4]='Diamond',
[5]=[[Action in cooldown]],
[6]='No.',
[7]=[[Tap Return again to leave the game.]],
[8]=[[This feature has now been launched. In order to embark on this expedition, ensure that you have updated the game to the latest version from the app store.]],
[9]=[[Go to download]],
[29]=[[This feature is currently unavailable in this server.]],
[30]=[[Disconnected from the network. Attempting to reconnect...]],
[31]=[[Failed to connect to the battle server. Return to the Hall and try again.]],
[32]=[[Network connection error. Please log in again.]],
[33]=[[Failed to create a character. Please log in again.
Invalid code/serial number.]],
[34]=[[Tip: Teamwork is the key to victory. Don't get too carried away by personal heroics!]],
[35]=[[Login Error]],
[36]=[[Error Type]],
[37]=[[Error Code]],
[38]='Tips',
[39]=[[Disconnected from the server.]],
[40]=[[Return to battlefield request timed out.]],
[41]=[[The match isn't over yet. Are you sure you want to join again?]],
[42]=[[Loading game, stand by...]],
[43]=[[Loading, stand by...]],
[49]='Uncompleted',
[50]='Claimed',
[51]='Change',
[52]='Reset',
[53]='Cost',
[54]=[[User Agreement]],
[55]='Accept',
[56]='Reject',
[57]='Confirm',
[58]=[[Based on your time zone, the server will open at %d-%d-%d %d:%02d!]],
[59]=[[You can't start the game without accepting the User Agreement. Are you sure you want to reject it?]],
[60]=[[Server connection timed out!]],
[61]=[[You were disconnected from the server. Return to the Hall and try again.]],
[62]='Loading...',
[63]=[[Enter the backend authorization code.]],
[64]=[[Tap to enter the authorization code ID.]],
[65]='Submit',
[66]=[[Server Opening Time]],
[67]=[[Server Maintenance]],
[68]=[[The server is under routine maintenance.]],
[69]=[[Requires Player Lv. %d]],
[70]='Day(s)',
[71]='Time(s)',
[72]=[[Tap any blank space to close.]],
[73]=[[Requires VIP Lv. %d]],
[74]='or',
[75]=[[Insufficient diamonds]],
[76]=[[This name is in violation of the rules. Try another one.]],
[77]=[[This name is in violation of the rules. Modify it and try again.]],
[78]=[[Obtain Item]],
[79]='Claim',
[80]=[[Tap any blank space to continue.]],
[81]=[[Insufficient gold]],
[82]='Level',
[83]='EXP',
[84]=[[Purchase Limit:]],
[85]=[[Reward Limit]],
[86]='Victory',
[87]='Defeat',
[88]=[[%s year(s)]],
[89]=[[%s month(s)]],
[90]=[[%s day(s)]],
[91]=[[%s hour(s)]],
[92]=[[%s minute(s)]],
[93]=[[%s second(s)]],
[94]=[[%s ago]],
[95]=[[%s later]],
[96]='Clear',
[97]='%s/day',
[98]=[[Network unavailable. Check your network connection and try again!]],
[99]=[[Unstable connection. The battle has been skipped.]],
[100]=[[Monster Buff]],
[101]=[[Friend Request]],
[102]='Invite',
[103]='Accept',
[104]='Reject',
[105]='Add',
[106]='Refresh',
[107]='Friends',
[108]=[[Request List]],
[109]='Delete',
[110]='Complete',
[111]=[[All Players]],
[112]='Online',
[113]='Offline',
[114]=[[In Game...]],
[115]=[[Not found.]],
[116]=[[Remove Friend]],
[117]='Tip',
[118]=[[Are you sure you want to delete your friend: %s?]],
[119]=[[Are you sure you want to add %s as your friend?]],
[120]='Send',
[121]=[[Enter a name to search.]],
[122]='Search',
[123]=[[Tap to enter.]],
[124]=[[Team Invite]],
[125]=[[Failed to send a team invite!]],
[126]=[[%s rejected your invite!]],
[127]=[[ invited you to join their team.]],
[128]=[[Friend added.]],
[129]=[[ accepted your request!]],
[130]=[[ rejected your request!]],
[131]=[[Friend removed.]],
[132]=[[%s has removed you from their friend list!]],
[133]=[[Enter the verification info (cannot exceed 17 characters).]],
[134]=[[Friend request sent to %s!]],
[135]=[[Team invite sent!]],
[136]=[[Early Morning]],
[137]='Morning',
[138]='Noon',
[139]='Afternoon',
[140]='Evening',
[141]=[[Failed to reply.]],
[142]=[[Failed to get chat data.]],
[143]=[[Cannot chat as this player is offline.]],
[144]=[[Your friend request was accepted!]],
[145]=[[Your friend request was rejected!]],
[146]=[[Teaming up...]],
[147]='Invited',
[148]='Recent',
[149]=[[Too many operations attempted.]],
[150]=[[Player not found!]],
[151]=[[The length cannot exceed 15 characters!]],
[152]=[[Deletion Prompt]],
[153]=[[Add Friends]],
[154]='Nearby',
[156]=[[Sort by]],
[157]='Status',
[158]='Name',
[159]='Friends:',
[160]=[[Manage Friends]],
[161]=[[Claim All and Send]],
[162]=[[Daily Limit:]],
[163]=[[Player Info]],
[164]='Send',
[165]='Add',
[166]=[[Remove Friend]],
[167]='Block',
[168]='Unblock',
[169]=[[Sent successfully!]],
[170]=[[Claim successful!]],
[171]=[[Claim All and Send successful!]],
[172]=[[Friend Request]],
[173]='Requests:',
[174]=[[Accept All]],
[175]=[[Ignore All]],
[176]='Apply',
[177]='Sent',
[178]='Friends:',
[179]=[[Enter number.]],
[180]=[[Block List]],
[181]=[[Blocked Players:]],
[182]='Remove',
[183]=[[Failed to send.]],
[184]=[[Failed to claim]],
[185]=[[Are you sure you want to block this player? You will no longer receive chat messages from them.]],
[186]=[[You have no friends yet. Go add some!]],
[187]=[[You have not blocked any players yet.]],
[188]=[[No search results. Try again later]],
[189]=[[You haven't received any friend requests!]],
[190]=[[You have no Hearts to send or claim.]],
[191]=[[You can collect up to %s Hearts.]],
[192]=[[Friend added.]],
[193]=[[The player's friend list is full.]],
[194]=[[Limit reached. Cannot send more Hearts today.]],
[195]=[[Limit reached. Cannot claim more Hearts today.]],
[196]='Kick',
[197]='Applied',
[198]=[[Are you sure you want to remove this friend?]],
[199]=[[Your friend list is full.]],
[200]=[[More Actions]],
[201]=[[Claim More]],
[202]=[[Premium Pass]],
[203]=[[Hero Badges can be obtained from Daily Quests and Weekly Quests.]],
[204]=[[Hero Pass]],
[205]=[[Hero Pass Help]],
[206]=[[Total rewards claimable during the event]],
[207]=[[Purchase now to claim rewards]],
[208]='Unlocked',
[209]=[[Time Remaining:]],
[221]=[[VIP Perks]],
[222]='More',
[223]=[[Recharge Now]],
[224]='Rewards',
[225]='Claim',
[226]=[[Deluxe Monthly Card Rewards (Purchase it to claim bonus rewards everyday)]],
[227]='Activate',
[228]='Perks',
[229]='Claimed',
[230]=[[%s may reach %s]],
[231]=[[Issued by mail each day]],
[232]=[[Accumulate %s VIP EXP during the event]],
[233]=[[Completed (%d/%d)]],
[234]=[[Claim Rewards]],
[235]=[[VIP EXP gained (%d/%d)]],
[236]=[[Activate Now]],
[237]=[[Adventure Quick AFK Reward Purchase Limit: %s]],
[238]=[[Hero Capacity: %s]],
[239]=[[AFK Gold Bonus, Player EXP, and Hero EXP bonus: %s]],
[240]=[[Acorn Tavern Daily Quests: %s]],
[241]=[[Proving Grounds Sweep Purchases: %s]],
[242]=[[Daily Arena Ticket Purchases: %s]],
[243]=[[AFK Reward Bonus Time Limit: %s]],
[244]=[[Gold Finger Daily Purchases: %s]],
[245]=[[Skip Battle unlocked!]],
[246]=[[Unlock [Quick Enhance] ]],
[247]=[[Garden Slot Speed Up unlocked!]],
[248]=[[Gold Finger Bonus: %s]],
[249]=[[Go to Alchemy?]],
[250]='Heroes',
[251]='Codex',
[252]='Item',
[253]='Gear',
[254]='Shard',
[255]='Daily',
[256]='Weekly',
[257]='Achievements',
[258]='Monthly',
[260]=[[Common Pass]],
[261]=[[Premium Pass]],
[262]='Claim',
[263]=[[Premium Pass]],
[264]=[[Pay <color=#ECDC54FF>%s</color> to unlock now.]],
[265]=[[Premium Pass Reward]],
[266]=[[Common Reward]],
[267]=[[Claim All]],
[268]=[[Unlock and claim all]],
[269]=[[Claim More]],
[270]=[[Bind Account]],
[271]=[[Bind your social media account for greater convenience and safety!]],
[272]=[[Bind Account]],
[273]=[[Choose one of the following ways to bind]],
[274]=[[Update Rewards]],
[275]=[[The old Hero Clash package will stop updating. For a better gaming experience, we recommend that you download the latest package for a more smooth gaming experience! You have been issued a wealth of rewards! If the tutorial pops up when you enter the game for the first time using the new package, make sure to complete it, then log in with your account to claim the rewards. Your account will not be lost.]],
[276]=[[Download Now]],
[277]=[[Update Rewards]],
[278]=[[Congratulations on the update and welcome back to Hero Clash! You have obtained the following rewards! Feel free to delete the old package, your progress will not be affected.]],
[279]=[[Claim Rewards]],
[280]=[[Cannot add more lineups to your Favorites.]],
[281]=[[You can select and activate talent skills from the Talent interface.]],
[282]='Tutorial',
[283]=[[Hero Traits:]],
[284]=[[Recom Training:]],
[285]=[[Recom Talent:]],
[286]=[[Recom Artifact:]],
[287]=[[Recom Lineup:]],
[288]=[[Hot Lineups]],
[289]='<color=#e18f01>Favorite</color>',
[290]='<color=#e18f01>Added</color>',
[291]=[[Added by %s player(s)]],
[292]='Reference',
[293]='Favorites',
[294]='Use',
[295]='Update',
[296]=[[Update Lineup]],
[297]=[[If you choose not to update, the original lineup will be retained.]],
[298]='Favorites',
[299]=[[There are no recommended lineups for this hero. Refer to hot lineups.]],
[300]=[[Recommended Lineup]],
[301]='Gender',
[302]='Nickname',
[303]=[[<color=red>Create</color> Character]],
[304]=[[Enter a character name.]],
[305]=[[Core Hero:]],
[306]=[[Lineup Strength:]],
[307]='Reason:',
[308]=[[This store is not available yet.]],
[309]=[[No lineup in Favorites.]],
[310]=[[Your bag is full. Reach <color=#DF2D52>VIP %s</color> to increase bag capacity by <color=#DF2D52>%s</color>.]],
[311]=[[EXP needed to reach <color=#DF2D52>VIP %s</color>: <color=#DF2D52>%s</color>]],
[312]=[[All sweeps have been used up for today. Reach <color=#DF2D52>VIP%s</color> to increase daily sweeps by <color=#DF2D52>%s</color>.]],
[313]=[[All Quick AFK charges have been used up for today. Reach <color=#DF2D52>VIP%s</color> to increase daily Quick AFK charges by <color=#DF2D52>%s</color>.]],
[314]=[[Reach <color=#DF2D52>VIP%s</color> to unlock this function.]],
[315]=[[Open <color=#ffea37>%s</color> or unlock <color=#ffea37>VIP%s</color> to skip.]],
[316]=[[Opens on turn <color=#ffea37>%s</color> or unlock <color=#ffea37>VIP%s</color> to skip.]],
[317]=[[Quick Enhance (Available at VIP %s)]],
[318]='Tip',
[319]=[[Go to Shop]],
[320]=[[Sweep List]],
[321]='Sweep',
[322]=[[Free Sweep]],
[323]=[[Free AFK]],
[324]=[[Remaining Charges: <color=#4D9B63FF>%s</color>/%s]],
[325]=[[Daily VIP EXP Rewards]],
[326]=[[VIP EXP Today:]],
[327]=[[Consecutive Logins:]],
[328]=[[VIP EXP Tomorrow:]],
[329]='Claim',
[330]=[[Come back tomorrow.]],
[331]=[[The more consecutive logins, the greater the VIP EXP received.]],
[332]=[[(VIP +%s%%)]],
[333]=[[(Total Bonus: %s)]],
[334]=[[%s day(s)]],
[335]=[[%s hour(s)]],
[336]=[[%s day(s)]],
[337]='Day(s)',
[338]=[[Your Personal Customer Service]],
[339]=[[Recharge $<color=#fe803d>%s</color> more to unlock your personal customer service.]],
[340]=[[Don't remind me again]],
[341]=[[Save Image]],
[342]='Copy',
[343]=[[Image saved.]],
[344]='Copied',
[345]=[[The guides come from data collected by players in the game and system recommendations. They are for reference only.]],
[346]=[[Clear Adventure <color=#df2d52>%s</color>
or reach <color=#df2d52>VIP%s</color> to unlock this function.]],
[400]=[[Downloading additional resources...]],
[401]='Downloading:',
[402]=[[Downloading with a WiFi network is recommended.]],
[403]=[[Current network environment:]],
[404]='WiFi',
[405]='Non-WiFi',
[406]=[[Resource download completed]],
[407]=[[Restart Now]],
[408]=[[Restart Later]],
[409]=[[New resources have been downloaded. Restart the game and enjoy!]],
[500]=[[Dear Lord, we are sorry that you can't log in to the game. Please try the following solutions:
(1) Restart the game (Recommended)
(2) Check your network connection and reconnect if unstable.]],
[501]=[[If you notice any game issues or have any suggestions, tap the floating action button to contact customer support. Thank you very much for your help!]],
[502]=[[The system is busy. Tap the floating action button to contact customer support.]],
[605]=[[Enter a character name.]],
[606]=[[Create Character]],
[614]=[[The system is busy. Try again later!]],
[615]=[[This character already exists!]],
[616]=[[Names cannot contain any special symbols.]],
[618]=[[Login error!]],
[619]=[[Do not send multiple creation requests!]],
[640]=[[Sign in with Apple]],
[651]='Settings',
[652]='Announcements',
[653]='Server',
[654]='Help',
[655]=[[Sound Effect Settings]],
[656]='SFX:',
[657]='Music:',
[658]='Language:',
[659]=[[Detail Settings]],
[660]=[[Basic Info]],
[661]=[[Back to Hall]],
[663]=[[Log Out]],
[664]=[[Switch Account]],
[665]=[[Bind Account]],
[667]=[[Language Settings]],
[668]=[[Bingchuan Account]],
[669]='Facebook',
[670]='Google',
[671]=[[Real-name authentication completed.]],
[672]=[[Real-name authentication successful.]],
[673]=[[Real-name authentication failed.]],
[674]='Low',
[675]='High',
[676]=[[Are you sure you want to switch your account?]],
[677]=[[Are you sure you want to switch the server?]],
[689]=[[Master Volume]],
[690]='Volume',
[691]=[[Sound Effects]],
[692]='Interface',
[693]='BGM',
[694]=[[Automatically join voice chat (team mode only)]],
[695]='All',
[696]=[[Team Only]],
[697]=[[My Receiver]],
[698]=[[My Microphone]],
[699]=[[Are you sure you want to log out of this account?]],
[732]='English',
[734]='简体中文',
[737]=[[Language changed. You must log in to the game again.]],
[738]=[[Change Language]],
[742]=[[Are you sure you want to quit this match?]],
[770]='繁體中文',
[771]='Indonesia',
[772]='Filipino',
[773]=[[Tiếng Việt]],
[774]='ภาษาไทย',
[781]='Portugues',
[782]='한국',
[783]='FAQ',
[784]='Help',
[785]='Feedback',
[786]=[[Enter your feedback here...]],
[787]=[[Cannot be empty!]],
[788]=[[Feedback received!]],
[789]=[[Feedback failed as you have attempted too many actions!]],
[790]='Twitter',
[791]='Discord',
[792]='Gallery',
[793]='Reddit',
[794]='Community',
[795]='Forum',
[796]='français',
[797]='Deutsch',
[798]='Malay',
[799]=[[Tap twice to exit the game.]],
[800]=[[You are currently logged in to a Guest Account. To secure your game data, we suggest binding your account.]],
[801]='Switch',
[802]=[[Select Server]],
[803]=[[Switch Server]],
[804]=[[Recommended Servers]],
[805]=[[All Servers]],
[806]=[[Русский язык]],
[807]='Español',
[808]=[[Complete real-name authentication and continue]],
[809]=[[Forerunner Server]],
[810]=[[Switch Server]],
[811]=[[Start Adventure]],
[812]='Server',
[813]=[[You already have a character.]],
[814]='Recommended',
[815]=[[Forerunner Server]],
[816]=[[Official Server]],
[817]='Smooth',
[818]=[[Under Maintenance]],
[819]='हिंदी',
[820]='한국어',
[821]='日本語',
[822]='Türkçe',
[823]='Italiano',
[824]='Polski',
[825]='عربي',
[826]='Nederlands',
[850]=[[Naver Game]],
[851]=[[Naver Cafe]],
[852]=[[Server %d–%d]],
[900]='Yesterday',
[901]=[[2 days ago]],
[902]=[[Not available yet.]],
[903]=[[Switch Channel]],
[904]=[[Language Channel]],
[905]=[[You are already in this channel.]],
[906]=[[Current Server]],
[907]=[[Private Chat]],
[908]=[[You have no private chats.]],
[909]=[[Tap a traveler's avatar to initiate a private chat.]],
[910]=[[Chinese Simplified - Channel %s]],
[911]='Translating...',
[912]='Translated',
[913]=[[Get %s consecutive likes]],
[914]='Full',
[915]='Crowded',
[916]='Smooth',
[917]=[[Failed to switch channel.]],
[918]=[[You've switched to a new channel.]],
[919]=[[This channel is full.]],
[920]='Sunday',
[921]='Monday',
[922]='Tuesday',
[923]='Wednesday',
[924]='Thursday',
[925]='Friday',
[926]='Saturday',
[927]='Like',
[928]=[[Parameters not initialized.]],
[929]=[[Channel assignment failed.]],
[930]=[[Chat data request failed.]],
[931]=[[Failed to get private chat data.]],
[932]=[[Channel configuration failed.]],
[933]=[[Someone @'d you]],
[934]=[[@ you]],
[935]=[[Choose the player you need to @]],
[936]='Search',
[937]=[[Chat closed. Submit your feedback and open another chat.]],
[938]=[[Official Customer Service]],
[939]=[[We've got plenty of talent here... love it!]],
[940]=[[I'm new to this game. Someone please help me level up.]],
[941]=[[Greetings, everyone!]],
[942]=[[The snow is falling and the gale is blowing [1&ej_4] ]],
[943]=[[It's been a while!]],
[944]=[[What fair weather!]],
[945]=[[European dog, please possess me!]],
[946]=[[I'm not an African.]],
[947]=[[Not bad.]],
[948]='Hey.',
[949]=[[What is it?]],
[950]=[[What's going on?]],
[951]=[[What's going on, man?]],
[952]=[[How have you been lately?]],
[953]=[[Goddess of Luck, please grant me white Sacred Gear!]],
[954]=[[What happened?]],
[955]='Haha',
[956]='[1&ej_8]',
[957]='[2&ej_4][2&ej_4]',
[958]='[2&ej_12][2&ej_12][2&ej_12][2&ej_12]',
[959]='[2&ej_8][2&ej_8][2&ej_8]',
[960]='[2&ej_16][2&ej_16]',
[961]='[2&ej_44][2&ej_40]',
[962]='[1&ej_56][1&ej_56]',
[963]='Hello',
[964]='Yeah',
[965]='[1&ej_28][1&ej_28][1&ej_28]',
[966]=[[Seems good [1&ej_28][1&ej_28][1&ej_28] ]],
[967]='[1&ej_0][1&ej_8]',
[968]='[1&ej_16][1&ej_16][1&ej_16]',
[969]=[[Customer Service Message]],
[970]='Auto-Translate',
[971]=[[Be on guard if you receive messages about trading accounts, power leveling, boosting services, selling in-game items, or exchanging gift codes. Offline transactions through unofficial channels are never secure. Make sure you don't trust others blindly and to not fall victim to fraud.]],
[972]='Monday',
[973]='Tuesday',
[974]='Wednesday',
[975]='Thursday',
[976]='Friday',
[977]='Saturday',
[978]='Sunday',
[979]='Jan',
[980]='Feb',
[981]='Mar',
[982]='Apr',
[983]='May',
[984]='Jun',
[985]='Jul',
[986]='Aug',
[987]='Sep',
[988]='Oct',
[989]='Nov',
[990]='Dec',
[1001]=[[This hero is already deployed in the %d team.]],
[1002]=[[No pet equipped.]],
[1003]=[[This hero is already deployed in the %d team.]],
[1004]=[[This hero is already deployed in the team.]],
[1005]=[[Deploy at least 1 hero to each team.]],
[1006]=[[You must configure a Defense Lineup.]],
[1007]='Next',
[1008]=[[Save Lineup]],
[1009]=[[Team Configuration]],
[1010]=[[Total Score]],
[1011]=[[Match %s]],
[1012]=[[Current Score]],
[1013]=[[Next Match]],
[1014]=[[There are duplicate heroes in the lineup.]],
[1102]=[[Dimensional War]],
[1200]='　',
[1301]=[[Codex Overview]],
[1302]=[[Not owned]],
[1303]=[[Can be activated]],
[1304]='Upgrade',
[1305]=[[Codex Stats Overview]],
[1306]=[[1. Obtain a hero and promote them to Excellent to activate the corresponding hero codex.
2. The hero codex level can be upgraded after awakening the hero to a certain star level.
3. The higher the codex level, the greater the stat bonuses.
4. Stat bonuses from the hero codex apply to all deployed heroes.]],
[1307]=[[Codex Stat Bonus Preview]],
[1308]=[[Upgrade %s to %s to unlock the following stats]],
[1309]=[[1. Clear %s to activate the codex stat bonuses.
2. The codex stat bonuses can only be activated after obtaining an Excellent and above hero.
3. The higher the codex level, the greater the stat bonuses.
4. Stat bonuses from the hero codex apply to all deployed heroes.]],
[1310]=[[Codex Stats]],
[1311]=[[Activate Codex]],
[1312]=[[Upgrade Codex]],
[1313]=[[Own %s %s to activate.]],
[1314]=[[Enhance %s to %s to upgrade.]],
[1315]=[[Max star level reached.]],
[1316]=[[Codex stats apply to all deployed heroes.]],
[1317]=[[Current Grade Effect]],
[1318]=[[Codex Stat Bonus %s unlocked.]],
[1319]=[[Codex Bonus]],
[1320]=[[Codex Preview]],
[1321]=[[Codex Lv. %d]],
[1322]=[[Codex not activated.]],
[1323]=[[Hero Codex upgrade available.]],
[1324]=[[Hero Codex upgrade available.]],
[1325]='Upgrade',
[1326]=[[Rune feature unlocked. Head to the Heroes page to view details.]],
[1351]=[[View Details]],
[1352]=[[Total Rewards]],
[1353]=[[Tap View Details to check the reward requirements.]],
[1354]=[[Permanently Unlock Luxury Chest]],
[1355]=[[Obtain more Stars to unlock the remaining rewards.]],
[1356]=[[Purchase Luxury Chests]],
[1357]=[[Obtain powerful heroes.]],
[1400]=[[3-Star Tournament]],
[1401]=[[Clear story stages to earn Stars and Hero Shards.]],
[1402]=[[Time Remaining:]],
[1403]='Claim',
[1404]='Claimable',
[1405]='Loading...',
[1406]=[[The leaderboard has expired. Make sure to claim your rewards in time.]],
[1407]=[[The Star Tournament has begun! The more stars you earn from clearing story stages, the better your rewards! Join the event now!]],
[1408]=[[Rank Up]],
[1409]=[[Rank Down]],
[1410]=[[Rank Change]],
[1411]=[[Current Ranking:]],
[1412]=[[Total Stars:]],
[1413]=[[The 2nd <color=#F5D017FF>Star Tournament</color> begin in: %s]],
[1414]=[[Current Stage: <color=#87F425>%s</color>]],
[1415]=[[An opponent has surpassed you]],
[1416]='Opponent',
[1417]=[[Main Hero]],
[1418]='Me',
[1419]=[[Rank 1 Rewards]],
[1420]=[[Players (Current Stage)]],
[1421]='All',
[1422]='Friends',
[1423]='Union',
[1424]='Tournament',
[1425]=[[Well done! Now clear more stages!]],
[1426]=[[Tip: Increase the level of %s to defeat %s.]],
[1427]=[[Tip: Increase the grade of %s to defeat %s.]],
[1428]=[[Tip: Push ahead to surpass your opponent.]],
[1429]=[[I surpassed %s]],
[1430]=[[%s surpassed me]],
[1431]=[[Tip: Increase the grade of %s to clear this stage quicker.]],
[1432]=[[Tip: Level up Scarlet Warden to Lv. 180 to vastly boost your CP.]],
[1433]=[[Tip: Recruit %s to form a powerful lineup.]],
[1434]=[[%s‘s %s reached Lv. %s]],
[1435]=[[%s obtained %s]],
[1150]=[[Gain VIP EXP from recharges]],
[1552]='Guest',
[1553]=[[Phone Login]],
[1554]='Account',
[1555]=[[Other Logins]],
[1556]=[[Select Login Type]],
[1557]='Announcements',
[1558]=[[Login failed: %s]],
[1559]='Language',
[1560]='Agreement',
[1561]=[[Creating guest account, please stand by...]],
[1562]=[[Login failed.]],
[1563]='Facebook',
[1564]='Google',
[1565]=[[Authentication failed.]],
[1566]='Login',
[1567]='Create',
[1568]='Reset',
[1569]=[[Player Login]],
[1570]=[[Player Registration]],
[1571]=[[Change Password]],
[1572]=[[Phone Number/Email/Username]],
[1573]=[[New Password]],
[1574]=[[Account does not exist.]],
[1575]=[[Enter the full account number.]],
[1576]=[[Your password cannot be shorter than 6 characters.]],
[1577]=[[Enter the verification code.]],
[1578]=[[The verification code has been sent by email.]],
[1579]=[[The verification code has been sent via SMS.]],
[1580]=[[Only phone login is allowed.]],
[1581]='Password',
[1582]=[[Recover Password]],
[1583]='Register',
[1584]=[[Bind Guest Account]],
[1585]='Account',
[1586]=[[Give your character a nickname!]],
[1587]=[[*The nickname cannot be longer than 8 characters.]],
[1588]=[[The passwords do not match.]],
[1589]=[[You were ranked no. <color=#FB8600FF>%d</color>]],
[1590]=[[Estimated waiting time: <color=#00A330FF>%s</color>]],
[1591]=[[Welcome to the game, <color=#FBCB21FF>%s</color>!]],
[1592]=[[Enter your account/phone number.]],
[1593]=[[No account yet? <color=#FD9527FF>Register Now</color>]],
[1594]=[[Forgot Password]],
[1595]=[[Enter your password]],
[1596]=[[Logging in, please wait...]],
[1597]=[[Switch Server]],
[1598]='Facebook',
[1599]=[[Quick Registration]],
[1600]=[[Account Login]],
[1601]=[[Account Registration]],
[1602]=[[Your account number should be 4-30 characters long.]],
[1603]=[[Your password should be 6-16 characters long.]],
[1604]=[[Confirm your password.]],
[1605]=[[Enter your phone number.]],
[1606]=[[Get verification code]],
[1607]=[[Quick Start]],
[1608]=[[Personal Account Registration]],
[1609]=[[Phone Number Registration]],
[1610]=[[I have read and agree to <color=#01A15FFF>Bingchuan Pass User Service Agreement</color>]],
[1611]=[[Verification code sent to:]],
[1612]=[[Resend (%d)]],
[1613]='Next',
[1614]=[[Set Login Password]],
[1615]=[[For the safety of your account,
 make sure to bind it as quickly as possible!]],
[1616]=[[Make sure to bind it as quickly as possible!]],
[1617]=[[Recommended Servers]],
[1618]=[[All Servers]],
[1619]='Smooth',
[1620]=[[Under Maintenance]],
[1621]=[[Europe and America]],
[1622]='Global',
[1623]='China',
[1624]=[[You must accept the Agreement first.]],
[1625]=[[Invalid account.]],
[1626]='Lord',
[1627]=[[A wonderful journey awaits.]],
[1628]=[[Reset Password]],
[1629]=[[Enter your phone number/email.]],
[1630]=[[Enter a valid verification code.]],
[1631]=[[Set Password]],
[1632]=[[The password you entered was incorrect.]],
[1633]=[[Under maintenance.]],
[1634]=[[Ask me later]],
[1635]=[[Switch Account]],
[1636]=[[Verify Password]],
[1637]=[[Set a password for your account %s to complete binding.]],
[1638]=[[Verify the password of your account %s to complete binding.]],
[1639]=[[Change the password of your account %s to complete binding.
The password will not be changed if you tap “Ask me later”.]],
[1640]=[[This account has already been bound to another trial account.]],
[1641]=[[Invalid verification code.]],
[1642]=[[Cannot bind right now, try again later.]],
[1643]=[[Your game is out of date. Update to the latest version.]],
[1644]=[[Bind Trial Account]],
[1645]=[[The password you entered was incorrect.]],
[1646]=[[Invalid password format.]],
[1647]=[[You entered the wrong password too many times. Switch accounts and try again.]],
[1648]=[[The account name cannot be shorter than 4 characters.]],
[1649]=[[Server under maintenance.]],
[1650]=[[Dear Lord,
To improve the gaming experience, the game is currently under <color=#01A15FFF>maintenance</color>. During the maintenance period, you will not be able to log in to the game. Thank you for your patience!]],
[1651]=[[Dear Lord,

We have detected network abnormalities. Please check your network connection and log in again.

If you still cannot access the game, please contact customer service on Facebook.]],
[1652]='Loading...',
[1653]=[[Game Center]],
[1654]='Guest',
[1655]='Reconnect',
[1656]=[[Update Now]],
[1657]=[[Dear Lord, your version of the game is out of date. Please update to the latest version.]],
[1658]=[[Read our User Agreement and Privacy Policy, then tap Agree to continue.]],
[1659]=[[User Agreement]],
[1660]=[[Privacy Policy]],
[1661]='Accept',
[1662]='Reject',
[1663]='Feedback',
[1664]=[[Bug Report]],
[1665]=[[Dear Lord, are you experiencing problems? If you need help, please select from the following options:]],
[1666]=[[Select from the following options:]],
[1667]=[[Cannot log into the game]],
[1668]=[[It takes too long to enter the game]],
[1669]=[[Contact us on Facebook]],
[1670]=[[Dear Lord, we are sorry that you can't log in to the game. Try to check your network connection or restart the game. You can also complete and submit the troubleshooting form below. We will fix the issue ASAP.]],
[1671]='Submit',
[1672]=[[Dear Lord, thank you very much for your feedback. We apologize for any inconvenience caused and will work to solve your problem ASAP. You can also reach out to us on Facebook, where our customer service team will be happy to assist you further!]],
[1673]=[[Please describe your problem...]],
[1674]=[[Restart the game to update!]],
[1675]=[[You need to update the game first.]],
[1676]=[[After rejecting this Agreement, you will no longer be able to play the game.]],
[1677]=[[We take the protection of your personal information and privacy very seriously. To better protect your personal rights, ]],
[1678]=[[please read ]],
[1679]=[[ and ]],
[1680]=[[Dear Lord,

we have detected network abnormalities. Please check your network connection and log in again.

If you still cannot access the game, please contact customer service on Facebook.]],
[1681]=[[Customer Service]],
[1682]=[[Check if your recharge was successful. Feel free to contact customer service if you run into any problems!]],
[1683]=[[Official Facebook Page]],
[1684]=[[Follow our Official Facebook Page]],
[1685]=[[Dear Lord, we couldn't identify your IP address. Please select a server manually.]],
[1686]=[[SEA Server]],
[1687]=[[Enter Game]],
[1688]=[[Login timed out. Tap Feedback and enter the problem you encountered.]],
[1689]=[[Login timed out. Please try again!]],
[1690]=[[Logging in, please wait...]],
[1691]=[[Tap Accept to continue the game.]],
[1692]=[[Delete Account]],
[1693]=[[Tap <a href=w>Delete Account</a> to request account deletion. Once you have submitted a request, you will be logged out and will no longer have access to this account. Proceed with caution.]],
[1694]=[[Tap <a href=w>Delete Account</a> to request deletion of your current account. Once you have submitted a request, your account will be deleted after a grace period of 15 days. All your character data will be cleared. Proceed with caution.]],
[1695]=[[Resources for the new version have been downloaded. Please restart the game!]],
[1696]=[[Version: %s]],
[1697]=[[Resource Version：%s]],
[1698]='Copy',
[1699]='Copied',
[1700]=[[Latest Resource Version: %s]],
[1701]=[[Obtained through event.]],
[1702]=[[Obtained by activating heroes.]],
[1703]=[[Purchased from the Arena Store.]],
[1704]='Gear',
[1705]='Equipped',
[1706]='Unequip',
[1707]=[[Obtained from:]],
[1708]=[[Hero Avatar]],
[1709]=[[Skin Avatar]],
[1710]=[[Special Avatar]],
[1711]=[[This name is too short.]],
[1712]=[[This name is too long.]],
[1713]=[[This name contains illegal characters.]],
[1714]=[[Traveler's Union:]],
[1715]=[[You are not in a Union.]],
[1716]=[[Stat Bonus]],
[1717]=[[Switch Stats]],
[1718]='Locked',
[1719]=[[The length must be between 4–16 characters...]],
[1720]='Rename',
[1721]=[[Enter a new name.]],
[1722]='Confirm',
[1723]=[[The name is empty.]],
[1724]='Obtained',
[1725]=[[Not owned]],
[1726]='Union',
[1727]=[[Default Avatar]],
[1728]=[[HP +2%
ATK +1%]],
[1729]=[[HP +3%
ATK +2%]],
[1730]=[[HP +5%
ATK +3%]],
[1731]=[[Armor PEN +5%]],
[1732]=[[Block +3%]],
[1733]=[[Obtained by sending flowers in the Union.]],
[1734]=[[You have no Avatar Stat Bonus yet!]],
[1735]=[[Obtained through the New Server event.]],
[1736]=[[Max characters (16/16) reached.]],
[1737]=[[You have reached the character limit.]],
[1738]='Avatar',
[1739]=[[Avatar Frame]],
[1740]=[[Obtained from:]],
[1741]=[[Obtained by default.]],
[1742]=[[Not owned]],
[1743]=[[Activate Stat]],
[1744]=[[Expires in:]],
[1745]=[[%d day(s)]],
[1746]=[[No stat bonus.]],
[1747]=[[Obtained through the Thanksgiving event.]],
[1748]='d',
[1749]=[[Obtained through the Consumption Gift event.]],
[1750]=[[Obtained from the Flower Leaderboard.]],
[1751]=[[Obtained through the Christmas event.]],
[1752]=[[Obtained through the Valentine's Day event.]],
[1753]=[[Obtained through the April Fools' event.]],
[1754]=[[Obtained through the Easter event.]],
[1755]=[[Obtained through the Phoenix's Arrival event.]],
[1756]=[[Obtained through the Inferno's Arrival event.]],
[1757]=[[Obtained through the Mecha's Arrival event.]],
[1758]=[[Obtained through the Blood's Arrival event.]],
[1759]=[[Obtained through the Dragon's Arrival event.]],
[1760]=[[Action in cooldown for %s.]],
[1763]=[[Operation Event]],
[1764]=[[Operation Event]],
[1766]=[[Obtained by returning travelers.]],
[1767]=[[Returning Traveler Avatar Frame]],
[1798]=[[Days after character creation: %d (%d/%d)]],
[1799]='pass',
[1800]=[[Obtained from the National Day Pack.]],
[1801]='Home',
[1802]='Quests',
[1803]='Adventure',
[1804]='Heroes',
[1805]='Bag',
[1806]=[[Acorn Tavern]],
[1807]=[[Summoning Hall]],
[1808]=[[Giant Merchant]],
[1809]='Union',
[1810]='Casino',
[1811]=[[X-War Conversion Pod]],
[1812]=[[Awakening Spring]],
[1813]=[[Training Room]],
[1814]=[[Gear Forge]],
[1815]=[[Abyss City]],
[1816]=[[Proving Grounds]],
[1817]='Arena',
[1818]=[[World Exploration]],
[1819]=[[Faction Mastery]],
[1820]='Alchemy',
[1821]=[[Hunting Battle]],
[1822]=[[Evergreen Garden]],
[1823]=[[Main Campaign]],
[1824]='Store',
[1825]='Events',
[1826]=[[Spirit Tower]],
[1827]=[[Hero Statue]],
[1828]='Wild',
[1829]=[[7D Challenge]],
[1830]='Mail',
[1831]=[[Ancient Summon]],
[1832]=[[Battlefield Invite]],
[1850]=[[%s day(s) %s hour(s)]],
[1851]=[[%s day(s)]],
[1890]=[[<color=#ffd938>Value Fund</color>]],
[1891]=[[<color=#ffd938>Deluxe Fund</color>]],
[1892]=[[<color=#ffd938>Monthly Pack</color>]],
[1893]=[[Hero Badge rewards are reset daily.]],
[1894]='(Activated)',
[1895]='(Inactive)',
[1896]=[[<color=#37e325ff>All activated bonuses (all teammates)</color>]],
[1897]=[[<color=#ffd938>All bonuses upon activation (all teammates)</color>]],
[1898]=[[Activity Badge Chest]],
[1899]=[[Hero Badge Chest]],
[1900]=[[Traveler <color=#dd3c2dFF>Market</color>]],
[1901]='<color=#dd3c2dFF>Summon</color>',
[1902]=[[<color=#ffd938>1st Recharge Pack</color>]],
[1903]=[[<color=#ffd938>Star Chest</color>]],
[1904]=[[<color=#ffd938>Growth Pack</color>]],
[1905]=[[<color=#ffd938>Summon Pack</color>]],
[1906]=[[<color=#ffd938>Monthly Card</color>]],
[1907]=[[Lv. %d]],
[1908]='(%s/%s)',
[1909]='Purchase',
[1910]=[[[Unlock Condition:]],
[1911]=[[%s Activity Points] ]],
[1912]=[[Activity Points:]],
[1913]='Unlocked',
[1914]=[[Union Badge]],
[1915]=[[Activity Badge]],
[1916]=[[VIP Badge]],
[1917]=[[Adventure Badge]],
[1918]=[[Hero Badge]],
[1919]=[[Proving Grounds Badge]],
[1920]=[[The badge target has not been completed.]],
[1921]=[[The badge target has been completed.]],
[1922]='Details',
[1923]='Claim',
[1924]=[[Activity Badge Rewards are reset every Monday.]],
[1925]=[[Your Weekly Activity Points are too low. The President recommends that you participate in %s to increase your Activity Points.]],
[1926]=[[Your VIP Level is too low. The President recommends that you purchase %s to boost your VIP Level.]],
[1927]=[[The President recommends that you continue with your Adventure challenge and unlock %s to boost your Adventure Badge level.]],
[1928]=[[The President recommends that you select and develop %d %s heroes from below to increase your Hero Badge level.]],
[1929]=[[The President recommends that you continue with your Proving Grounds challenge and unlock Floor %d to boost your Adventure Badge level.]],
[1930]='Claim',
[1931]='Go',
[1932]=[[President Tutorial]],
[1933]='Rank',
[1934]='Nickname',
[1935]='Position',
[1936]=[[Weekly Activity Points Contribution]],
[1937]='Elder',
[1938]='President',
[1939]=[[Auto Reminder]],
[1940]=[[One-tap to remind all inactive players]],
[1941]=[[Weekly Activity Points]],
[1942]='VIP',
[1943]='Adventure',
[1944]='Heroes',
[1945]=[[Proving Grounds]],
[1946]=[[%d players have reached %d Weekly Activity Points.]],
[1947]=[[%d players have reached VIP%d.]],
[1948]=[[%d players have cleared Stage %s.]],
[1949]=[[%d players own %d %s heroes.]],
[1950]=[[%d players have reached F%d.]],
[1951]=[[Obtained <color=#37e325ff>Weekly %s*%d</color>]],
[1952]=[[Obtained <color=#37e325ff>%s*%d</color>]],
[1953]=[[Obtained <color=#37e325ff>%s*%d</color>]],
[1954]=[[Free Gold Finger Charges: %s]],
[1955]=[[Acorn Tavern Daily Quests: %s]],
[1956]=[[Proving Grounds Sweep Purchases: %s]],
[1957]=[[AFK Gold: %s]],
[1958]=[[AFK Hero EXP: %s]],
[1959]=[[AFK Rankup Stones: %s]],
[1960]=[[Rare Hero Shard: %s]],
[1961]=[[Excellent Hero Shard: %s]],
[1962]=[[Obtained <color=#37e325ff>Free Daily Gold Finger Charges +%d</color>]],
[1963]=[[Obtained <color=#37e325ff>Acorn Tavern Daily Quests +%d</color>]],
[1964]=[[Obtained <color=#37e325ff>Proving Grounds Sweep Purchases +%d</color>]],
[1965]=[[Obtained <color=#37e325ff>Daily AFK Gold +%d%%</color>]],
[1966]=[[Obtained <color=#37e325ff>Daily AFK EXP +%d%%</color>]],
[1967]=[[Obtained <color=#37e325ff>Daily AFK Rankup Stones +%d%%</color>]],
[1968]=[[Obtained <color=#37e325ff>Daily %s*%d</color>]],
[1969]=[[Obtained <color=#37e325ff>Daily %s*%d</color>]],
[1970]=[[Obtained <color=#37e325ff>Rare Hero Shards Per Sweep +%d</color>]],
[1971]=[[Obtained <color=#37e325ff>Excellent Hero Shards Per Sweep +%d</color>]],
[1972]=[[Requires %d players to reach %d Weekly Activity Points.]],
[1973]=[[Requires %d players to reach VIP%d.]],
[1974]=[[Requires %d players to clear Stage %s.]],
[1975]=[[Requires %d players to own %d %s heroes.]],
[1976]=[[Requires %d players to reach F%d.]],
[1977]=[[Activate to obtain <color=#ffd938>%s*%d</color> every week.]],
[1978]=[[Activate to obtain <color=#ffd938>%s*%d</color>.]],
[1979]=[[Activate to obtain <color=#ffd938>%s*%d</color>.]],
[1980]=[[Activate to obtain <color=#ffd938>Free Gold Finger Charges +%d</color> every day.]],
[1981]=[[Activate to obtain <color=#ffd938>Acorn Tavern Daily Quests +%d</color> every day.]],
[1982]=[[Activate to obtain <color=#ffd938>Proving Grounds Sweep Purchases +%d</color> every day.]],
[1983]=[[Activate to obtain Daily AFK Gold <color=#ffd938>+%d%%</color>.]],
[1984]=[[Activate to obtain Daily AFK EXP <color=#ffd938>+%d%%</color>.]],
[1985]=[[Obtained Daily AFK <color=#ffd938>Rankup Stones +%d%%</color>.]],
[1986]=[[Activate to obtain <color=#ffd938>%s*%d</color> every day.]],
[1987]=[[Activate to obtain <color=#ffd938>%s*%d</color>.]],
[1988]=[[Activate to obtain <color=#ffd938>Rare Hero Shards +%d</color> from each sweep.]],
[1989]=[[Activate to obtain <color=#ffd938>Excellent Hero Shard +%d</color> from each sweep.]],
[1990]=',',
[1991]=[[VIP Level]],
[1992]='Stage',
[1993]='Completed',
[1994]=[[<color=#ffd938>Hero Summon</color>]],
[1995]=[[<color=#ffd938>Faction Summon</color>]],
[1996]=[[<color=#ffd938>AFK Income</color>]],
[1997]=[[<color=#ffd938>Union Channel Help</color>]],
[1998]=[[<color=#ffd938>Quick AFK</color>]],
[1999]=[[<color=#ffd938>Tower of Fate 3F</color>]],
[2000]=[[Free Summon]],
[2001]='Confirm',
[2002]='Summon',
[2003]='10x',
[2004]=[[First Time Free]],
[2005]='Skip',
[2006]=[[Insufficient Common Scrolls. Purchase more from the Giant Merchant.]],
[2007]=[[Insufficient Hearts. Add friends and gift hearts to each other!]],
[2008]=[[The first <size=65>10x</size> Summon is guaranteed to provide an <color=#ffd800><size=65>Excellent</size></color> Hero.]],
[2011]=[[You can buy 10x Common Scrolls with 1,250 Diamonds each day. Head to the Giant Merchant now?]],
[2012]=[[Hero Summon]],
[2013]=[[Heart Summon]],
[2014]=[[Faction Summon]],
[2015]=[[Obtain <size=65>any</size> hero at random.]],
[2016]=[[Obtain <size=65>Nightfall</size> heroes.]],
[2017]=[[Obtain <size=65>Human</size> heroes.]],
[2018]=[[Obtain <size=65>Beastkin</size> heroes.]],
[2019]=[[Obtain <size=65>Forest</size> faction heroes]],
[2020]=[[Obtain <size=65>God/Voider</size> heroes.]],
[2021]=[[1. You will accumulate progress for any non-Heart Summon.]],
[2022]=[[2. Reach the required total progress to claim rewards in the chest.]],
[2023]=[[Claimed Rewards]],
[2024]=[[Summon Chest]],
[2025]=[[The Summon Chest event is ongoing. Reach the required summons to claim additional rewards!]],
[2026]=[[Bonus rewards will be granted for the first 500 summons.]],
[2027]=[[After 500 summons, the chest will be upgraded to a more advanced Summon Chest.]],
[2028]=[[Summon Chest Rewards]],
[2029]=[[Thanksgiving Rewards]],
[2030]=[[Recommended Packs]],
[2031]=[[Faction Store]],
[2032]=[[Faction Crystal]],
[2033]=[[Weekly Limit]],
[2034]=[[Monthly Limit]],
[2035]=[[Insufficient Faction Crystals!]],
[2036]=[[Can be exchanged for heroes in the Faction Store. Obtained from Faction Summon.]],
[2037]=[[Card Summon]],
[2038]=[[Faction Scroll]],
[2039]=[[Can be used to summon heroes in Card Summon. Obtained from Faction Summon.]],
[2040]=[[Faction Card Summon]],
[2041]=[[Spend faction cards and choose a faction to summon an Excellent Hero.]],
[2042]=[[Reach VIP%s to summon.]],
[2043]=[[Insufficient Faction Cards!]],
[2044]=[[4. In Faction Summon, every Excellent Faction Scroll consumed grants 1 Faction Crystal and 50 Faction Cards that can be used for exchanges or hero summons.]],
[2045]=[[Specific Summon]],
[2046]=[[Remaining Rounds: %s]],
[2047]=[[Round resets after %s.]],
[2048]=[[The selected hero is guaranteed after %s summons.]],
[2049]=[[Clear %s to unlock Specific Summon.]],
[2050]=[[Own %s Mythic heroes to unlock the next round of Specific Summon.]],
[2051]=[[Own 5 Mythic heroes to activate a second chance to get a Choice Hero.]],
[2052]=[[No more Specific Summons available. Wait for the next round to reset.]],
[2053]=[[Choice Hero]],
[2054]=[[· When you obtain an S+ hero from this card pool, they will be replaced by your Choice Hero.]],
[2055]=[[· Own %s Mythic heroes to unlock the next round of Specific Summon.]],
[2056]=[[· Own 5 Mythic heroes to activate a second chance to get a Choice Hero.]],
[2057]='Save',
[2058]=[[Choose a hero before summoning.]],
[2100]='DMG',
[2101]='Healing',
[2102]='Ally',
[2103]='Enemy',
[2104]=[[Total DMG:]],
[2105]=[[Total Healing:]],
[2106]=[[Suited for heroes with high DPS.]],
[2107]=[[Suited for heroes with high DEF.]],
[2108]='Deploy',
[2109]='Best',
[2110]=[[Switched to standard speed.]],
[2111]=[[Switched to 2x speed.]],
[2112]=[[Cannot skip boss stages.]],
[2113]='Previous',
[2114]=[[Next Stage]],
[2130]=[[You can improve your strength in the following ways:]],
[2131]=[[Challenge Again]],
[2132]='Go',
[2133]=[[Battle victory]],
[2134]=[[Less than %s turn(s).]],
[2135]=[[Less than %s hero deaths.]],
[2136]=[[Have %s surviving heroes.]],
[2141]=[[Upgrade Hero]],
[2142]=[[Enhance Gear]],
[2143]=[[Equip high tier gear.]],
[2144]=[[Boost hero grade.]],
[2145]=[[Adjust Lineup]],
[2146]=[[Boost your Faction Mastery.]],
[2147]=[[You already have this hero in your lineup.]],
[2148]=[[Cannot skip Elite stages.]],
[2149]=[[You can skip the battle after Turn %s.]],
[2150]='Turn',
[2151]='DMG',
[2152]='Crush',
[2153]=[[If CP of your heroes far exceeds the required CP of the stage, you can directly crush the stage to obtain 3-Star rating.]],
[2154]=[[Easy victory! You won without fighting!]],
[2155]=[[Return to Game]],
[2156]=[[(Closes in %ss)]],
[2157]='Switch',
[2158]=[[Cannot switch in boss stages.]],
[2159]='Speed',
[2160]='Skip',
[2161]=[[Auto Skip]],
[2200]='Claim',
[2201]='Go',
[2202]='Uncompleted',
[2203]=[[Refresh Time:]],
[2301]=[[Complete daily quests.]],
[2302]=[[Perform 1 Hero Summon.]],
[2303]=[[Collect AFK Rewards 2 times.]],
[2304]=[[Complete Arena Battles 3 times.]],
[2305]=[[Send a heart 1 time.]],
[2306]=[[Accept 2 Acorn Tavern quests.]],
[2307]=[[Purchase 1 item from the Giant Merchant.]],
[2308]=[[Send flowers in Union 2 times]],
[2309]='Complete',
[2310]=[[Quick AFK 2 times.]],
[2311]=[[Complete Proving Grounds sweep %d time(s)]],
[2312]=[[Collect resources from the Garden 2 times.]],
[2313]=[[Complete 4 Weekly Quests.]],
[2314]=[[Win 20 battles in Ancient Summon.]],
[2315]=[[Purchase 1 item in Abyss City.]],
[2316]=[[Sweep Hero Expeditions 8 times.]],
[2317]=[[Awaken 1 hero to Excellent.]],
[2318]=[[Complete Arena Battle 15 times.]],
[2319]=[[Awaken 1 Epic Hero.]],
[2320]=[[Fight 3 battles in Union Warzone.]],
[2321]=[[Complete the event objectives of an entire month.]],
[2322]=[[Defeat the Tower of Fate F1 boss 3 times.]],
[2323]=[[Defeat the Tower of Fate F3 boss 1 time.]],
[2324]=[[Complete all Acorn Tavern quests.]],
[2325]=[[Complete all Arena quests (victory +2 points, defeat +1 point).]],
[2326]=[[Complete a 4-Star Acorn Tavern quest.]],
[2327]=[[Complete a 5-Star Acorn Tavern quest.]],
[2328]=[[Complete a 6-Star Acorn Tavern quest.]],
[2329]=[[Complete a 7-Star Acorn Tavern quest.]],
[2330]=[[Obtain 50 points in Daily Arena.]],
[2331]=[[Obtain 100 points in Daily Arena.]],
[2332]=[[Obtain 160 points in Daily Arena.]],
[2333]=[[Obtain 250 points in Daily Arena.]],
[2334]=[[Awaken a hero to Excellent 1 time.]],
[2335]=[[Awaken a hero to Excellent 2 times.]],
[2336]=[[Awaken a hero to Excellent 3 times.]],
[2337]=[[Awaken a hero to Excellent 4 times.]],
[2338]=[[Awaken a hero to Excellent 5 times.]],
[2339]=[[Awaken a hero to Excellent 6 times.]],
[2340]=[[Awaken a hero to Excellent 7 times.]],
[2341]=[[Awaken a hero to Excellent 8 times.]],
[2342]=[[Awaken a hero to Excellent 9 times.]],
[2343]=[[Awaken a hero to Excellent 10 times.]],
[2344]=[[Awaken a hero to Excellent 11 times.]],
[2345]=[[Awaken a hero to Excellent 12 times.]],
[2346]=[[Awaken a hero to Excellent 13 times.]],
[2347]=[[Awaken a hero to Excellent 14 times.]],
[2348]=[[Awaken a hero to Excellent 15 times.]],
[2349]=[[Awaken a hero to Excellent 16 times.]],
[2350]=[[Awaken a hero to Excellent 17 times.]],
[2351]=[[Awaken a hero to Excellent 18 times.]],
[2352]=[[Awaken a hero to Excellent 19 times.]],
[2353]=[[Awaken a hero to Excellent 20 times.]],
[2354]=[[Awaken a hero to Epic 1 times.]],
[2355]=[[Awaken a hero to Epic 2 times.]],
[2356]=[[Awaken a hero to Epic 3 times.]],
[2357]=[[Awaken a hero to Epic 4 times.]],
[2358]=[[Awaken a hero to Epic 5 times.]],
[2359]=[[Awaken a hero to Epic 6 times.]],
[2360]=[[Awaken a hero to Epic 7 times.]],
[2361]=[[Awaken a hero to Epic 8 times.]],
[2362]=[[Awaken a hero to Legendary 1 time.]],
[2363]=[[Awaken a hero to Legendary 2 times]],
[2364]=[[Awaken a hero to Legendary 3 times.]],
[2365]=[[Awaken a hero to Mythical 1 time.]],
[2366]=[[Purchase 5 items from the Giant Merchant.]],
[2367]=[[Draw 2 times in the Casino.]],
[2368]=[[Complete an 8-Star Acorn Tavern quest.]],
[2369]=[[Complete a 9-Star Acorn Tavern quest.]],
[2370]=[[Challenge an Adventure Stage 1 time.]],
[2371]=[[Challenge the Proving Grounds 1 time.]],
[2372]=[[Challenge a Hunting Battle 4 times.]],
[2373]=[[Requires %s %s hero(es).]],
[2374]=[[Requires %s hero(es).]],
[2375]=[[%s and %s]],
[2376]=[[Requires %s %s hero(es).]],
[2377]=[[%s or %s]],
[2378]=[[Ask for help in Union Channel 1 time.]],
[2379]='Ready:',
[2380]='Deployed:',
[2381]=[[Win 1 battle in Ancient Summon.]],
[2382]=[[Defeat the Tower of Fate F1 boss 1 time.]],
[2383]=[[Defeat the Tower of Fate F1 boss 2 times.]],
[2384]=[[Send a message in the Language Channel 1 time.]],
[2385]=[[Enhance any gear 1 time.]],
[2386]=[[Clear 40 Adventure stages.]],
[2387]=[[Clear 20 Adventure stages.]],
[2388]=[[Clear 10 Adventure stages.]],
[2389]=[[Clear 5 Adventure stages.]],
[2390]=[[Purchase 1 item from the Giant Merchant.]],
[2391]=[[Use Gold Finger 5 times.]],
[2392]=[[Complete battles in the Legendary Duel 3 times]],
[2393]=[[Perform Warrior's Path gather 4 times.]],
[2394]='Deployed',
[2401]='Leaderboard',
[2402]=[[Stage Leaderboard]],
[2403]=[[Hero Leaderboard]],
[2404]=[[[Adventure] Progress Leaderboard]],
[2405]=[[Proving Grounds Progress Leaderboard]],
[2406]=[[Abyss City Progress Leaderboard]],
[2407]=[[God Points Leaderboard]],
[2408]=[[Voider Points Leaderboard]],
[2409]=[[Human Points Leaderboard]],
[2410]=[[Nightfall Points Leaderboard]],
[2411]=[[Forest Points Leaderboard]],
[2412]=[[Beastkin Points Leaderboard]],
[2413]=[[1st Completion Rewards]],
[2414]=[[Serverwide Rewards]],
[2415]=[[Serverwide Achievements]],
[2416]='%dF',
[2417]=[[%s: %d]],
[2418]=[[The first 5 players to get the achievement]],
[2419]=[[To Be Confirmed]],
[2420]=[[Not ranked]],
[2501]=[[Hero Awakening]],
[2502]='Arena',
[2503]=[[Acorn Tavern]],
[2504]=[[Quick AFK]],
[2505]=[[Complete All]],
[2506]=[[Complete all types of quests to claim the final reward.]],
[2507]=[[Choose an S+ hero as the final reward.]],
[2508]=[[Choice Hero]],
[2509]=[[Select a hero to obtain after completing all monthly quests in this round.]],
[2510]='Save',
[2511]=[[Claim Rewards]],
[2512]=[[All quests completed. You can change your Choice Hero before claiming the reward.]],
[2513]=[[Change Hero]],
[2514]='Claim',
[2515]=[[Monthly quests are available. Complete all monthly quests to get an S+ hero.]],
[2516]=[[You can choose one of the following powerful heroes and get your Choice Hero after completing all monthly quests in this round.]],
[2517]=[[Quick AFK 4 times.]],
[2518]=[[Quick AFK 8 times.]],
[2519]=[[Quick AFK 12 times.]],
[2520]=[[Quick AFK 16 times.]],
[2521]=[[Quick AFK 20 times.]],
[2522]=[[Quick AFK 26 times.]],
[2523]=[[Quick AFK 32 times.]],
[2524]=[[Quick AFK 38 times.]],
[2525]=[[Quick AFK 46 times.]],
[2526]=[[Quick AFK 54 times.]],
[2527]=[[Quick AFK 60 times.]],
[2528]=[[Quick AFK 66 times.]],
[2529]=[[Complete all Quick AFK quests.]],
[2540]=[[Rewards Obtained: %s/%s]],
[2541]=[[Choose a hero before saving.]],
[2542]=[[Hero Awakening]],
[2543]=[[Acorn Tavern]],
[2601]=[[Legendary Bonus Stats x4]],
[2602]='x4',
[2603]=[[Deluxe Monthly Card User Exclusive - Deluxe Fund]],
[2604]='Claim',
[2605]=[[View Details]],
[2606]=[[Reward Value: 70000]],
[2607]='Go',
[2701]=[[Key Puzzle]],
[2702]=[[Tower Battle]],
[2703]=[[The treasure lies right ahead! Use your wisdom to claim what is yours!]],
[2704]=[[Behold! We have found a place with many hidden key puzzles!]],
[2705]=[[New key puzzle stage unlocked. Let's check it out!]],
[2706]=[[Another key puzzle stage has been unlocked. Let's go challenge it!]],
[2707]=[[Push your limits in Adventure to unlock more stages. Keep fighting!]],
[2708]=[[Almost there. Try another method!]],
[2709]='Exit',
[2710]='Retry',
[2711]='Skip',
[2712]=[[%s unlocked]],
[2713]=[[Verdant Prairie]],
[2714]=[[Azure Bay]],
[2715]=[[Border Valley]],
[2716]=[[Polar Snowfield]],
[2717]=[[Unlocked after clearing all stages in the previous chapter.]],
[2718]=[[Unlocked after clearing all previous stages.]],
[2719]='Resume',
[2720]=[[Key Puzzle]],
[2721]=[[Tower Battle]],
[2722]=[[Tap the screen to move the key and the hero will move automatically. You win when the hero reaches the chest.]],
[2723]=[[Drag the blue minions to fight enemies in the tower. You will win the battle after defeating all enemies.]],
[2724]=[[Activate Puzzle Games]],
[2725]=[[Choose the puzzle game type you wish to activate.]],
[2726]=[[Activate %s]],
[2727]=[[Each player can only activate one type of puzzle game. Are you sure you want to activate %s?]],
[2728]='Activate',
[2729]=[[Unlocked by clearing Adventure %s.]],
[2730]=[[Invaders from alien planets are destroying Yggdrasil. We need your urgent help!]],
[2731]=[[The World Administration Bureau has sent two Mage heroes, but they are trapped. We need to go rescue them!]],
[2732]=[[We need strong Tank heroes to help us withstand enemy damage. They are fighting in the tower. Go challenge the tower now!]],
[2733]=[[Clear Stage 5 to claim awesome milestone rewards.]],
[2734]=[[Tower Battle offers amazing milestone rewards. Go challenge it now!]],
[2735]=[[It's Winter Phantom and Withered Sand! Both of them have first-rate AoE damage abilities!]],
[2736]=[[Stages cleared: %s]],
[2737]=[[%s isn't that easy to tackle. Devise a wise strategy to win.]],
[2738]=[[Dear Traveler, you just worked miracles and defeated powerful enemies. There is a battle going on near Yggdrasil, let's go and help our heroes!]],
[2739]=[[The Sage's Academy has sent two powerful heroes. Complete the stage challenge in %s to win their trust!]],
[2740]=[[%s offers abundant resources that can help strengthen our heroes.]],
[2741]=[[Clear Stage 5 to get additional milestone rewards.]],
[2742]=[[Resources obtained. We can use them to strengthen our heroes.]],
[2743]=[[We need strong Warrior heroes to help us withstand DMG. Go accept their %s challenge!]],
[2744]=[[Complete the challenge and welcome the Warrior heroes!]],
[2745]=[[New stage unlocked: %s. Go challenge it now!]],
[2746]=[[New chapters unlocked in %s. Wonderful rewards await!]],
[2747]=[[New chapters unlocked in %s.]],
[2748]=[[Unlocked by clearing Adventure %s.]],
[2749]=[[Dear Traveler, complete Adventure challenges to unlock more %s stages!]],
[2750]='Challenge',
[2751]=[[Clear Stage 4 to get additional milestone rewards.]],
[2760]=[[Cannon War]],
[2761]=[[Puzzle Games]],
[2762]=[[The frost has taken over New York. Fortunately, we can help our interdimensional heroes to crack puzzles, earn rewards, and prepare them for the upcoming battles!]],
[2763]=[[Help the Esper Squad defeat enemies, and gain an edge in the upcoming Esper Battle!]],
[2764]=[[The city is under siege by the Frost Dragon! We mush smash through any obstacles in our path and take it down.]],
[2765]=[[The Esper Battle is just around the corner! Help Floral Shadowblade get the chest to face the imminent Esper Battle!]],
[2766]=[[The frost has taken over New York. Fortunately, we can help our Esper heroes to crack puzzles, earn rewards, and prepare them for the upcoming battles!]],
[2767]=[[The Frost Dragon has descended! We must get moving now. The clock's ticking!]],
[2768]=[[Help the Esper Squad defeat enemies, and gain an edge in the upcoming Esper Battle!]],
[2769]=[[Winter Valley]],
[2770]=[[New milestone rewards unlocked.]],
[2799]='Used',
[2801]=[[Rampage Squad]],
[2802]=[[Slide to the Destination]],
[2803]=[[Start Sliding]],
[2805]=[[Sort Master]],
[2806]=[[Flying Blade Challenge]],
[2807]=[[King of the Ring]],
[2808]=[[Roller Skating Expert]],
[2809]='Tangram',
[2810]=[[Squirrel Treasure Hunt]],
[2811]=[[City War]],
[2812]=[[Javelin Pro]],
[2813]=[[Swipe to the left and adjust your vertical shooting direction.]],
[2814]=[[Joy of Bouncing]],
[2815]=[[Castle Assault]],
[2816]=[[Gold Miner]],
[2817]=[[Big Brain]],
[2818]=[[Brick King]],
[2819]=[[Psychic Warrior]],
[2820]=[[Arrow Rain]],
[2821]='Zuma',
[2822]=[[Digital Duel]],
[2823]=[[Lord of Destruction]],
[2824]=[[Survival Challenge]],
[2825]='Marksman',
[2826]=[[Tap Tap Remove]],
[2827]=[[One-Line Draw]],
[2828]=[[Storm of Blades]],
[2829]=[[Ninja Fight]],
[2830]=[[Push Everything]],
[2831]=[[Merge /Dinosaur]],
[2832]=[[Colorful Tower Defense]],
[2833]=[[Master Assassin]],
[2834]=[[Gluttonous Fish]],
[2835]=[[Here They Come]],
[2836]=[[King of Arena]],
[2837]=[[Master Assassin]],
[2838]=[[Parking Genius]],
[2839]=[[Vehicle Tower Defense]],
[2840]=[[Perfect Cut]],
[2841]=[[Perfect Line]],
[2842]=[[Parking Lot]],
[2843]=[[Berserk Dino]],
[2844]=[[Farm Master]],
[2845]=[[Crafted Saber Tiger]],
[2846]=[[Giant Parkour]],
[2850]=[[Game Hall]],
[2851]=[[Do you want to download %s? The game will be restarted after you tap Confirm.]],
[2852]=[[Mini-game rewards cannot be claimed repeatedly. Do you want to switch to [%s]? The game will be restarted after you tap Confirm.]],
[2853]=[[Your current client version is too old. Could not download [%s] resources. Update your client to the latest version.]],
[2854]=[[Tap to switch to the corresponding mini-game.]],
[2855]=[[Unlocked after clearing Esper %s.]],
[2856]=[[Clear Adventure to unlock more puzzle games.]],
[2857]=[[Do you want to download [%s]? Confirm to restart the game and download the required resources.]],
[2858]=[[Mini-game rewards cannot be claimed repeatedly. You must restart the game to switch to [%s].]],
[2859]='Release',
[2860]=[[Hold and Move]],
[2901]=[[Knot Master]],
[2902]=[[Brick Remove]],
[2903]=[[Zombie's Arrival]],
[2905]=[[Lone Hero]],
[2909]='Hit&Run',
[2910]=[[Marble Hero]],
[2911]=[[Operation Unity]],
[2912]=[[Warrior Clash]],
[2913]=[[King of Guns]],
[2914]=[[Bridge Builder]],
[2915]=[[Primal Clash]],
[2916]=[[Save the Dog]],
[2917]=[[Ingot Master]],
[2918]=[[Arrival of the Ants]],
[2919]=[[Draw Rush]],
[2920]=[[Merge Turret]],
[2921]=[[Animal Herder]],
[2922]=[[Ant Parkour]],
[2923]=[[Bridge Building]],
[2924]=[[Ball
2048]],
[2925]=[[Multicolor Parkour]],
[2927]=[[Boarding Party]],
[2928]=[[Into the Black Hole]],
[2929]=[[Balloon /Rush]],
[2932]=[[Hanging by a Thread]],
[2933]=[[Gun Assembly Master]],
[2934]=[[Stacking Knight]],
[2936]=[[Miniball Maze]],
[2937]=[[Character Magic]],
[2938]=[[Mecha Rush]],
[2939]='Hide-and-Seek',
[2941]=[[Wizard War]],
[2942]=[[Brick Remover]],
[2943]=[[Cola Jet]],
[2944]=[[Joy Rush]],
[2950]=[[Draw the line slowly to avoid any discontinuity.]],
[2954]='DrawBreak',
[2957]=[[Protect/ the Balloon]],
[2960]=[[Clear Tiles]],
[2963]=[[Hero Tower Defense]],
[2971]='SaveEmAll',
[2973]=[[Knight Clash]],
[2974]='Snowball/Game',
[2975]=[[Animal Herder]],
[2978]=[[Control the Squares]],
[2984]='CollectFruit',
[2987]=[[Catch the Ball]],
[2990]=[[Ocean War]],
[3001]=[[I expected the challenge to be tougher, humph! Easy-peasy!]],
[3002]=[[That's interesting! Even if I don't get to meet evil face-to-face, this new challenge can train my mind.]],
[3005]=[[Moves Remaining:]],
[3006]=[[Merge until only one number remains.]],
[3007]='Piece/Wanted',
[3009]='Morph/Boost',
[3012]=[[Tap the test tubes to mix solutions of the same color together to complete the game]],
[3014]='Stage',
[3015]=[[Help the police catch the thief!]],
[3065]=[[Rocket Boy]],
[3066]='BrickTower',
[3069]=[[Draw Escape]],
[3073]=[[Master Inspector]],
[3074]=[[Rail Connector]],
[3076]=[[Time left: ]],
[3077]=[[Draw a line to save the boy]],
[3078]=[[The line you draw couldn't touch the boy]],
[3079]='Stage',
[3086]=[[Mind the/Gap]],
[3102]=[[Cat Worm]],
[3103]=[[Knock Over]],
[3104]=[[Child Saver]],
[3105]=[[Fingertip/ Swing]],
[3107]='Stop/Them',
[3112]=[[Expert Plumber]],
[3113]=[[Draw
Expert]],
[3124]='Painted/squares',
[3125]=[[Painted in the corresponding color]],
[3133]=[[Merge Legion]],
[3160]=[[Control/the Arrow]],
[3161]=[[Objective: Fill in the tiles and save the lamb.]],
[3162]=[[Hang in there! I'm coming!]],
[3163]=[[Save me]],
[3167]=[[One-Line Draw]],
[3169]='Complete',
[3170]='Victory',
[3171]=[[Purchase Unit]],
[3172]='Ascend',
[3173]='Rampage',
[3174]=[[Shield of Invulnerability]],
[3175]=[[Random Unit]],
[3176]=[[2x Troops]],
[3177]=[[Drag to merge.]],
[3178]=[[Tap to purchase.]],
[3179]=[[Drag the item and place it in front of the troop to make sure it works.]],
[3180]=[[Drag the troop to recycle and sell it.]],
[3181]=[[Tap Start to begin the battle!]],
[3182]=[[Dual Sword Fight]],
[3192]=[[Marble Expert]],
[3193]=[[Break all squares to clear the stage.]],
[3194]=[[Break all squares and defeat all enemies to clear the stage.]],
[3200]='Quest',
[3201]='Store',
[3202]=[[Start Game]],
[3203]=[[Stage List]],
[3204]=[[Current Stage]],
[3205]=[[Insufficient diamonds]],
[3206]=[[Would you like to purchase diamonds from the store?]],
[3207]='Yes',
[3208]='No',
[3209]=[[You must clear the previous stage first.]],
[3210]=[[All stages cleared.]],
[3211]=[[Claim Again]],
[3212]=[[Next Stage]],
[3213]=[[Initial Players]],
[3298]=[[Drag to move.
Tap to attack.]],
[3299]=[[Tap and hold to move.]],
[3300]=[[Don't trip!]],
[3301]=[[Move to the next area
or
push everything away!]],
[3302]=[[The area is empty.]],
[3303]=[[You are in fury mode.
Release your finger and see what happens!]],
[3304]=[[You've become larger.]],
[3305]=[[Complete the stage.]],
[3306]=[[Collect to improve your weapons.]],
[3307]=[[Release your finger to
push everything forward!]],
[3454]=[[Eyes of the Sage]],
[3455]=[[Protection of the Sage]],
[3456]=[[Protection of the Sage expires]],
[3601]=[[Your account is logged in on another device.]],
[3602]=[[The admin has kicked you from the server.]],
[3603]=[[Your account is banned until %s due to violation of the game rules.]],
[3604]=[[You are muted until %s due to violation of the chat rules.]],
[3605]=[[Your account has been permanently banned due to violation of the game rules.]],
[3606]=[[Your account is banned until %s due to violation of the game rules. If you have any objections, contact us via our Official Facebook Page: Hero Clash.]],
[3607]=[[You are muted until %s due to violation of the chat rules. If you have any objections, contact us via our Official Facebook Page: X-Clash.]],
[3608]=[[Your account has been permanently banned due to violation of the game rules. If you have any objections, contact us via our Official Facebook Page: Hero Clash.]],
[3609]=[[Level Up]],
[3610]=[[Player Level]],
[3611]=[[Upgrade Rewards]],
[3612]=[[Banned User ID: %s]],
[3613]=[[Ban Type ID: %s]],
[3506]='Arrest',
[3507]='Search',
[3508]='Release',
[3509]=[[Selection error. Try another selection.]],
[3510]=[[There's nothing on the passenger.]],
[3511]=[[Green Apple]],
[3512]=[[Red Apple]],
[3513]='Magazine',
[3514]='Ham',
[3515]='Sunscreen',
[3516]=[[Face Wash]],
[3517]=[[Chocolate Doughnut]],
[3518]='Doughnut',
[3519]=[[Strawberry Doughnut]],
[3520]='Cola',
[3521]='Fork',
[3522]='Hammer',
[3523]=[[Iron Hammer]],
[3524]='Grapefruit',
[3525]='Chip',
[3526]='Pliers',
[3527]=[[Small Pliers]],
[3528]=[[Big Pliers]],
[3529]=[[Iron Pliers]],
[3530]='Potato',
[3531]=[[Floppy Disk]],
[3532]=[[Blue Floppy Disk]],
[3533]='Ketchup',
[3534]='Scissors',
[3535]='Screwdriver',
[3536]=[[Blue Screwdriver]],
[3537]=[[Yellow Screwdriver]],
[3538]='Hairspray',
[3539]=[[Spray Paint]],
[3540]='Wrench',
[3541]=[[Big Wrench]],
[3571]=[[Battle monster configuration error.]],
[3572]=[[Battle hero parameter error.]],
[3573]=[[Battle error.]],
[3574]=[[Battle stage parameter error.]],
[3721]=[[Wishlist request failed.]],
[3722]=[[Failed to select wish rewards.]],
[3723]=[[Unlock conditions not met.]],
[3724]=[[Hero not found.]],
[3725]=[[Perform sufficient draws to unlock Firefly.]],
[4051]=[[Choose one of the following ways to bind]],
[4052]=[[Bingchuan Account]],
[4053]=[[Google Play]],
[4054]='Facebook',
[4055]=[[A guest account will be created to allow you to log in. We suggest that you bind an account as quickly as possible!]],
[4056]='Back',
[4057]=[[Quick Game]],
[4058]='Me',
[4059]=[[Basic Info]],
[4060]=[[Bind Account]],
[4061]=[[Log Out]],
[4062]=[[Bind Now]],
[4063]=[[Bind Later]],
[4064]='Tip',
[4065]=[[For the safety of your account, make sure to bind it as quickly as possible!]],
[4066]=[[Do you want to bind your guest account to the current account?]],
[4067]=[[Binding successful.]],
[4068]=[[Account bound successfully! Please log in again.]],
[4069]='Tip',
[4070]=[[Logging in, please wait...]],
[4071]=[[Login successful.]],
[4072]=[[Hello, welcome to the game!]],
[4073]=[[The character does not match your bound character.]],
[4074]=[[For the safety of your account, make sure to bind it as soon as possible.
If you wish to play this game on two devices,
bind this account, then
tap Switch Account on your new device to log in.]],
[4075]=[[Failed to bind. Your account has already bound a character. Tap Switch Account or bind another social media account.]],
[4076]='Bound',
[4077]='Huawei',
[4078]=[[You haven't bound a Huawei account.]],
[4101]=[[Warrior ]],
[4102]=[[Mage ]],
[4103]=[[Hunter ]],
[4104]=[[Assassin ]],
[4105]=[[Priest ]],
[4106]=[[Nightfall ]],
[4107]=[[Human ]],
[4108]=[[Beastkin ]],
[4109]='Forest',
[4110]=[[Voider ]],
[4111]=[[God ]],
[4112]='Heroes',
[4113]='1-Star',
[4114]='2-Star',
[4115]='3-Star',
[4116]='4-Star',
[4117]='5-Star',
[4118]='6-Star',
[4119]='7-Star',
[4120]='8-Star',
[4121]='9-Star',
[4122]='10-Star',
[4123]=[[Hero gear and abilities have been returned to your bag.]],
[4124]=[[Level %d]],
[4125]=[[%s Faction Heroes]],
[4126]='All',
[4127]='HP',
[4128]='ATK',
[4129]='Armor',
[4130]='Speed',
[4131]=[[Go AFK farming in Adventure for a chance to obtain new gear!]],
[4132]=[[Hero Awakening]],
[4133]=[[Select a hero!]],
[4134]=[[All Faction Heroes]],
[4135]='Elite',
[4136]=[[Continuous Damage]],
[4137]=[[Burst DMG]],
[4138]=[[AoE Damage]],
[4139]='Buff',
[4140]='Debuff',
[4141]=[[DMG Received]],
[4142]='Healing',
[4143]='Assassinate',
[4144]='CC',
[4145]=[[Major Responsibility:]],
[4146]=[[Minor Responsibility:]],
[4147]='Faction:',
[4148]='Class:',
[4149]=[[Skip in %ss.]],
[4150]=[[Common Hero (B)]],
[4151]=[[Legendary Hero (A)]],
[4152]=[[Mythical Hero (S)]],
[4153]=[[Mythical Hero (S+)]],
[4154]=[[Upgrade to Lv. %s]],
[4155]=[[Lv. ]],
[4156]='Upgrade',
[4157]=[[DMG Stacking]],
[4158]=[[Single DMG]],
[4200]='Rank',
[4201]=[[Hero Avatar]],
[4202]=[[Hero Name]],
[4203]=[[Deploy Rate]],
[4204]=[[Rank Change]],
[4205]=[[Boss Battle]],
[4206]='Arena',
[4207]=[[For Masters]],
[4208]=[[For Experts]],
[4209]=[[For Advanced Players]],
[4210]=[[For Rookies]],
[4211]=[[An amazing lineup in the early stage, boasting continuous DMG and steady DPS, as well as a degree of CC.]],
[4212]=[[A highly stable lineup in the mid-stage. Consists of remarkable CC and Tank heroes in the front row to withstand DMG and guarantee stable DPS from back-row DMG dealers.]],
[4213]=[[An extremely powerful lineup in the late stage with Tank heroes in the front row capable of withstanding and dealing DMG, coupled with huge DMG bonuses from other heroes.]],
[4214]=[[This lineup is quite easy to put together and offers satisfactory DMG, making it a good choice for most players.]],
[4215]=[[An awesome lineup boasting extremely high DMG. You can't go without it!]],
[4216]=[[You'll find it very hard to put together this incredible lineup. But when you do, nothing will be able to stand in your way.]],
[4217]=[[This lineup is quite easy to put together and offers satisfactory DMG, making it a good choice for most players.]],
[4218]=[[An awesome lineup boasting extremely high DMG. You can't go without it!]],
[4219]=[[You'll find it very hard to put together this incredible lineup. But when you do, nothing will be able to stand in your way.]],
[4220]=[[It's quite easy to get these five heroes in the early stage, and they all possess great one-on-one abilities.]],
[4221]=[[This lineup will help you get a decent position on the Leaderboard.]],
[4222]=[[This ultimate lineup allows you to dominate Abyss City. The hard part is getting all five heroes.]],
[4223]=[[Achieve Rank 1 on the Leaderboard to unlock Exclusive Avatar Frame: %s]],
[5000]='CP',
[5001]='HP',
[5002]='ATK',
[5003]='Armor',
[5004]='Speed',
[5005]='CRIT',
[5006]=[[CRIT DMG]],
[5007]=[[Debuff Hit]],
[5008]=[[Debuff RES]],
[5009]='Hit',
[5010]='Dodge',
[5011]='Accuracy',
[5012]='Block',
[5013]=[[Armor PEN]],
[5014]=[[DMG Reduction]],
[5015]='HP',
[5016]='ATK',
[5017]='Armor',
[5018]='Speed',
[5019]=[[Skill DMG]],
[5020]='Rage',
[5021]=[[Holy DMG]],
[5022]='HP',
[5023]='ATK',
[5024]='Armor',
[5025]='Speed',
[5026]='MP',
[5027]=[[DMG to Warriors]],
[5028]=[[DMG to Mages]],
[5029]=[[DMG to Hunters]],
[5030]=[[DMG to Assassins]],
[5031]=[[DMG to Priests]],
[5032]='HP',
[5033]='ATK',
[5034]='Armor',
[5035]='Speed',
[5036]=[[Crit DMG Reduction]],
[5037]=[[You have not set a Nucleus Crystal. Tap Assemble to automatically equip other Nucleus Crystals!]],
[5038]=[[Unlocks at hero level 100.]],
[5039]=[[Unlocks at Legendary+.]],
[5040]='Weapon',
[5041]='Armor',
[5042]='Helmet',
[5043]='Boots',
[5044]=[[Destruction Crystal]],
[5045]=[[Creation Crystal]],
[5046]=[[Type: %s]],
[5047]=[[Quick Fill]],
[5048]=[[No lower-grade gear.]],
[5049]=[[No gear can be upgraded.]],
[5050]=[[This gear does not need to be upgraded.]],
[5051]='Requirements:',
[5052]=[[Lv. 1]],
[5053]=[[Lv. 100]],
[5054]=[[Awaken a 7-Star hero.]],
[5055]='View',
[5056]=[[Bag is full.]],
[5057]=[[The gear bag is full. You cannot claim the reward.]],
[5058]=[[Quick Fill only works for gear below Grade 6.]],
[5059]='Selected',
[5060]=[[Select the gear which you want to use.]],
[5100]=[[Select the gear to place in.]],
[5101]=[[Level cap reached.]],
[5102]=[[This hero has not equipped any gear!]],
[5103]=[[Tap and hold the icon to view Gear Stats!]],
[5104]=[[There is no gear in your bag!]],
[5105]='Tap',
[5106]=[[Unequip the gear.]],
[5107]=[[You must select a gear first!]],
[5108]=[[You must add materials first!]],
[5109]=[[Max level reached!]],
[5110]=[[The gear you selected as material is better than the one you are trying to upgrade. Are you sure you want to continue]],
[5111]='Enhance',
[5112]=[[Show All]],
[5113]=[[You must select materials!]],
[5114]=[[There is no %s in your bag!]],
[5200]=[[%s MP is required.]],
[5201]=[[Equipped by]],
[5202]=[[Materials Cost:]],
[5203]=[[Modify Skill]],
[5204]=[[Skill List]],
[5205]='Upgrade',
[5206]=[[Next Force Refresh:]],
[5207]=[[Leveled up!]],
[5208]=[[Cannot equip skill as the hero does not have sufficient MP!]],
[5209]=[[Max Level]],
[5210]=[[Faction Bonus]],
[5211]=[[Faction Bonus deals 25% additional damage.]],
[5212]=[[This is a Nightfall faction hero. They counter Human heroes and are countered by Forest heroes.]],
[5213]=[[This is a Human faction hero. They counter Beastkin heroes and are countered by Nightfall heroes.]],
[5214]=[[This is a Beastkin faction hero. They counter Forest heroes and are countered by Human heroes.]],
[5215]=[[This is a Forest faction hero. They counter Nightfall heroes and are countered by Beastkin heroes.]],
[5216]=[[This is a Voider faction hero. They counter God heroes and are countered by God heroes.]],
[5217]=[[This is a God faction hero. They counter Voider heroes and are countered by Voider heroes.]],
[5301]=[[[%s Exclusive] ]],
[5302]=[[Exclusive Gear Skill:]],
[5303]=[[[+%d unlocked] ]],
[5304]=[[Exclusive Gear Backstory]],
[5305]=[[Unlock New Skill]],
[5306]=[[Unlock New Skill Effect]],
[5307]=[[Exclusive Gear Stats]],
[5308]=[[Enhancement Bonus]],
[5309]='Activate',
[5310]='Enhance',
[5311]=[[Activation Successful]],
[5312]=[[Enhancement Successful]],
[5313]=[[<color=#ffffff>Insufficient materials. Obtain more from Summoning Hall, Weekly Packs, and Adventure AFK.</color>]],
[5314]=[[Exclusive Gear unlocked.]],
[5315]=[[Activate Now]],
[5316]=[[Gear Skill Effect:]],
[5320]=[[Clear Adventure%sto unlock Quick Sweep on this floor.]],
[5321]=[[1-Tap Sweep]],
[5322]=[[Subscription Privilege grants]],
[5323]=[[Sweep the current floor!]],
[5324]=[[Quickly sweep the current floor]],
[5325]=[[Automatically select Advanced Heroes (if any) on the current floor.]],
[5326]=[[Auto-select Buffs]],
[5327]=[[Don't miss the Tower Merchant (if any)]],
[5328]=[[Sweep NOW]],
[5329]=[[The Tower challenge has begun, you cannot sweep again now!]],
[5330]=[[Skip the lucky draw animation.]],
[5331]=[[Skip the lucky draw animation!]],
[5332]=[[Significantly shorten the lucky draw animation.]],
[5333]=[[Auto-select the steal/attack targets.]],
[5334]=[[Click to skip the steal/attack process.]],
[5335]=[[Quick Selection Settings]],
[5336]=[[Auto-choose the targets to attack.]],
[5337]=[[Auto-choose the resources to steal.]],
[5338]=[[Auto selection steals the selected resources first.]],
[5339]=[[Less than 2 kinds of resources selected!]],
[5340]=[[Auto-choose randomly after %ds]],
[5341]=[[Select the resources to steal (%d/2)]],
[5342]=[[You cannot select more than 2 kinds of resources!]],
[5343]=[[Claim Hero]],
[5344]=[[You encounter a hero who will assist you in battle.]],
[5403]='Owned',
[5451]=[[Insufficient card flips.]],
[5452]=[[Insufficient purchases.]],
[5453]=[[The game hasn't started yet!]],
[5454]=[[This card has been flipped. Tap and open another card.]],
[5500]=[[Hero bag is full. Do you want to increase the bag capacity?]],
[5501]='Grade',
[5502]='Descending',
[5503]='Ascending',
[5504]=[[Cannot be sold.]],
[5505]='Details',
[5506]='Unequip',
[5507]='Equip',
[5508]='Replace',
[5509]=[[Obtained from]],
[5510]=[[There is a chance to obtain the following items after use]],
[5511]=[[Final Rewards]],
[5512]=[[Rankup Rewards]],
[5513]=[[Special Rewards]],
[5514]='Use',
[5515]='Exchange',
[5683]='HP',
[5684]='ATK',
[5685]='Armor',
[5686]='Speed',
[5625]='CRIT',
[5631]='CRIT',
[5632]=[[CRIT DMG]],
[5633]=[[Debuff Hit]],
[5634]=[[Debuff RES]],
[5635]='Hit',
[5636]='Dodge',
[5637]='Accuracy',
[5638]='Block',
[5639]=[[Armor PEN]],
[5640]=[[DMG Reduction]],
[5641]='HP',
[5642]='ATK',
[5643]='Armor',
[5644]='Speed',
[5645]=[[Skill DMG]],
[5646]='Energy',
[5647]=[[Holy DMG]],
[5648]='HP',
[5649]='ATK',
[5650]='Armor',
[5651]='Speed',
[5652]='MP',
[5653]=[[DMG to Warriors]],
[5654]=[[DMG to Mages]],
[5655]=[[DMG to Hunters]],
[5656]=[[DMG to Assassins]],
[5657]=[[DMG to Priests]],
[5658]='HP',
[5659]='ATK',
[5660]='Armor',
[5661]='Speed',
[5662]=[[Crit DMG Reduction]],
[5799]='End',
[5801]=[[System Mail]],
[5802]=[[Player Mail]],
[5803]=[[Claim All]],
[5804]=[[Delete All]],
[5805]=[[Select a mail]],
[5806]=[[Are you sure you want to delete all read mail?
Any mail with unclaimed rewards will not be deleted.]],
[5807]=[[Are you sure you want to delete this mail?]],
[5808]=[[You haven't claimed the rewards in this mail. Are you sure you want to delete?]],
[5809]=[[Claimed successfully]],
[5810]='Gold',
[5811]='Diamond',
[5812]=[[Hero Shards]],
[5813]=[[Expires today]],
[5814]=[[Expires in %d day(s)]],
[5815]='Today',
[5816]=[[%d day(s) ago]],
[5817]='Claim',
[5818]=[[You have no mail.]],
[5819]='Delete',
[5820]='10K',
[5821]=[[Select a friend]],
[5822]=[[Enter content here (no more than 100 characters).]],
[5823]='Recipient',
[5824]='Content',
[5825]='Send',
[5826]='Create',
[5827]='Send',
[5828]='Reply',
[5829]='Reply',
[5830]=[[The mail content cannot be empty.]],
[5831]=[[You must add a recipient.]],
[5832]=[[Mail sent successfully!]],
[5833]=[[Failed to send mail!]],
[5834]=[[Recipient not found.]],
[5835]=[[Send mail in %ds.]],
[5836]=[[Mail Count:]],
[5837]=[[No attachments to claim.]],
[5838]='System',
[5901]=[[Hero Talent]],
[5902]=[[Legendary Talent]],
[5903]=[[Legendary+ Talent]],
[5904]=[[Mythic Talent]],
[5905]=[[Mythic+ Talent]],
[5906]=[[Transcendent Talent]],
[5907]=[[Transcendent+ Talent]],
[5908]=[[Transcendent++ Talent]],
[5909]=[[Eternal Talent]],
[5910]='Activate',
[5911]=[[Talent Overview]],
[5912]=[[No Talents to activate at the current star level]],
[5913]=[[Reach %s to unlock]],
[5914]='Talent',
[5915]=[[Activate the %s Talent]],
[5916]=[[For every Talent level unlocked, you can choose 1 from the list and activate it.]],
[5917]=[[Unlocks when the hero's star level reaches %s]],
[5918]='(Activated)',
[5919]=[[Cannot activate duplicate Talents]],
[6000]='Stage',
[6001]='Advance',
[6002]='Wyrmrest',
[6003]=[[Catastrophe in New York]],
[6004]=[[Oklahoma Encounter]],
[6005]=[[Battlefield of Ice and Fire]],
[6006]=[[Ohio Inferno]],
[6007]=[[Frozen Manhattan]],
[6008]=[[The City that Never Sleeps]],
[6009]=[[War-Torn Hawaii]],
[6010]=[[Thor's Throne]],
[6011]=[[Nightmare Valley]],
[6012]=[[Nightfall Beastkin]],
[6013]='Prologue',
[6998]=[[Obtained from]],
[6999]=[[Stage 1,000]],
[7000]='Requirements',
[7001]='Level',
[7002]='Help',
[7003]=[[1. The battle consists of multiple smaller stages.]],
[7004]=[[2. Every time you enter a new stage, a battle will be triggered.]],
[7005]=[[3. After completing the battle and meeting the Team CP requirement...]],
[7006]=[[AFK Rewards]],
[7007]='Claim',
[7008]='Loot',
[7009]='Team',
[7010]='Challenge',
[7011]='Map',
[7012]=[[Deploy Hero]],
[7013]=[[Front Row]],
[7014]=[[Back Row]],
[7015]='Select',
[7016]=[[Awakening Battle]],
[7017]=[[Chaos Battle]],
[7018]=[[Transformation Battle]],
[7019]=[[Destruction Battle]],
[7020]=[[Savior Battle]],
[7021]=[[Must clear stage %s.]],
[7022]=[[AFK requires %d Team CP.]],
[7023]=[[Must clear the battle encounter of the previous stage.]],
[7024]=[[Stage Rewards]],
[7025]=[[Stage Drops]],
[7026]=[[Sweep Now]],
[7027]=[[Dropped Rewards]],
[7028]='Claim',
[7029]='Confirm',
[7030]=[[Select Magic Beast]],
[7031]=[[Enhance Gear]],
[7032]=[[Upgrade Hero]],
[7033]=[[Summon Hero]],
[7034]=[[Take the following steps to become stronger]],
[7035]='Turn',
[7036]=[[%d CP required to enter the next stage.]],
[7037]=[[Requires Player Lv. %d]],
[7038]=[[No new loot!]],
[7039]=[[Stage %d has not been unlocked.]],
[7040]=[[Congratulations on reaching Lv. %d!]],
[7041]=[[Next Stage]],
[7042]=[[Drop Info]],
[7043]=[[Tap any blank space to close.]],
[7044]=[[This is the last stage!]],
[7045]='Boss',
[7046]=[[Stage Info]],
[7047]=[[Stage Rewards]],
[7048]=[[AFK Rewards]],
[7049]=[[AFK Duration:]],
[7050]='Loot',
[7051]=[[AFK Reward Boost]],
[7052]=[[Union Members (Current Stage)]],
[7053]=[[Friends (Current Stage)]],
[7054]=[[Boss Info]],
[7055]=[[World Map]],
[7056]=[[Stage %s]],
[7057]=[[Stage Chest]],
[7058]=[[Star Chest]],
[7059]='Open',
[7060]=[[Common Chest]],
[7061]=[[Luxury Chest]],
[7062]=[[Permanently Unlock Luxury Chest]],
[7063]=[[Claimable Rewards]],
[7064]=[[Unlocked after clearing the previous world.]],
[7065]=[[Can be claimed after clearing the target stage.]],
[7066]=[[Insufficient Stars]],
[7067]='Healing',
[7068]='Support',
[7069]='pass',
[7070]=[[Next Chapter]],
[7071]=[[Latest Chapter]],
[7072]=[[-Chapter Cleared-]],
[7073]=[[Perfect Clear!]],
[7080]=[[Star Requirements]],
[7081]=[[Battle victory]],
[7082]=[[Complete the battle in less than %s turns.]],
[7083]=[[Have no more than %s allied hero deaths.]],
[7084]=[[Have 0 allied hero deaths.]],
[7085]=[[Deploy no more than %s heroes.]],
[7086]=[[Have %s surviving heroes.]],
[7087]=[[Common Chest]],
[7088]=[[Luxury Chest]],
[7089]='Claim',
[7090]=[[Claim Luxury Chest]],
[7091]='Purchase',
[7092]=[[Unlock Luxury Rewards]],
[7093]=[[Clear all stages in Chapter %s.]],
[7094]=[[AFK Income]],
[7095]=[[Clear Adventure stages to boost AFK Rewards.]],
[7101]=[[Your current rank is %d. Keep it up to obtain:]],
[7102]=[[Rank rewards will be issued at 21:00 each day.]],
[7103]=[[Rewards will be issued based on players' ranks when the season ends.]],
[7104]=[[Ends in:]],
[7105]=[[Begins in:]],
[7106]=[[! %jd %Hh %Mm %Ss]],
[7107]='forward',
[7108]='Quest',
[7109]=[[Free Challenge x %d]],
[7110]=[[You must deploy at least 1 hero.]],
[7111]=[[Enter a valid team name.]],
[7112]=[[Enter a valid CP value.]],
[7113]=[[Enter a valid team number.]],
[7114]=[[You are attempting too many refreshes.]],
[7115]='Apply',
[7116]='Applied',
[7117]=[[Only the Leader can manage the team.]],
[7118]=[[Requires 3 allies.]],
[7119]=[[Insufficient stamina.]],
[7120]=[[Next Match]],
[7121]='Confirm',
[7122]=[[Below Lv. %d]],
[7123]=[[Challenges can only be initiated by the Leader.]],
[7124]=[[Are you sure you want to disband the team?]],
[7125]=[[Team disbanded.]],
[7126]=[[Match 1]],
[7127]=[[Match 2]],
[7128]=[[Match 3]],
[7129]=[[Deploy at least 1 hero in each lineup.]],
[7130]=[[Challenge Cost:]],
[7131]=[[Stamina Regen]],
[7132]='Invite',
[7133]='Accept',
[7134]='Reject',
[7135]=[[Ignore All]],
[7136]=[[This team does not exist.]],
[7137]=[[Your CP is too low.]],
[7138]=[[Daily Arena]],
[7139]=[[A periodic round robin that grants massive amounts of daily rewards and season rewards.]],
[7140]=[[Arena Weekly Battle]],
[7141]=[[A periodic individual match available to all players above Lv. 55.]],
[7142]=[[Team Weekly Battle]],
[7143]=[[A periodic individual match available to all players above Lv. 60.]],
[7144]='Leaderboard',
[7145]=[[Season Rewards]],
[7146]=[[Battle Log]],
[7147]=[[My Rank:]],
[7148]=[[My Points:]],
[7149]=[[Time Remaining:]],
[7150]='Refresh',
[7151]=[[Adjust Lineup]],
[7152]='Register',
[7153]=[[Previous Ranking]],
[7154]=[[Invite Info]],
[7155]=[[Create Team]],
[7156]=[[Team Name]],
[7157]=[[Required CP]],
[7158]='Disband',
[7159]=[[Invite List]],
[7160]=[[Team Invite]],
[7161]=[[Ignore All]],
[7162]=[[Daily Rewards]],
[7163]='CP',
[7164]=[[CP Limit]],
[7165]=[[Enter number.]],
[7166]=[[x %d]],
[7167]=[[No Battle Log found.]],
[7168]=[[You are not in a team right now. You may create your own.]],
[7169]=[[You do not have any join requests.]],
[7170]=[[No team invites found.]],
[7171]=[[No players to invite.]],
[7172]=[[Max Rank reached:]],
[7173]=[[My Rank]],
[7174]=[[My CP]],
[7175]=[[Arena challenge error.]],
[7176]=[[Failed to load the Arena Battle Log.]],
[7177]=[[Failed to load Arena Leaderboard info.]],
[7178]=[[Failed to load the Arena Defense Lineup.]],
[7179]=[[Failed to enter the Arena.]],
[7180]=[[Failed to load Arena opponent list.]],
[7181]=[[This player is not in the team.]],
[7182]=[[This team does not exist.]],
[7183]=[[Failed to fetch mail details.]],
[7184]=[[Daily Settlement Countdown:]],
[7185]='Bronze',
[7186]='Silver',
[7187]='Gold',
[7188]='Platinum',
[7189]='Diamond',
[7190]='Master',
[7191]='King',
[7192]='Arena',
[7193]=[[Advanced Arena]],
[7194]=[[Current Rank]],
[7195]=[[Next Rank]],
[7196]=[[Advanced Arena Medal: Obtained from the Advanced Arena and can be exchanged for heroes in the Advanced Arena Store.]],
[7197]=[[Advanced Arena Medals will be issued to the reward chest of the Advanced Arena every hour based on your rank. The chest can store up to 10,000 medals at a time. Don't forget to claim them!]],
[7198]='Server:',
[7199]=[[To avoid any losses, make sure to claim your challenge chest rewards before challenging again.]],
[7200]=[[You can spend %s %d to instantly refresh the cooldown. Do you want to refresh?]],
[7270]=[[Buff Effect]],
[7401]=[[Your wins will be cleared after the refresh. Refresh?]],
[7402]=[[Owned %s%d]],
[7403]=[[Cooldown %s]],
[7404]=[[Rewards will be granted based on players' ranks at the end of the season. Rewards corresponding to each rank are only granted once upon reaching the rank for the first time.]],
[7405]=[[Arena Points]],
[7406]='Wins',
[7407]='Rank',
[7408]=[[Season Rewards]],
[7409]=[[Battle Rewards]],
[7410]=[[Reach Rank %s to claim]],
[7411]='Legendary',
[7412]=[[Rank rewards claimed]],
[7413]=[[Refresh in cooldown]],
[7414]=[[Refresh doesn't need to be reset]],
[7415]=[[Not enough wins]],
[7416]=[[You have unclaimed challenge chest rewards]],
[7417]=[[Required rank level not met]],
[7418]=[[Chest Rewards]],
[7419]='Defeated',
[7420]=[[Congratulations, your rank went up!]],
[7421]=[[You've reached Rank 1, there's no opponent for you.]],
[7422]=[[Complete %s challenge(s) to unlock Quick Battle.]],
[7423]=[[This hero is in the Crucible lineup. Remove them?]],
[7424]=[[This hero is in the Crucible lineup. Remove them?]],
[7425]=[[This hero is in the defense lineup in Legendary Duel. Remove them?]],
[7426]=[[This hero removed. Please add another hero to your lineup.]],
[7427]=[[This hero is in the Crucible lineup. You need to deploy at least 1 hero to participate in the match. Please adjust your lineup manually.]],
[7428]=[[This hero is in the Crucible lineup. You need to deploy at least 1 hero to participate in the match. Please adjust your lineup manually.]],
[7429]=[[This hero is in the defense lineup in Legendary Duel. You need to deploy at least 1 hero to participate in the match. Please adjust your lineup manually.]],
[7430]=[[Unit Price:]],
[7431]='Owned',
[7432]=[[Your castle is currently in a cross-server state and cannot be entered. Please return to your original server first.]],
[7501]=[[Faction Summon]],
[7502]=[[Hero Substitution]],
[7503]='Summon',
[7504]='Obtain',
[7505]=[[Insufficient Excellent Faction Scrolls. Purchase more from the Giant Merchant.]],
[7506]=[[Insufficient Phasic Particles. Purchase more from the Giant Merchant.]],
[7507]='Confirm',
[7508]='Keep',
[7509]='Convert',
[7601]=[[You have entered fatigue game time. Your points will drop to 50%. For your health, please log out and rest, exercise properly, and use your time in a healthy manner.]],
[7602]=[[You have entered unhealthy game time. Please log out immediately to rest. If you don't, your points will be cut to zero and your health may be affected. Your points will be reset to 100% after you have logged out for over 5 hours.]],
[7603]=[[Log Out]],
[7604]='Confirm',
[7605]=[[Anti-Addiction Warning]],
[7701]=[[Power Up]],
[7702]=[[Divine Tree of Origin]],
[7703]=[[Ultimate Pets Workshop]],
[7704]=[[The Tree House of Bonds]],
[7705]=[[Are you sure you want to activate %s?]],
[7706]='Level:',
[7707]='Slots:',
[7708]=[[Level Cap:]],
[7709]='Upgrade',
[7710]='Capacity:',
[7711]='Amount:',
[7712]='/Day',
[7713]='Plant',
[7714]=[[Divine Tree of Origin Level Requirement:]],
[7715]='Remove',
[7716]=[[Level cap reached.]],
[7717]='Planted!',
[7718]='Removed!',
[7719]=[[Leveled up!]],
[7720]=[[Unlock Pets:]],
[7721]=[[Divine Tree of Origin will be unlocked upon reaching Lv. %d.]],
[7722]=[[Plant <color=#dd3c2dFF>Tree</color>]],
[7723]=[[After you remove a tree:

1. All materials used to upgrade the tree will be refunded.

2. A portion of Gold used to upgrade the tree will be refunded.]],
[7724]=[[Are you sure you want to remove this tree?]],
[7725]='Details',
[7726]='Activate',
[7727]='Skills',
[7728]='Parts',
[7729]=[[Insufficient Gold. You can obtain more from Adventures!]],
[7730]=[[Insufficient Life Crystals. You can obtain more from lucky draws!]],
[7731]=[[Requires Divine Tree of Origin Lv. %d for further upgrades!]],
[7732]=[[No resources under production!]],
[7733]=[[You have reached the cap for this tree. You can't plant any more!]],
[7734]=[[Insufficient materials to plant!]],
[7735]='Recommended',
[7736]='Refresh',
[7737]=[[Request List]],
[7738]='Requests:',
[7739]=[[Request failed. This player has already established a bond with another player!]],
[7740]=[[Bond established!]],
[7741]=[[Player Search]],
[7742]=[[Enter the Player ID.]],
[7743]=[[No Results]],
[7744]=[[Character does not exist. Make sure to check the ID!]],
[7745]=[[Request sent. Wait for the player to respond!]],
[7746]=[[Bond Level:]],
[7747]='Visit',
[7748]='Bond',
[7749]='Cancel',
[7750]=[[Bond Bonus]],
[7751]=[[Pet Part Bonus]],
[7752]=[[Plant Production Bonus]],
[7753]=[[Cancel Bond]],
[7754]=[[After Bond Cancellation:]],
[7755]=[[1. The Bond Level will be reduced to 0.]],
[7756]=[[2. You will no longer receive any bond bonuses.]],
[7757]=[[Are you sure you want to cancel the bond?]],
[7758]=[[Bond EXP]],
[7759]='Log',
[7760]=[[%s helped water your plants. Both of you have obtained additional rewards!]],
[7761]=[[%s helped check on your pets. Both of you have obtained additional rewards!]],
[7762]=[[%s helped you defeat invading monsters. Both of you have obtained additional rewards!]],
[7763]=[[Request failed. This player has already established a bond with another player!]],
[7764]=[[You cannot send multiple requests!]],
[7765]=[[Request rejected!]],
[7766]=[[Insufficient Stamina. Try again later!]],
[7767]=[[Invading Monsters]],
[7768]=[[Battle Rewards]],
[7769]=[[No new record!]],
[7770]='Pets',
[7771]=[[Pet List]],
[7772]=[[You canceled your bond with %s!]],
[7773]=[[Don't trespass on someone else's property!]],
[7774]=[[%s defeated the invading monsters. Both of you have obtained additional rewards!]],
[7775]=[[%s watered their plants. Both of you have obtained additional rewards!]],
[7776]=[[%s checked on their pets. Both of you have obtained additional rewards!]],
[7777]=[[Insufficient %s. You can obtain more from the Well of Life!]],
[7778]=[[Establish Bond]],
[7779]=[[Cancel Bond]],
[7780]='Garden',
[7781]=[[Quick Level]],
[7801]=[[Gold Mine]],
[7802]=[[Diamond Mine]],
[7803]=[[Pure Essence Mine]],
[7804]=[[Microchip Mine]],
[7805]=[[Well of Life]],
[7806]=[[Magical Tree]],
[7807]=[[Divine Tree of Origin will be unlocked upon reaching Lv. %d.]],
[7808]=[[Pet Reset]],
[7809]=[[After Resetting a Pet:

1. All materials used to upgrade the Pet will be refunded.

2. The Pet will be reset to its original form.]],
[7810]=[[Are you sure you want to reset this pet?]],
[7811]=[[Activation Successful!]],
[7812]=[[Skills will be unlocked upon reaching Lv. %s.]],
[7813]='Gear',
[7814]=[[Already equipped by %d.]],
[7815]=[[Divine Tree of Origin will be unlocked upon reaching Lv. %d.]],
[7816]='Info',
[7817]=[[Pet: Borias]],
[7818]=[[Pets are automatically activated when obtained.]],
[7901]=[[Obtain From]],
[7902]=[[Enhance Hero]],
[7903]=[[Obtain From]],
[7904]=[[Use Gear]],
[7905]=[[Obtain From]],
[7906]=[[Upgrade Skill]],
[7907]=[[How to become stronger?]],
[7908]=[[Based on your current level, this is how strong you are right now.]],
[7909]=[[Other Resources]],
[7910]='Heroes',
[7911]='Gear',
[7912]='Skills',
[7913]='Pets',
[7914]='Team',
[7915]='Beastkin',
[7916]=[[Power Up]],
[7917]=[[Are you sure you want to keep going?]],
[8001]=[[Legendary Duel]],
[8002]=[[Registration ends in:]],
[8003]=[[Ends in:]],
[8004]=[[Silver Group]],
[8005]=[[Gold Group]],
[8006]=[[Diamond Group]],
[8007]=[[Dominator Group]],
[8008]=[[Legend Group]],
[8009]='Register',
[8010]=[[Point Match]],
[8011]=[[Legend Store]],
[8012]=[[When in %s teams, deploy at least %d heroes. ]],
[8013]=[[Only heroes above %s can be deployed.]],
[8014]=[[Unlocked after clearing %s.]],
[8015]=[[Team Settings]],
[8016]=[[Are you sure you want to register for <color=#d48e13>%s</color>?]],
[8017]=[[<color=#f9988a>(You cannot change season group upon registration)
Season ends in: <color=#d48e13>%s</color></color>]],
[8018]=[[Deploy at least 1 qualified hero in each team.]],
[8019]=[[Registration successful! Wait for the Point Match to begin!]],
[8020]='Registered',
[8021]='Ongoing...',
[8022]=[[Begins in %s]],
[8023]=[[Match with players from other servers to obtain S+ heroes!]],
[8024]='Register',
[8025]='Countdown',
[8026]='Rewards',
[8027]='Defense',
[8028]=[[Battle Report]],
[8029]=[[Drag teams to adjust their sequence.]],
[8030]='Quest',
[8031]=[[Skip Battle]],
[8032]=[[Remaining Challenges:]],
[8033]=[[Battle Log]],
[8034]='Challenger',
[8035]=[[ minute(s) ago]],
[8036]=[[ hour(s) ago]],
[8037]=[[ day(s) ago]],
[8038]=[[Revenge taken.]],
[8039]=[[Register for different groups to get medals that can be exchanged for various heroes in the Legend Store.]],
[8040]=[[Rank rewards will be granted when the season ends. Players will receive their unclaimed rewards automatically when entering the Legendary Duel again in the next season.]],
[8041]=[[Previous Season]],
[8042]=[[Medals obtained from the Legendary Duel can be exchanged for heroes at different counters!]],
[8043]=[[1. Each season of Legendary Duel lasts for 7 days. Players can register for any group in the first 2 days (registration phase). The higher the group level, the more the teams and stronger the heroes required. And of course, the more amazing the rewards.]],
[8044]=[[2. After the registration phase, players will engage in point matches with other players in the same group. The point matches last for 5 days and rewards are granted based on players' point rankings. The higher the rank, the better the rewards.]],
[8045]=[[3. Medals can be exchanged for heroes in the Legend Store. Go to different counters to exchange for different heroes at different costs. Participating in higher-level matches grants better Medals that can be exchanged for higher-grade heroes.]],
[8046]=[[1. The point match phase lasts for 5 days. You can challenge other players in the same group 2 times each day for free. After that, challenges will cost Legendary Tournament tickets. Tickets can be obtained from Daily Quest activity chests or purchased with Diamonds. When challenged multiple times in a day by the same player, up to 3 battle logs related to that player can be saved.]],
[8047]=[[2. The heroes in the scoring match will be adjusted to the highest level of the hero's current quality. If the battle is over 15 rounds, the defending team will win.]],
[8048]=[[3. The winner will gain Season Points while the loser will lose Season Points. The bigger the point difference between both sides, the more the points are granted or lost.]],
[8049]=[[4. When the season ends, rewards will be granted based on players' point ranking. The higher the rank, the better the rewards. Players can claim rewards on the point match rewards interface.]],
[8050]=[[5. Season Points will be cleared and recalculated at the start of a new season. Players will get their unclaimed rewards from the previous season automatically when entering Legendary Duel again.]],
[8051]=[[Point Match hasn't started yet]],
[8052]=[[Registration hasn't started yet]],
[8053]=[[You need %s heroes of %s quality or above to register]],
[8054]=[[%s Exchange Counter]],
[8055]=[[Hero is in the defense lineup in Legendary Duel.]],
[8056]=[[Gold Medal]],
[8057]=[[Diamond Medal]],
[8058]=[[King Medal]],
[8059]=[[Legend Medal]],
[8060]=[[The exclusive Medal for the Silver Group in Legendary Duel. Can be exchanged for heroes in Legend Store - Silver Exchange Counter!]],
[8061]=[[The exclusive Medal for the Gold Group in Legendary Duel. Can be exchanged for heroes in Legend Store - Gold Exchange Counter!]],
[8062]=[[The exclusive Medal for the Diamond Group in Legendary Duel. Can be exchanged for heroes in Legend Store - Diamond Exchange Counter!]],
[8063]=[[The exclusive Medal for the Dominator Group in Legendary Duel. Can be exchanged for heroes in Legend Store - Dominator Exchange Counter!]],
[8064]=[[Team info hidden]],
[8065]=[[Silver Medal]],
[8066]='Silver',
[8067]='Gold',
[8068]='Diamond',
[8069]='King',
[8070]='Legendary',
[8071]=[[Next Season:]],
[8072]=[[Trial Group]],
[8073]=[[The exclusive Medal for the Legend Group in Legendary Duel. Can be exchanged for heroes in Legend Store - Legend Exchange Counter!]],
[8074]=[[Registration successful! Challenge opponents in point matches to win points!]],
[8075]=[[Medals can be exchanged for powerful heroes in the Legend Store. Go check it out!]],
[8076]=[[Challenge 5 times in the Trial/Silver Group.]],
[8077]=[[Challenge 10 times in the Trial/Silver Group.]],
[8078]=[[Challenge 20 times in the Gold Group.]],
[8079]=[[Win 50 times in the Gold Group.]],
[8080]=[[Challenge 40 times in the Diamond Group.]],
[8081]=[[Win 80 times in the Diamond Group.]],
[8082]=[[Challenge 60 times in the Dominator Group.]],
[8083]=[[Win 100 times in the Dominator Group.]],
[8084]=[[Challenge 80 times in the Legend Group.]],
[8085]=[[Win 120 times in the Legend Group.]],
[8086]=[[Searching for more opponents...]],
[8087]=[[Hide hero info in Teams 2–3]],
[8088]=[[Hide hero info in Teams 3–5]],
[8089]=[[Congrats! Your rank went up and you got more season rewards!]],
[8090]=[[Oh no! Your rank went down and you got fewer season rewards!]],
[8091]=[[Adjust Lineup]],
[8092]=[[Start Battle]],
[8093]=[[Legendary Duel Rewards]],
[8094]=[[Dear Traveler, you have unclaimed Legendary Duel rewards. They have now been reissued to you via mail. Please check!]],
[8095]=[[Too many requests. Please try again in %ss!]],
[8096]=[[Syncing data. Please try again later!]],
[8097]='Tip',
[8098]=[[This hero is in the Legendary Duel Defense Lineup. Remove the hero from the lineup first!]],
[8101]='Bahamut',
[8102]='Tiamat',
[8103]=[[Horn of Bahamut]],
[8104]=[[Claw of Bahamut]],
[8105]=[[Wing of Bahamut]],
[8106]=[[Tail of Bahamut]],
[8107]=[[Horn of Tiamat]],
[8108]=[[Claw of Tiamat]],
[8109]=[[Tail of Tiamat]],
[8110]=[[Fin of Tiamat]],
[8200]=[[Mystic Beast's Wrath]],
[8201]=[[Deal <color=#3A6AA9>15,909</color> DMG to 3 random enemies.]],
[8202]=[[Deal <color=#3A6AA9>16,775</color> DMG to 3 random enemies.]],
[8203]=[[Deal <color=#3A6AA9>17,641</color> DMG to 3 random enemies.]],
[8204]=[[Deal <color=#3A6AA9>18,507</color> DMG to 3 random enemies.]],
[8205]=[[Deal <color=#3A6AA9>19,373</color> DMG to 3 random enemies.]],
[8206]=[[Deal <color=#3A6AA9>20,239</color> DMG to 3 random enemies.]],
[8207]=[[Deal <color=#3A6AA9>21,105</color> DMG to 3 random enemies.]],
[8208]=[[Deal <color=#3A6AA9>21,971</color> DMG to 3 random enemies.]],
[8209]=[[Deal <color=#3A6AA9>22,837</color> DMG to 3 random enemies.]],
[8210]=[[Deal <color=#3A6AA9>23,703</color> DMG to 3 random enemies.]],
[8211]=[[Deal <color=#3A6AA9>24,569</color> DMG to 3 random enemies.]],
[8212]=[[Deal <color=#3A6AA9>25,435</color> DMG to 3 random enemies.]],
[8213]=[[Deal <color=#3A6AA9>26,301</color> DMG to 3 random enemies.]],
[8214]=[[Deal <color=#3A6AA9>27,167</color> DMG to 3 random enemies.]],
[8215]=[[Deal <color=#3A6AA9>28,033</color> DMG to 3 random enemies.]],
[8216]=[[Deal <color=#3A6AA9>28,899</color> DMG to 3 random enemies.]],
[8217]=[[Deal <color=#3A6AA9>29,765</color> DMG to 3 random enemies.]],
[8218]=[[Deal <color=#3A6AA9>30,631</color> DMG to 3 random enemies.]],
[8219]=[[Deal <color=#3A6AA9>31,497</color> DMG to 3 random enemies.]],
[8220]=[[Deal <color=#3A6AA9>32,363</color> DMG to 3 random enemies.]],
[8221]=[[Deal <color=#3A6AA9>33,229</color> DMG to 3 random enemies.]],
[8222]=[[Deal <color=#3A6AA9>34,095</color> DMG to 3 random enemies.]],
[8223]=[[Deal <color=#3A6AA9>34,961</color> DMG to 3 random enemies.]],
[8224]=[[Deal <color=#3A6AA9>35,827</color> DMG to 3 random enemies.]],
[8225]=[[Deal <color=#3A6AA9>36,693</color> DMG to 3 random enemies.]],
[8226]=[[Deal <color=#3A6AA9>37,559</color> DMG to 3 random enemies.]],
[8227]=[[Deal <color=#3A6AA9>38,425</color> DMG to 3 random enemies.]],
[8228]=[[Deal <color=#3A6AA9>39,291</color> DMG to 3 random enemies.]],
[8229]=[[Deal <color=#3A6AA9>40,157</color> DMG to 3 random enemies.]],
[8230]=[[Deal <color=#3A6AA9>40,183</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>26,788</color> DMG every turn for 3 turns.]],
[8231]=[[Deal <color=#3A6AA9>43,064</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>28,709</color> DMG every turn for 3 turns.]],
[8232]=[[Deal <color=#3A6AA9>45,945</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>30,630</color> DMG every turn for 3 turns.]],
[8233]=[[Deal <color=#3A6AA9>48,826</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>32,550</color> DMG every turn for 3 turns.]],
[8234]=[[Deal <color=#3A6AA9>51,707</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>34,471</color> DMG every turn for 3 turns.]],
[8235]=[[Deal <color=#3A6AA9>54,588</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>36,392</color> DMG every turn for 3 turns.]],
[8236]=[[Deal <color=#3A6AA9>57,469</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>38,312</color> DMG every turn for 3 turns.]],
[8237]=[[Deal <color=#3A6AA9>60,350</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>40,233</color> DMG every turn for 3 turns.]],
[8238]=[[Deal <color=#3A6AA9>63,231</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>42,154</color> DMG every turn for 3 turns.]],
[8239]=[[Deal <color=#3A6AA9>66,112</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>44,074</color> DMG every turn for 3 turns.]],
[8240]=[[Deal <color=#3A6AA9>68,993</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>45,995</color> DMG every turn for 3 turns.]],
[8241]=[[Deal <color=#3A6AA9>71,874</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>47,916</color> DMG every turn for 3 turns.]],
[8242]=[[Deal <color=#3A6AA9>74,755</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>49,836</color> DMG every turn for 3 turns.]],
[8243]=[[Deal <color=#3A6AA9>77,636</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>51,757</color> DMG every turn for 3 turns.]],
[8244]=[[Deal <color=#3A6AA9>80,517</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>53,678</color> DMG every turn for 3 turns.]],
[8245]=[[Deal <color=#3A6AA9>83,398</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>55,598</color> DMG every turn for 3 turns.]],
[8246]=[[Deal <color=#3A6AA9>86,279</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>57,519</color> DMG every turn for 3 turns.]],
[8247]=[[Deal <color=#3A6AA9>89,160</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>59,440</color> DMG every turn for 3 turns.]],
[8248]=[[Deal <color=#3A6AA9>92,041</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>61,360</color> DMG every turn for 3 turns.]],
[8249]=[[Deal <color=#3A6AA9>94,922</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>63,281</color> DMG every turn for 3 turns.]],
[8250]=[[Deal <color=#3A6AA9>97,803</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>65,202</color> DMG every turn for 3 turns.]],
[8251]=[[Deal <color=#3A6AA9>100,684</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>67,122</color> DMG every turn for 3 turns.]],
[8252]=[[Deal <color=#3A6AA9>103,565</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>69,043</color> DMG every turn for 3 turns.]],
[8253]=[[Deal <color=#3A6AA9>106,446</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>70,964</color> DMG every turn for 3 turns.]],
[8254]=[[Deal <color=#3A6AA9>109,327</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>72,884</color> DMG every turn for 3 turns.]],
[8255]=[[Deal <color=#3A6AA9>112,208</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>74,805</color> DMG every turn for 3 turns.]],
[8256]=[[Deal <color=#3A6AA9>115,089</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>76,726</color> DMG every turn for 3 turns.]],
[8257]=[[Deal <color=#3A6AA9>117,970</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>78,646</color> DMG every turn for 3 turns.]],
[8258]=[[Deal <color=#3A6AA9>120,851</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>80,567</color> DMG every turn for 3 turns.]],
[8259]=[[Deal <color=#3A6AA9>123,732</color> DMG to 3 random enemies and burn them, inflicting <color=#3A6AA9>82,488</color> DMG every turn for 3 turns.]],
[8260]=[[Deal <color=#3A6AA9>123,745</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>82,496</color> DMG every turn for 3 turns.]],
[8261]=[[Deal <color=#3A6AA9>132,389</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>88,259</color> DMG every turn for 3 turns.]],
[8262]=[[Deal <color=#3A6AA9>141,033</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>94,022</color> DMG every turn for 3 turns.]],
[8263]=[[Deal <color=#3A6AA9>149,677</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>99,784</color> DMG every turn for 3 turns.]],
[8264]=[[Deal <color=#3A6AA9>158,321</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>105,547</color> DMG every turn for 3 turns.]],
[8265]=[[Deal <color=#3A6AA9>166,965</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>111,310</color> DMG every turn for 3 turns.]],
[8266]=[[Deal <color=#3A6AA9>175,609</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>117,072</color> DMG every turn for 3 turns.]],
[8267]=[[Deal <color=#3A6AA9>184,253</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>122,835</color> DMG every turn for 3 turns.]],
[8268]=[[Deal <color=#3A6AA9>192,897</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>128,598</color> DMG every turn for 3 turns.]],
[8269]=[[Deal <color=#3A6AA9>201,541</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>134,360</color> DMG every turn for 3 turns.]],
[8270]=[[Deal <color=#3A6AA9>210,185</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>140,123</color> DMG every turn for 3 turns.]],
[8271]=[[Deal <color=#3A6AA9>218,829</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>145,886</color> DMG every turn for 3 turns.]],
[8272]=[[Deal <color=#3A6AA9>227,473</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>151,648</color> DMG every turn for 3 turns.]],
[8273]=[[Deal <color=#3A6AA9>236,117</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>157,411</color> DMG every turn for 3 turns.]],
[8274]=[[Deal <color=#3A6AA9>244,761</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>163,174</color> DMG every turn for 3 turns.]],
[8275]=[[Deal <color=#3A6AA9>253,405</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>168,936</color> DMG every turn for 3 turns.]],
[8276]=[[Deal <color=#3A6AA9>262,049</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>174,699</color> DMG every turn for 3 turns.]],
[8277]=[[Deal <color=#3A6AA9>270,693</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>180,462</color> DMG every turn for 3 turns.]],
[8278]=[[Deal <color=#3A6AA9>279,337</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>186,224</color> DMG every turn for 3 turns.]],
[8279]=[[Deal <color=#3A6AA9>287,981</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>191,987</color> DMG every turn for 3 turns.]],
[8280]=[[Deal <color=#3A6AA9>296,625</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>197,750</color> DMG every turn for 3 turns.]],
[8281]=[[Deal <color=#3A6AA9>305,269</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>203,512</color> DMG every turn for 3 turns.]],
[8282]=[[Deal <color=#3A6AA9>313,913</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>209,275</color> DMG every turn for 3 turns.]],
[8283]=[[Deal <color=#3A6AA9>322,557</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>215,038</color> DMG every turn for 3 turns.]],
[8284]=[[Deal <color=#3A6AA9>331,201</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>220,800</color> DMG every turn for 3 turns.]],
[8285]=[[Deal <color=#3A6AA9>339,845</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>226,563</color> DMG every turn for 3 turns.]],
[8286]=[[Deal <color=#3A6AA9>348,489</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>232,326</color> DMG every turn for 3 turns.]],
[8287]=[[Deal <color=#3A6AA9>357,133</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>238,088</color> DMG every turn for 3 turns.]],
[8288]=[[Deal <color=#3A6AA9>365,777</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>243,851</color> DMG every turn for 3 turns.]],
[8289]=[[Deal <color=#3A6AA9>374,421</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>249,614</color> DMG every turn for 3 turns.]],
[8290]=[[Deal <color=#3A6AA9>383,065</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>255,376</color> DMG every turn for 3 turns and increasing all allies' ATK by 15% for 2 turns.]],
[8291]=[[Deal <color=#3A6AA9>391,709</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>261,139</color> DMG every turn for 3 turns and increasing all allies' ATK by 15% for 2 turns.]],
[8292]=[[Deal <color=#3A6AA9>400,353</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>266,902</color> DMG every turn for 3 turns and increasing all allies' ATK by 15% for 2 turns.]],
[8293]=[[Deal <color=#3A6AA9>408,997</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>272,664</color> DMG every turn for 3 turns and increasing all allies' ATK by 15% for 2 turns.]],
[8294]=[[Deal <color=#3A6AA9>417,641</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>278,427</color> DMG every turn for 3 turns and increasing all allies' ATK by 15% for 2 turns.]],
[8295]=[[Deal <color=#3A6AA9>426,285</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>284,190</color> DMG every turn for 3 turns and increasing all allies' ATK by 15% for 2 turns.]],
[8296]=[[Deal <color=#3A6AA9>434929</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>289952</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8297]=[[Deal <color=#3A6AA9>443573</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>295715</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8298]=[[Deal <color=#3A6AA9>452217</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>301478</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8299]=[[Deal <color=#3A6AA9>460861</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>307240</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8300]=[[Deal <color=#3A6AA9>469505</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>313003</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8301]=[[Deal <color=#3A6AA9>478149</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>318766</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8302]=[[Deal <color=#3A6AA9>486793</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>324528</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8303]=[[Deal <color=#3A6AA9>495437</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>330291</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8304]=[[Deal <color=#3A6AA9>504081</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>336054</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8305]=[[Deal <color=#3A6AA9>512725</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>341816</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8306]=[[Deal <color=#3A6AA9>521369</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>347579</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8307]=[[Deal <color=#3A6AA9>530013</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>353342</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8308]=[[Deal <color=#3A6AA9>538657</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>359104</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8309]=[[Deal <color=#3A6AA9>547301</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>364867</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8310]=[[Deal <color=#3A6AA9>555945</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>370630</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8311]=[[Deal <color=#3A6AA9>564589</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>376392</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8312]=[[Deal <color=#3A6AA9>573233</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>382155</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8313]=[[Deal <color=#3A6AA9>581877</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>387918</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8314]=[[Deal <color=#3A6AA9>590521</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>393680</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8315]=[[Deal <color=#3A6AA9>599165</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>399443</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8316]=[[Deal <color=#3A6AA9>607809</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>405206</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8317]=[[Deal <color=#3A6AA9>616453</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>410968</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8318]=[[Deal <color=#3A6AA9>625097</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>416731</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8319]=[[Deal <color=#3A6AA9>633741</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>422494</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% for 2 turns.]],
[8320]=[[Deal <color=#3A6AA9>642385</color> DMG to all enemies and burn them, inflicting <color=#3A6AA9>428256</color> DMG every turn for 3 turns. Increase the ATK of all allies by 15% and their CRIT by 10% for 2 turns.]],
[8400]=[[Tidal Shock]],
[8401]=[[Deal <color=#3A6AA9>11136</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>6443</color>.]],
[8402]=[[Deal <color=#3A6AA9>11742</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>6740</color>.]],
[8403]=[[Deal <color=#3A6AA9>12348</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>7032</color>.]],
[8404]=[[Deal <color=#3A6AA9>12954</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>7319</color>.]],
[8405]=[[Deal <color=#3A6AA9>13561</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>7600</color>.]],
[8406]=[[Deal <color=#3A6AA9>14167</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>7876</color>.]],
[8407]=[[Deal <color=#3A6AA9>14773</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>8146</color>.]],
[8408]=[[Deal <color=#3A6AA9>15379</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>8411</color>.]],
[8409]=[[Deal <color=#3A6AA9>15985</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>8670</color>.]],
[8410]=[[Deal <color=#3A6AA9>16592</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>8924</color>.]],
[8411]=[[Deal <color=#3A6AA9>17198</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>9172</color>.]],
[8412]=[[Deal <color=#3A6AA9>17804</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>9415</color>.]],
[8413]=[[Deal <color=#3A6AA9>18410</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>9652</color>.]],
[8414]=[[Deal <color=#3A6AA9>19016</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>9884</color>.]],
[8415]=[[Deal <color=#3A6AA9>19623</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>10110</color>.]],
[8416]=[[Deal <color=#3A6AA9>20229</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>10331</color>.]],
[8417]=[[Deal <color=#3A6AA9>20835</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>10546</color>.]],
[8418]=[[Deal <color=#3A6AA9>21441</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>10756</color>.]],
[8419]=[[Deal <color=#3A6AA9>22047</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>10960</color>.]],
[8420]=[[Deal <color=#3A6AA9>22654</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>11159</color>.]],
[8421]=[[Deal <color=#3A6AA9>23260</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>11353</color>.]],
[8422]=[[Deal <color=#3A6AA9>23866</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>11541</color>.]],
[8423]=[[Deal <color=#3A6AA9>24472</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>11723</color>.]],
[8424]=[[Deal <color=#3A6AA9>25078</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>11900</color>.]],
[8425]=[[Deal <color=#3A6AA9>25685</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>12071</color>.]],
[8426]=[[Deal <color=#3A6AA9>26291</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>12237</color>.]],
[8427]=[[Deal <color=#3A6AA9>26897</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>12398</color>.]],
[8428]=[[Deal <color=#3A6AA9>27503</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>12553</color>.]],
[8429]=[[Deal <color=#3A6AA9>28109</color> DMG to enemies in the front row and heal the 3 allies with the lowest HP by <color=#3A6AA9>12702</color>.]],
[8430]=[[Deal <color=#3A6AA9>28128</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>12054</color>.]],
[8431]=[[Deal <color=#3A6AA9>30144</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>12919</color>.]],
[8432]=[[Deal <color=#3A6AA9>32161</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>13783</color>.]],
[8433]=[[Deal <color=#3A6AA9>34178</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>14647</color>.]],
[8434]=[[Deal <color=#3A6AA9>36194</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>15512</color>.]],
[8435]=[[Deal <color=#3A6AA9>38211</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>16376</color>.]],
[8436]=[[Deal <color=#3A6AA9>40228</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>17240</color>.]],
[8437]=[[Deal <color=#3A6AA9>42245</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>18105</color>.]],
[8438]=[[Deal <color=#3A6AA9>44261</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>18969</color>.]],
[8439]=[[Deal <color=#3A6AA9>46278</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>19833</color>.]],
[8440]=[[Deal <color=#3A6AA9>48295</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>20697</color>.]],
[8441]=[[Deal <color=#3A6AA9>50311</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>21562</color>.]],
[8442]=[[Deal <color=#3A6AA9>52328</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>22426</color>.]],
[8443]=[[Deal <color=#3A6AA9>54345</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>23290</color>.]],
[8444]=[[Deal <color=#3A6AA9>56361</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>24155</color>.]],
[8445]=[[Deal <color=#3A6AA9>58378</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>25019</color>.]],
[8446]=[[Deal <color=#3A6AA9>60395</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>25883</color>.]],
[8447]=[[Deal <color=#3A6AA9>62412</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>26748</color>.]],
[8448]=[[Deal <color=#3A6AA9>64428</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>27612</color>.]],
[8449]=[[Deal <color=#3A6AA9>66445</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>28476</color>.]],
[8450]=[[Deal <color=#3A6AA9>68462</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>29340</color>.]],
[8451]=[[Deal <color=#3A6AA9>70478</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>30205</color>.]],
[8452]=[[Deal <color=#3A6AA9>72495</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>31069</color>.]],
[8453]=[[Deal <color=#3A6AA9>74512</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>31933</color>.]],
[8454]=[[Deal <color=#3A6AA9>76528</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>32798</color>.]],
[8455]=[[Deal <color=#3A6AA9>78545</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>33662</color>.]],
[8456]=[[Deal <color=#3A6AA9>80562</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>34526</color>.]],
[8457]=[[Deal <color=#3A6AA9>82579</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>35391</color>.]],
[8458]=[[Deal <color=#3A6AA9>84595</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>36255</color>.]],
[8459]=[[Deal <color=#3A6AA9>86612</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal the 3 allies with the lowest HP by <color=#3A6AA9>37119</color>.]],
[8460]=[[Deals <color=#3A6AA9>86621</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>37123</color> HP to the all allies.]],
[8461]=[[Deals <color=#3A6AA9>92672</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>39716</color> HP to the all allies.]],
[8462]=[[Deals <color=#3A6AA9>98,723</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>42,309</color> HP to the all allies.]],
[8463]=[[Deals <color=#3A6AA9>104773</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>44903</color> HP to the all allies.]],
[8464]=[[Deals <color=#3A6AA9>110824</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>47496</color> HP to the all allies.]],
[8465]=[[Deals <color=#3A6AA9>116875</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>50089</color> HP to the all allies.]],
[8466]=[[Deals <color=#3A6AA9>122926</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>52682</color> HP to the all allies.]],
[8467]=[[Deals <color=#3A6AA9>128977</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>55275</color> HP to the all allies.]],
[8468]=[[Deals <color=#3A6AA9>135027</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>57869</color> HP to the all allies.]],
[8469]=[[Deals <color=#3A6AA9>141078</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>60462</color> HP to the all allies.]],
[8470]=[[Deals <color=#3A6AA9>147129</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>63055</color> HP to the all allies.]],
[8471]=[[Deals <color=#3A6AA9>153180</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>65648</color> HP to the all allies.]],
[8472]=[[Deals <color=#3A6AA9>159231</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>68241</color> HP to the all allies.]],
[8473]=[[Deals <color=#3A6AA9>165281</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>70835</color> HP to the all allies.]],
[8474]=[[Deals <color=#3A6AA9>171332</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>73428</color> HP to the all allies.]],
[8475]=[[Deals <color=#3A6AA9>177383</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>76021</color> HP to the all allies.]],
[8476]=[[Deals <color=#3A6AA9>183434</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>78614</color> HP to the all allies.]],
[8477]=[[Deals <color=#3A6AA9>189485</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>81207</color> HP to the all allies.]],
[8478]=[[Deals <color=#3A6AA9>195535</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>83801</color> HP to the all allies.]],
[8479]=[[Deals <color=#3A6AA9>201586</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>86394</color> HP to the all allies.]],
[8480]=[[Deals <color=#3A6AA9>207637</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>88987</color> HP to the all allies.]],
[8481]=[[Deals <color=#3A6AA9>213688</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>91580</color> HP to the all allies.]],
[8482]=[[Deals <color=#3A6AA9>219739</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>94173</color> HP to the all allies.]],
[8483]=[[Deals <color=#3A6AA9>225789</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>96767</color> HP to the all allies.]],
[8484]=[[Deals <color=#3A6AA9>231840</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>99360</color> HP to the all allies.]],
[8485]=[[Deals <color=#3A6AA9>237891</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>101953</color> HP to the all allies.]],
[8486]=[[Deals <color=#3A6AA9>243942</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>104546</color> HP to the all allies.]],
[8487]=[[Deals <color=#3A6AA9>249993</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>107139</color> HP to the all allies.]],
[8488]=[[Deals <color=#3A6AA9>256043</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>109733</color> HP to the all allies.]],
[8489]=[[Deals <color=#3A6AA9>262094</color> damage to front row enemies. Also increases all allies' armor by 30% for 2 turns, and restores <color=#3A6AA9>112326</color> HP to the all allies.]],
[8490]=[[Deal <color=#3A6AA9>268145</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>112329</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8491]=[[Deal <color=#3A6AA9>274196</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>117512</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8492]=[[Deal <color=#3A6AA9>280247</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>120105</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8493]=[[Deal <color=#3A6AA9>286297</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>122699</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8494]=[[Deal <color=#3A6AA9>292348</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>125292</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8495]=[[Deal <color=#3A6AA9>298399</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>127885</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8496]=[[Deal <color=#3A6AA9>304450</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>130478</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8497]=[[Deal <color=#3A6AA9>310501</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>133071</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8498]=[[Deal <color=#3A6AA9>316551</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>135665</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8499]=[[Deal <color=#3A6AA9>322602</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>138258</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8500]=[[Deal <color=#3A6AA9>328653</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>140851</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8501]=[[Deal <color=#3A6AA9>334704</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>143444</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8502]=[[Deal <color=#3A6AA9>340755</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>146037</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8503]=[[Deal <color=#3A6AA9>346805</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>148631</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8504]=[[Deal <color=#3A6AA9>352856</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>151224</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8505]=[[Deal <color=#3A6AA9>358907</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>153817</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8506]=[[Deal <color=#3A6AA9>364958</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>156410</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8507]=[[Deal <color=#3A6AA9>371009</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>159003</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8508]=[[Deal <color=#3A6AA9>377059</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>161597</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8509]=[[Deal <color=#3A6AA9>383110</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>164190</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8510]=[[Deal <color=#3A6AA9>389161</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>166783</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8511]=[[Deal <color=#3A6AA9>395212</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>169376</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8512]=[[Deal <color=#3A6AA9>401263</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>171969</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8513]=[[Deal <color=#3A6AA9>407313</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>174563</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8514]=[[Deal <color=#3A6AA9>413364</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>177156</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8515]=[[Deal <color=#3A6AA9>419415</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>179749</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8516]=[[Deal <color=#3A6AA9>425466</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>182342</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8517]=[[Deal <color=#3A6AA9>431517</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>184935</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8518]=[[Deal <color=#3A6AA9>437567</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>187529</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8519]=[[Deal <color=#3A6AA9>443618</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>190122</color> and restore 5% of their Max HP every turn for 3 turns.]],
[8520]=[[Deal <color=#3A6AA9>449669</color> DMG to enemies in the front row and increase the Armor of all allied heroes by 30% for 2 turns. Heal all allies by <color=#3A6AA9>192715</color> and restore 5% of their Max HP every turn for 3 turns. Increase the received healing of all allies by 10%.]],
[8521]=[[Horn of BoRias]],
[8522]=[[Crown of BoRias]],
[8523]=[[Fluff of BoRias]],
[8524]=[[Tail of BoRias]],
[8525]=[[Ice Roar]],
[8526]='Borias',
[8527]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>15909</color> DMG.]],
[8528]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>16775</color> DMG.]],
[8529]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>17641</color> DMG.]],
[8530]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>18507</color> DMG.]],
[8531]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>19373</color> DMG.]],
[8532]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>20239</color> DMG.]],
[8533]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>21105</color> DMG.]],
[8534]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>21971</color> DMG.]],
[8535]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>22837</color> DMG.]],
[8536]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>23703</color> DMG.]],
[8537]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>24569</color> DMG.]],
[8538]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>25435</color> DMG.]],
[8539]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>26301</color> DMG.]],
[8540]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>27167</color> DMG.]],
[8541]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>28033</color> DMG.]],
[8542]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>28899</color> DMG.]],
[8543]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>29765</color> DMG.]],
[8544]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>30631</color> DMG.]],
[8545]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>31497</color> DMG.]],
[8546]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>32363</color> DMG.]],
[8547]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>33229</color> DMG.]],
[8548]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>34095</color> DMG.]],
[8549]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>34961</color> DMG.]],
[8550]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>35827</color> DMG.]],
[8551]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>36693</color> DMG.]],
[8552]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>37559</color> DMG.]],
[8553]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>38425</color> DMG.]],
[8554]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>39291</color> DMG.]],
[8555]=[[Launch <color=#3A6AA9>3</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>40157</color> DMG.]],
[8556]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>70319</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8557]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>75361</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8558]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>80403</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8559]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>85444</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8560]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>90486</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8561]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>95529</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8562]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>100569</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8563]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>105612</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8564]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>110654</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8565]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>115695</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8566]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>120737</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8567]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>125779</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8568]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>130820</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8569]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>135862</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8570]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>140904</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8571]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>145945</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8572]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>150987</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8573]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>156030</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8574]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>161070</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8575]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>166113</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8576]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>171155</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8577]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>176196</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8578]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>181238</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8579]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>186280</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8580]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>191321</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8581]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>196363</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8582]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>201405</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8583]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>206446</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8584]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>211488</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8585]=[[Launch <color=#3A6AA9>4</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>216531</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 300K).]],
[8586]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>173242</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8587]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>185344</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8588]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>197446</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8589]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>209547</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8590]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>221649</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8591]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>233751</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8592]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>245851</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8593]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>257953</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8594]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>270055</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8595]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>282156</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8596]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>294258</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8597]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>306360</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8598]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>318461</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8599]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>330563</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8600]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>342665</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8601]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>354766</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8602]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>366868</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8603]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>378970</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8604]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>391071</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8605]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>403173</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8606]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>415275</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8607]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>427375</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8608]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>439477</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8609]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>451579</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8610]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>463680</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8611]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>475782</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8612]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>487884</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8613]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>499985</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8614]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>512087</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8615]=[[Launch <color=#3A6AA9>5</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>524189</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 600K).]],
[8616]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>446908</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8617]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>456993</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8618]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>467078</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8619]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>477162</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8620]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>487247</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8621]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>497332</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8622]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>507416</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8623]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>517501</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8624]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>527586</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8625]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>537670</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8626]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>547755</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8627]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>557840</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8628]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>567924</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8629]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>578009</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8630]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>588094</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8631]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>598178</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8632]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>608263</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8633]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>618348</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8634]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>628432</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8635]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>638517</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8636]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>648602</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8637]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>658686</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8638]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>668771</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8639]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>678856</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8640]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>688940</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8641]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>699025</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8642]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>709110</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8643]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>719194</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8644]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>729279</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8645]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>739364</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 800K). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG.]],
[8646]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, with each attack dealing <color=#3A6AA9>749448</color> DMG and reducing the target's Max HP by <color=#3A6AA9>2%</color> (total DMG cannot exceed 1M). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8647]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>757650</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8648]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>765852</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8649]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>774054</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8650]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>782256</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8651]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>790458</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8652]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>798660</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8653]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>806862</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8654]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>815064</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8655]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>823266</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8656]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>831468</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8657]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>839670</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8658]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>847872</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8659]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>856074</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8660]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>864276</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8661]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>872478</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8662]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>880680</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8663]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>888882</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8664]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>897084</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8665]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>905286</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8666]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>913488</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8667]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>921690</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8668]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>929892</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8669]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>938094</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8670]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>946296</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8671]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>954498</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8672]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>962700</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8673]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>970902</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8674]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>979104</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8675]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>987306</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%.]],
[8676]=[[Launch <color=#3A6AA9>6</color> attacks against random enemies, dealing <color=#3A6AA9>995508</color> DMG each time; reducing the enemy's Max HP by <color=#3A6AA9>2%</color> for each attack (not more than 1M of the Max HP). For every 3 attacks against the same target, deal <color=#3A6AA9>2%</color> of their Max HP as Bonus DMG and reduce their Armor by 30%. When the enemy receives <color=#3A6AA9>6</color> attacks, deals additional DMG equal to <color=#3A6AA9>5%</color> of its HP and the DMG shall not exceed 2M.]],
[8677]=[[Head to the Evergreen Garden to unlock the corresponding Pets.]],
[8701]=[[Awakening Skill]],
[8702]=[[Full Screen]],
[8703]=[[Bonus Overview]],
[8704]=[[Activate Awakening Skill]],
[8705]=[[Consume Excellent+ heroes to activate the Awakening Skill.]],
[8706]=[[Awaken the hero to <color=#FADCFF>Eternal</color> to unlock their <color=#FFD100>Awakening Skills</color>.]],
[8707]=[[You have not selected enough heroes.]],
[8708]=[[No qualified heroes.]],
[8798]=[[The gear and skills of consumed heroes have been returned to your bag.]],
[8799]='Used',
[8800]=[[Glorious Arena]],
[8801]=[[The top %s players in the Daily Arena will automatically participate in the Glorious Arena.]],
[8802]=[[Opens every Saturday and Sunday.]],
[8803]=[[Current Server]],
[8804]='Cross-Server',
[8805]=[[You will enter the battle zone after the server has opened for %s days. Matches take place every Saturday and Sunday.]],
[8806]=[[The match begins in: %s days.]],
[8807]='Team',
[8808]=[[Battle Report]],
[8809]='Showdown',
[8810]='Dominion',
[8811]='Invincible',
[8812]='Supremacy',
[8813]='Countdown',
[8814]=[[The teams will be deployed in the descending order based on their CP. Each team can only be deployed once.]],
[8815]=[[Rewards will be sent to players automatically via mail when the match is over.]],
[8816]=[[Attention! <color=#f69999>%s</color> defeated you and taken your rank in the leaderboard!]],
[8817]='Rank:',
[8818]=[[Glorious Arena Title Activated]],
[8819]=[[Glorious Arena Title Lost]],
[8820]=[[Dear Traveler, congrats on activating the Dominion title in the Glorious Arena. It will remain valid for 7 days!]],
[8821]=[[Dear Traveler, congrats on activating the Invincible title in the Glorious Arena. It will remain valid for 7 days!]],
[8822]=[[Dear Traveler, congrats on activating the Supremacy title in the Glorious Arena. It will remain valid for 7 days!]],
[8823]=[[Dear Traveler, your Dominion title has expired!]],
[8824]=[[Dear Traveler, your Invincible title has expired!]],
[8825]=[[Dear Traveler, your Supremacy title has expired!]],
[8826]=[[Empty teams cannot deploy pets!]],
[8827]=[[This hero is deployed in a Glorious Arena lineup. Are you sure you want to remove them?]],
[8828]=[[Glorious Arena Invitation]],
[8829]=[[Congratulations! You have been invited to participate in the weekend Glorious Arena! Come and deploy your heroes to earn awesome rewards and unique titles of honor!]],
[8830]=[[Configure your lineups before issuing challenges!]],
[8831]=[[1. The Glorious Arena opens at 00:00 every Saturday and ends at 24:00 every Sunday. Players who achieve a high rank in Daily Arena will be automatically invited to engage in cross-server battles with players from neighboring servers.]],
[8832]=[[2. You can challenge players with a higher rank than you. If you win, your ranks will be swapped. If you lose, your ranks will remain unchanged.]],
[8833]=[[3. During the event, 10 free challenges are granted to each player every day. Challenges are reset at 00:00 each day. After using up all free challenges, you may purchase tickets to issue additional challenges.]],
[8834]=[[4. The battle adopts a multi-team mode. You can deploy 3 teams when challenging the top 20 players and 2 teams when challenging other players.]],
[8835]=[[5. The teams will be deployed in the descending order based on their CP. Each team can only be deployed once. Each victory grants 1 point and the player with more points will win.]],
[8836]=[[6. If the number of teams deployed by the two players is even and they get the same points, the player with more heroes remaining will win. If both players have the same heroes remaining, the player who deals the higher DMG will win.]],
[8837]=[[7. Ranking rewards and titles will be sent via mail after the match is over. The title will take effect when claimed and remain valid for 7 days.]],
[8838]=[[Help Text]],
[8839]=[[Help Text]],
[8840]=[[Help Text]],
[8841]=[[Not battle report found.]],
[8842]=[[Free Charges]],
[8843]='Tickets',
[8844]=[[The top %s players in the Daily Arena will automatically participate. You have not participated yet this season.]],
[8845]=[[You've reached Rank 1, there's no opponent for you.]],
[8846]=[[The hero is in the Glorious Arena lineup. You need to deploy at least one hero to participate in the match. Please adjust your lineup manually.]],
[8847]=[[The top %s players in the Daily Arena will automatically participate. You are qualified to participate in the match.]],
[8848]='Quest',
[8849]=[[Rank Change]],
[8850]=[[1. The Glorious Arena opens at 00:00 every Saturday and ends at 23:00 every Sunday. Players who achieve a high rank in Daily Arena will be automatically invited to engage in cross-server battles with players from neighboring servers.]],
[8901]='Dodge',
[8902]=[[ATK SPD]],
[8903]=[[ATK Armor]],
[9000]=[[Scrolling Text]],
[9001]=[[Gear Upgrade]],
[9002]=[[Enhancement Success Rate: %s]],
[9003]=[[Extremely Low]],
[9004]='Low',
[9005]='Moderate',
[9006]='High',
[9007]=[[Max +%d]],
[9008]=[[Bonus Stats]],
[9009]=[[Unlocks at Lv. %d]],
[9010]=[[Single Enhance]],
[9011]=[[Enhance to %s]],
[9012]='Enhance',
[9013]='Cancel',
[9014]=[[Enhancement Successful]],
[9015]=[[Enhancement Failed]],
[9016]=[[Gear has reached the enhancement cap.]],
[9017]='Guaranteed',
[9018]=[[Cap Reached]],
[9019]=[[Bonus Stats]],
[9020]=[[Gear cannot be enhanced.]],
[9021]=[[Gear Disassembly]],
[9022]=[[Select the gear you wish to disassemble.]],
[9023]=[[Disassembly Returns]],
[9024]='Obtained',
[9025]=[[Auto Select]],
[9026]='Disassemble',
[9027]=[[Tier %d-%d]],
[9028]=[[Auto Select]],
[9029]='Locked',
[9030]='Unlocked',
[9031]=[[1. You will obtain Geolites from disassembling gear.]],
[9032]=[[2. Geolites can be used to enhance gear.]],
[9033]=[[3. Gear that is currently in use cannot be disassembled.]],
[9034]=[[4. Locked gear must be unlocked before it can be disassembled.]],
[9035]=[[You have selected an advanced piece of gear. Are you sure you want to disassemble it?]],
[9036]=[[Enhancement Lv. %d gear selected. Are you sure you want to disassemble it?]],
[9037]=[[Enter "delete" to confirm the disassembly.]],
[9038]=[[You have no gear to disassemble.]],
[9039]=[[Verification failed. Try again.]],
[9040]=[[Set Enhancement Lv. %d]],
[9041]=[[Current Set Stats]],
[9042]=[[Set Stats (Next Lv.)]],
[9043]=[[%s Completed]],
[9044]=[[Current Set Stats]],
[9045]=[[Set Enhancement Lv. 1]],
[9046]=[[Set Enhancement Lv. 2]],
[9047]=[[Set Enhancement Lv. 3]],
[9048]=[[Set Enhancement Lv. 4]],
[9049]=[[Set Enhancement Lv. 5]],
[9050]=[[Set Enhancement Lv. 6]],
[9051]=[[Set Enhancement Lv. 7]],
[9052]=[[Set Enhancement Lv. 8]],
[9061]=[[Base Stats:]],
[9062]=[[Enhanced Stats:]],
[9063]='%s:',
[9064]=[[When next level of set stats will be activated when the hero equips four pieces of Lv. %d gear.]],
[9065]=[[Enhancement Level: %d]],
[9066]=[[Your gear bag is full. Make sure to clear some space in your bag as soon as possible.]],
[9067]='Disassemble',
[9068]=[[%s will be activated when the hero equips four pieces of Lv. %d gear.]],
[9069]=[[Set Stats (Inactive)]],
[9070]=[[No Gear]],
[9071]=[[x%d bonus stats can be activated.]],
[9072]=[[Replace Gear]],
[9073]='Equip',
[9074]='Replace',
[9075]='Equip',
[9076]=[[Select the gear from the list.]],
[9077]=[[No gear to replace.]],
[9078]=[[Not gear to equip.]],
[9079]=[[No gear to select.]],
[9080]=[[Must be equipped to enhance.]],
[9081]=[[Quick Selection Settings]],
[9082]=[[Manual Select]],
[9083]=[[Auto Select]],
[9084]=[[Automatically select relic of the following quality and faction.]],
[9085]=[[Automatically select gear that fit the following criteria:
1. Common grade.
2. Non-recommended gear of the current hero.]],
[9086]=[[You must select at least 1 grade.]],
[9087]=[[Bonus Stats:]],
[9088]=[[ - ]],
[9089]='Inferior',
[9090]='Common',
[9091]='Rare',
[9092]='Excellent',
[9093]='Epic',
[9094]='Legendary',
[9095]='Mythic',
[9096]=[[5. When dismantling gear, you have a chance to obtain raw materials of the same grade for reforging.]],
[9097]=[[Congratulations, you have successfully enhanced your gear to Lv. %s. Would you like to share the news?]],
[9098]=[[Reached Lv. %s]],
[9099]=[[Enhancement cap reached.]],
[9100]=[[Enhance Set]],
[9101]=[[Insufficient Diamonds. Would you like to purchase more at the store?]],
[9102]=[[Insufficient Diamonds]],
[9103]=[[Failed to recharge.]],
[9104]=[[Recharge successful. Obtained Diamonds x%s.]],
[9105]=[[Recharge failed.]],
[9106]=[[Diamond Store]],
[9107]=[[Cancel Recharge]],
[9108]=[[You must bind your account before making a recharge.]],
[9109]=[[Head to Store]],
[9110]='4.3',
[9111]='9.99',
[9112]='18.6',
[9113]='47.7',
[9114]='94.2',
[9115]='47.7',
[9116]=[[Recharge successful.]],
[9117]=[[Not available yet.]],
[9118]=[[Cannot recharge from a guest account. You must bind your account first!]],
[9119]=[[Recharge any amount to claim the Super Value Pack!]],
[9120]=[[Quest complete. Tap to claim the pack!]],
[9121]=[[Bonus +60]],
[9122]=[[Bonus +330]],
[9123]=[[Bonus +750]],
[9124]=[[Bonus +1430]],
[9125]=[[Bonus +3860]],
[9126]=[[Bonus +8088]],
[9127]=[[First Recharge x2]],
[9128]=[[Recharge Sale Event Time Remaining: %dd %dh]],
[9129]=[[First Recharge Event Ongoing]],
[9130]=[[Recharge Sale Event Ongoing]],
[9131]=[[Players get 2x Diamonds on their first recharge.]],
[9132]='60',
[9133]='330',
[9134]='750',
[9135]='1430',
[9136]='3860',
[9137]='8088',
[9138]=[[After you purchase a %s Deluxe Monthly Card, you will
immediately obtain Diamond %d and VIP EXP %d.
For %s days after the purchase, log in and check your mail each day 
to obtain Diamond %d.
You will get a total of Diamond %d! Don't miss out on this amazing 
deal!]],
[9139]=[[Hero Capacity: %s
Bonus AFK Gold, Player EXP, Hero EXP:%s
Acorn Tavern Daily Quest Limit: %s
Proving Grounds Sweep Purchases: %s
 Adventure Quick AFK Purchases: %s
Daily Arena Ticket Purchases:%s]],
[9140]=[[Go to Achievements after reaching the specified VIP Level to obtain:]],
[9141]=[[Bonus daily rewards after Deluxe Monthly Card purchase:]],
[9142]=[[<color=red>·</color>Unlock Normal Casino 10x mode.]],
[9143]=[[<color=red>·</color>Unlock 2x Battle Speed.]],
[9144]=[[You must recharge %s more.]],
[9145]='0.87',
[9146]='8.7',
[9147]=[[%d day(s)]],
[9148]='Perks',
[9149]='Store',
[9150]=[[Recharge successful. Monthly Card obtained.]],
[9151]=[[Recharge successful. Dual Monthly Card obtained.]],
[9152]=[[Reach Lv. 5 or VIP 1 to unlock 2x speed.]],
[9153]=[[Total obtained %d super crystal diamonds
+%d noble experience]],
[9154]='1',
[9155]=[[Purchase one of the Gold packs listed below to get a large amount of gold for free each day!]],
[9156]=[[Refresh after <color=#FFF400FF>%s</color>.]],
[9157]=[[In Cooldown]],
[9158]=[[Gold <color=#DD3C2DFF>Bonus</color>]],
[9159]=[[First Recharge Diamonds]],
[9160]=[[Claim for Free]],
[9161]=[[Gold Bonus]],
[9162]='First',
[9163]=[[Common Monthly Card]],
[9164]=[[Deluxe Monthly Card]],
[9165]=[[250 Star Diamond Gift Pack]],
[9166]=[[500 Star Diamond Gift Pack]],
[9167]=[[1,000 Star Diamond Gift Pack]],
[9168]=[[2,500 Star Diamond Gift Pack]],
[9169]=[[5,000 Star Diamond Gift Pack]],
[9170]=[[5,000 Star Diamond Gift Pack]],
[9171]=[[Reach VIP 3 or Turn 4 to skip battle.]],
[9173]=[[After you purchase a %s Common Monthly Card, you will
immediately obtain Diamond %d and VIP EXP %d.
For %s days after the purchase, log in and check your mail each day 
to obtain Diamond %d.
You will get a total of Diamond %d! Don't miss out on this amazing 
deal!]],
[9172]=[[After you purchase a %s Deluxe Monthly Card, you will
immediately obtain Diamond %d and VIP EXP %d.
For %s days after the purchase, log in and check your mail each day 
to obtain Diamond %d.
You will get a total of Diamond %d! Don't miss out on this amazing 
deal!]],
[9176]='28.8',
[9177]='65.16',
[9178]='$',
[9179]='%.0f',
[9180]='14.25',
[9181]=[[Value Pack]],
[9182]=[[Hero Capacity: +<color=#fff881ff>%d</color>
Bonus AFK Gold, Player EXP, Hero EXP: +<color=#fff881ff>%d%%</color>
Acorn Tavern Daily Quest Limit: <color=#fff881ff>%d</color>
Proving Grounds Sweep Purchases: <color=#fff881ff>%d</color>
 Adventure Quick AFK Purchases: <color=#fff881ff>%d</color>
Daily Arena Ticket Purchases:<color=#fff881ff>%d</color>]],
[9183]=[[Reach VIP %d to enjoy the following perks:]],
[9184]=[[More Perks]],
[9185]=[[Super Value]],
[9186]=[[Diamond Store]],
[9187]=[[VIP Perks]],
[9188]='Requirements',
[9189]=[[%d<color=#FFF7E4>Reach</color> VIP %d.]],
[9190]=[[VIP %d Rewards]],
[9191]=[[VIP %d Perks]],
[9192]=[[Recharge failed.]],
[9193]=[[Not granting permissions could lead to your recharge failing!
Try the following:
1. Enter the Permissions tab.
2. Enable Pop-ups.
If the recharge still fails, please contact us via our Facebook page.]],
[9194]=[[VIP Level Boost]],
[9195]=[[Hero Capacity: %s
Bonus AFK Gold, Player EXP, Hero EXP:%s
Acorn Tavern Daily Quest Limit: %s
Proving Grounds Sweep Purchases: %s
 Adventure Quick AFK Purchases: %s
Daily Arena Ticket Purchases:%s]],
[9196]=[[Purchase Limit: %d/%d]],
[9197]=[[Unlock the Faction Summon (Voider/God) feature.]],
[9198]=[[Unlock the Casino feature.]],
[9199]=[[Unlock the Smart Awakening feature.]],
[9200]=[[Unlock the Ancient Summon Sweep feature.]],
[9201]=[[Congrats! You have levelled up!]],
[9202]=[[Commiserations! Your level has dropped!]],
[9203]='Rank',
[9204]=[[Tap anywhere to close.]],
[9251]=[[Time Remaining: %s]],
[9261]=[[Red Diamond Purchase]],
[9262]=[[Insufficient Red Diamonds]],
[9263]='Cancel',
[9264]='Purchase',
[9265]='Recharge',
[9266]=[[Purchase with%s Red Diamonds?]],
[9267]=[[Insufficient Red Diamonds. Would you like to recharge?]],
[9270]=[[Note: Tap Purchase Now to pay.]],
[9271]=[[Red Diamond Recharge]],
[9272]=[[Reminder: 1 USD=100 Red Diamond, clicking on the red diamond recharge button will jump to the online recharge page]],
[9273]=[[Red Diamond balance insufficient! Click the blue button below to purchase]],
[9301]=[[Make sure you have enough BP
to weather the battles ahead. Make your way to the Esper
Tower to summon Esper heroes and form a
powerful Esper Assault team.]],
[9302]=[[Tap Summon to find out who will join your team!]],
[9303]=[[Come on, let's make another
Heart Summon!]],
[9304]=[[After you gather enough heroes,
the real battle will begin!]],
[9305]=[[Traveler, tap
Adventure to make your way
to the battlefield.]],
[9306]=[[Tap here to begin battle!]],
[9307]=[[Select a hero to deploy!]],
[9308]=[[Tap Deploy and let us find out who our enemies are.]],
[9309]=[[Your heroes will
keep battling automatically! Even if you aren't online,
you can still obtain Gold and EXP. Just remember
to come back to claim them!]],
[9310]=[[Tap to claim and obtain the loot
your heroes have obtained in battle.
The more difficult the stage, the greater the AFK rewards.
Try to clear as many stages as possible!]],
[9311]=[[Heroes don't just spend all their time
trying to save the world. There are many
fun activities to try and bonuses to claim.
Let's go and check them out!]],
[9312]=[[Let's see what's going on over here!]],
[9313]=[[This is the weekly event.
It will refresh every week. Let's see
how much we've completed this week.]],
[9314]=[[Congratulations on tackling the 
level up challenge for the first time. The rewards will be sent
to your mail automatically.]],
[9315]=[[There is the monthly challenge too.
It will refresh every month. Complete it to earn
lucrative rewards. Remember to
check it out from time to time.]],
[9316]=[[Your mail over here.
Tap it to claim the Excellent Hero
we just obtained!]],
[9317]=[[We obtained an Excellent Hero Shard earlier.
You can merge it into a hero.
Tap your bag and check out the shard!]],
[9318]=[[Remember to claim your AFK Rewards.
Focus on the challenges and events that you can tackle.
I look forward to seeing your progress!]],
[9319]=[[You can always tap here whenever you need guidance.]],
[9320]=[[Congratulations on reaching Lv. 50.
It's time to teach you about
merging!]],
[9321]=[[You can merge your weak heroes to make them
stronger. Just keep in mind that
this requires a lot of materials.]],
[9322]=[[Let's claim a shard
from your mail.]],
[9323]=[[Tap here to start merging.]],
[9324]=[[We've got a 5-Star
Biochemical Werewolf here. Tap and find out
what heroes we need to use as materials.]],
[9325]=[[Open your bag and
merge these shards into a hero.]],
[9326]=[[Wow! You sure have collected a lot of 
Hero Shards. You must have completed so many challenges
and obtained so many rewards.]],
[9327]=[[Done! Now let's 
merge a 5-Star Mad Blade.]],
[9328]=[[Tap here to add the materials.
You can use the Auto button to speed up the process.]],
[9329]=[[Congratulations on obtaining a Mad Blade. There is more to learn
about merging, but you'll have to find out about those things yourself.]],
[9330]=[[Uh oh... Looks like we failed. But that's fine, you can also level up your heroes to increase your strength!]],
[9331]=[[Suited for heroes with high DPS!]],
[9332]=[[Suited for heroes with high DEF!]],
[9333]=[[This is the Goddess of War we are trying to merge!]],
[9334]=[[Enhance Gear]],
[9335]=[[Faction Mastery]],
[9336]=[[Uh oh... Looks like we failed. But that's fine, you can also enhance gear to increase your strength!]],
[9337]=[[Uh oh... Looks like we failed. But that's fine,
you can also use Faction Mastery to increase your strength!]],
[9338]=[[New Hero!]],
[9339]=[[Tap me and
I'll tell you how to obtain more resources!]],
[9340]=[[New Feature]],
[9341]=[[You have powerful heroes to deploy.]],
[9342]=[[Gather your Esper Heroes and head to the battlefield!]],
[9343]=[[Tap Berserk Axe's avatar to deploy them.]],
[9344]=[[Berserk Axe needs some support. Elemental Guard is a good choice here. Tap Elemental Guard's avatar so they can join the fray.]],
[9345]=[[When my Rage is full, I will unleash my ultimate!]],
[9346]=[[There are still many monsters over there. We've got to make haste.]],
[9347]=[[Haha! Winter Phantom has joined us too. Tap on their avatar to deploy them.]],
[9348]=[[Tap and drag the heroes around to adjust their formation.]],
[9349]=[[Don't forget about me, I'm also part of the team!]],
[9350]=[[Tap here to claim your AFK rewards.]],
[9351]=[[Let's learn more about them.]],
[9352]=[[Tap my avatar to enter the hero interface.]],
[9353]=[[Tap Level Up to raise my level and increase my CP.]],
[9354]=[[You can also swap gear here to further boost BP.]],
[9355]=[[Wow! We've got two new pieces of gear. Tap Auto Equip to equip them on me and raise my stats.]],
[9356]=[[The enemies are becoming stronger. We need new heroes to join our team!]],
[9357]=[[You can summon new heroes from the Summoning Hall. Give it a go!]],
[9358]=[[Wow! What a pleasant surprise, It's Leos! Our team just got a lot stronger!]],
[9359]=[[Worry not. Failure is the mother of success. We can tackle the challenge again after claiming our loot and upgrading our heroes!]],
[9360]=[[I'm more suited for the back row.]],
[9361]=[[I'm more suited for the front row.]],
[9362]=[[Welcome to Hero Clash. The battle has already begun!]],
[9363]=[[Congratulations on obtaining a new hero!]],
[9364]=[[Unlocked by clearing [Adventure] 2-1.]],
[9365]=[[Tap here to change the team composition.]],
[9366]=[[This hero has reached the level cap. Try leveling your other heroes!]],
[9367]=[[Tap and hold the Level Up button to continuously level up your hero.]],
[9368]=[[I feel my energy overflowing! It's time for battle!]],
[9369]=[[Eternal Tree]],
[9370]='Denise',
[9371]=[[Faceless Man]],
[9372]='Catherine',
[9373]=[[Knight of Rebirth]],
[9374]=[[Giant boulder, crush my enemies!]],
[9375]=[[My friend, you have quite the special ability!]],
[9376]=[[Their struggle is futile! Crush them!]],
[9377]=[[There are a lot of enemies. Looks like we'll have to take things a little more seriously!]],
[9378]=[[Is that all you've got?]],
[9379]=[[Use your best moves. Don't let them grow complacent!]],
[9380]=[[I'm getting tired of this. Awaken, Light's Judgment!]],
[9381]=[[They're too strong! I'm using my ultimate skill, Ruinous Boulder!]],
[9382]=[[Don't give up! Even if we're in a hopeless situation, we have to remain steadfast!]],
[9383]=[[You're not going anywhere! I'll destroy you all!]],
[9384]='Urrgghh!',
[9385]=[[Hang in there, Eternal Tree! There's still a chance...]],
[9386]=[[Embrace your faith, and let your will drive you!]],
[9387]=[[Interesting... Seems the power of their faith has sent a seed of hope to the next world...]],
[9388]=[[In that case... I shall destroy every world and make them all bow before me!]],
[9389]=[[The next world...

Danger is upon us...

The journey is about to begin!]],
[9390]=[[Unlocked by clearing [tile] %s]],
[9391]=[[<color=#fdf077ff>Tip: Common and Rare cards are important merge materials!</color>]],
[9392]=[[<color=#fdf077ff>Wow! A Excellent Hero! Let's check it out in the Hero interface!</color>]],
[9393]=[[Fire Eater]],
[9394]=[[The flames shall reduce you to ash!]],
[9395]=[[Disintegrate within the pitch-black light...]],
[9396]=[[Skip >>]],
[9397]=[[Complete the objectives to activate the Sacred Gear.]],
[9398]=[[Tap to claim rewards.]],
[9399]=[[Tap again.]],
[9400]=[[Congratulations on obtaining a powerful weapon. Why don't you equip it now?]],
[9401]=[[The Sacred Gear has made you stronger.]],
[9402]=[[Mirror World #5428's
Esper Assault Base is under attack!

War breaks out in the blink of an eye...]],
[9404]=[[Tap to learn more.]],
[9405]=[[Congratulations on obtaining Moon Phoenix. Head to your bag to merge!]],
[9406]=[[When the energy bar is full, the hero will cast their ultimate skill automatically. Tap the interface to continue the battle!]],
[9407]=[[Tap Level Up to increase CP.]],
[9408]=[[Tap Auto Equip to increase BP with stronger gear.]],
[9409]=[[Skip Tutorial]],
[9410]=[[We just obtained a huge amount of Rankup Stones. Let's make use of them!]],
[9411]=[[You can increase a hero's rank after reaching the specified level. Let's give it a try!]],
[9412]=[[Place Winter Phantom in the back row to keep them safe!]],
[9413]=[[You have unlocked the Proving Grounds. You can now obtain Rankup Stones to increase the rank of your heroes.]],
[9414]=[[We've obtained a powerful tank. Tap on their avatar to deploy them.]],
[9415]=[[Tanks can soak up a lot of damage. Place them in Slot 1.]],
[9416]=[[Tap the Elemental Guard's avatar to deploy them.]],
[9417]=[[Haha! A Mage has joined our team. Let's deploy them!]],
[9418]=[[Click to sort the teams.]],
[9419]=[[Drag the teams to adjust the order.]],
[9420]=[[Betalians are invading Earth! New York City is in grave danger, we need your help now!]],
[9421]=[[Tap to select Garlic Samurai.]],
[9422]=[[A new hero has joined us, but the battle continues. We've gotta take action now!]],
[9423]=[[Tap on their avatar to deploy them to battle!]],
[9424]=[[Our new companion is a warrior who should be used to protect the team from Position. Drag their avatar to move them!]],
[9425]=[[Alright~! Let's show them what we're made of!]],
[9426]=[[A new companion has joined our team. Let's find out what they're capable of!]],
[9427]=[[Tap on their avatar to learn more about them!]],
[9428]=[[Seems like they're a mage. Tap Upgrade to boost their abilities and make them even stronger!]],
[9429]=[[Here's some gear. Tap Quick Equip to boost your hero's CP!]],
[9430]=[[Alright! Now let's wipe them all out!]],
[9431]=[[Don't forget about our new companion. Let's show our enemies just how powerful mages are!]],
[9432]=[[The battle isn't over yet. Tap to go to the next chapter!]],
[9433]=[[Don't be upset. Tap here to collect our resources. Let's upgrade our CP and try again!]],
[9450]=[[Welcome to Hero Clash. The battle has begun!]],
[9451]=[[Berserk Axe is a powerful tank. Tap on their avatar to deploy them.]],
[9452]=[[Bloody Colt makes for a wonderful support. Tap on their avatar to deploy them.]],
[9453]=[[Tap Deploy to defeat the enemies.]],
[9454]=[[Basic attacks restore energy. When their energy bar is full, the hero will cast their ultimate automatically. Tap the interface to continue.]],
[9455]=[[The battle has grown fierce and brutal. The World Administration Bureau has sent reinforcements. Choose one of the following heroes to join your team.]],
[9456]=[[This mage couldn't wait to join the battle. Let's find out how good they are.]],
[9457]=[[This mage can use skills to attack multiple enemy units. Tap their avatar to deploy them.]],
[9458]=[[You can drag heroes to change their position. Place heroes in the back row to avoid damage. Positioning your heroes well is the key to victory.]],
[9459]=[[I can't wait to watch this battle!]],
[9460]=[[The crisis remains. We've got to hurry up and go to the next stage.]],
[9461]=[[This new hero couldn't wait to join the battle. Let's find out how good they are.]],
[9462]=[[They can use their skills to heal allies. Tap their avatar to deploy them.]],
[9463]=[[Get ready for the boss stage! Let's enhance our heroes first.]],
[9464]=[[Tap on the hero's avatar to enter the hero interface.]],
[9465]=[[Tap Enhance to upgrade and increase the hero's power.]],
[9466]=[[Wow! New gear! Tap Quick Equip to boost your hero's stats.]],
[9467]=[[Wow! You've collected 15 stars. Now just tap to claim rewards.]],
[9468]=[[Wow! An advanced summon scroll! This is a pretty rare item. It can be used to summon heroes in the Summoning Hall.]],
[9469]=[[You can summon new heroes from the Summoning Hall. Give it a go!]],
[9470]=[[Tap Summon to find out who will join your team!]],
[9471]=[[Wow, what a nice surprise! It's Scarlet Warden! Our team just got stronger!]],
[9472]=[[Hello, Traveler! The stages have now moved to the map. Tap a stage to challenge it.]],
[9473]=[[We've unlocked a new chapter. Tap the world map and head to the next destination.]],
[9474]=[[Elemental Guard is a powerful warrior. Tap on their avatar to deploy them.]],
[9475]=[[This warrior couldn't wait to join the battle. Let's find out how good they are.]],
[9476]=[[Warriors excel in soaking damage and protecting allies. Tap on their avatar to deploy them.]],
[9477]=[[Drag hero avatars to change their positions. The hero in Position 1 will be attacked by enemies first. You should place a warrior here.]],
[9478]=[[The following stages will be more difficult. Let's make sure we enhance our heroes first.]],
[9479]=[[Congratulations on clearing this stage! Tap on the chest to receive rewards.]],
[9480]=[[Placed at <color=red>Position %s in the front row</color> to make full use of my counterattack skill.]],
[9481]=[[Visit the Awakening Spring from Home to awaken your duplicate heroes!]],
[9482]=[[Select the hero you wish to awaken, select the heroes you wish to use as materials, then tap Awaken.]],
[9483]=[[We've unlocked a new chapter. Tap to head to the next destination.]],
[9484]=[[Gear Upgrade has been unlocked. Let's go and take a look.]],
[9485]=[[Gear Upgrade unlocked. Gear of Tier 3 and above can be enhanced to increase their strength!]],
[9486]=[[Once gear is upgraded to a certain level, you can unlock additional stats to obtain greater bonuses.]],
[9487]=[[Ancient Summon unlocked! Time to get some new gear!]],
[9488]=[[Ancient Summon is a great source of new gear.]],
[9489]=[[Tap here to toggle 2x Speed.]],
[9490]=[[The Proving Grounds offers a variety of amazing rewards. Let's check it out.]],
[9491]=[[The Tower of Fate offers many amazing rewards. Let's check it out.]],
[9492]=[[Ancient Summon offers many amazing rewards. Let's check it out.]],
[9493]=[[The Arena offers many amazing rewards. Let's check it out.]],
[9494]=[[Garden Slots offer many amazing rewards. Let's check it out.]],
[9495]=[[Hero Codex Stat Bonus unlocked. Let's check it out.]],
[9496]=[[Activate Hero Codex Stats to obtain stat bonuses for your heroes.]],
[9497]=[[Specific Summon unlocked. Let's check it out.]],
[9498]=[[Select a hero and summon. If you get an S+ hero, they will be replaced by the chosen hero.]],
[9500]=[[I have faith in your luck~!]],
[9510]=[[See any rewards you want?]],
[9520]=[[You can skip those you don't like by refreshing!]],
[9521]=[[Not everyone can come to this place.]],
[9522]=[[Make your choice wisely...]],
[9523]=[[Have you tried a Free Summon today?]],
[9524]=[[The Giant Merchant offers scrolls here every day.]],
[9525]=[[Strength is more important than luck here!]],
[9526]=[[Tickets? I bet the Giant Merchant and Acorn Tavern have plenty of stuff in stock.]],
[9527]=[[A reasonably matched lineup is the key to your victory.]],
[9528]=[[A reasonably matched lineup is the key to your victory.]],
[9529]=[[Acquire <color=red>Faction Mastery</color> to enhance the stats of corresponding heroes.]],
[9530]=[[Visit the <color=red>Giant Merchant</color> to get a wealth of good stuff.]],
[9531]=[[The event offers rich rewards that are refreshed every week!]],
[9532]=[[Which hero do you like, and why?]],
[9533]=[[I want better gear!]],
[9534]=[[I want better skills!]],
[9535]=[[Familiarize yourself with your heroes' skills to grow stronger.]],
[9536]=[[I'll find loot for you, even when you're away.]],
[9537]=[[Remember to claim loot every once in a while.]],
[9538]=[[The <color=red>Summoning Hall</color> offers free summons every once in a while.]],
[9539]=[[Quests in the <color=red>Acorn Tavern</color> can be refreshed. High-tier quests offer awesome rewards.]],
[9540]=[[Have you established a bond in the <color=red>Garden</color>? What are they like?]],
[9541]=[[The union is a big family that relies on contribution of all members.]],
[9542]=[[You can use <color=red>Awakening Spring</color> to create stronger heroes.]],
[9543]=[[You can upgrade your gear via the <color=red>Gear Forge</color>.]],
[9544]=[[The <color=red>Esper Conversion Pod</color> allows you to summon heroes of specific factions.]],
[9545]=[[You can disassemble idle heroes via <color=red>Alchemy</color> to get more resources.]],
[9546]=[[Try your luck in the <color=red>Casino</color> when you have time. You may win rare items there.]],
[9547]=[[The <color=red>Proving Grounds</color> is where you can get abundant hero rankup materials.]],
[9548]=[[You may get rare rewards when visiting certain floors of the <color=red>Proving Grounds</color>.]],
[9549]=[[Daily quests also offer amazing rewards. Remember to complete them!]],
[9550]=[[In my eyes, you are the best Traveler ever.]],
[9551]=[[Traveler, you seem a bit partial.]],
[9552]=[[What is your bonded friend like?]],
[9553]=[[Trust me, we shall become stronger!]],
[9554]=[[And darkness shall be defeated.]],
[9555]=[[Remember to collect rewards in the <color=red>Garden</color>.]],
[9556]=[[Thank you for playing our game.]],
[9557]=[[If you run into any difficulty, you can give up feedback via Settings.]],
[9558]='New!',
[9601]=[[Quick Enhance]],
[9602]=[[Quick Enhance (Available at VIP6)]],
[9603]=[[VIP Level too low.]],
[9604]=[[Enhance failure x%d.]],
[9605]=[[Enhance success x%d.]],
[9606]=[[Insufficient materials (enhancement terminated).]],
[9607]=[[Quick Enhance successful.]],
[9608]=[[Enhancement Cost]],
[9609]=[[Network unstable (enhancement terminated).]],
[9610]=[[Tap to terminate Quick Enhance.]],
[9611]=[[Terminate Enhance]],
[9701]='Crucible',
[9702]='Ongoing...',
[9703]=[[The top %s players in the Daily Arena will be invited to participate in the match.]],
[9704]=[[Hall of Glory]],
[9705]=[[Group Round]],
[9706]=[[Elimination Round]],
[9707]=[[Championship Round]],
[9708]='Guess',
[9709]='Honor',
[9710]='Rewards',
[9711]=[[Defense Lineup]],
[9712]=[[Zone %s]],
[9713]=[[Group %s]],
[9714]=[[Only the player with the most wins will be promoted to the next stage.]],
[9715]='Countdown:',
[9716]=[[Lineup locks in:]],
[9717]=[[Battle begins in:]],
[9718]=[[Waiting for the match to begin...]],
[9719]=[[The match has ended.]],
[9720]=[[Battle Details]],
[9721]=[[Round %s]],
[9722]=[[Peak Leaderboard]],
[9723]=[[Hall of Fame]],
[9724]=[[Top 8]],
[9725]=[[Top 4]],
[9726]='Champion',
[9727]=[[Season Honor]],
[9728]=[[Players can claim rewards corresponding to their ranking after the Crucible ends.]],
[9729]=[[Issued when the Crucible ends and remains valid for 14 days.]],
[9730]=[[Champion Exclusive Avatar Frame & Title]],
[9731]=[[Top 4 Exclusive Avatar Frame]],
[9732]=[[Top 8 Exclusive Avatar Frame]],
[9733]='Result',
[9734]=[[Your prediction was correct. You have been returned 2x of your wager.]],
[9735]=[[Support the player whom you think will win.]],
[9736]=[[Prediction Correct]],
[9737]=[[Prediction Wrong]],
[9738]=[[Winning Streak:]],
[9739]=[[Total Correct Predictions:]],
[9740]=[[Rewards will be sent via mail.]],
[9741]=[[Not promoted.]],
[9742]=[[Congratulations! You are invited to participate in the Crucible!]],
[9743]=[[Deploy heroes to win an exclusive avatar frame and title of honor.]],
[9744]='Deploy',
[9745]=[[Who is the strongest?]],
[9746]=[[Participate in the prediction to win gold rewards.]],
[9747]='Predict',
[9748]=[[King of Arena]],
[9749]=[[You ranked No. <color=#9ef6a2>%s</color> in this Crucible. Keep up the good work!]],
[9750]='Support',
[9751]=[[You must to deploy at least 1 hero!]],
[9752]=[[Insufficient gold to make a prediction!]],
[9753]=[[Participants in the match can only support themselves!]],
[9754]=[[Top 64]],
[9755]=[[The match has begun. You can't make any more predictions!]],
[9756]='Crucible',
[9757]=[[No one on the leaderboard yet.]],
[9758]=[[Title: Life +5% Attack +5%
Avatar frame: Life +10% Attack +10%]],
[9759]=[[Portrait frame: Life +8% Attack +8%]],
[9760]=[[Portrait frame: Life +6% Attack +6%]],
[9761]=[[Begins in %s days.]],
[9762]=[[Only heroes above %s can be deployed.]],
[9763]=[[The Crucible adopts a <color=#e59b4c>revolving battle</color> system. If your team is powerful enough, you can defeat all your opponent's teams with just one team. As such, <color=#e59b4c>it makes sense to deploy powerful heroes in the same team!</color>]],
[9764]=[[Do you want to save the current lineup?]],
[9765]=[[Heroes deployed in the Crucible cannot be consumed.]],
[9766]=[[The top <color=#9ef6a2>%s</color> in Daily Arena at <color=#9ef6a2>00:00</color> on the match day will automatically be qualified.]],
[9780]='Crucible',
[9781]=[[1. Top-ranking players in Daily Arena will be selected to participate in the match.]],
[9782]=[[2. Crucible is a cross-server match only available to heroes above Legendary+. Up to 3 teams, totaling 18 heroes, can be deployed. You need at least 1 Legendary+ hero to participate in the Crucible.]],
[9783]=[[3. All participants will be divided into groups of 64 at random. The No.1 in each group will be promoted to the Round of 64. The group matches employ the round robin system and players are ranked based on the number of wins. If several players have the same number of wins, the player who dealt more DMG will have a higher ranking.]],
[9784]=[[4. The top 64 players will engage in knockout matches in 8 groups formed at random. The No.1 in each group will be promoted to the Round of 8.]],
[9785]=[[5. Crucible adopts a revolving battle system where surviving heroes will proceed to the next turn with their current HP preserved.]],
[9786]=[[6. If no winner emerges after 15 turns, the player with more heroes remaining wins. If the two players have the same number of remaining heroes, or all of their heroes are dead, the player who dealt higher total DMG will win.]],
[9787]=[[7. Tap a player's avatar to view their Mythic heroes. Lineups will be locked 1 hour before the battle begins.]],
[9788]=[[8. The champion of Crucible will receive an extremely rare title of honor. The Top 8, Top 4, and Champion will obtain exclusive avatar frame rewards.]],
[9789]=[[Hall of Glory]],
[9790]=[[1. When Crucible ends, the Peak Leaderboard will be updated to show the top 8 players in the season.]],
[9791]=[[2. All players who have won the Championship of the Crucible will automatically enter the Hall of Fame.]],
[9792]=[[3. Players in Hall of Fame will be ranked based on the number of championships they have. For players with the same number of championships, the one who entered the Hall of Fame earlier will have a higher ranking.]],
[9793]='Honor',
[9794]=[[1. The champion of Crucible will receive an extremely rare title of honor which will be displayed before their name when they post on any channel.]],
[9795]=[[2. The Champion, Top 4, and Top 8 players will get rare exclusive titles.]],
[9796]=[[3. The titles and avatar frames will be activated after the Crucible ends and will remain valid for 14 days.]],
[9797]='Guess',
[9798]=[[1. When the Crucible kicks off, all players can support 1 participant each day. If that participant wins, their prediction will be deemed successful.]],
[9799]=[[2. The matches in each guess are picked by the system. If you participate in the event, matches of your group members will be recommended to you. If not, matches of your union members will be recommended to you.]],
[9800]=[[3. Gold produced from 24h AFK is required to make predictions. If your prediction is correct, twice as much gold will be returned via mail.]],
[9801]=[[4. You must support yourself in matches that you are fighting in.]],
[9802]=[[The Pinnacle]],
[9803]=[[1. The top 256 players in the Daily Arena will be selected to participate in the match.]],
[9804]=[[2. You need at least 1 Epic hero to participate in The Pinnacle.]],
[9805]=[[3. All participants will be divided into groups of 64 at random. The No.1 in each group will be promoted to the Round of 64. The group matches employ the round robin system and players are ranked based on the number of wins. If several players have the same number of wins, the player who dealt more DMG will have a higher ranking.]],
[9806]=[[4. The top 64 players will engage in knockout matches in 8 groups formed at random. The No.1 in each group will be promoted to the Round of 8.]],
[9807]=[[5. The Pinnacle adopts a revolving battle system where surviving heroes will proceed to the next turn with their current HP preserved.]],
[9808]=[[6. If no winner emerges after 15 turns, the player with more heroes remaining wins. If the two players have the same number of remaining heroes, or all of their heroes are dead, the player who dealt higher total DMG will win.]],
[9809]=[[7. Tap a player's avatar to view their heroes. Lineups will be locked 1 hour before the battle begins.]],
[9810]=[[8. The champion of The Pinnacle will receive an extremely rare title of honor. The Top 8, Top 4, and Champion will obtain exclusive avatar frame rewards.]],
[9811]='Guess',
[9812]=[[1. When the Crucible kicks off, all players can support 1 participant each day. If that participant wins, their prediction will be deemed successful.]],
[9813]=[[2. The matches in each guess are picked by the system. If you participate in the event, matches of your group members will be recommended to you. If not, matches of your union members will be recommended to you.]],
[9814]=[[3. Gold produced from 6h AFK is required to make predictions. If your prediction is correct, twice as much gold will be returned via mail.]],
[9815]=[[4. You must support yourself in matches that you are fighting in.]],
[9816]=[[(Round of 64)]],
[9817]=[[(Round of 32)]],
[9818]=[[(Round of 16)]],
[9819]='(Quarterfinals)',
[9820]='(Semifinals)',
[9821]='(Final)',
[9822]=[[Adjust Lineup]],
[9823]='Me',
[9901]=[[Almighty Mage]],
[9902]=[[Field Freezer]],
[9903]=[[Agile Shadow]],
[9904]=[[Burst DMG]],
[9905]=[[Powerful Warrior]],
[9906]=[[Super Healer]],
[9907]=[[Strong Tank]],
[9908]=[[Super CC]],
[9999]='【客户自己模块提示结束】',
[15001]=[[Common Summon]],
[15002]=[[Hero Summon]],
[15003]=[[Obtain any hero at random.]],
[15004]=[[Obtain any hero at random.]],
[15005]='Current',
[15006]=[[x Summons guarantee]],
[15007]='Excellent',
[15008]='Heroes',
[15009]=[[Obtain 1x]],
[15010]='Heroes',
[15011]='within',
[15012]=[[Lucky Draws]],
[15013]=[[until guaranteed]],
[15014]='Select',
[15015]=[[Obtain 1x]],
[15016]=[[Celestial Summon]],
[15017]=[[Celestial Wish Hero]],
[15018]=[[Selected heroes are guaranteed in <color=#fded76FF>%s</color> summons.]],
[15019]=[[Select your Wish Hero before summoning!]],
[15100]=[[Daily 50% Discount]],
[15101]=[[Giant Merchant]],
[15102]='Refresh',
[15103]='Free',
[15104]=[[Are you sure you want to purchase?]],
[15105]=[[Insufficient refreshes or refresh runes.]],
[15106]=[[Purchase successful.]],
[15107]=[[Refresh purchases]],
[15108]=[[Refresh successful.]],
[15109]=[[More items will be available in <color=#87F425>%s</color>. You may also spend <color=#FFDD22>100 Diamonds</color> to refresh now!]],
[15110]=[[Free Refresh:]],
[15111]='Purchase',
[15112]=[[Giant Merchant]],
[15113]=[[Union Store]],
[15114]=[[Arena Store]],
[15115]=[[Advanced Arena Store]],
[15116]=[[Dismissal Store]],
[15117]=[[Event Store]],
[15118]=[[Tower Store]],
[15119]=[[Limited Items]],
[15120]=[[Treasure Store]],
[15121]=[[Hero Store]],
[15122]=[[Skill Store]],
[15123]=[[Nucleus Crystal Store]],
[15124]=[[Do you want to spend %s%d to refresh items in the store?]],
[15125]=[[Do you want to spend %s%d to refresh purchases?]],
[15126]=[[Clear %s to unlock more advanced items.]],
[15127]=[[Clear %s to unlock more advanced gear.]],
[15128]=[[Sold Out]],
[15129]='Owned:',
[15200]=[[Rewards for clearing %d stages]],
[15201]=[[Requires Player Lv. %d]],
[15202]='Floor',
[15203]='Combo',
[15204]='Floor',
[15205]=[[Remaining daily chances]],
[15206]=[[Enter the amount of sweeps.]],
[15207]=[[You have used all sweeps.]],
[15208]=[[VIP %d only allow to purchase %d.]],
[15209]=[[Daily Chances: %d]],
[15210]=[[Clear Floor %d to unlock sweep.]],
[15211]='Quest',
[15212]='Sweep',
[15213]='Cleared',
[15214]='Locked',
[15215]='Recent',
[15216]=[[Stage Challenge Rewards]],
[15217]=[[No one has cleared this stage.]],
[15218]=[[You are on Floor %d. Press on to get awesome rewards!]],
[15219]=[[Clear Floor %d to receive sweep rewards.]],
[15220]=[[Clear the corresponding floors to get challenge rewards.]],
[15221]=[[Clear Floor %d]],
[15222]=[[Stage %d]],
[15223]=[[Clear Floor %s to get this reward.]],
[15224]=[[More items will be available in <color=#87F425>%s</color>.]],
[15250]=[[You cannot send empty messages!]],
[15251]=[[You can post after %ds.]],
[15252]=[[You are restricted from posting until %s.]],
[15253]=[[Report sent!]],
[15254]='Shared!',
[15255]=[[Failed to share!]],
[15256]='World',
[15257]='Union',
[15258]='Territory',
[15259]='Union:',
[15260]=[[Hide VIP]],
[15261]=[[Block World Chat]],
[15262]=[[Block Union Chat]],
[15263]=[[View Info]],
[15264]='Defense',
[15265]=[[Reported Content:]],
[15266]=[[Reported Player:]],
[15267]='Type:',
[15268]='Reactionary',
[15269]='Pornographic',
[15270]='Violence',
[15271]='Other',
[15272]=[[Specify (Optional)]],
[15273]='Report',
[15274]='Share',
[15275]=[[Select the channel you wish to share with.]],
[15276]=[[Are you sure you want to block this player?]],
[15277]=[[Block Territory Chat]],
[15278]=[[Tap to go to Facebook homepage]],
[15279]=[[Tap to go to Facebook group]],
[15280]='Chat',
[15281]='Community',
[15282]=[[Strongest Lineup]],
[15283]=[[Cannot be empty.]],
[15284]=[[You are not in any union. Tap here to join a union!]],
[15285]=[[Recruit Members]],
[15286]=[[Active players wanted.
Come and join us!]],
[15287]=[[You need to leave your current union first!]],
[15288]='Join',
[15289]='Recruit',
[15290]='Intro',
[15291]='Save',
[15292]=[[Tap to enter. The content cannot be longer than %s bytes.]],
[15293]=[[Content is too long!]],
[15294]='Saved!',
[15295]=[[Cannot join as this union is not in your warzone.]],
[15296]=[[No available recruitment now. Go to the top right to view all the recruitments information~]],
[15297]=[[All Recruitments]],
[15298]=[[Official Customer Service]],
[15299]=[[Official staff account. Cannot display personal info.]],
[15300]=[[Alchemist Guild's Request]],
[15301]=[[Defeat the Abyss Legion]],
[15302]=[[Clean the Swamp]],
[15303]=[[Garlic Samurai's Worries]],
[15304]=[[Find Anta's Lamb]],
[15305]=[[Forest's Commission]],
[15306]=[[Eternal Tree's Commission]],
[15307]=[[Extinguish the Forest Fire]],
[15308]=[[Find Denise]],
[15309]=[[Onik's Confusion]],
[15310]=[[Protect Bard]],
[15311]=[[Explore the Mysterious Cave]],
[15312]=[[Find Snow Spirit]],
[15313]=[[Sage's Academy's Commission]],
[15314]=[[Collect Kira's Snowflakes]],
[15315]=[[Remove the Vipers]],
[15316]=[[Noemi's Confusion]],
[15317]=[[Imperial Warrior's Woes]],
[15318]=[[The Bard's Performance]],
[15319]=[[Repair Andal's Accordion]],
[15320]=[[Find the Time Traveler]],
[15321]=[[Challenge in Abyss City]],
[15322]=[[Collect Phoenix Feathers]],
[15323]=[[Control the Snowfield Storm]],
[15324]=[[Cupid's Trouble]],
[15325]=[[Poison Toad's Commission]],
[15326]=[[Evil Shaman's Confusion]],
[15327]=[[Defeat Roger]],
[15328]=[[Ice Kingdom's Ball]],
[15329]=[[Play Hide-and-Seek With Void Twin]],
[15330]=[[Uncovering the Faceless Man]],
[15331]=[[Logging in the Hazy Forest]],
[15332]=[[Cleaning Weeds for the Floral Spirit]],
[15333]=[[Natalie's Troubles]],
[15334]=[[Knifer's Commission]],
[15335]=[[Kerridia's Request]],
[15336]=[[Spore Warrior's Confusion]],
[15337]=[[Heal Nicole's Wounds]],
[15338]=[[Forest's Gifts]],
[15339]=[[Investigate Abyss City]],
[15340]=[[Find the Ocean God's Trident]],
[15341]=[[Heal Renika]],
[15342]=[[Electric Boxer's Secret]],
[15343]=[[Assist the Holy Inquisitor]],
[15344]=[[Find Marissa]],
[15345]=[[Help the Merciful Tree Spirit]],
[15346]=[[Pursue the Dark Wizard]],
[15347]=[[Solve Nocturn Queen's Dilemma]],
[15348]=[[Cross the World]],
[15349]=[[Investigate Dracula]],
[15350]=[[Werewolf's Track]],
[15351]=[[Cupid's Arrow]],
[15352]=[[Floral Shadowblade's Match]],
[15353]=[[Listen to the Divine Blessing]],
[15354]=[[Drive Away the Fear Raven]],
[15355]=[[Help Silsa Dispense Medicine]],
[15356]=[[Angel's Commission]],
[15357]=[[5-Star Acorn Tavern Quest 6]],
[15358]=[[5-Star Acorn Tavern Quest 7]],
[15359]=[[5-Star Acorn Tavern Quest 8]],
[15360]=[[5-Star Acorn Tavern Quest 9]],
[15361]=[[5-Star Acorn Tavern Quest 10]],
[15362]=[[5-Star Acorn Tavern Quest 11]],
[15363]=[[5-Star Acorn Tavern Quest 12]],
[15364]=[[5-Star Acorn Tavern Quest 13]],
[15365]=[[6-Star Acorn Tavern Quest 1]],
[15366]=[[6-Star Acorn Tavern Quest 2]],
[15367]=[[6-Star Acorn Tavern Quest 3]],
[15368]=[[6-Star Acorn Tavern Quest 4]],
[15369]=[[6-Star Acorn Tavern Quest 5]],
[15370]=[[6-Star Acorn Tavern Quest 6]],
[15371]=[[6-Star Acorn Tavern Quest 7]],
[15372]=[[6-Star Acorn Tavern Quest 8]],
[15373]=[[6-Star Acorn Tavern Quest 9]],
[15374]=[[6-Star Acorn Tavern Quest 10]],
[15375]=[[6-Star Acorn Tavern Quest 11]],
[15376]=[[6-Star Acorn Tavern Quest 12]],
[15377]=[[6-Star Acorn Tavern Quest 13]],
[15378]=[[7-Star Acorn Tavern Quest 1]],
[15379]=[[7-Star Acorn Tavern Quest 2]],
[15380]=[[7-Star Acorn Tavern Quest 3]],
[15381]=[[7-Star Acorn Tavern Quest 4]],
[15382]=[[7-Star Acorn Tavern Quest 5]],
[15383]=[[7-Star Acorn Tavern Quest 6]],
[15384]=[[7-Star Acorn Tavern Quest 7]],
[15385]=[[7-Star Acorn Tavern Quest 8]],
[15386]=[[7-Star Acorn Tavern Quest 9]],
[15387]=[[7-Star Acorn Tavern Quest 10]],
[15388]=[[7-Star Acorn Tavern Quest 11]],
[15389]=[[7-Star Acorn Tavern Quest 12]],
[15390]=[[7-Star Acorn Tavern Quest 13]],
[15428]=[[Do you want to spend %d Diamond to speed up this quest?]],
[15429]=[[You cannot unlock quests that have already been started.]],
[15430]=[[Insufficient Diamonds]],
[15431]=[[Do you want to reset this hero quest?]],
[15432]=[[You have a rare quest. Are you sure you want to refresh?]],
[15433]=[[Insufficient Basic Quest Coupons.]],
[15434]=[[Insufficient Advanced Quest Coupons.]],
[15435]=[[This hero is already deployed.]],
[15436]=[[You have deployed enough heroes for the quest.]],
[15437]=[[You do not have enough heroes that qualify for this quest.]],
[15438]=[[This hero has not met the quest requirements.]],
[15500]='h(s)',
[15501]='min(s)',
[15502]='Complete',
[15503]=[[The quest (%d/%d) will be reset in]],
[15504]='Requirements',
[15505]='Select',
[15506]=[[Quest requirements not met:]],
[15507]=[[Choose which quests you want to refresh.]],
[15508]=[[Below 4-Star]],
[15509]=[[Below 5-Star]],
[15510]=[[Below 6-Star]],
[15511]='Accept',
[15512]=[[Speed Up]],
[15513]='Use',
[15514]=[[Reset Countdown]],
[15515]='Quests',
[15516]=[[Below 7-Star]],
[15517]=[[Refresh all quests]],
[15518]='All',
[15519]=[[All dispatched quests completed.]],
[15520]=[[Bounty Quest]],
[15521]=[[Lv. 1]],
[15522]=[[Lv. 2]],
[15523]=[[Lv. 3]],
[15524]=[[Lv. 4]],
[15525]=[[Lv. 5]],
[15526]=[[Lv. 6]],
[15527]=[[Lv. 7]],
[15528]=[[Desired Quest Grades]],
[15529]=[[Current Stage]],
[15530]=[[Next Stage]],
[15531]=[[Requirements to enter next stage]],
[15532]=[[[Adventure] %s cleared]],
[15533]=[[%s: %s heroes]],
[15534]=[[Highest stage reached!]],
[15535]=[[Below 8-Star]],
[15536]=[[Below 9-Star]],
[15537]=[[9-Star and Below]],
[15538]=[[Bar Guide]],
[15539]='Deployed',
[15540]=[[High Star Quest Chance Up!]],
[15541]=[[Unlocked by clearing [Adventure] %s.]],
[15542]=[[Sweepes <color=#87F425>+%d</color>]],
[15543]=[[Sweepes after clear]],
[15544]=[[Core Rewards]],
[15545]=[[Clear difficult dungeons to get amazing rewards.]],
[15546]=[[Abyss City: Available to Top 30 heroes by BP.]],
[15547]=[[Buffs are only valid in dungeons of the current difficulty level. Once you enter the next difficulty level or the dungeons reset, obtained buffs will disappear.]],
[15548]=[[Stage Rewards]],
[15549]=[[New items added to store.]],
[15550]='Dangerous',
[15551]=[[No stage to sweep.]],
[15552]=[[All Claimed]],
[15553]='Sweep',
[15554]='Reset',
[15555]=[[After reset:
1. All dead heroes will be resurrected.
2. Obtained relics will be cleared.
3. You will return to Stage 1 of the current difficulty level.

Are you sure you want to reset? (<color=#2DB52DFF>Resets Remaining: %d</color>)]],
[15601]=[[Recovery Potion]],
[15602]=[[Archangel Potion]],
[15603]=[[Energy Potion]],
[15604]=[[Fatality Potion]],
[15605]=[[Armor PEN Potion]],
[15606]=[[DEF Down Potion]],
[15607]=[[Use to recover 35% of HP.]],
[15608]=[[Use to recover 100% of HP.]],
[15609]=[[Use to recover 50% of HP and 100 Energy.]],
[15610]=[[Lower the effectiveness of enemy healing by 10% in Abyss City.]],
[15611]=[[Increases hero Armor PEN by 10% in Abyss City.]],
[15612]=[[Lowers enemy Armor by 10% in Abyss City.]],
[15613]=[[Are you sure you want to use this potion?]],
[15614]='Novice',
[15615]='Easy',
[15616]='Common',
[15617]='Hard',
[15618]='Nightmare',
[15619]='Hell',
[15620]='Destruction',
[15621]='Insane',
[15622]='Legendary',
[15623]='Mythic',
[15624]='Peak',
[15625]='Transcendent',
[15630]=[[Countdown Begins]],
[15631]=[[Countdown Ends]],
[15632]=[[No heroes deployed.]],
[15633]=[[Skip Battle]],
[15634]='Buff',
[15635]=[[Select A Buff]],
[15636]=[[Abyss City not yet unlocked!]],
[15637]=[[Stage Cleared]],
[15638]='Stage',
[15639]=[[Stage failed. Select other heroes to complete the challenge.]],
[15640]=[[All heroes have been defeated. Challenge failed.]],
[15641]=[[Challenge Failed]],
[15642]=[[Start Battle]],
[15643]=[[You cannot adjust your lineup once the battle has started.]],
[15644]=[[Monster Preview]],
[15645]=[[Abyss City will become unavailable after %s. Make sure to tackle it while it is still available.]],
[15646]=[[Stage Rewards]],
[15647]=[[Stage %s]],
[15648]=[[No purchasable items.]],
[15649]=[[The event has not started.]],
[15650]='Completed',
[15651]=[[Complete the previous stage to unlock.]],
[15652]=[[Current stage completed.]],
[15653]=[[Stage Completion Achievement]],
[15654]=[[Sweep Rewards]],
[15655]=[[Start Adventure]],
[15656]=[[Sweep Rewards]],
[15660]=[[Novice's Journey]],
[15661]=[[Easy Battle]],
[15662]=[[Unexpected Challenge]],
[15663]=[[Breaking Out]],
[15664]=[[Nightmare Lord]],
[15665]=[[Hell Trial]],
[15666]=[[Shadow of Death]],
[15667]=[[Insane Battle]],
[15668]=[[I Am Legend]],
[15669]=[[Creating Legends]],
[15670]=[[Affected Heroes:]],
[15671]=[[All Heroes]],
[15672]=[[No Heroes]],
[15673]=[[All Back Row Heroes]],
[15674]=[[All Front Row Heroes]],
[15675]=[[Enemy Back Row Heroes]],
[15690]=[[Reward Collection Progress]],
[15691]=[[Exploration Rewards]],
[15692]=[[Remaining Rewards]],
[15693]=[[Quick Sweep]],
[15694]=[[Current Sweep Difficulty: %s. There are chests available.]],
[15695]=[[Clear Stage %s to obtain chest rewards.]],
[15696]=[[No Buffs]],
[15697]=[[Dungeon Store]],
[15698]=[[Sweep is currently not available.]],
[15699]=[[Open chests to obtain rewards.]],
[15795]=[[%s has kicked %s from the union.]],
[15796]=[[Are you sure you want to kick <color=#db5200>%s</color>?]],
[15797]=[[You've joined a new union!]],
[15798]=[[Order limit reached.]],
[15799]=[[You haven't made an order yet.]],
[15800]=[[Order Amount]],
[15801]='h(s)',
[15802]='Start',
[15803]=[[Obtained Order]],
[15804]=[[Reset Time]],
[15805]=[[Contribution Level]],
[15806]=[[Order Production]],
[15807]=[[Boost Contribution]],
[15808]=[[Union Check-In]],
[15809]=[[Hero Expedition]],
[15810]=[[Union Warzone]],
[15811]=[[Contribution Ranking]],
[15812]=[[Your order has been completed.]],
[15813]=[[%d Diamonds will be consumed to upgrade the order. Are you sure you want to continue?]],
[15814]='Go',
[15815]=[[Union <color=#dd3c2dFF>Hall</color>]],
[15816]=[[Union Name:]],
[15817]=[[No Announcements]],
[15818]='No.:',
[15819]='Members:',
[15820]='Log',
[15821]=[[Manage Union]],
[15822]='Move',
[15823]=[[Apply for Officer]],
[15824]='Applied',
[15825]='Officer',
[15826]='Online',
[15827]=[[ minute(s) ago]],
[15828]='h(s)',
[15829]=[[ hour(s) ago]],
[15830]='Day(s)',
[15831]='Kick',
[15832]='Edit',
[15833]='Announcements',
[15834]=[[Enter an announcement.]],
[15835]=[[The announcement is too long.]],
[15836]=[[Union Log]],
[15837]=[[Join Union]],
[15838]=[[Leave Union]],
[15839]=[[You've been transferred to another union as you haven't logged in for 3 consecutive days]],
[15840]=[[Accept Officer Role]],
[15841]='Resign',
[15842]=[[Manage Union]],
[15843]=[[Request List]],
[15844]='Accepted',
[15845]='Rejected',
[15846]=[[Remove Officer]],
[15847]=[[Group Mail]],
[15848]=[[No Applications]],
[15849]=[[Are you sure you want to resign from your officer role?]],
[15850]=[[Select Union Banner]],
[15851]='Recommended',
[15852]='Search',
[15853]='Apply',
[15854]=[[Auto Move]],
[15855]=[[Recommended Union:]],
[15856]=[[Enter a union name.]],
[15857]=[[Demote Officer]],
[15858]=[[Begin Countdown:]],
[15860]=[[Union %s has successfully conquered the Time Fortress.]],
[15861]=[[Attack Fortress]],
[15868]=[[Resurrection Countdown: %s]],
[15869]=[[Instant Resurrection]],
[15870]=[[The Leader of Darkness has been defeated. Go to the next page to continue the challenge.]],
[15872]='Defeat',
[15873]=[[Return Rewards]],
[15874]='Conquer',
[15875]='Lineup',
[15876]=[[Home Level:]],
[15877]='Power:',
[15878]='Territory:',
[15879]='Garrison',
[15880]='Enemy',
[15881]='Ally',
[15882]=[[Tap to Close]],
[15883]=[[Allied Team]],
[15884]='Mark',
[15885]=[[Expedition Target]],
[15886]='(Dangerous)',
[15887]=[[The garrison protecting this territory is quite strong. Make sure you tread carefully.]],
[15888]=[[Refresh %s]],
[15889]=[[Remaining HP]],
[15890]=[[Warzone Info]],
[15891]=[[Warzone Production]],
[15892]=[[Total Capacity]],
[15893]=[[DMG Source]],
[15894]=[[Ally Union]],
[15895]='Go',
[15896]=[[Warzone Report]],
[15897]=[[Union Name]],
[15898]=[[Union Update]],
[15899]='Player',
[15900]='Union',
[15901]=[[Union Exploration]],
[15902]='Exploration',
[15903]=[[Leave Trap]],
[15904]=[[Challenge Enemy]],
[15905]='Rewards',
[15906]='Sweep',
[15907]='Quest',
[15908]=[[Weekly Point Ranking]],
[15909]=[[Weekly Flower Ranking]],
[15910]=[[Stamina Recovery Time: <color=#30E05EFF>%s</color>]],
[15911]=[[Stamina Full]],
[15912]=[[You have found the Leader of Darkness. Defeat them to obtain rewards.]],
[15913]=[[You've fallen into a trap. Hurry! Ask for help in the union chat!]],
[15914]=[[The Leader of Darkness has been defeated.]],
[15915]=[[<color=#ffffff>Insufficient flowers. Purchase more from the Giant Merchant.</color>]],
[15916]=[[You can't gift yourself flowers.]],
[15917]=[[Daily flower gifting limit reached.]],
[15918]=[[Insufficient Stamina. Ask for help in the union chat.]],
[15919]=[[Ask for help in the union chat to quickly escape the trap.]],
[15920]=[[You must defeat the Leader of Darkness first.]],
[15921]=[[You have helped someone escape from a trap.]],
[15922]=[[I saved %s from a trap.]],
[15923]='Rewards',
[15924]='Deploy',
[15925]='Location',
[15926]='Attack',
[15927]='Defense',
[15928]='Power',
[15929]=[[Garrison Strength]],
[15930]=[[Territory Garrison]],
[15931]=[[Stamina Cost]],
[15932]=[[March Distance]],
[15933]=[[March Time]],
[15934]=[[Arrival Time]],
[15935]='Marching...',
[15936]=[[<color=#ffffff>Successfully marked. Visible to all union members.</color>]],
[15937]=[[Union Officer Mark]],
[15938]=[[<color=#ffffff>Mark removed</color>.]],
[15939]='Remove',
[15940]=[[Lv. 1 Territory]],
[15941]=[[Lv. 2 Territory]],
[15942]=[[Lv. 3 Territory]],
[15943]=[[Lv. 4 Territory]],
[15944]=[[Lv. 5 Territory]],
[15945]=[[Lv. 1 Gold Mine]],
[15946]=[[Lv. 2 Gold Mine]],
[15947]=[[Lv. 3 Gold Mine]],
[15948]=[[Lv. 4 Gold Mine]],
[15949]=[[Diamond Mine]],
[15950]=[[Warzone Lord]],
[15951]=[[Warzone Lord]],
[15952]=[[Warzone Lord]],
[15953]='Home',
[15954]=[[Union Hall]],
[15955]=[[Union Store]],
[15956]=[[Light Hall]],
[15957]=[[Sealed Land]],
[15958]='Spar',
[15959]='Retreating...',
[15960]='Rewards',
[15961]='Gold',
[15962]='Diamond',
[15963]=[[Change Lineup]],
[15964]=[[Conquest Successful]],
[15965]=[[Conquest Failed]],
[15966]=[[Defense Successful]],
[15967]=[[Defense Failed]],
[15968]=[[Current Position]],
[15969]='Distance',
[15970]=[[Time Remaining]],
[15971]='Rank',
[15972]='Territories',
[15973]='Mines',
[15974]=[[Arena Lord]],
[15975]='Contribution',
[15976]=[[No ranking info found.]],
[15977]=[[No order info found.]],
[15978]='Accept',
[15979]=[[Order Amount]],
[15980]=[[Union Check-In]],
[15981]='Challenge',
[15982]=[[Territory Battle]],
[15983]=[[Lv. <color=white>%d</color>]],
[15984]=[[Battle Rewards]],
[15985]=[[Finishing Blow Rewards]],
[15986]=[[Battle Rewards]],
[15987]='Leader',
[15988]=[[Union Exploration]],
[15989]='Points',
[15990]=[[Point Ranking]],
[15991]=[[Point Ranking Rewards]],
[15992]=[[Flower Ranking]],
[15993]=[[Flower Ranking Rewards]],
[15994]='Member',
[15995]=[[ day(s) ago]],
[15996]=[[%s has activated the event.]],
[15997]=[[Tap to edit union announcements.]],
[15998]=[[Treasure Hunt]],
[15999]=[[You gifted %s flowers.]],
[16000]=[[Level Cap]],
[16001]=[[Level Cap: 140]],
[16002]=[[Level Cap: 160]],
[16003]=[[Level Cap: 180]],
[16004]=[[Level Cap: 200]],
[16005]=[[Level Cap: 240]],
[16006]=[[Level Cap: 260]],
[16007]=[[Level Cap: 280]],
[16008]=[[Level Cap: 300]],
[16009]=[[Stat Boost: 20%]],
[16010]=[[Stat Boost: 30%]],
[16011]=[[HP Boost: 14%]],
[16012]=[[ATK Boost: 10%]],
[16013]=[[Stat Boost: 100%]],
[16046]=[[Weekly Maintenance:]],
[16047]='Applied',
[16048]='Invited',
[16049]=[[Quick Join]],
[16050]=[[Are you sure you want to kick %s from the union?]],
[16051]=[[Group mail sent.]],
[16052]=[[You must enter a valid auto clear time.]],
[16053]=[[Unlocked at Lv. %s.]],
[16054]=[[You must enter valid restriction info.]],
[16055]='No.:',
[16056]='Members:',
[16057]='Level:',
[16058]=[[Union Officers:]],
[16059]=[[Promotion Settings]],
[16060]=[[Each day, the player with the highest Activity Points will be promoted to elder.]],
[16061]=[[No Auto Promotion]],
[16062]=[[Promotion Settings]],
[16063]=[[Each day, the player with the highest Activity Points will be promoted to elder.]],
[16064]=[[No Auto Promotion]],
[16065]=[[Invite to Union]],
[16066]='Invite',
[16067]=[[Apply to Join Union]],
[16068]='Accept',
[16069]='Reject',
[16070]=[[Union Invitation]],
[16071]=[[Union Assistance]],
[16072]=[[1. Each time a union member earns 1 Activity Point, the union gains 1 EXP. If no union member earns Activity Points for three consecutive days, the union will be automatically dissolved.]],
[16073]=[[2. Union activity quests refresh once per week.]],
[16074]=[[3. The union members tab will show the total Activity Points over the last 7 days.]],
[16075]=[[4. When the union leader is offline for over 48 hours, other union members can start the impeachment process. Once the number of members who agree to the impeachment exceeds 50% of the total online members over the last 24 hours, the impeachment will be successful.]],
[16076]=[[5. After impeachment, the union president will be demoted a regular member. The member or elder with the highest amount of Activity Points and has logged in to the game in the last 24 hours will automatically become the new president.]],
[16077]=[[No Invitation Info]],
[16078]=[[Union Promotion:]],
[16079]=[[1. When the union is formed, the auto promotion setting will be enabled by default.]],
[16080]='Rating:',
[16081]=[[Exceeded %s players]],
[16082]=[[Lucky 1x Draw: %s Rating]],
[16083]=[[Lucky 10x Draw: %s Rating]],
[16084]='Share',
[16085]=[[How to Play]],
[16086]=[[Rally Allies]],
[16087]=[[Strategy Battle]],
[16088]=[[2. After activating Auto Promotion, the union member with the highest Activity Points will be promoted to Elder at UTC 00:00 each day if there are available vacancies.]],
[16089]=[[Joint Growth]],
[16090]='Battle',
[16091]=[[Claim Rewards]],
[16092]='Cleared',
[16093]=[[Ranking Rewards]],
[16094]='Claim',
[16095]=[[All Rewards]],
[16096]=[[Current Rank:]],
[16301]=[[Trial progress reaches 20F for 4 factions]],
[16302]=[[Trial progress reaches 40F for 4 factions]],
[16303]=[[Trial progress reaches 60F for 4 factions]],
[16304]=[[Trial progress reaches 80F for 4 factions]],
[16305]=[[Trial progress reaches 100F for 4 factions]],
[16306]=[[Trial progress reaches 120F for 4 factions]],
[16307]=[[Trial progress reaches 140F for 4 factions]],
[16308]=[[Trial progress reaches 160F for 4 factions]],
[16309]=[[Trial progress reaches 180F for 4 factions]],
[16310]=[[Trial progress reaches 200F for 4 factions]],
[16311]=[[Trial progress reaches 220F for 4 factions]],
[16312]=[[Trial progress reaches 240F for 4 factions]],
[16313]=[[Trial progress reaches 260F for 4 factions]],
[16314]=[[Trial progress reaches 280F for 4 factions]],
[16315]=[[Trial progress reaches 300F for 4 factions]],
[16316]=[[Trial progress reaches 320F for 4 factions]],
[16317]=[[Trial progress reaches 340F for 4 factions]],
[16318]=[[Trial progress reaches 350F for 4 factions]],
[16319]=[[Trial progress reaches 380F for 4 factions]],
[16320]=[[Trial progress reaches 400F for 4 factions]],
[16350]=[[Proving Grounds]],
[16351]=[[Faction Mastery]],
[16352]=[[Lv. %s]],
[16353]=[[Faction Trial]],
[16354]=[[Max level reached!]],
[16355]='Achievements',
[16356]='Opening...',
[16357]=[[Opens every <color=#87F425>%s</color>]],
[16358]='1',
[16359]='2',
[16360]='3',
[16361]='4',
[16362]='5',
[16363]='6',
[16364]='7',
[16365]='%sF',
[16366]=[[Cannot challenge. Up to %s floors can be challenged per day.]],
[16367]=[[Clear %sF]],
[16368]=[[Clear %sF in 4 Faction Trials]],
[16369]='<color=#f7da15>God</color><color=#f08ddb>/Voider</color>',
[16370]=[[Bonus inactive]],
[16371]=[[Faction Trial Progress Leaderboard]],
[16372]=[[You can only deploy heroes from designated factions]],
[16373]=[[Faction Mastery Upgrade]],
[16374]=[[Upgrade Faction Mastery to Lv. %s to activate the following stat bonuses:]],
[16375]=[[Opens in %s]],
[16376]=[[Clear %s F to unlock]],
[16377]='Activate',
[16378]='Activated',
[16379]='Upgrade',
[16380]='N.A.',
[16381]=[[Mastery Bonus]],
[16382]=[[Proving Grounds]],
[16383]=[[Forbidden Land]],
[16384]=[[Fanged Cave]],
[16385]=[[Thorn Vine]],
[16386]=[[Endless Abyss]],
[16387]=[[Clear the previous floor first]],
[16388]=[[Complete the camp trial level {%s1}]],
[16401]='Create',
[16402]='Replace',
[16403]=[[The length must be between 4–16 characters...]],
[16404]=[[Diamond Cost]],
[16405]='Cancel',
[16406]='Create',
[16407]=[[Union Icon]],
[16408]=[[Union Level]],
[16409]=[[Please choose the icon you like]],
[16410]='Level',
[16411]='President',
[16412]='Elder',
[16413]='Member',
[16414]=[[Union Log]],
[16415]=[[Manage Union]],
[16416]=[[Recommended Unions]],
[16417]=[[Please enter Union name or ID]],
[16418]=[[Union Name]],
[16419]='Level',
[16420]='Members',
[16421]=[[Activity Points]],
[16422]='Search',
[16423]=[[Minimum level required]],
[16424]=[[Union Size]],
[16425]=[[Union Language]],
[16426]=[[Union Verification Info]],
[16427]=[[Union Members]],
[16428]=[[Join Now]],
[16429]=[[Apply to Join]],
[16430]='Union:',
[16431]=[[%s has been kicked from the union!]],
[16432]=[[%s has kicked you from the union!]],
[16433]=[[Manage Members]],
[16434]=[[Player %s got the position of %s!]],
[16435]=[[This position has already been filled!]],
[16436]=[[%s has become the new President!]],
[16437]=[[Request List]],
[16438]=[[%s has joined the union!]],
[16439]='Apply',
[16440]=[[Union Settings]],
[16441]=[[Notify All]],
[16442]=[[Search for Union]],
[16443]=[[Leave Union]],
[16444]=[[Impeach President]],
[16445]=[[Impeachment successful. %s has become the new President!]],
[16446]=[[Impeachment failed. The President remains unchanged!]],
[16447]=[[Impeachment in progress]],
[16448]='Announcements',
[16449]='Slogan',
[16450]=[[Clear Settings]],
[16451]=[[Tap to edit union announcements.]],
[16452]=[[Tap to edit union slogan.]],
[16453]=[[%s has a new announcement. Please check!]],
[16454]=[[%s changed the union announcement!]],
[16455]=[[Language Settings]],
[16456]='Rename',
[16457]=[[Please enter a union name]],
[16458]=[[Reject all requests]],
[16459]=[[Accept all requests]],
[16460]=[[Meet the following criteria to apply]],
[16461]=[[Auto-Clear Off]],
[16462]=[[Meet the following conditions to auto-clear]],
[16463]=[[Union disbanded!]],
[16464]=[[<color=#E20101>Anyone can join</color>]],
[16465]=[[<color=#ff9c01>Apply to join</color>]],
[16466]=[[<color=#ee3135>No one can join</color>]],
[16467]=[[Application Settings]],
[16468]=[[Minimum Level]],
[16469]=[[Minimum CP]],
[16470]=[[Offline Duration]],
[16471]=[[Transfer Presidency]],
[16472]='Promote',
[16473]='Demote',
[16474]='Dismiss',
[16475]=[[Level too low. Please upgrade your officer level first.]],
[16476]=[[The slogan cannot be empty. Please enter something.]],
[16477]=[[The slogan is too long. Please shorten it.]],
[16478]=[[Please enter details.]],
[16479]=[[You've joined a new union!]],
[16480]=[[You've left the union.]],
[16481]=[[Settings saved]],
[16482]=[[Leave the current union? You'll need to wait 10 minutes before you can join a new one.]],
[16483]=[[%s has left the union!]],
[16484]=[[%s changed the union icon!]],
[16485]=[[%s changed the union name!]],
[16486]=[[%s created the union %s!]],
[16487]=[[%s changed the union slogan!]],
[16488]=[[%s changed the union language!]],
[16489]=[[Thanks to everyone's hard work, the union has reached Lv. %s!]],
[16490]=[[Transfer the position of President to %s?]],
[16491]=[[Position changed successfully]],
[16492]=[[Disband Union]],
[16493]=[[Disband the union? Once disbanded, all information related to this union will be deleted.]],
[16494]=[[You haven't joined a union yet!]],
[16495]=[[Under development. Coming soon!]],
[16496]=[[Congratulations on unlocking Unions. Go take a look!]],
[16497]=[[Join a union to enjoy the game with like-minded friends!]],
[16498]=[[Promote %s to Elder?]],
[16499]=[[Demote %s to Member?]],
[16700]=[[Lv. %s]],
[16701]=[[Resource Output]],
[16702]=[[Garrison CP]],
[16703]='Unreachable',
[16704]=[[Mark Message]],
[16705]=[[Enter content here (no more than 100 characters).]],
[16706]=[[Gold Mine]],
[16707]=[[To X: %s Y: %s]],
[16708]=[[Lv. %s Union Warzone]],
[16709]=[[This season will end in %sd %sh %sm %ss]],
[16710]=[[Battle Rewards]],
[16711]=[[I shared coordinates of a Lv. %s Gold Mine (%s, %s)]],
[16712]=[[I shared coordinates of a Lv. %s Territory (%s, %s)]],
[16713]=[[I shared coordinates of a Domination (%s, %s)]],
[16714]=[[I shared coordinates of union %s's Home (%s, %s)]],
[16715]=[[I shared coordinates of a Diamond Mine (%s, %s)]],
[16716]=[[(Union %s) %s occupied a Territory (%s, %s)]],
[16717]=[[(Union %s) %s attacked a Domination (%s, %s)]],
[16718]=[[(Union %s) %s occupied a Diamond Mine (%s, %s)]],
[16719]=[[(Union %s) %s occupied a Lv. %s Gold Mine (%s, %s)]],
[16720]=[[Insufficient authority. Cannot mark.]],
[16721]='N.A.',
[16750]=[[%s is asking for help]],
[16751]='Help',
[16752]=[[Reward for helping a union member]],
[16753]='Help',
[16754]='Claim',
[16755]='Progress',
[16756]='Rank',
[16757]='Member',
[16758]='Week',
[16759]='Week/Total',
[16760]=[[Assist Leaderboard]],
[16761]=[[%s assisted you %s times.]],
[16762]=[[Select Reward]],
[16763]=[[You must select a reward first.]],
[16764]='Assists:',
[16765]=[[Refresh Rewards:]],
[16766]=[[S+ Hero Shard]],
[16767]=[[Rare Resource]],
[16768]=[[Players can pick 1 S+ Shard each day.]],
[16769]=[[Players can pick 10 rare resources each day.]],
[16770]=[[Only 1 reward can be chosen.]],
[16771]=[[Activates each day when Activity Points reach %s.]],
[16772]=[[Select an assistance reward.]],
[16773]=[[There are no active assist requests.]],
[16774]=[[Number of mutual assistance rewards has reached today's limit and you cannot receive any more.]],
[16788]=[[The mode will open in %s.]],
[16789]='Empty',
[16790]=[[Battle Log]],
[16791]=[[Dear Traveler, this is the battle log of your Union while you were offline.]],
[16792]=[[Total offline duration: %sh %sm]],
[16793]=[[%sh %sm ago]],
[16794]=[[<color=#70DFFFFF> attacked you but your defense was </color><color=#FF6467FF>successful</color>!]],
[16795]=[[<color=#ffffff>%s</color><color=#70DFFFFF> attacked you and your defense </color><color=#A5A5A3FF>failed</color>!]],
[17500]=[[Quick AFK will grant <color=#9AFF9A>120 minutes</color> worth of AFK rewards. Quick AFK Perk: Gain <color=#9AFF9A>3</color> additional free Quick AFKs.]],
[17501]=[[Quick AFK]],
[17502]=[[Free Uses:]],
[17503]='Purchases:',
[17504]=[[Quick AFK (120m)]],
[17505]=[[Obtain Item]],
[17506]=[[Randomly Obtained From Gathering:]],
[17507]=[[Spent %d Diamonds on Quick AFK]],
[17508]='Gather',
[17509]=[[Reset Time:]],
[17510]=[[Free Charges]],
[17511]=[[Guaranteed Gather Rewards: Fuel x%s]],
[17512]=[[Bonus Free Quick AFKs: +%s]],
[17513]=[[Purchase Perks]],
[17514]=[[Purchase Rewards:]],
[17515]=[[Activate Perk (30 days):]],
[17516]='Activate',
[17517]=[[Quick AFK Perk]],
[17526]=[[All Rewards Unlocked]],
[17527]=[[Guaranteed Gather Rewards: Energy Capsule x%s]],
[17599]=[[[Quick Battle] Completed]],
[18201]=[[Purchase 10x Common Scrolls to start summoning. You are guaranteed to get an Elite hero within your first 10 summons.]],
[18202]=[[You have completed many achievements! Tap to claim all your achievement rewards.]],
[18203]=[[Upgrading Team Mastery can increase hero stats and unlock new lineups when certain criteria are met.]],
[18204]=[[Select a gear to extract. You can raise a gear's level to give it higher stats.]],
[18205]=[[Enter the Union Hub to obtain a huge amount of Gold. You can also obtain lucrative rewards by joining Union Exploration and defeating enemy leaders.]],
[18206]=[[Unlocked the [Garden]. This is the start of a new journey!]],
[18207]=[[Select your desired faction in the Esper Conversion Pod. Tap summon to obtain a hero from the specified faction.]],
[18208]=[[Use the scroll for a 10x draw! You are guaranteed to get a Excellent Hero within your first 10 summons!]],
[18209]=[[You have completed many achievements! Tap to claim all your achievement rewards.]],
[18210]=[[Welcome to the Arena! You can pit yourself against other players here.]],
[18211]=[[Don't worry if you win or lose. You'll be rewarded either way!]],
[18212]=[[Tap here to start the battle!]],
[18213]=[[Upgrade Faction Mastery to greatly increase the power of heroes in that faction.]],
[18214]=[[Unlock high-level teams by raising Faction Mastery.]],
[18215]=[[We recommended upgrading the Light that is compatible with your heroes first.]],
[18216]=[[This is a private, personal space.]],
[18217]=[[We can plant Resource Trees here to obtain the corresponding resources.]],
[18218]=[[It needs time to fully grow. Let's go check out the tree house.]],
[18219]=[[You can establish bonds with other players here. They can help you out when you're busy.]],
[18220]=[[Tap here to collect the resources]],
[18221]=[[Congratulations on joining a new union.]],
[18222]=[[Tap here to check in. You can obtain rewards by checking in every day.]],
[18223]=[[You can find many treasures by exploring. Try it out!]],
[18224]=[[You can obtain rewards just by attacking the Boss. When the Boss is defeated, all participating members will obtain additional rewards.]],
[18225]=[[Check it out when you're free!]],
[18226]=[[It needs time to fully grow. Check on it every now and then.]],
[18240]=[[Welcome to the Union Warzone. This is where wars between unions take place.]],
[18241]=[[The green zones mark out your union's territory. Those with green avatar frames are your allies.]],
[18242]=[[Those with blue avatar frames are your union's allies. Everyone else is an enemy.]],
[18243]=[[This is the union's objective. Complete the objective to obtain great rewards.]],
[18244]=[[Tap on a territory to learn more about it.]],
[18245]=[[You can attack or defend against neighboring tiles.]],
[18246]=[[Now that you understand the basics, let's start the battle.]],
[18247]=[[Tap here to confirm the movement. Note that teams will take some time to move.]],
[18248]=[[You can enter the 2nd level after clearing Stage %s. Return to [Adventure] to continue with the challenge.]],
[18249]=[[Your team suffered a devastating blow. Return to Adventure to continue with the challenge.]],
[18250]=[[Let us slot in a new hero first.]],
[18251]=[[Select a hero, then tap [Confirm]. This hero will be raised to the same level as the lowest-level Soul Source.]],
[18252]=[[This is the power of the Spirit Tower. You will obtain the Soul Source that can unlock more AFK Rewards slots for the next [Adventure]. Good luck, Traveler!]],
[18253]=[[Tap to open the Dismiss Hero interface. You can dismiss unneeded heroes and obtain Bloodstones.]],
[18254]=[[Tap to open the [Hero Reset] interface. Heroes can be reset back to Lv. 1. Resources spent on upgrading rest heroes will be returned.]],
[18255]=[[Use the scrolls for a 10x Summon.]],
[18256]=[[There are more rewards waiting for you in the Tower of Fate.
Keep up the challenges!]],
[18257]=[[Use the Resurrection Potion
to restore the HP and Energy of all heroes.]],
[18258]=[[There are more levels at the Proving Grounds.
You can obtain more Rankup Stones there. What are you waiting for?]],
[18285]=[[Sign up to set up a defense lineup. If the defensive lineup loses, you will lose Battle Points.]],
[18286]=[[You have successfully signed up. The battle will start in the coming weekend.]],
[18287]=[[You can choose to attack your opponents' defense lineups. If you win or reduce the lineups' HP, you will gain points. Select an opponent.]],
[18288]=[[Each hero can only be used once in battle. You can obtain more points for your union by using synergistic offensive lineups.]],
[18289]=[[Warzone has been unlocked. Join the battle and help your union achieve victory!]],
[18290]=[[Defeat your opponents to gain their Defense Points. Take note that with each victory, the amount of Defense Points will decrease. Now, let the battle begin!]],
[18301]=[[Insufficient %s]],
[18302]=[[How to get %s]],
[18303]=[[Below Lv. %d]],
[18304]=[[You are at Lv. %d. Get character EXP via the following methods:]],
[18305]=[[Insufficient resources]],
[18306]=[[Can be obtained via the following methods:]],
[18307]='Recommended',
[18308]=[[Clear %s to unlock.]],
[18321]=[[Insufficient Advanced Chips. You can obtain more from the Giant Merchant.]],
[18322]=[[Insufficient Bloodstone Shards. You can obtain more from Alchemy.]],
[18323]=[[Insufficient Genesis Relics. You can obtain more from Weekly Events.]],
[18500]=[[You gifted %s flowers.]],
[18501]=[[You defeated <color=#db5200>%s</color> in battle.]],
[18502]=[[You were defeated by <color=#db5200>%s</color> in battle.]],
[18503]='Battle',
[18510]='Share',
[18511]=[[Share to Union Channel]],
[18512]=[[Share to World Channel]],
[18513]=[[Shared a battle replay.]],
[18514]=[[Replay expired.]],
[18520]=[[Play the battle replay?]],
[18530]=[[I defeated %s.]],
[18531]=[[Attack Domination %s with me.]],
[18532]=[[I marked %s. Come here!]],
[18533]=[[The Domination has died.]],
[18801]='Claimable',
[18802]=[[Next Stage Rewards: %s]],
[18803]=[[Rewards claimed.]],
[18804]=[[Day %d]],
[18805]=[[%d minute(s)]],
[18806]=[[Log in for <size=60>10</size> days in total.]],
[18807]=[[Free <size=72>100</size> draws.]],
[18808]='Claimable',
[18850]=[[Chance x2]],
[18852]='Events',
[19201]=[[Challenge the Proving Grounds to get abundant EXP and Rankup Stones.]],
[19202]=[[Use the Gold Finger to obtain abundant amounts of Gold.]],
[19220]='Occupy',
[19400]='Title',
[19401]=[[Activated Stat:]],
[19402]=[[Display Channel:]],
[19403]=[[All Channels]],
[19404]=[[Server Channel, Union Channel, Private Chat]],
[19405]=[[Union Channel, Private Chat]],
[19406]=[[Avatar Frame Stats]],
[19407]=[[Title Stats]],
[19408]=[[Overlord Ring]],
[19409]=[[Fearless Ring]],
[19410]=[[Warrior Ring]],
[19411]=[[King of Arena]],
[19412]=[[Best Novice]],
[19437]=[[30 Titles]],
[19438]=[[Obtained from:]],
[19439]=[[Defeat the Overlord Boss in the Hero Ring.]],
[19440]=[[Defeat the Fearless Boss in the Hero Ring.]],
[19441]=[[Defeat the Warrior Boss in the Hero Ring.]],
[19442]=[[Win the championship in the Crucible.]],
[19443]=[[Win the championship in the Pinnacle.]],
[19444]=[[Reach Rank 1 in the Glorious Arena.]],
[19445]=[[Reach Rank 2 in the Glorious Arena.]],
[19446]=[[Reach Rank 3 in the Glorious Arena.]],
[19447]=[[Operation Event]],
[19448]=[[Operation Event]],
[19449]=[[Source 11]],
[19450]=[[Source 12]],
[19451]=[[Source 13]],
[19452]=[[Source 14]],
[19453]=[[Source 15]],
[19454]=[[Source 16]],
[19455]=[[Source 17]],
[19456]=[[Source 18]],
[19457]=[[Source 19]],
[19458]=[[Source 20]],
[19459]='Stats',
[20001]=[[Drop Rates]],
[20002]=[[1. Players can obtain heroes from a specified faction when using Faction Summon. This consumes either Diamonds or Excellent Faction Scrolls.]],
[20003]=[[2. The drop rates of Human Esper Summon are as follows: Rare Hero Shard: 7%, Rare Human Hero: 60%, Excellent Hero Shard: 24%, Excellent Human Hero: 9%.]],
[20004]=[[3. The drop rates of Beastkin Esper Summon are as follows: Rare Hero Shard: 7%, Rare Beastkin Hero: 60%, Excellent Hero Shard: 24%, Excellent Beastkin Hero: 9%.]],
[20005]=[[4. The drop rates of Forest Esper Summon are as follows: Rare Hero Shard: 7%, Rare Forest Hero: 60%, Excellent Hero Shard: 24%, Excellent Forest Hero: 9%.]],
[20006]=[[5. The drop rates of Voider/God Esper Summon are as follows: Rare Voider/God Hero Shard: 40%, Rare Voider/God Hero: 28%, Excellent Voider/God Hero Shard: 28%, Excellent Voider/God Hero: 4%.]],
[20007]='Help',
[20008]=[[1. Excellent Faction Scrolls can be obtained through events, Acorn Tavern quests, and the Giant Merchant.]],
[20009]=[[2. Spend 1 Excellent Faction Scroll to summon heroes from a specified faction once.]],
[20010]=[[3. Use the Esper Conversion Pod for a chance to obtain the specified faction's Rare heroes, Excellent heroes (including those that have awakened through the Awakening Spring), Rare Universal Shards, and Excellent Universal Shards.]],
[20011]=[[4. Each summon will also produce a certain amount of Phasic Particles, which can be used for Hero Substitution.]],
[20012]=[[1. Clear hero quests within the time limit to obtain lucrative rewards. Heroes cannot take on multiple quests at the same time.]],
[20013]=[[2. There is a daily hero quest limit. Raise your VIP level to increase the hero quest limit.]],
[20014]=[[3. Quests that haven't been started can be refreshed. Quests will be refreshed automatically once per day. Players can also spend Diamonds to refresh them.]],
[20015]=[[4. Quests added using Quest Coupons will be refreshed too.]],
[20016]=[[5. Premium Diamonds can be used to speed up the completion of quests.]],
[20017]=[[Refresh Rates: 1-Star Quests - 25%, 2-Star Quests - 25%, 3-Star Quests - 25%, 4-Star Quests - 20%, 5-Star Quests - 4.5%, 6-Star Quests - 0.45%, 7-Star Quests - 0.05%.]],
[20018]=[[1. Proving Grounds Tokens will be consumed upon being defeated in the Proving Grounds. You need at least 1 Proving Grounds Token to issue challenges.
2. Every 30 minutes, 1 Proving Grounds Token will be replenished, up to a maximum of 10.
3. The Proving Grounds will get increasingly difficult. Defeat the Proving Grounds' leader to win amazing rewards.
4. Proving Grounds rankings will be determined according to the number of levels each player has defeated.]],
[20019]=[[1. Select a hero and insert the specified heroes as material to perform ascension.]],
[20020]=[[2. A hero will retain their level and gear after ascension.]],
[20021]=[[3. The gear of heroes being used as materials will be returned to the player.]],
[20022]=[[4. The base stats and skill effects of a hero will increase after ascension.]],
[20023]=[[1. The Casino is automatically refreshed once every 24 hours. Manually refreshing Will RESet the Casino's refresh timer.]],
[20024]=[[2. Players can use Chips for Casino draws. There is a chance to obtain hero shards, gear, sacred gear, and other items.]],
[20025]=[[3. Players will obtain 10 Lucky Coins for each draw attempt. Lucky Coins can be exchanged for hero shards, gear, and other items in the Lucky Store.]],
[20026]=[[3. Chips can be directly purchased with Diamonds, or from the Giant Merchant with Gold or Diamond. Chips can also be obtained by purchasing event packs or from special events.]],
[20027]=[[1. Players can claim a reward every day after logging in.]],
[20028]=[[2. The reward pool will only be refreshed after all 30 days worth of rewards have been claimed.]],
[20029]=[[3. Unclaimed rewards can be claimed on the next day. A maximum of 3 days worth of rewards can be accumulated.]],
[20030]=[[1. Phasic Particles can be used to substitute a hero for another hero of the same faction and star level.]],
[20031]=[[2. If the new stats are undesirable, players can choose instead to retain the hero's original stats. In this case, the spent Phasic Particles will not be returned.]],
[20032]=[[3. Locked heroes and heroes deployed in the Arena cannot be substituted. Heroes from the Voider and God factions cannot be substituted. Only Rare and Excellent heroes can be substituted.]],
[20033]=[[4. Players can obtain Excellent heroes that are already awakened through substitution.]],
[20034]=[[5. A successful substitution will shift the original hero's levels and gear to the new hero. DIY skills will not be retained.]],
[20035]=[[6. Players can view the stats of the substitute hero as Lv. 1 for reference.]],
[20036]=[[1. Clear Stage 9-10 to activate World Exploration events.]],
[20037]=[[2. Clear Stage 9-10 to unlock the ability to deploy a team. Clear Stages 9-15 and 10-20 to unlock the ability to deploy 2 teams. Each team will have its own fuel supply.]],
[20038]=[[3. World Exploration has 5 increasingly challenging and rewarding stages.]],
[20039]=[[4. Each move will consume fuel. In battle, the fuel consumed will be calculated based on the amount of HP lost.]],
[20040]=[[5. Clear a stage by completing the objectives. After each stage, all teams will recover an amount of fuel.]],
[20041]=[[6. When defeated, teams will be resurrected at the spawn point.]],
[20042]=[[1. Abyss City refreshes once every 96 hours. Stage progress will be reset upon refresh.]],
[20043]=[[2. When Abyss City is open, the 30 strongest heroes will automatically be selected to join the instance. Once deployed, heroes cannot be changed.]],
[20044]=[[3. Fixed amounts and types of monsters will be sent out each wave. Players can manually select heroes based on the monster info to counter the monsters.]],
[20045]=[[4. Players can select Quick Battle to skip straight to the battle result, or view the process and result of every battle.]],
[20046]=[[5. Heroes and monsters do not recover HP after each battle. Defeated heroes cannot rejoin the battle.]],
[20047]=[[6. Each battle lasts a maximum of 15 turns. The heroes win if the monsters are defeated within 15 turns. The heroes lose when they are all defeated or when the battle lasts more than 15 turns.]],
[20048]=[[7. Obtain either a chance to buy something from the store or a buff at random after each victory.]],
[20049]=[[8. Players will obtain fixed rewards on their first victory.]],
[20050]=[[9. Dungeon Store: Tap the store icon to buy items. Unpurchased items will remain available until the end of the event.]],
[20051]=[[10. Buff: There are many types of buffs to obtain. Buffs will last for either a single battle or until the end of the chapter.]],
[20052]=[[11. Buffs will be reset when the dungeon is reset.]],
[20053]=[[12. The challenge ends when all allied heroes are defeated.]],
[20054]=[[13. There are 12 stages in the Abyss City. Buffs will be reset after each stage. The difficulty of stages cleared will be recorded so players can tackle a harder difficulty next time.]],
[20055]=[[14. Each time the Abyss City opens, players get a single chance to sweep and obtain rewards.]],
[20056]=[[During the event, players can spend the required number of Heroes, Gold, and Diamonds to complete Elite Exchange quests and obtain lucrative rewards.]],
[20057]=[[During the event, players can spend the required number of Gear, Gold, and Diamonds to complete Elite Exchange quests and obtain lucrative rewards.]],
[20058]=[[During the event, players can spend the required number of Skills, Gold, and Diamonds to complete Elite Exchange quests and obtain lucrative rewards.]],
[20059]=[[Union Assistance]],
[20060]=[[1. Lv. 10 players will be automatically deployed to a union to provide assistance.]],
[20061]=[[2. Union members can apply to become officers. On the morning of Monday and Friday, officers will be determined based on the contribution level of applicants.]],
[20062]=[[3. Officers can kick up to 3 members per day.]],
[20063]=[[4. Quick Move can only be used once every 24 hours. Using Quick Move will allow a player to join a new union immediately.]],
[20064]=[[5. Players who haven't been online for 3 days will be temporarily kicked from their union.]],
[20065]=[[6. Officers who haven't been online for 2 days will be automatically demoted.]],
[20066]=[[7. Applications to specific unions need to be approved by officers.]],
[20070]=[[Dismiss Hero]],
[20071]=[[1. When a hero is dismissed, all the materials used to upgrade the hero will be refunded, and other dismissal rewards will be obtained.]],
[20072]=[[2. Dismissed heroes will disappear, with their gear and skills returned to the bag.]],
[20073]=[[3. Players can purchase heroes from the store using Bloodstones obtained from dismissing heroes.]],
[20074]=[[4. Only B Heroes can be dismissed in Dismiss Hero.]],
[20075]=[[1. During the event, there will be 3 Void Dominators that can be challenged. Each challenge will consume 1 Challenge Badge. Players can use Diamonds to buy up to 300 Challenge Badges.]],
[20076]=[[2. Each Void Dominator drops different rewards.]],
[20077]=[[3. Players will obtain random rewards after each battle.]],
[20078]=[[4. Rewards for defeating Void Dominators will be issued via mail.]],
[20079]=[[5. If players use sweep, the sweep will stop when the Boss is defeated. Unused Challenge Badges will be returned to the player.]],
[20081]=[[1. Union members can enter and challenge the Dominator Stage.]],
[20082]=[[2. Players will obtain single-battle rewards when the battle ends.]],
[20083]=[[3. When the Dominator is defeated, players will obtain different amounts of rewards via mail, depending on how much damage they dealt to the Dominator. The player who dealt the killing blow will also receive a bonus reward.]],
[20084]=[[4. Players will have to wait for the challenge to reset if they fail. During the challenge, players can use Diamonds to resurrect up to 3 times.]],
[20090]=[[1. Union members can join the fight for territory. Up to a maximum of 4 unions can be active on the map.]],
[20091]=[[2. Players will obtain single-battle rewards when the battle ends.]],
[20092]=[[3. Union members can claim territory rewards every day. The rewards will be determined by the home level and power level.]],
[20093]=[[4. Union members can attack all hostile neighboring tiles. Each Dominator invasion attempt will cost 20 Stamina, while invasion attempts on other tiles will cost 10 Stamina. Every 6 minutes, 1 Stamina will be recovered.]],
[20094]=[[5. When a hero in the player's team drops below 30% HP, they cannot be deployed, but can be replaced with another hero and recover HP over time. When all heroes have 0 HP, the team is automatically returned to the Home.]],
[20095]=[[6. Dominators and mines will spawn on the map at random. Union members can obtain great rewards by participating in battle and defeating the dominators.]],
[20096]=[[7. The Union Warzone will be active for 19 days, then be unavailable for 2 days. The final rewards will be determined by union rankings, home level, and player points.]],
[20100]=[[1. Mythic Substitutes allows players to substitute their Mythic heroes with other Mythic heroes of the same faction.]],
[20101]=[[2. Each Mythic Substitute will consume 5 Soul Boxes and an amount of Excellent Heroes.]],
[20102]=[[3. The number of Excellent Heroes is determined by the star level of the Mythic Hero being substituted.]],
[20103]=[[4. After the substitution, the new Mythic Hero will match the level of the original Mythic Hero.]],
[20105]=[[1. Mythic Heroes and above can be descended.]],
[20106]=[[2. After a hero is descended, players will obtain Puppets and corresponding Excellent Heroes depending on the star level of the descended hero.]],
[20107]=[[3. Puppets cannot be awakened, substituted, or descended.]],
[20108]=[[4. Gear equipped by descended heroes will be refunded to the bag.]],
[20109]=[[5. Materials used to upgrade descended heroes won't be refunded.]],
[20111]=[[1. Union members can perform exploration once every 8 hours. There is a chance to obtain treasure, trigger a trap, or encounter the enemy leader.]],
[20112]=[[2. When players trigger a trap, they need to wait for some time before they can resume exploration. Members of the same union can help players escape the trap.]],
[20113]=[[3. When players encounter the enemy leader, all union members can spend Stamina to attack the leader. Participants will obtain rewards and damage points. When the enemy leader is defeated, the player who deals the finishing blow and the player who first encountered the enemy leader will receive an additional reward.]],
[20114]=[[4. Every Monday, players will be ranked according to the points they have. Gold, Diamonds, Hearts, and other rewards will be sent via mail, and the points will be reset.]],
[20115]=[[5. Hearts can be used for Heart Summons, while flowers can be gifted to fellow union members. Players who receive flowers will obtain Flower Points and rewards.]],
[20116]=[[6. Players can gift a maximum of 10 flowers per day, and a maximum of 2 flowers per day to the same player.]],
[20117]=[[7. Players can gain Flower Points by giving out flowers and participating in events. Every Monday, players will be ranked according to their Flower Points, and the top 3 players will receive rewards.]],
[20118]=[[1. Hero Summon grants a higher chance to summon higher-grade heroes, and requires Diamonds or Common Scrolls to perform.]],
[20119]=[[2. Heart Summon is used to summon heroes by consuming Hearts. Players can have up to 1,000 Hearts at any one time.]],
[20120]=[[1. Upon obtaining specific avatars, players can unlock bonus effects.]],
[20121]=[[2. When a piece of gear grants bonus effects, heroes who meet the criteria will enjoy the bonus effects as well.]],
[20122]=[[3. Unlocked bonus effects and avatars can be used freely as the effects are not bound to avatars.]],
[20123]=[[1. Upgrade the basic team of various factions to increase the base stats of their heroes.]],
[20124]=[[2. When you reset your upgraded teams, a portion of resources will be returned. Unlocked teams will become locked again.]],
[20125]=[[3. Some teams can only be unlocked after reaching the required total team level.]],
[20126]=[[4. The total team level is determined by adding up the levels of all basic teams.]],
[20127]=[[5. Only 1 team can be active at a time. When multiple teams are active, the highest-level team will be the one that is used.]],
[20128]=[[6. Locked teams cannot be activated even if all the activation criteria are met.]],
[20129]=[[7. After a team is activated, even when the heroes die, the team will not disappear.]],
[20130]=[[Battle Rules]],
[20131]=[[1. Every day at 21:00 UTC/GMT, rank rewards will be issued via mail.]],
[20132]=[[2. Use tickets to participate in the Arena. Tickets can be purchased with Diamonds.]],
[20133]=[[3. Each Arena battle lasts 15 turns. The attacker loses if the turn limit is exceeded.]],
[20134]=[[4. Points will be calculated after the battle. The winners will gain the points, while the losers will lose the points. Rankings are determined based on player points.]],
[20135]=[[5. Players can view their last 10 battles from the Battle Log.]],
[20136]=[[Reward Rules]],
[20137]=[[Rewards will be issued via mail each day when the rankings are finalized.
You can learn more in [Daily Rewards].]],
[20250]=[[Esper Badge]],
[20251]=[[1. Esper Badge: During the event, players can obtain additional rewards by collecting badges.]],
[20252]=[[2. Esper Badge: During the event, travelers can obtain additional rewards by collecting badges.]],
[20253]=[[2. Badge Store: Esper Badges can be exchanged for rewards in the Badge Store.]],
[20254]=[[3. Badge Store: Esper Badges can be exchanged for rewards in the Badge Store.]],
[20255]=[[4. Travelers can obtain Esper Badges by completing specific quests. All quests can be completed multiple times.]],
[20256]=[[5. Event Packs: Value packs that contain Diamonds and Esper Badges will be available for purchase.]],
[20260]=[[Elite Exchange]],
[20261]=[[1. During the event, players can complete Elite Exchange quests by exchanging Heroes, Gold, and Diamonds to obtain lucrative rewards.]],
[20262]=[[2. Value packs that contain Diamonds, Common Scrolls, and Excellent Faction Scrolls will be available for purchase.]],
[20263]=[[1. Log in during the event to obtain a new login gift each day.]],
[20264]=[[4. Hero Rebirth: The Alchemy Store will have 2 Elite heroes available for purchase.]],
[20280]=[[Hunting Battle]],
[20281]=[[1. The Hunting Battle is held once per week.]],
[20282]=[[2. Players will enter Hunting Teams of varying levels according to the strength of their own teams. All players will contribute to their team's rank.]],
[20283]=[[3. Whenever the Void Dominator loses a certain amount of HP, they will gain a stack of Stat Up and become more powerful.]],
[20284]=[[4. Players can participate in 10 battles every day, but only the 2 battles with the highest DMG dealt will contribute to the overall damage score. Players will obtain a certain amount of chests and rewards based on the DMG dealt.]],
[20285]=[[5. DMG dealt by players contributes to the Total Damage Leaderboard. After the event ends, rewards will be issued according to player rankings.]],
[20286]=[[6. Players must collect event rewards before progressing to the next event.]],
[20287]=[[7. The highest ranked player at 21:00 each day will be granted an exclusive Advanced Arena avatar frame. When a player loses their top rank, the avatar frame will expire on 21:00 of the following day.]],
[20288]=[[1. Advanced Hero Pass will be available on the 31st day of a player's character creation.]],
[20289]=[[2. The Advanced Hero Pass event lasts for 30 days.]],
[20290]=[[3. The more Hero Badges you obtain during the event, the better the rewards you can obtain in Advanced Hero Pass. Hero Badges can be obtained from Daily Quests and Weekly Quests.]],
[20291]=[[4. Unclaimed rewards will be sent via mail when the Advanced Hero Pass event ends.]],
[20292]=[[5. Unlocking Advanced Hero Pass awards 500 VIP EXP.]],
[20340]=[[3-Star Tournament]],
[20341]=[[1. The 3-Star Tournament will last for 3 days.]],
[20342]=[[2. Player rankings will be determined by the number of stars obtained from clearing story stages. The more stars a player has, the higher their rank.]],
[20343]=[[3. The higher a player's rank, the more valuable their rewards.]],
[20344]=[[4. Players will be assigned to random groups based on when they join the 3-Star Tournament.]],
[20345]=[[5. Each group will have 50 players.]],
[20346]=[[6. The groups will not be competing against each other. Each group will have their own prize pool.]],
[20350]=[[Fallen Forest]],
[20351]=[[1. An Ancient Summon has brought you to the Fallen Forest.]],
[20352]=[[2. The Fallen Forest is an ancient and mysterious place, with monsters from Abyss City collecting pieces of gear scattered around. Looks like you'll have to defeat them to get your hands on the gear.]],
[20353]=[[3. There is no escaping the corrupted air in the Fallen Forest. To avoid the bewitching of the darkness, the Magical Academy created a Potion of Refreshment. You'll need to use this potion whenever you fight the monsters. Your supply will be fully replenished on the next day.]],
[20354]=[[4. Defeat monsters to obtain the <color=#be2ae4>gear</color> that they have collected.]],
[20355]=[[5. Some hero skills will mutate under the influence of the corrupted air. Tread carefully, Traveler.]],
[20356]=[[6. Hero Mutation: The back row of the Boss lineup is immune to CC, and some of their skills will have additional effects, which can be viewed by tapping the hero's avatar in the lineup interface.]],
[20360]=[[Battle Rules]],
[20361]=[[1. Each season of the Daily Arena lasts for 12 days.]],
[20362]=[[2. Use tickets to participate in the King of the Hill battle. Tickets can be purchased with Diamonds.]],
[20363]=[[3. Each King of the Hill battle lasts 15 turns. The attacker loses if the time limit is exceeded.]],
[20364]=[[4. Points will be calculated after the battle. The winners will gain the points, while the losers will lose the points. Rankings are determined based on player points.]],
[20365]=[[5. Each player's points determines their rank. There are a total of 8 ranks, from Bronze to Dominator. After each season, player ranks will decay slightly.]],
[20366]=[[6. Players can view their last 10 battles in the Battle Record.]],
[20367]=[[Reward Rules]],
[20368]=[[1. After every battle, players will obtain rewards based on their current ranking.]],
[20369]=[[2. Once players reach a certain number of victories, they can obtain Challenge Chest rewards.]],
[20370]=[[3. After each season, players will obtain rewards based on their rank.]],
[20371]=[[4. Players will also obtain rank rewards the first time they obtain each specific rank.]],
[20380]=[[1. Players can conduct Divination using Advanced Chips to obtain specific heroes, mythic weapons (with guaranteed advanced bonus stats), faction scrolls, summon scrolls, and other rare rewards!]],
[20381]=[[2. Rewards obtained will be removed from the pool. Players can obtain all the rewards in 10 Divinations!]],
[20382]=[[3. Players will obtain Casino EXP through Divination. Unlock more rewards by raising the Casino Level!]],
[20383]=[[4. Players will obtain 10 Advanced Chips and 1,500 VIP EXP upon purchasing a Casino Black Card. For 30 days after purchase, players can collect 3 Advanced Chips via mail by logging in each day, for a total of 100 Advanced Chips!]],
[20390]=[[Sealed Land]],
[20391]=[[1. Union members contribute 1 Union Activity Point whenever they obtain 1 Personal Activity Point. The total amount of Union Activity Points that can be contributed by all members per day is capped at the Union's membership limit.]],
[20392]=[[2. Sealed Land trials vary from 1-Star to 6-Star. Union Presidents and Officers can spend Union Activity Points to activate trials upon reaching the required Union Level. The higher the Trial Boss star level, the greater the rewards.]],
[20393]=[[3. Each trial lasts 3 days from the time of activation. Each union member can challenge up to 5 times per day, but only the attempt with the highest DMG will contribute to the total Union DMG.]],
[20394]=[[4. Whenever the Trial Boss loses a certain amount of HP, they will gain a stack of Stat Up and become more powerful.]],
[20395]=[[5. Attack the Trial Boss to earn Gold, Diamonds, Gear, Union Coins, and more. After the event, each player will receive Objective Rewards according to their progress. Objective Rewards can only be claimed once every 3 days.]],
[20396]=[[6. If a player joins a union within 48 hours after the previous settlement of their Sealed Land rewards, they will not be able to attack the trial boss.]],
[20397]='Tip',
[20398]=[[New members in a Union must wait for 48 hours before joining any battles.\nWaiting time left: %s]],
[20500]=[[Value Fund]],
[20501]=[[1. The Value Fund lasts for 30 days and unlocks a new reward each day. After 30 days, the Value Fund event will be reset.]],
[20502]=[[2. Obtain all the unlocked rewards immediately upon purchase.]],
[20503]=[[3. Activate Deluxe Monthly Card to purchase Value Fund]],
[20504]=[[4. The Value Fund and Deluxe Fund can be purchased at the same time.]],
[20505]=[[Deluxe Fund]],
[20506]=[[1. The Deluxe Fund lasts for 30 days and unlocks a new reward each day. After 30 days, the Deluxe Fund event will be reset.]],
[20507]=[[2. Obtain all the unlocked rewards immediately upon purchase.]],
[20508]=[[3. Activate Deluxe Monthly Card to purchase Deluxe Fund]],
[20509]=[[4. The Value Fund and Deluxe Fund can be purchased at the same time.]],
[20654]=[[Each Wish Summon Card and Wish Chest returns 30k Gold.]],
[20655]=[[1. The Awakening Spring can improve a hero's quality.]],
[20656]=[[2. Hero quality levels are listed in ascending order as follows: Rare, Rare+, Excellent, Excellent+, Epic, Epic+, Legendary, Legendary+, Mythic, Mythic+, Transcendent, Transcendent+, Transcendent++, Eternal.]],
[20657]=[[3. Any hero consumed as materials during the Awakening will refund you an equivalent amount of Gold, Hero EXP, and Rankup Stones that they have used in their development. Any Gear equipped by consumed heroes will be returned to your bag.]],
[20701]=[[Available Items]],
[20702]='Next',
[20703]=[[Select an item]],
[20711]=[[Day 1]],
[20712]=[[Day 2]],
[20713]=[[Day 3]],
[20714]=[[Day 4]],
[20715]=[[Day 5]],
[20716]=[[Day 6]],
[20717]=[[Day 7]],
[20718]=[[Daily Recharge Reward Reissue]],
[20719]=[[Dear Traveler, the Daily Recharge event has ended and your unclaimed rewards will be sent via mail.]],
[20720]=[[Item Details]],
[20721]=[[Daily Recharge]],
[20722]='Customize',
[20723]=[[Resource Pack]],
[20724]=[[Hero Summon Pack]],
[20725]=[[Gear Rankup Pack]],
[20726]=[[Relic/Hero Pack]],
[20727]=[[Hero Pack]],
[20728]=[[Hero Training Pack]],
[20729]=[[Union Material Pack]],
[20730]=[[CP Enhancement Pack]],
[21001]=[[Select the hero you wish to dismiss]],
[21002]=[[You've selected a Rare hero. Dismiss them?]],
[21003]=[[You can dismiss up to 10 heroes at a time.]],
[21004]=[[Common Hero Auto-Dismiss Rewards]],
[21005]='Dismissed',
[21006]='Preview',
[21007]=[[Number of Heroes]],
[21008]='Dismiss',
[21009]=[[Auto Select]],
[21010]=[[Selected hero ≤ 2-Star]],
[21011]=[[Save 4 duplicate heroes]],
[21012]=[[Save 4 duplicate heroes]],
[21013]='Select',
[21014]=[[<color=red>*Dismiss heroes to get Rankup Stones.</color>]],
[21015]=[[Please select a hero]],
[21016]='Tip',
[21017]=[[Hero selected]],
[21018]=[[Fewer than 4 duplicate heroes are saved and not selected]],
[21019]=[[Automatically Dismiss B Heroes]],
[21020]=[[No heroes to dismiss]],
[21021]=[[1. Dismiss heroes to get Bloodstones and all development materials.]],
[21022]=[[2. Bloodstones can be used to purchase heroes at the store.]],
[21023]=[[Hero Reset]],
[21024]=[[Resetting a hero returns the original hero and all development materials]],
[21025]='Reset',
[21026]=[[Dismiss Hero]],
[21027]=[[Reset Rewards]],
[21028]=[[Reset successfully]],
[21029]=[[Dismiss the hero to get]],
[21030]=[[Reset the hero to get]],
[21031]=[[You will get]],
[21032]=[[There have been some changes to the Arena Defense Lineup. Make adjustments?]],
[21033]=[[There have been some changes to the Arena Defense Lineup. Make adjustments?]],
[21034]='Go',
[21035]=[[Auto-Dismiss to get]],
[21036]=[[Dismiss the hero to get]],
[21037]=[[Reset the hero to get]],
[21038]=[[Hero Descension]],
[21039]='Descend',
[21040]=[[Descend the hero to get]],
[21041]=[[Descension Rewards]],
[21042]=[[Descended successfully]],
[21043]=[[Descend to get]],
[21044]=[[1. When you have duplicate Epic and above heroes, the heroes of the lower grade can be descended.
2. Mythic heroes can be descended by using Time Essence.]],
[21045]=[[No heroes to descend]],
[21046]=[[The hero has participated in today's Warzone. Cannot descend today.]],
[21047]=[[Hero capacity full. Please increase the capacity or clear your hero bag.]],
[21101]=[[Cannot select more materials.]],
[21102]='Merge',
[21103]=[[Heroes available for awakening]],
[21104]=[[No material heroes.]],
[21105]='Awakened',
[21106]=[[Awakening Details]],
[21107]=[[Required Card(s)]],
[21108]=[[You must select a card.]],
[21109]=[[Select the required material cards.]],
[21110]=[[The awakening materials contain the Mythic hero %s. Are you sure you want to continue?]],
[21111]=[[Smart Awaken]],
[21112]=[[Awaken all Rare, Rare+, and Excellent heroes.]],
[21113]=[[Requires %s %s %s(s)]],
[21114]=[[Congratulations, you obtained]],
[21115]=',',
[21116]=[[Awaken Now]],
[21117]=[[Max Grade]],
[21118]=[[Max grade reached.]],
[21119]=[[You already own a higher-grade %s. Save this card and use it for awakening later.]],
[21120]=[[Select the required cards below.]],
[21121]=[[Invalid awakening material.]],
[21122]=[[Add all the required cards below.]],
[21123]=[[%s %s%s heroes required.]],
[21124]=[[Requirements not met. You need %s %s %s]],
[21125]=[[Requirements not met. You need %s %s%s heroes.]],
[21126]=[[You have met the requirements. Tap to Awaken.]],
[21127]=[[No cards can be awakened.]],
[21128]=[[Clear %s or reach VIP%s to unlock.]],
[21129]=[[Mythic heroes are very powerful. You are advised to save them until Stage 5-15.]],
[21130]=[[Awaken Priority]],
[21131]='Max',
[21132]='High',
[21133]='Moderate',
[21134]=[[Hero CP]],
[21135]=[[Awaken a hero to <color=#FF2525>Legendary</color> to unlock <color=#FFD100>Talents</color>.]],
[21136]=[[Awaken to unlock the <color=#FFD100>Talent</color> function.]],
[21137]=[[Awaken a hero to <color=#FADCFF>Mythic</color> to unlock the next Talent.]],
[21138]=[[Awaken to unlock the next Talent.]],
[21139]=[[Awaken a hero to <color=#FADCFF>Transcendent</color> to unlock the next Talent.]],
[21140]=[[Awaken the hero to <color=#FADCFF>Eternal</color> to unlock their <color=#FFD100>Ultimate Talent</color>.]],
[21141]=[[Awaken to unlock an <color=#FADCFF>Ultimate Talent</color>.]],
[30701]=[[Error Report]],
[30702]=[[My Location:]],
[30703]='Hall',
[30704]=[[Problems encountered:]],
[30705]='Other:',
[30706]=[[Tap to enter]],
[30707]='Cancel',
[30708]='Confirm',
[30709]=[[Building display error]],
[30710]=[[Scene display error]],
[30711]=[[Minion pathfinding error]],
[30712]=[[City troop power error]],
[30713]=[[No response when tapping]],
[30714]=[[Deployment error]],
[30715]=[[Feedback failed as you were making too many operations!]],
[30716]=[[Feedback sent!]],
[30717]='Battlefield',
[30718]='Showdown',
[30801]='Construction',
[30802]=[[Stage Info]],
[30803]='Battle',
[30804]=[[How to Obtain Heroes]],
[30805]=[[How to Enhance Heroes]],
[30806]=[[Hero Types]],
[30807]='Gear',
[30808]='Union',
[30809]='Friends',
[30810]=[[FAQ: Game]],
[30811]=[[FAQ: Socializing]],
[30812]=[[FAQ: Recharging]],
[30813]=[[Contact Us]],
[30814]=[[Campaign
Dispatch your hero teams into battle, go AFK, and finish the battle. In the campaign, you can gain Player EXP, Hero EXP, Gold, Gear, and other rewards. The Dev team will activate double drops from time to time.]],
[30815]=[[Proving Grounds
Defeat the guards of the Proving Grounds to earn Rankup Stones and powerful Gear. Reach designated floors to obtain a 5-star Hero. The higher you climb, the greater the challenge.]],
[30816]=[[Arena
Defeat other players in the Arena to increase your rank and earn Diamonds. Every Friday, Saturday, and Sunday, the Arena Weekly Battle will open, allowing you to challenge players from other servers!]],
[30817]=[[Summoning Hall
The fastest way to obtain new Heroes. Gather Hearts or Common Scrolls to summon Heroes for free.]],
[30818]=[[Awakening Spring
Ascend your Heroes at the Awakening Spring, up to a max star level of Epic. Collect the materials needed to ascend your Heroes. Any Heroes consumed as materials will refund you an equivalent amount of Hero EXP and Rankup Stones. Any Gear equipped by consumed Heroes will be returned to your bag. The more Heroes you own, the more chances you have to increase the star level!]],
[30820]=[[Alchemy
You can disassemble your spare Heroes to obtain Hero EXP, Rankup Stones, Magic Dust, and Bloodstones. The Gear, Crystals, and Sacred Gear of disassembled heroes will be returned to your bag. Bloodstones can be traded for heroes at the Alchemy Store. You can also obtain Bloodstones from Event Packs.]],
[30821]=[[Nucleus Crystal Extraction Array
Merge low-grade gear into high-grade gear through the Nucleus Crystal Extraction Array. Equipping a set will activate the set stats!]],
[30822]=[[Casino
Spend chips in Casino to obtain Hero Shards, Sacred Gear, Gold, Hero EXP, and other items.]],
[30823]=[[Giant Merchant
The Giant Merchant sells Hero EXP, Rankup Stones, Hero Shards, Gear, Common Scrolls, Arena Tickets, and Chips. The items available will refresh at set intervals (or manually refreshed).]],
[30824]=[[Esper Conversion Pod
You can carry out faction-specific summoning in the Esper Conversion Pod! Use Excellent Faction Scrolls to perform a faction summon for a chance to obtain Rare/Excellent Heroes and Rare/Excellent Universal Shards from that Faction. Phasic Particles can also be obtained after each summon. You can spend certain blessings to substitute a Hero for another Hero of the same Faction. Note that only Rare/Excellent Heroes from Factions other than Light and Dark can be substituted.]],
[30825]=[[Acorn Tavern
Hero Quests of various Star Levels will be refreshed in Acorn Tavern every 24 hours. Complete the Hero Quests to obtain great rewards, or refresh the star level of Quests using Diamonds. Use Acorn Tavern Quest Scrolls to increase the number of Hero Quests (Acorn Tavern Quests generated by Scrolls will be reset after refresh if not completed). Acorn Tavern Quest Scrolls may drop during AFK.]],
[30826]=[[Overview
Adventure Maps are divided into five difficulties: Awakening Battle, Chaos Battle, Transformation Battle, Destruction Battle, and Savior Battle. Each difficulty is composed of multiple stages, and the difficulties must be unlocked in sequential order.]],
[30827]=[[Stage Star Level
Each battle will be given a star level according to the settlement. Regardless of the score, the stage will be deemed as "PASSED" and players may enter the next stage. Players may challenge the stages again to attempt for higher star levels.]],
[30828]=[[Unlocking Chapters & Maps
When you clear all the stages in a region, the next Chapter and Map will be unlocked.]],
[30829]=[[Battle Event
When entering a new stage, a battle event for that stage will be triggered. Clear the battle event to obtain generous rewards.]],
[30830]=[[Team
Hero lineups are saved in teams. As you level up, your team slots will increase. The higher your Heroes' power and the more Heroes you deploy, the higher your CP (only impacts unlocking the next stage, not drops).]],
[30831]=[[Auto Combat
AFK Rewards will increase as time passes. You may go AFK for up to 18 hours. No bonus rewards will be granted after 18 hours.]],
[30832]=[[Yields and Collection
Every stage will yield Player EXP, Gold, and Hero EXP, all of which can be gained through AFK. The harder and further you progress through stages, the better your yields. VIP levels will also increase yields further.]],
[30833]=[[Drops and Loot
You may check the Item Drop details to see which items may be dropped in the current stage. When you AFK in a certain stage, there is a chance for you to obtain that stage's item drops. Obtained drops can be viewed through Loot.]],
[30834]=[[Pre-Battle Positioning
A Hero in the Front Row is more susceptible to attacks than a Hero in the Back Row. Adjust your hero positioning based on their classes to allow them to survive longer in battle.]],
[30835]=[[Faction Bonus
Adjust your team before the battle according to the makeup of enemy heroes to make best use out of Faction Bonuses.]],
[30836]=[[Faction Mastery
Upgrade each Faction to activate various Faction Masteries, which grants your deployed heroes will powerful Light effects.]],
[30850]=[[Battle Loot
You have a chance to obtain Hero Shards from Adventure AFK rewards, Battle Events, Event Dungeons, and the Proving Grounds.]],
[30851]=[[Store Purchase
Hero Shards are sold by the Giant Merchant, Dismissal Store, World Exploration Store, Union Store, and Arena Weekly Battle Store.]],
[30857]=[[Hero Level Up
Hero levels can be increased by spending Hero EXP.]],
[30858]=[[Hero Rank Up
When a Hero reaches their max level, use Rankup Stones to upgrade them further.]],
[30859]=[[Hero Ascension
When a Hero reaches their max level, you can increase their Star Level at the Awakening Spring to boost their stats. Their Level and Gear will be retained.]],
[30860]=[[Hero Awakening
Use material heroes to awaken Heroes at Epic and above. Awakening will increase their Star Level, Stats, and Passive Skill. Consumed Heroes will be converted into Hero EXP.]],
[30861]=[[Gear and Sacred Gear
Heroes can equip Gear and Sacred Gear to improve their CP. The higher the Star Level and Grade, the more CP the Hero will gain.]],
[30862]=[[Crystal Level Up
Make your Heroes stronger by leveling up and converting their Crystals with Magic Dust and Gold.]],
[30863]=[[Classes
Heroes are divided into 5 classes: Warrior, Mage, Hunter, Assassin, and Priest. Warriors have high HP and DEF
but low ATK, making them suitable as tanks in the Front Row. Mages and Hunters have low HP and DEF but high
ATK. Most of them do AoE DMG, making them suitable as DPS in the Back Row. Assassins have low HP and DEF but very high ATK.
Most of them do single target DMG, making them suitable as DPS in the Front Row. Priests have low HP, ATK, and DEF, but are
capable of Healing and CC, making them suitable as Support in the Back Row.]],
[30864]=[[Grade
Heroes have an initial grade. The highest grade is Excellent. The higher the grade, the higher their basic stats, and the stronger their skills. Use awakening to improve a hero's grade.]],
[30865]=[[Factions
Heroes are divided into 6 Factions: Voider, God, Forest, Nightfall, Human, and Beastkin. The Factions also counter each other: Forest > Nightfall > Human > Beastkin > Forest, and Voider <> God. Countered Heroes will take 25% Bonus DMG.]],
[30866]=[[Gear Grades
Gear can be divided into 7 grades: Gray, Green, Blue, Purple, Yellow, Red, and White. The higher the grade, the more stats the gear provides.]],
[30867]=[[Gear Star Level
Blue and Gold Gear have 2 (1-2) Star Levels. Purple Gear has 3 (1-3) Star Levels and any Gear above Green has 4 (1-4) Star Levels. Blue and Gold Sacred Gear have 4 (1-4) Star Levels, while Purple and Green Sacred Gear have 5 (1-5) Star Levels. Gear that is Red or above has 6 (1-6) Star Levels. Gear of the same grade & Sacred Gear Star Level.]],
[30868]=[[Merging Gear
Merge low-grade Gear in the Nucleus Crystal Extraction Array to obtain high-grade Gear.]],
[30869]=[[Obtaining Gear
Acquire new Gear from AFK Farming, Proving Grounds, and Battle Events. Visit the Giant Merchant, World Exploration Store, and Union Store to find Gear for sale.]],
[30870]=[[Upgrade Stats
Equip a certain number of Gear at the required Star Level to activate additional Gear Upgrade Stats.]],
[30871]=[[Upgrading Sacred Gear
Consume surplus Sacred Gear to upgrade your current Sacred Gear and increase its Star Level.]],
[30872]=[[Obtaining Sacred Gear
Acquire Sacred Gear by merging Sacred Gear Shards dropped in AFK Farming or purchased from the Union Store.]],
[30873]=[[Joining a Union
On the Union interface, search for an Union by entering the Union ID and tapping Apply to send an application. Once the application has been accepted, you will be able to join the Union.]],
[30874]=[[Creating a Union
Create an Union from the Union interface. To create an Union, you must spend Diamonds and reach a certain level. Once you have created an Union, you will be able to accept other players' applications and add them as members of your Union.]],
[30875]=[[Appoint Union President
The President of a Union can go to the Union Members menu and tap Appoint President to pass on the position of President to another Union member.]],
[30876]=[[Disbanding an Union
The President of a Union can go to the Union Members menu and tap Disband Union to disband their Union. Once confirmed, the Union will be disbanded after 2 hours.]],
[30877]=[[Union Level
Union members can accumulate Activity Points each day to increase the Union Level.]],
[30878]=[[Union Gameplay
Union members can participate in different game modes such as Lord of Battle, Sealed Land, and Union Warzone to obtain Union Coins and other rewards.]],
[30879]=[[Light Hall
In the Light Hall, you can use Union Coins and Gold to increase Union Skills. Increasing the Union Skill corresponding to a certain Class can enhance your Heroes of that Class.]],
[30880]=[[Union Function
Union members can accumulate Activity Points to increase the Union Level. The number of times the members can increase Union EXP by signing in each day cannot exceed the number of Union members. Union members can participate in different game modes such as Lord of Battle, Sealed Land, and Union Warzone to earn Union Coins and other rewards. Union Coins can be used to purchase items in the Union Store or to enhance Union Skills with Beastkin.]],
[30881]=[[Adding Friends
From the Friend interface, search for a player's ID or tap Friend Recommendations to send a Friend Request. In a Chat Channel, tap on a player's avatar to send a Friend Request. Tap a Union member's icon to send a Friend Request.]],
[30882]=[[Friend Request
You can check your Friend Requests in the request list. Tap Accept to add another player as a Friend.]],
[30883]=[[Hearts
Friends can exchange Hearts, which can be used in the Summoning Hall for Friendship Summons.]],
[30884]=[[Deleting Friends
On the Friend interface, you can tap a friend's avatar to check their profile and tap Delete on the information box.]],
[30885]=[[Friend Assistance
Every 8 hours, you gain one Discovery Chance, which changes your probability of finding the Eccentric Lord, Gold, Hero Shards, and other rewards. Helping a Friend defeat the Eccentric Lord will earn you random rewards and points, which will be distributed based on the weekly ranking.]],
[30950]=[[1. Q: How can I continue playing after changing my device?

A: Once you have downloaded and entered the game, you can log in with your account and password on the login interface to start the game.]],
[30951]=[[2. Q: What should I do if my account was stolen or if my password was changed?

A: If your account password was changed, contact customer service as soon as possible.]],
[30952]=[[3. Q: What should I do if there is a connection timeout error and I can't log in?

A: If you cannot log into the game, try reconnecting or restarting the game.]],
[30953]=[[4. Q: What should I do if the game is constantly updating and I can't enter?

 A: If you encounter a situation where the game is stuck in update mode, try ending the task or switching to a different network, then re-enter the game. If the issue still isn't resolved, contact customer service.]],
[30954]=[[5. Q: What should I do if I lose my game data or account?

A: If you lost your data, make sure that you logged into the correct server, as data is not shared between different servers. If the server is working but you are still having issues, contact us immediately. When contacting us, provide as much information as possible, such as your game ID and account name, and we will do our best to assist you.]],
[30955]=[[6. Q: What should I do if I keep getting disconnected while playing?

A: If you experience frequent disconnections while playing, check your device's network connection status and try again after adjusting your connection. If the problem persists, contact our customer service staff.]],
[30956]=[[7. Q: What should I do if the game keeps crashing?

 A: When the game crashes, end the task on your device and try re-entering the game. If the problem persists, try restarting your device before entering the game again, or contact customer service to report the issue.]],
[30957]=[[8. Q: What should I do if I find a cheater?

A: We are committed to creating a fair gaming environment for all players. Disrupting in-game balance by exploiting loopholes is strictly prohibited. If you come across a cheating player, report them to us as soon as possible so that we can take appropriate action.]],
[30959]=[[1. Q: What should I do if I see a player posting ads or fraudulent information in a chat channel?
A: You can contact customer service and report players who post messages that violate our guidelines by providing their account ID.]],
[30961]=[[2. Q: How can players interact with each other?
A: You can send likes to show appreciation and chat to build friendships.]],
[30962]=[[3. Q: How do I join a Union?
A: You can build the Union Center, select a recommended Union or search by ID, then send an application. You can also create your own Union and invite other players to join.]],
[30963]=[[4. Q: What happens if I change my Union?
A: Switching Unions during certain events may affect your eligibility for rewards.]],
[30964]=[[5. Q: What is the purpose of Union Technology?
A: Lords can donate Gold and Diamonds to advance Union technology and fill the EXP bar. Once the bar is full, the Union can start to research the technology.]],
[30965]=[[6. Q: What is the purpose of Union Mutual-help?
A: Allies can help each other upgrade buildings, research technologies, and heal wounded soldiers.]],
[30966]=[[1. Q: How to become a VIP?
A: You can purchase the VIP status or Monthly Card packs to activate VIP Perks. The higher your VIP level, the more exclusive perks you will be able to enjoy.]],
[30967]=[[2. Q: What bonus do I get on my first recharge?
A: You can unlock ultra-rare S+ heroes, alongside 10-pull Recruit Vouchers and other deals.]],
[30968]=[[3. Q: What should I do if I made a recharge didn't receive the items?
A: If the payment was successful but you haven't received the items, try restarting the game. If you still don't see the items after multiple attempts, contact us immediately.]],
[30969]=[[4. Q: Why does the amount deducted after a successful recharge vary from the in-game price?

A: The amount deducted after a purchase is converted and charged based on the exchange rate of the currency in your current country, which may fluctuate.

]],
[30970]=[[If you notice any game issues or have any suggestions, you can send us a message in the feedback section.]],
[30971]='Stats',
[30972]=[[CP
Calculated based on hero attributes to represent their strength.]],
[30973]=[[HP
Increases the hero's base health.]],
[30974]=[[ATK
Increases the hero's attack.]],
[30975]=[[Armor
Increases the hero's defense. The higher the Armor, the less damage is taken.]],
[30976]=[[Speed
Determines the order in which heroes attack each turn. When speed is the same, defenders attack first in the order of their formation (from 6 to 1).]],
[30977]=[[CRIT
Increases the hero's chance to inflict critical strikes.]],
[30978]=[[CRIT RES
Reduces the hero's chance of taking critical strikes.]],
[30979]=[[CRIT DMG
Increases the damage dealt by critical strikes.]],
[30980]=[[CRIT DMG RES
Reduces the damage received by the hero when they are dealt a critical strike.]],
[30981]=[[Skill DMG
Increases the damage to the hero's skills.]],
[30982]=[[Holy DMG
Deals damage that is not affected by Armor, DMG Reduction, or Armor PEN.]],
[30983]=[[True DMG
Deals damage that ignores shields and is not affected by Armor, DMG Reduction, Armor PEN, and CRIT.]],
[30984]=[[Debuff Hit
Increases the Hit Rate of all controlling effects (Hit Rate is calculated separately).]],
[30985]=[[Debuff RES
Reduces the Hit Rate of all controlling effects (Hit Rate is calculated separately).]],
[30986]=[[Hit
Increases the hero's chance of hitting the target.]],
[30987]=[[Dodge
Reduces the chance of being hit by hero skills. A successful dodge means that no damage is taken.]],
[30988]=[[Accuracy
Reduces the chance of blocks.]],
[30989]=[[Block
Increases the chance of blocking an attack. A successful block reduces DMG taken by 1/3.]],
[30990]=[[Armor PEN
Increases damage inflicted by heroes by 1% for every 1% of Armor PEN.]],
[30991]=[[DMG Reduction
Reduces damage taken by heroes by 1% for every 1% of DMG Reduction.]],
[32001]='Diamond',
[32002]=[[Obtain Gold]],
[32003]=[[Hero Shards]],
[32004]=[[Hero EXP]],
[32005]=[[Rankup Stone]],
[32006]=[[Obtain Gear]],
[32007]=[[Obtain Skills]],
[32008]=[[Phasic Key]],
[32009]=[[Enhance Heroes]],
[32010]=[[Enhance Gear]],
[32011]=[[<color=#ffde00ff>5-Star Hero</color>]],
[32021]=[[Complete Tavern Quests]],
[32022]=[[Daily Arena]],
[32023]='AFK',
[32024]=[[Head to Casino]],
[32025]=[[Complete Daily Quests]],
[32026]=[[Upgrade player level to get Diamond rewards.]],
[32027]=[[Kill plunderers]],
[32028]=[[Complete achievements]],
[32029]=[[Gold Bonus]],
[32030]=[[Dropped in the Proving Grounds.]],
[32031]=[[Challenge Union Dungeon]],
[32032]=[[Dropped in certain floors of the Proving Grounds.]],
[32033]=[[Sold in the Union Store.]],
[32034]=[[Sold in the Awakening Spring.]],
[32035]=[[Obtained from Friend Search.]],
[32036]=[[Purchased from the Giant Merchant.]],
[32037]=[[Obtained from the Esper Conversion Pod.]],
[32038]=[[Exchanged from festival events.]],
[32039]=[[Festival gifts and event packs.]],
[32040]=[[Complete 6-Star and 7-Star Tavern Quests for a chance to obtain.]],
[32041]=[[Arena Store]],
[32042]=[[Hero Upgrade and Rankup]],
[32043]=[[Obtained from the Awakening Spring.]],
[32044]=[[Summon Hero]],
[32045]=[[Merge Gear]],
[32046]=[[Faction Summon]],
[32047]=[[Daily Sign-in]],
[32048]=[[Reach VIP1]],
[32049]=[[Reach Lv. 2]],
[32050]=[[Equip gear to heroes]],
[32051]=[[Upgrade Gear]],
[32052]=[[Training Room]],
[32053]=[[Clear every 25 floors in the Proving Grounds to obtain]],
[32054]=[[Clear %d/%d floors in the Proving Grounds to obtain]],
[32055]=[[Sera's strength increases]],
[34000]=[[Super control mage.]],
[34001]=[[One of the twin stars. Boasts high CRIT.]],
[34002]=[[A perfect tank hero with amazing CC abilities in the mid-phase.]],
[34003]=[[A must-have AoE damage dealer in the mid-phase.]],
[34004]=[[A must-have support in Boss battles.]],
[34005]=[[A must-have AoE damage dealer in the early-phase.]],
[34006]=[[A powerful support granting DMG boosts.]],
[34007]=[[The perfect tank that counters enemy damage dealers.]],
[34008]=[[One of three ultimate damage dealers.]],
[34009]=[[The ultimate support with high DEF.]],
[34010]=[[The must-have ultimate AoE damage dealer.]],
[34011]=[[The eternal King of the Arena.]],
[34012]=[[A super powerful buff mage in the back row.]],
[34013]=[[One of the two powerful harvesters.]],
[34014]=[[One of the two powerful harvesters.]],
[34015]=[[A super crushing tank with high DMG.]],
[34016]=[[A tank with super powerful CC abilities.]],
[34017]=[[A high-burst mage with high endurance.]],
[34018]=[[A high-burst mage.]],
[34019]=[[A super tank with high CRIT.]],
[34020]=[[A burst mage with CC abilities.]],
[34021]=[[An ultimate damage dealer.]],
[34022]=[[An ultimate damage dealer.]],
[34023]=[[A damage dealer mage capable of AoE CC.]],
[34024]=[[A support mage capable of AoE DMG.]],
[34025]=[[A damage dealer support capable of AoE CC.]],
[34026]=[[A super AoE damage dealer mage.]],
[34027]=[[The ultimate burst tank hero.]],
[34028]=[[The ultimate damage dealer tank hero.]],
[34029]=[[The ultimate single-target support hero.]],
[34030]=[[A super damage dealer mage capable of healing.]],
[34031]=[[A super counter tank.]],
[34032]=[[A super damage dealer mage capable of AoE CC.]],
[34033]=[[The ultimate tank with high CRIT.]],
[34034]=[[An ultimate damage dealer.]],
[34035]=[[A super support capable of purification and healing.]],
[34036]=[[An ultimate damage dealer.]],
[34037]=[[A super damage dealer mage with superior endurance.]],
[34038]=[[An ultimate CC tank with superior endurance.]],
[34039]=[[The ultimate healer support with superior endurance.]],
[34040]=[[The ultimate CC support with superior endurance.]],
[34041]=[[An ultimate tank with superior endurance and DEF.]],
[34042]=[[A mage with high burst DMG in boss battles.]],
[34043]=[[A must-have support in Boss battles.]],
[34044]=[[One of three ultimate damage dealers.]],
[34045]=[[One of the twin stars. Boasts high CRIT.]],
[34046]=[[One of three ultimate damage dealers.]],
[34047]=[[The ultimate single-target support hero.]],
[34049]=[[Rank up your heroes to acquire more powerful skills.]],
[34050]=[[Awaken heroes to get more powerful stats.]],
[34051]=[[Warrior-Mage-Priest composition.]],
[34052]=[[A must-have damage dealer in Boss battles.]],
[34053]=[[One of three ultimate damage dealers.]],
[34054]=[[A high DPS back-row killer.]],
[34056]=[[King of the Arena]],
[34057]=[[Ultimate Single Burst DMG]],
[34058]=[[Buff Burst DMG]],
[34059]=[[Super Healing DMG Taker Support]],
[34060]=[[Super CC DMG]],
[34061]=[[Super assist burst DMG]],
[34062]=[[Super Multi-Action Buff Support]],
[34063]=[[Super Mage With Continuous CC DMG]],
[34064]=[[Super Buff Support]],
[34065]=[[Super Reap DMG]],
[34066]=[[Super Buff Tank]],
[34067]=[[Super Buff Support Capable of Dispelling]],
[34068]=[[Super Debuff DMG Dealer]],
[34070]=[[Super DMG Taker Support with Strong Healing]],
[34071]=[[Super DMG Warrior]],
[34072]=[[Super Buff Tank]],
[34073]=[[Super Single-target CC Mage]],
[34074]=[[Super AoE Mage with High DEF]],
[34076]=[[Super Clearing Power Mage]],
[34079]=[[Terrifying sustained DMG]],
[34081]=[[Super Burst DPS Warrior]],
[34082]=[[Mighty Shieldbearer]],
[34085]=[[Super AoE Taunt Warrior]],
[34088]=[[Super AoE Burst Damage Mage]],
[34091]=[[Super Buff & AoE Healing Support]],
[34100]=[[New Account Benefits]],
[34101]=[[Create a new character and enter the game to claim the benefits]],
[35001]=[[Nightfall Mastery]],
[35002]=[[Human Mastery]],
[35003]=[[Beastkin Mastery]],
[35004]=[[Forest Mastery]],
[35005]=[[Voider Mastery]],
[35006]=[[God Mastery]],
[35007]=[[Total Mastery]],
[35008]=[[Special Effect:]],
[35009]=[[Unlock Condition:]],
[35010]=[[Reset Mastery to get %s Formation Chips back. Are you sure you want to reset?]],
[35011]=[[Level up and boost the stats of %s heroes to activate more teams.]],
[35012]='Learn',
[35013]=[[Cannot reset from Lv. 1.]],
[35014]=[[Nightfall Mastery Lv. %d]],
[35015]=[[Human Mastery Lv. %d]],
[35016]=[[Beastkin Mastery Lv. %d]],
[35017]=[[Forest Mastery Lv. %d]],
[35018]=[[Voider Mastery Lv. %d]],
[35019]=[[God Mastery Lv. %d]],
[35020]=[[Total Faction Mastery Lv. %d]],
[35021]='Level',
[35022]=[[Insufficient Formation Chips. You can obtain more Formation Chips from the Void Dominator event or purchase them from the Giant Merchant.]],
[35023]='More',
[35024]=[[Team activation successful.]],
[35025]=[[Team stats are granted to all deployed heroes.]],
[35026]=[[Unlocked Teams]],
[35027]=[[More Teams]],
[35028]=[[Star Level: %s]],
[35029]=[[Reach Mastery Lv. %d for all four factions.]],
[35030]=[[Unlocks based on the average level of all factions.]],
[35031]=[[Divine/Voider Mastery]],
[36001]='Sign-In',
[36002]=[[Signed in]],
[36003]=[[Total Sign-In]],
[36004]=[[Round %d]],
[36005]=[[Extra Rewards]],
[36006]=[[2x Claim]],
[36007]=[[Spend %s to activate 2x Sign-In.]],
[36008]=[[Sign-In Rewards]],
[36009]=[[7D Login]],
[36010]=[[Day 1]],
[36011]=[[Day 2]],
[36012]=[[Day 3]],
[36013]=[[Day 4]],
[36014]=[[Day 5]],
[36015]=[[Day 6]],
[36016]=[[Day 7]],
[36017]=[[Skill Preview]],
[36018]=[[10x Summon]],
[36019]=[[Claimable after %s!]],
[36020]='Claim',
[36021]=[[Come back tomorrow]],
[36022]=[[Activate x2 to claim]],
[36023]='x2',
[36024]='Claim',
[36025]=[[Day %d]],
[36032]=[[Eternal Tree]],
[36033]='Roger',
[36034]='Anta',
[36035]='Renika',
[36036]='Onik',
[36037]=[[Nocturn Queen]],
[36038]=[[AoE CC Support with high DEF]],
[36039]=[[Powerful single-target CC Tank]],
[36040]=[[AoE CC Mage with healing]],
[36041]=[[High-Endurance AoE Marskman]],
[36042]=[[Burst DMG Mage]],
[36043]=[[Resurrection Support]],
[36050]=[[7D Challenge]],
[36051]=[[Day 1]],
[36052]=[[Day 2]],
[36053]=[[Day 3]],
[36054]=[[Day 4]],
[36055]=[[Day 5]],
[36056]=[[Day 6]],
[36057]=[[Day 7]],
[36058]='Points',
[36059]='Claim',
[36060]='Go',
[36061]='Day(s)',
[36062]=[[Points +1]],
[36063]=[[Complete challenge objectives]],
[36064]=[[Unlocks in %d day(s)!]],
[36065]=[[Challenge Points]],
[36066]=[[Points obtained from the 7D Challenge event. Accumulate points to get abundant rewards.]],
[36067]=[[A powerful mage with CC and burst abilities.]],
[36068]='Challenge',
[36069]=[[Event Time Remaining: %s]],
[36070]=[[The 7D Challenge has begun. Complete challenges to get abundant rewards!]],
[36071]=[[A series of new challenge objectives will be unlocked each day during the event. You can claim the ultimate reward after getting 75 points from completing challenge objectives.]],
[36072]=[[Basic Training Camp]],
[36073]=[[Elite Training Camp]],
[36074]=[[Ultimate Training Camp]],
[36075]=[[Unlocks in %s]],
[36076]=[[Complete the challenge to get S+ heroes and awesome rewards!]],
[36077]=[[Complete the challenge to get artifacts and awesome rewards!]],
[36078]=[[Day %s]],
[36081]=[[Claim Hero]],
[36082]=[[Claim Rewards]],
[36083]=[[Log in tomorrow to receive]],
[36084]=[[Login for %d more day(s) to obtain]],
[36085]=[[All rewards have been claimed.]],
[40064]='Gold',
[40065]='Diamond',
[40071]=[[Common Scroll]],
[40072]=[[Common Scroll]],
[40073]=[[Heart Summon]],
[40074]=[[Excellent Faction Scroll]],
[40075]=[[Common Chip]],
[40076]=[[Advanced Chip]],
[40077]=[[Rankup Stone]],
[40078]=[[Hero EXP]],
[40079]=[[Basic Quest Coupon]],
[40080]=[[Advanced Quest Coupon]],
[40081]=[[Phasic Particle]],
[40082]=[[Bloodstone Fragment]],
[40083]=[[Arena Medal]],
[40084]=[[Common Hero Shard]],
[40085]=[[Rare Hero Shard]],
[40086]=[[Excellent Hero Shard]],
[40087]=[[Excellent Elite Hero Shard]],
[40088]=[[God Rare Hero Shard]],
[40089]=[[God Excellent Hero Shard]],
[40090]=[[Voider Rare Hero Shard]],
[40091]=[[Voider Excellent Hero Shard]],
[40092]=[[Forest Excellent Hero Shard]],
[40093]=[[Nightfall Excellent Hero Shard]],
[40094]=[[Human Excellent Hero Shard]],
[40095]=[[Beastkin Excellent Hero Shard]],
[40096]=[[Proving Grounds Token]],
[40097]=[[Genesis Relic]],
[40098]=[[Proving Grounds Token]],
[40099]=[[Phasic Crystal]],
[40101]=[[Common Depraved Outlaw Shard]],
[40102]=[[Common Dark Shadow Shard]],
[40103]=[[Excellent Mad Blade Shard]],
[40104]=[[Excellent Scarlet Warden Shard]],
[40105]=[[Excellent Bling Barbie Shard]],
[40106]=[[Rare Aaron Shard]],
[40107]=[[Rare Type-0 Shard]],
[40108]=[[Rare Ominous Sound Shard]],
[40109]=[[Excellent Ancient Dragon Shard]],
[40110]=[[Excellent Moon Phoenix Shard]],
[40111]=[[Excellent Tekken Chief Shard]],
[40112]=[[Excellent Klass Shard]],
[40113]=[[Excellent Vampire Nurse Shard]],
[40114]=[[Excellent Onik Shard]],
[40115]=[[Excellent Dark Knight Shard]],
[40116]=[[Excellent Reina Shard]],
[40117]=[[Excellent Roger Shard]],
[40118]=[[Excellent Deadly Bartender Shard]],
[40119]=[[Excellent Goddess of Reincarnation Shard]],
[40120]=[[Excellent Queen of Havoc Shard]],
[40121]=[[Excellent Dragonslayer Shard]],
[40122]=[[Excellent Takeaway Pioneer Shard]],
[40123]=[[Common Bounty Hunter Shard]],
[40124]=[[Rare Winter Phantom Shard]],
[40125]=[[Excellent Denise Shard]],
[40126]=[[Excellent Withered Sand Shard]],
[40127]=[[Rare Dragon Warrior Shard]],
[40128]=[[Excellent Geo Shard]],
[40129]=[[Rare Ironblood Hunter Shard]],
[40130]=[[Excellent Depraved Outlaw Shard]],
[40131]=[[Excellent Silsa Shard]],
[40132]=[[Excellent Marissa Shard]],
[40133]=[[Rare Imperial Warrior Shard]],
[40134]=[[Excellent Nocturn Queen Shard]],
[40135]=[[Excellent Dark Wizard Shard]],
[40136]=[[Excellent Nightblade Ranger Shard]],
[40137]=[[Excellent Alvarez Shard]],
[40138]=[[Rare Holy Inquisitor Shard]],
[40139]=[[Excellent Spirit Messenger Shard]],
[40140]=[[Common Arena Battle Soul Shard]],
[40141]=[[Common Galaxy Hunter Shard]],
[40142]=[[Excellent Electric Boxer Shard]],
[40143]=[[Rare Kilmain Shard]],
[40144]=[[Common Fiery Dragon Shard]],
[40145]=[[Rare Steel Fist Shard]],
[40146]=[[Rare Hunting Spirit Shard]],
[40147]=[[Rare Berserk Axe Shard]],
[40148]=[[Excellent Odd Pioneer Shard]],
[40149]=[[Excellent Mecha Zombie Shard]],
[40150]=[[Excellent Fox Shadow Shard]],
[40151]=[[Excellent Carbon Exterminator Shard]],
[40152]=[[Excellent Anta Shard]],
[40153]=[[Excellent Evil Shaman Shard]],
[40154]=[[Excellent Type-0 Shard]],
[40155]=[[Excellent Sky Feather Shard]],
[40156]=[[Excellent Andal Shard]],
[40157]=[[Excellent Avilia Shard]],
[40158]=[[Excellent Dragon Warrior Shard]],
[40159]=[[Excellent Primal Guard Shard]],
[40160]=[[Excellent Dragonborn Shard]],
[40161]=[[Excellent Boxer Kangaroo Shard]],
[40162]=[[Common Garlic Samurai Shard]],
[40163]=[[Common Elemental Guard Shard]],
[40164]=[[Excellent Heart of Glory Shard]],
[40165]=[[Excellent Merciful Tree Spirit Shard]],
[40166]=[[Rare Blood Moon Shard]],
[40167]=[[Excellent Frost Roar Shard]],
[40168]=[[Excellent Renika Shard]],
[40169]=[[Rare Ruins Boulder Shard]],
[40170]=[[Excellent Karey Shard]],
[40171]=[[Excellent Leos Shard]],
[40172]=[[Excellent Steel Surfer Shard]],
[40173]=[[Excellent Nicole Shard]],
[40174]=[[Excellent Willow Sickle Shard]],
[40175]=[[Excellent Hamster Knight Shard]],
[40176]=[[Excellent Floral Shadowblade Shard]],
[40177]=[[Excellent Shark Walker Shard]],
[40178]=[[Excellent Eternal Tree Shard]],
[40179]=[[Rare Spore Warrior Shard]],
[40180]=[[Excellent Floral Spirit Shard]],
[40181]=[[Rare Evalyn Shard]],
[40182]=[[Excellent Bull Shard]],
[40183]=[[Excellent Ben Shard]],
[40184]=[[Common Chaos Shard]],
[40185]=[[Excellent Orianfi Shard]],
[40186]=[[Excellent Void Twin Shard]],
[40187]=[[Rare Forest Shard]],
[40188]=[[Excellent Mies Shard]],
[40189]=[[Excellent Valkyrie Shard]],
[40190]=[[Excellent Ocean Princess Shard]],
[40191]=[[Rare Scarlet Earl Shard]],
[40192]=[[Rare Cupid Shard]],
[40193]=[[Excellent God of Souls Shard]],
[40194]=[[Excellent Sun Goddess Shard]],
[40195]=[[Excellent Deep Terror Shard]],
[40196]=[[Excellent God of Thunder Shard]],
[40197]=[[Excellent Faceless Man Shard]],
[40198]=[[Excellent Catherine Shard]],
[40199]=[[Common Angel Shard]],
[40200]=[[Rare Goddess of Wisdom Shard]],
[40201]=[[Beastkin Rare Hero Shard]],
[40202]=[[Giant Merchant Refresh Rune]],
[40203]=[[Arena Ticket]],
[40204]=[[Character EXP]],
[40205]=[[Character Avatar]],
[40206]='Fuel',
[40207]=[[Union Coin]],
[40208]=[[Challenge Badge]],
[40209]=[[Union Contribution]],
[40210]='Bouquet',
[40211]=[[Flower Point]],
[40212]='Heart',
[40213]=[[Team Chip]],
[40214]=[[5-Star Evalyn Shard]],
[40215]=[[5-Star Light Shard]],
[40216]=[[3-Star Ares Shard]],
[40217]=[[4-Star Athena Shard]],
[40218]=[[Union Warzone Points]],
[40219]=[[Gene Source]],
[40220]=[[Glacial Tear]],
[40221]=[[Nightfall Rare Hero Shard]],
[40222]=[[Human Rare Hero Shard]],
[40223]=[[Forest Rare Hero Shard]],
[40224]=[[Life Crystal]],
[40225]=[[Beast Soul Essence]],
[40226]=[[Excellent Knight of Rebirth Shard]],
[40227]=[[Beast Soul Core]],
[40228]=[[You can upgrade the level of pet parts in the Garden. Equip pets to provide deployed heroes with high stat bonuses.]],
[40229]=[[Avatar: Light]],
[40230]=[[Avatar: Faith]],
[40231]=[[Pile of Gold]],
[40232]=[[Crates of Gold]],
[40233]=[[Pile of Hero EXP]],
[40234]=[[Crates of Hero EXP]],
[40235]=[[Pile of Rankup Stones]],
[40236]=[[Crates of Rankup Stones]],
[40237]=[[Pile of AFK Rewards]],
[40238]=[[Crates of AFK Rewards]],
[40239]=[[VIP EXP]],
[40240]=[[God Excellent Hero Shard]],
[40241]=[[God Excellent Elite Hero Shard]],
[40242]=[[Voider Excellent Hero Shard]],
[40243]=[[Voider Excellent Elite Hero Shard]],
[40244]=[[Forest Excellent Elite Hero Shard]],
[40245]=[[Nightfall Excellent Elite Hero Shard]],
[40246]=[[Human Excellent Elite Hero Shard]],
[40247]=[[Beastkin Excellent Elite Hero Shard]],
[40248]=[[Forest Excellent Hero Shard]],
[40249]=[[Nightfall Excellent Hero Shard]],
[40250]=[[Human Excellent Hero Shard]],
[40251]=[[Beastkin Excellent Hero Shard]],
[40252]=[[Fiend Coin]],
[40253]=[[Resurrection Potion]],
[40254]=[[Sweep Rewards]],
[40255]=[[Hero Badge]],
[40256]=[[Nightfall Hero Selection Card]],
[40257]=[[Human Hero Selection Card]],
[40258]=[[Beastkin Hero Selection Card]],
[40259]=[[Forest Hero Selection Card]],
[40260]=[[Voider Hero Selection Card]],
[40261]=[[God Hero Selection Card]],
[40262]=[[Hero Selection Card]],
[40263]=[[Advanced Arena Medal]],
[40264]=[[Legendary Duel Ticket]],
[40265]=[[Faction Selection Card]],
[40266]=[[Soul Source]],
[40267]='Turkey',
[40268]=[[Pumpkin Pie]],
[40269]=[[Avatar Frame]],
[40270]=[[God and Voider Elite Hero Shard]],
[40271]=[[God and Voider Excellent Hero Shard]],
[40272]=[[God and Voider Excellent Hero Shard]],
[40273]=[[Thanksgiving Event Avatar Frame]],
[40274]=[[Thanksgiving Event Avatar]],
[40275]=[[Adventure Progress Rank 1]],
[40276]=[[Proving Grounds Progress Rank 1]],
[40277]=[[Abyss City Progress Rank 1]],
[40278]=[[God Point Rank 1]],
[40279]=[[Voider Point Rank 1]],
[40280]=[[Human Point Rank 1]],
[40281]=[[Nightfall Point Rank 1]],
[40282]=[[Forest Point Rank 1]],
[40283]=[[Beastkin Point Rank 1]],
[40284]=[[Advanced Arena Rank 1]],
[40285]=[[Christmas Card]],
[40286]=[[Christmas Candy]],
[40287]=[[Hero Selection Card]],
[40288]=[[4-Faction Main S+ Hero Selection Card]],
[40289]=[[Hero Selection Card]],
[40290]=[[Hero Selection Card]],
[40291]=[[Hero Selection Card]],
[40292]=[[Hero Selection Card]],
[40293]=[[Rare Mad Blade Shard]],
[40294]=[[Rare Scarlet Warden Shard]],
[40295]=[[Rare Tekken Chief Shard]],
[40296]=[[Rare Dark Knight Shard]],
[40297]=[[Excellent Winter Phantom Shard]],
[40298]=[[Rare Withered Sand Shard]],
[40299]=[[Excellent Berserk Axe Shard]],
[40300]=[[Excellent Imperial Warrior Shard]],
[40301]=[[Rare Nightblade Ranger Shard]],
[40302]=[[Rare Spirit Messenger Shard]],
[40303]=[[Excellent Cupid Shard]],
[40304]=[[Rare Fox Shadow Shard]],
[40305]=[[Rare Evil Shaman Shard]],
[40306]=[[Rare Sky Feather Shard]],
[40307]=[[Excellent Ruins Boulder Shard]],
[40308]=[[Rare Karey Shard]],
[40309]=[[Rare Floral Shadowblade Shard]],
[40310]=[[Rare Bull Shard]],
[40311]=[[Rare Ben Shard]],
[40312]=[[Rare Valkyrie Shard]],
[40313]=[[Christmas Event Avatar]],
[40314]=[[New Year's Day Event Avatar]],
[40315]=[[Christmas Event Avatar Frame]],
[40316]=[[Common Gear Upgrade Chip]],
[40317]=[[Rare Gear Upgrade Chip]],
[40318]=[[Epic Gear Upgrade Chip]],
[40319]=[[Esper Badge]],
[40320]='Rose',
[40321]='Chocolate',
[40322]=[[Cupid's Blessing]],
[40323]=[[Soulmate Exclusive]],
[40324]=[[Hero Reward Selection Card]],
[40325]=[[Handful of Gold]],
[40326]=[[Handful of Hero EXP]],
[40327]=[[Magic Hat]],
[40328]=[[JOKER Card]],
[40329]=[[Magic Show]],
[40330]='JOKER',
[40331]=[[Epic Dragonborn Shard]],
[40332]='Geolite',
[40333]=[[Portion of Gold]],
[40334]=[[Portion of Hero EXP]],
[40335]=[[Small amount of Rankup Stones]],
[40336]=[[Handful of Rankup Stones]],
[40337]=[[Easter Egg Paint]],
[40338]=[[Bunny Chocolate]],
[40339]=[[Easter Event Avatar]],
[40340]=[[Easter Event Avatar Frame]],
[40341]=[[Excellent Gear Voucher]],
[40342]=[[Excellent+ Gear Voucher]],
[40343]=[[Epic Gear Voucher]],
[40344]=[[Epic+ Gear Voucher]],
[40345]=[[Legendary Gear Voucher]],
[40346]=[[Legendary+ Gear Voucher]],
[40347]=[[Portion of Geolite]],
[40348]=[[Trove of Geolite]],
[40349]=[[Crates of Geolite]],
[40350]='Fuel',
[40351]=[[T1 Gear]],
[40352]=[[T2 Gear]],
[40353]=[[T3 Gear]],
[40354]=[[T4 Gear]],
[40355]=[[T5 Gear]],
[40356]=[[T6 Gear]],
[40357]=[[T7 Gear]],
[40358]=[[T8 Gear]],
[40359]=[[T9 Gear]],
[40360]=[[T10 Gear]],
[40361]=[[Activity Points]],
[40362]=[[Divine Tree Energy]],
[40363]=[[When the gacha machine's energy is running low, you can use Divine Tree Energy to make draws.]],
[40364]=[[Epic Hero Shard]],
[40365]=[[Epic Faction Selection Card]],
[40366]=[[Legendary+ Gear Card]],
[40367]=[[Golden Chip]],
[40368]=[[Epic Faction Selection Card]],
[40369]=[[God and Voider Legendary Awakening Armor]],
[40370]=[[4-Faction Legendary Awakening Armor]],
[40371]=[[God and Voider Epic Awakening Armor]],
[40372]=[[4-Faction Epic Awakening Armor]],
[40373]=[[God and Voider Excellent Awakening Armor]],
[40374]=[[4-Faction Excellent Awakening Armor]],
[40375]=[[Core S Hero Selection Card]],
[40376]=[[4-Faction S Hero Selection Card]],
[40377]=[[4-Faction Main S+ Hero Selection Card]],
[40378]=[[God and Voider S+ Hero Selection Card]],
[40379]=[[Arena Support Hero Selection Card]],
[40380]=[[Excellent Gear Selection Chest]],
[40381]=[[Excellent+ Gear Selection Chest]],
[40382]=[[Epic Gear Selection Chest]],
[40383]=[[Epic+ Gear Selection Chest]],
[40384]=[[Legendary Gear Selection Chest]],
[40385]=[[Legendary+ Gear Selection Chest]],
[40386]=[[4-Faction Excellent Awakening Armor Shard]],
[40387]=[[Phoenix Summon Scroll]],
[40388]=[[Time Essence]],
[40389]=[[Fenixia Avatar]],
[40390]=[[Hero Opus EXP]],
[40391]=[[Excellent Fenixia Shard]],
[40392]=[[Rare Hero Selection Card]],
[40393]=[[Inferno Souls Summon Scroll]],
[40394]=[[Inferno Souls Avatar]],
[40395]='Pumpkin',
[40396]=[[Excellent Inferno Souls Shard]],
[40397]=[[Mecha Summon Scroll]],
[40398]=[[Firefox Spirit Avatar]],
[40399]=[[Excellent Firefox Spirit Shard]],
[40400]=[[Crucible Champion Avatar Frame]],
[40401]=[[Crucible Top 4 Avatar Frame]],
[40402]=[[Crucible Top 8 Avatar Frame]],
[40403]=[[Crucible Champion]],
[40404]=[[Crucible Top 4]],
[40405]=[[Crucible Top 8]],
[40406]=[[Epic Geo Shard]],
[40407]=[[Resource Selection Chest]],
[40408]=[[Normal Dice]],
[40409]=[[Lucky Dice]],
[40410]=[[Collection EXP]],
[40411]=[[4-Faction S Hero Advanced Selection Card A]],
[40412]=[[4-Faction S Hero Advanced Selection Card B]],
[40413]=[[4-Faction S+ Hero Selection Card]],
[40414]=[[God Key]],
[40415]=[[Mystic Dreamwing]],
[40416]='Rose',
[40417]=[[Heart Diamond]],
[40418]=[[4-Faction S+ Hero Advanced Selection Card A]],
[40419]=[[4-Faction S+ Hero Advanced Selection Card B]],
[40420]=[[Celestial S Hero Advanced Selection Card A]],
[40421]=[[Celestial S Hero Advanced Selection Card B]],
[40422]=[[Nightfall S+ Hero Selection Card]],
[40423]=[[Human S+ Hero Selection Card]],
[40424]=[[Beastkin S+ Hero Selection Card]],
[40425]=[[Forest S+ Hero Selection Card]],
[40426]=[[Blood Summon Scroll]],
[40427]=[[God Slayer Avatar]],
[40428]=[[Excellent God Slayer Shard]],
[40429]=[[Dragon Summon Scroll]],
[40430]=[[Dream Messenger Avatar]],
[40431]=[[Excellent Dream Messenger Shard]],
[40432]=[[Rice Dumpling]],
[40433]=[[Frozeblaze Dragoness]],
[40434]=[[The Pinnacle Avatar Frame]],
[40435]=[[The Pinnacle Top 4 Avatar Frame]],
[40436]=[[The Pinnacle Top 8 Avatar Frame]],
[40437]=[[The Pinnacle Champion]],
[40438]=[[The Pinnacle Top 4]],
[40439]=[[The Pinnacle Top 8]],
[40440]=[[HP +3%
ATK +3%]],
[40441]=[[4-Faction Common S Hero Selection Card]],
[40442]=[[4-Faction Core S Hero Selection Card]],
[40443]=[[God and Voider S Hero Selection Card]],
[40444]=[[4-Faction S+ Hero Selection Card]],
[40445]=[[Shadow Medal]],
[40446]=[[Dimension Key]],
[40447]=[[All-Faction Epic Awakening Armor]],
[40448]=[[Basic Wish Ticket]],
[40449]=[[Medium Wish Ticket]],
[40450]=[[Advanced Wish Ticket]],
[40451]=[[4-Star Rune Selection Pack]],
[40452]=[[5-Star Rune Selection Pack]],
[40453]=[[6-Star Rune Selection Pack]],
[40454]=[[3-Star Rune Selection Pack]],
[40471]=[[Excellent Marissa Shard]],
[40472]=[[Excellent Saint Judge Shard]],
[40473]=[[Excellent Nicole Shard]],
[40474]=[[Spore Warrior Shard]],
[40475]=[[Excellent Aaron Shard]],
[40476]=[[Excellent Scarlet Earl Shard]],
[40477]=[[Excellent Kilmain Shard]],
[40478]=[[Excellent Hunting Spirit Shard]],
[40479]=[[Excellent Goddess of Life Shard]],
[40480]=[[God and Voider Rare Hero Selection Card]],
[40481]=[[Daily Surprise Giftbox]],
[40482]=[[4-Faction S+ Hero Shard Selection Chest]],
[40483]=[[Gold Treasure]],
[40484]=[[Hero EXP Treasure]],
[40485]=[[All-Faction Legendary Awakening Armor]],
[40486]=[[All-Faction Epic Awakening Armor]],
[40487]=[[All-Faction Excellent Awakening Armor]],
[40488]=[[Red Diamond]],
[41100]='Select',
[41101]=[[Are you sure you want to select this?]],
[42001]='Currency',
[42002]=[[Hero Skills]],
[42003]='Gear',
[42004]='Heroes',
[42005]=[[Summon Hero]],
[42006]=[[Summoning Hall]],
[42007]=[[Faction Summon]],
[42008]='Casino',
[42009]=[[Hero Rankup]],
[42010]=[[Hero Upgrade]],
[42011]=[[Acorn Tavern]],
[42012]='Alchemy',
[42013]='Summon',
[42014]='Sell',
[42015]='Adventure',
[42016]=[[Faction Summon]],
[42017]=[[Summoning Hall]],
[42018]=[[Awakening Spring]],
[42019]='Casino',
[42020]=[[Proving Grounds]],
[42021]=[[Acorn Tavern]],
[42022]=[[Event Dungeon]],
[42023]='Arena',
[42024]=[[Abyss City]],
[42025]=[[World Exploration]],
[42026]=[[Faction Mastery]],
[42027]=[[Giant Merchant]],
[42028]=[[Nucleus Crystal Extraction]],
[42029]=[[Training Room]],
[42030]=[[Chance: %0.2f]],
[42031]=[[No gear obtained.]],
[42032]=[[No items obtained.]],
[42033]=[[No shards obtained.]],
[42034]=[[No skills obtained.]],
[42035]=[[Grade: %d]],
[42036]='Events',
[42037]=[[Skill List]],
[42038]=[[Upgrade Skill]],
[42039]=[[Giant Merchant]],
[42040]='Arena',
[42041]=[[Upgrade Character]],
[42042]='Avatar',
[42043]='Stamina',
[42044]='Union',
[42045]=[[Faction Mastery]],
[42046]='Garden',
[42047]='Consumables',
[42048]=[[VIP EXP]],
[42049]=[[Tower of Fate]],
[42050]=[[Hero Pass]],
[42051]=[[Hero Selection Card]],
[42052]=[[Spirit Tower]],
[42053]=[[Thanksgiving Event]],
[42054]=[[Avatar Frame]],
[42055]=[[Exclusive Gear]],
[42056]='Artifact',
[42057]=[[Phoenix Summon]],
[42058]='Alchemy',
[42061]=[[Value Fund Chest]],
[42062]=[[Open the chest to claim any one of the following rewards.]],
[42063]=[[Value Fund]],
[42064]=[[Deluxe Fund Chest]],
[42065]=[[Open the chest to claim any one of the following rewards.]],
[42066]=[[Deluxe Fund]],
[42067]=[[Training Boost]],
[42068]=[[Super 30x Rebate]],
[42069]='50000',
[42070]='120000',
[42071]=[[Total Reward Value]],
[42101]=[[These stones that contain the energy of Yggdrasil are scattered all over the world. Used to activate and enhance Exclusive Gear of +1 to +10 heroes from all factions. Dropped from AFK rewards after clearing Stage 16-20.]],
[42102]=[[These stones that contain the exceptionally pure energy of Yggdrasil are buried deep underground. Used to enhance the Exclusive Gear of +11 to +20 heroes from all factions. Dropped from AFK rewards after clearing Stage 17-20.]],
[42103]=[[Contains the immense power of night. Used to enhance the Exclusive Gear of +21 to +40 Nightfall heroes. Dropped from AFK rewards after clearing Stage 18-20.]],
[42104]=[[Contains the great power of faith. Used to upgrade the Exclusive Gear of +21 to +40 Human heroes. Dropped from AFK rewards after clearing Stage 18-20.]],
[42105]=[[Contains the primal power of beasts. Used to upgrade the Exclusive Gear of +21 to +40 Beastkin heroes. Dropped from AFK rewards after clearing Stage 18-20.]],
[42106]=[[Contains the pure power of nature. Used to upgrade the Exclusive Gear of +21 to +40 Forest heroes. Dropped from AFK rewards after clearing Stage 18-20.]],
[42107]=[[Contains the mysterious power of the void. Used to upgrade the Exclusive Gear of +21 to +40 Voider heroes. Dropped from AFK rewards after clearing Stage 18-20.]],
[42108]=[[Contains the unfiltered power of domination. Used to upgrade the Exclusive Gear of +21 to +40 God heroes. Dropped from AFK rewards after clearing Stage 18-20.]],
[42109]=[[Open the chest to choose one of the following Exclusive Gear upgrade materials.]],
[42110]=[[Material required to enhance the Energy Amulet and Thunder Amulet. Contains the power of void stars and can be obtained from Abyss City, Acorn Tavern, and AFK Rewards in Adventure.]],
[42111]=[[Material required to upgrade the Shiny Amulet and Ocean Amulet. Contains the power of void stars and can be obtained from Abyss City, Acorn Tavern, and AFK Rewards in Adventure.]],
[42112]=[[Material required to enhance the Icy Amulet. Contains the power of void stars and can be obtained from Abyss City, Acorn Tavern, and AFK Rewards in Adventure.]],
[42113]=[[Material required to enhance the Lava Amulet. Contains the power of void stars and can be obtained from Abyss City, Acorn Tavern, and AFK Rewards in Adventure.]],
[42114]=[[Material required to enhance the Time Amulet and the Silvermoon Amulet. Contains the power of void stars and can be obtained from Abyss City, Acorn Tavern, and AFK Rewards in Adventure.]],
[42115]=[[Material required to enhance the Sun Amulet. Contains the power of void stars and can be obtained from Abyss City, Acorn Tavern, and AFK Rewards in Adventure.]],
[42116]=[[Open the chest to choose one of the following artifact enhancement materials.]],
[42117]=[[Clear Abyss City - Novice]],
[42118]=[[Clear Abyss City - Easy]],
[42119]=[[Clear Abyss City - Normal]],
[42120]=[[Clear Abyss City - Hard]],
[42121]=[[Clear Abyss City - Nightmare]],
[42122]=[[Clear Abyss City - Hell]],
[42123]=[[Clear Abyss City - Destruction]],
[42124]=[[Clear Abyss City - Insane]],
[42125]=[[Clear Abyss City - Legendary]],
[42126]=[[Clear Abyss City - Mythic]],
[42127]=[[Christmas Event]],
[42128]=[[Chest Content]],
[42129]='Use',
[42130]=[[You haven't selected any rewards!]],
[42131]=[[Esper Badge Event]],
[42132]=[[Valentine's Day Event]],
[42133]=[[April Fool's Event]],
[42134]=[[Gear Upgrade]],
[42135]=[[Dragon Boat Event]],
[42136]=[[Gear Voucher]],
[42137]=[[Activity Points]],
[42138]=[[Heartbeat Roulette]],
[42139]=[[Open the chest to choose one of the following artifact enhancement materials.]],
[42140]=[[Clear Abyss City - Novice 10]],
[42141]=[[Phoenix's Arrival]],
[42142]=[[Event Collection]],
[42143]=[[Obtained from supreme chests.]],
[42144]=[[Obtained from events and supreme chests.]],
[42145]=[[Obtained from monthly packs and supreme chests.]],
[42146]=[[Obtained from the Giant Merchant.]],
[42147]=[[Open the Gear Chest to choose a <color=#dd3c2dFF>Legendary+</color> Gear of any type with at least 2 Legendary attributes.]],
[42148]=[[Obtained from events.]],
[42149]=[[Obtained from VIP Rewards.]],
[42150]=[[Inferno's Arrival]],
[42151]=[[Inferno Souls Summon]],
[42152]=[[Mecha's Arrival]],
[42153]=[[Gear Reforge]],
[42154]=[[Blood's Arrival]],
[42155]=[[Witch's Adventure]],
[42156]=[[Dragon's Arrival]],
[42157]=[[EXP Pass]],
[42158]=[[Puzzle Games]],
[42159]=[[Wish Summon Card]],
[42160]=[[Wish Weekly Pass]],
[42161]=[[Weekly Wish Card]],
[42162]='Chest',
[42163]=[[Random Chest]],
[42164]=[[Use to obtain one of the following rewards at random.]],
[42165]=[[Red Diamond]],
[42166]=[[Mid-Autumn Festival Word Collection Event]],
[42167]=[[Mid-Autumn Festival Gift]],
[42168]=[[National Day Bingo Event]],
[42169]=[[National Day Word Collection Event]],
[42171]=[[Wish Candy]],
[42172]=[[Dark Artifact Selection]],
[42173]='Select',
[42174]='Obtained',
[42175]='Selected',
[42176]=[[Qixi Lantern Event]],
[42177]=[[Peach Blossom Bingo]],
[42178]=[[Qixi Pack]],
[42179]=[[Lantern Sign-in]],
[42180]=[[Peach Blossom Quest]],
[42181]=[[You are tapping too quickly. Try again later.]],
[42182]=[[Obtained from tower chests.]],
[42183]=[[Obtained from the Anniversary Celebration Event.]],
[42201]=[[Relic Pass]],
[42202]=[[Relic Medal]],
[42203]=[[Relic Medals can be obtained from the Faction Expedition, Daily Arena, and Legendary Duel.]],
[42204]=[[Colorful Treasure Chest]],
[42207]=[[Monthly Card Chest]],
[42208]=[[Use to obtain one of the following rewards at random.]],
[42209]=[[Monthly Card VIP]],
[42211]=[[Avatar Selection Pack]],
[42212]=[[Open the pack to select an avatar from the list.]],
[50001]=[[Ruin Set]],
[50002]=[[Shadow Set]],
[50003]=[[Growth Set]],
[50004]=[[Harden Set]],
[50005]=[[Cross-Dimension Set]],
[50006]=[[Mage Set]],
[50007]=[[Soulheal Set]],
[50008]=[[Wrath Set]],
[50009]=[[Maniac Set]],
[50010]=[[Control Set]],
[50011]=[[Enchant Set]],
[50012]=[[Annihilation Set]],
[50013]=[[Multi-Dimension Set]],
[50014]=[[Soul Warden Set]],
[50015]=[[Fearless Set]],
[50016]=[[Faith Set]],
[50017]=[[Tenacity Set]],
[50018]=[[String Set]],
[50019]=[[Lv. 8 Guardian Set]],
[50020]=[[Lv. 8 Sage Set]],
[50021]=[[Lv. 8 Archangel Set]],
[50022]=[[Lv. 9 Earth Set]],
[50023]=[[Lv. 9 Lupine Set]],
[50024]=[[Lv. 9 Storm Set]],
[50025]=[[Lv. 9 Flame Beast Set]],
[50026]=[[Lv. 9 Guardian Set]],
[50027]=[[Lv. 9 Sage Set]],
[50028]=[[Lv. 9 Archangel Set]],
[50029]=[[Shoddy Set]],
[50030]=[[Iron Set]],
[50031]=[[Warrior Set]],
[50032]=[[Azure Set]],
[50033]=[[Justice Set]],
[50034]=[[Resilient Set]],
[50035]=[[Gaia Set]],
[50036]=[[Judgment Set]],
[50037]=[[Thunder Set]],
[50038]=[[Origin Set]],
[50039]=[[Fate Set]],
[50040]=[[Genesis Set]],
[50051]=[[ATK +10%]],
[50052]=[[CRIT +5%]],
[50053]=[[HP +15%]],
[50054]=[[Armor +10%]],
[50055]=[[SPD +10]],
[50056]=[[Energy +50]],
[50057]=[[Increase healing by 20%.]],
[50058]=[[Lose half Energy Regen and gain 30% ATK.]],
[50059]=[[Gain 5% ATK for every 10% missing HP.]],
[50060]=[[Skill DMG +50%.]],
[50061]=[[Have a 10% chance to stun the target when dealing DMG.]],
[50062]=[[Attacks have a 40% chance to deal Bonus DMG equal to 10% of Max HP (cannot exceed 200% of ATK).]],
[50063]=[[Gain 20% DMG Reduction.]],
[50064]=[[At the start of the battle, provide allies with 30% DMG Reduction and lower their ATK by 15% for 3 turns.]],
[50065]=[[All allies gain 15% ATK but lose 30% DEF.]],
[50066]=[[Reduce the ATK and DEF of allied heroes by 15%. For every allied hero on the battlefield, gain 6% ATK and DEF.]],
[50067]=[[Grant 10% DMG Reduction to allies in the front row, but reduce the DMG Reduction of allies in the back row by 10%.]],
[50068]=[[Grant 10% DMG to allies in the back row, but reduce the ATK of allies in the front row by 10%.]],
[50101]=[[T3 Gear Shard]],
[50102]=[[T4 Gear Shard]],
[50103]=[[Reduce Everything to Ashes]],
[50104]=[[Lv. 2 Crimson Crystal]],
[50105]=[[Lv. 1 Ruin Crystal]],
[50106]=[[Lv. 1 Shadow Crystal]],
[50107]=[[Lv. 1 Growth Crystal]],
[50108]=[[Lv. 1 Harden Crystal]],
[50109]=[[Lv. 1 Cross-Dimension Crystal]],
[50110]=[[Lv. 1 Mage Crystal]],
[50111]=[[Lv. 1 Soulheal Crystal]],
[50112]=[[Lv. 1 Wrath Crystal]],
[50113]=[[Lv. 1 Maniac Crystal]],
[50114]=[[Lv. 1 Control Crystal]],
[50115]=[[Lv. 1 Illusion Crystal]],
[50116]=[[Lv. 1 Annihilation Crystal]],
[50117]=[[Lv. 1 Multi-Dimension Crystal]],
[50118]=[[Lv. 1 Soul Warden Crystal]],
[50119]=[[Lv. 1 Fearless Crystal]],
[50120]=[[Lv. 1 Faith Crystal]],
[50121]=[[Lv. 1 Tenacity Crystal]],
[50122]=[[Lv. 1 Chord Crystal]],
[50123]=[[Lv. 2 Ruin Crystal]],
[50124]=[[Lv. 2 Shadow Crystal]],
[50125]=[[Lv. 2 Growth Crystal]],
[50126]=[[Lv. 2 Harden Crystal]],
[50127]=[[Lv. 2 Cross-Dimension Crystal]],
[50128]=[[Lv. 2 Mage Crystal]],
[50129]=[[Lv. 2 Soulheal Crystal]],
[50130]=[[Lv. 2 Wrath Crystal]],
[50131]=[[Lv. 2 Maniac Crystal]],
[50132]=[[Lv. 2 Control Crystal]],
[50133]=[[Lv. 2 Illusion Crystal]],
[50134]=[[Lv. 2 Annihilation Crystal]],
[50135]=[[Lv. 2 Multi-Dimension Crystal]],
[50136]=[[Lv. 2 Soul Warden Crystal]],
[50137]=[[Lv. 2 Fearless Crystal]],
[50138]=[[Lv. 2 Faith Crystal]],
[50139]=[[Lv. 2 Tenacity Crystal]],
[50140]=[[Lv. 2 Chord Crystal]],
[50141]=[[Lv. 3 Ruin Crystal]],
[50142]=[[Lv. 3 Shadow Crystal]],
[50143]=[[Lv. 3 Growth Crystal]],
[50144]=[[Lv. 3 Harden Crystal]],
[50145]=[[Lv. 3 Cross-Dimension Crystal]],
[50146]=[[Lv. 3 Mage Crystal]],
[50147]=[[Lv. 3 Soulheal Crystal]],
[50148]=[[Lv. 3 Wrath Crystal]],
[50149]=[[Lv. 3 Maniac Crystal]],
[50150]=[[Lv. 3 Control Crystal]],
[50151]=[[Lv. 3 Illusion Crystal]],
[50152]=[[Lv. 3 Annihilation Crystal]],
[50153]=[[Lv. 3 Multi-Dimension Crystal]],
[50154]=[[Lv. 3 Soul Warden Crystal]],
[50155]=[[Lv. 3 Fearless Crystal]],
[50156]=[[Lv. 3 Faith Crystal]],
[50157]=[[Lv. 3 Tenacity Crystal]],
[50158]=[[Lv. 3 Chord Crystal]],
[50159]=[[Lv. 4 Ruin Crystal]],
[50160]=[[Lv. 4 Shadow Crystal]],
[50161]=[[Lv. 4 Growth Crystal]],
[50162]=[[Lv. 4 Harden Crystal]],
[50163]=[[Lv. 4 Cross-Dimension Crystal]],
[50164]=[[Lv. 4 Mage Crystal]],
[50165]=[[Lv. 4 Soulheal Crystal]],
[50166]=[[Lv. 4 Wrath Crystal]],
[50167]=[[Lv. 4 Maniac Crystal]],
[50168]=[[Lv. 4 Control Crystal]],
[50169]=[[Lv. 4 Illusion Crystal]],
[50170]=[[Lv. 4 Annihilation Crystal]],
[50171]=[[Lv. 4 Multi-Dimension Crystal]],
[50172]=[[Lv. 4 Soul Warden Crystal]],
[50173]=[[Lv. 4 Fearless Crystal]],
[50174]=[[Lv. 4 Faith Crystal]],
[50175]=[[Lv. 4 Tenacity Crystal]],
[50176]=[[Lv. 4 Chord Crystal]],
[50177]=[[Lv. 5 Ruin Crystal]],
[50178]=[[Lv. 5 Shadow Crystal]],
[50179]=[[Lv. 5 Growth Crystal]],
[50180]=[[Lv. 5 Harden Crystal]],
[50181]=[[Lv. 5 Cross-Dimension Crystal]],
[50182]=[[Lv. 5 Mage Crystal]],
[50183]=[[Lv. 5 Soulheal Crystal]],
[50184]=[[Lv. 5 Wrath Crystal]],
[50185]=[[Lv. 5 Maniac Crystal]],
[50186]=[[Lv. 5 Control Crystal]],
[50187]=[[Lv. 5 Illusion Crystal]],
[50188]=[[Lv. 5 Annihilation Crystal]],
[50189]=[[Lv. 5 Multi-Dimension Crystal]],
[50190]=[[Lv. 5 Soul Warden Crystal]],
[50191]=[[Lv. 5 Fearless Crystal]],
[50192]=[[Lv. 5 Faith Crystal]],
[50193]=[[Lv. 5 Tenacity Crystal]],
[50194]=[[Lv. 5 Chord Crystal]],
[50195]=[[Lv. 6 Ruin Crystal]],
[50196]=[[Lv. 6 Shadow Crystal]],
[50197]=[[Lv. 6 Growth Crystal]],
[50198]=[[Lv. 6 Harden Crystal]],
[50199]=[[Lv. 6 Cross-Dimension Crystal]],
[50200]=[[Lv. 6 Mage Crystal]],
[50201]=[[Lv. 6 Soulheal Crystal]],
[50202]=[[Lv. 6 Wrath Crystal]],
[50203]=[[Lv. 6 Maniac Crystal]],
[50204]=[[Lv. 6 Control Crystal]],
[50205]=[[Lv. 6 Illusion Crystal]],
[50206]=[[Lv. 6 Annihilation Crystal]],
[50207]=[[Lv. 6 Multi-Dimension Crystal]],
[50208]=[[Lv. 6 Soul Warden Crystal]],
[50209]=[[Lv. 6 Fearless Crystal]],
[50210]=[[Lv. 6 Faith Crystal]],
[50211]=[[Lv. 6 Tenacity Crystal]],
[50212]=[[Lv. 6 Chord Crystal]],
[50213]=[[Ruin Crystal]],
[50214]=[[Shadow Crystal]],
[50215]=[[Growth Crystal]],
[50216]=[[Harden Crystal]],
[50217]=[[Cross-Dimension Crystal]],
[50218]=[[Mage Crystal]],
[50219]=[[Soulheal Crystal]],
[50220]=[[Wrath Crystal]],
[50221]=[[Maniac Crystal]],
[50222]=[[Control Crystal]],
[50223]=[[Illusion Crystal]],
[50224]=[[Annihilation Crystal]],
[50225]=[[Multi-Dimension Crystal]],
[50226]=[[Soul Warden Crystal]],
[50227]=[[Fearless Crystal]],
[50228]=[[Faith Crystal]],
[50229]=[[Tenacity Crystal]],
[50230]=[[Chord Crystal]],
[50231]=[[T3 Crystal Shard]],
[50232]=[[T4 Crystal Shard]],
[50233]=[[Shoddy Dagger]],
[50234]=[[Iron Dagger]],
[50235]=[[Warrior Sword]],
[50236]=[[Azure Blade]],
[50237]=[[Justice Dagger]],
[50238]=[[Resilient Claymore]],
[50239]=[[Gaia Saber]],
[50240]=[[Judgment Sword]],
[50241]=[[Thunder Blade]],
[50242]=[[Origin Blade]],
[50243]=[[Fate Sword]],
[50244]=[[Genesis Blade]],
[50245]=[[Shoddy Cuirass]],
[50246]=[[Iron Cuirass]],
[50247]=[[Warrior Armor]],
[50248]=[[Azure Armor]],
[50249]=[[Justice Armor]],
[50250]=[[Resilient Armor]],
[50251]=[[Gaia Cuirass]],
[50252]=[[Judgment Armor]],
[50253]=[[Thunder Armor]],
[50254]=[[Origin Armor]],
[50255]=[[Fate Armor]],
[50256]=[[Genesis Armor]],
[50257]=[[Shoddy Helmet]],
[50258]=[[Iron Helmet]],
[50259]=[[Warrior Helmet]],
[50260]=[[Azure Helmet]],
[50261]=[[Justice Helmet]],
[50262]=[[Resilient Helmet]],
[50263]=[[Gaia Helmet]],
[50264]=[[Judgment Helmet]],
[50265]=[[Thunder Helmet]],
[50266]=[[Origin Helmet]],
[50267]=[[Fate Helmet]],
[50268]=[[Genesis Helmet]],
[50269]=[[Shoddy Boots]],
[50270]=[[Iron Boots]],
[50271]=[[Warrior Boots]],
[50272]=[[Azure Boots]],
[50273]=[[Justice Boots]],
[50274]=[[Resilient Boots]],
[50275]=[[Gaia Boots]],
[50276]=[[Judgment Boots]],
[50277]=[[Thunder Boots]],
[50278]=[[Origin Boots]],
[50279]=[[Fate Boots]],
[50280]=[[Genesis Boots]],
[50281]=[[Wrath Crystal]],
[50282]=[[Wrath Crystal]],
[50283]=[[Wrath Crystal]],
[50284]=[[Wrath Crystal]],
[50285]=[[Wrath Crystal]],
[50286]=[[Wrath Crystal]],
[50287]=[[Wrath Crystal]],
[50288]=[[Mage Crystal]],
[50289]=[[Mage Crystal]],
[50290]=[[Mage Crystal]],
[50291]=[[Mage Crystal]],
[50292]=[[Mage Crystal]],
[50293]=[[Mage Crystal]],
[50294]=[[Mage Crystal]],
[50295]=[[Ruin Crystal]],
[50296]=[[Ruin Crystal]],
[50297]=[[Ruin Crystal]],
[50298]=[[Ruin Crystal]],
[50299]=[[Ruin Crystal]],
[50300]=[[Ruin Crystal]],
[50301]=[[Ruin Crystal]],
[50302]=[[Growth Crystal]],
[50303]=[[Growth Crystal]],
[50304]=[[Growth Crystal]],
[50305]=[[Growth Crystal]],
[50306]=[[Growth Crystal]],
[50307]=[[Growth Crystal]],
[50308]=[[Growth Crystal]],
[50309]=[[Annihilation Crystal]],
[50310]=[[Annihilation Crystal]],
[50311]=[[Annihilation Crystal]],
[50312]=[[Annihilation Crystal]],
[50313]=[[Annihilation Crystal]],
[50314]=[[Annihilation Crystal]],
[50315]=[[Annihilation Crystal]],
[50316]=[[Cross-Dimension Crystal]],
[50317]=[[Cross-Dimension Crystal]],
[50318]=[[Cross-Dimension Crystal]],
[50319]=[[Cross-Dimension Crystal]],
[50320]=[[Cross-Dimension Crystal]],
[50321]=[[Cross-Dimension Crystal]],
[50322]=[[Cross-Dimension Crystal]],
[50323]=[[Tenacity Crystal]],
[50324]=[[Tenacity Crystal]],
[50325]=[[Tenacity Crystal]],
[50326]=[[Tenacity Crystal]],
[50327]=[[Tenacity Crystal]],
[50328]=[[Tenacity Crystal]],
[50329]=[[Tenacity Crystal]],
[50330]=[[Shadow Crystal]],
[50331]=[[Shadow Crystal]],
[50332]=[[Shadow Crystal]],
[50333]=[[Shadow Crystal]],
[50334]=[[Shadow Crystal]],
[50335]=[[Shadow Crystal]],
[50336]=[[Shadow Crystal]],
[50337]=[[Multi-Dimension Crystal]],
[50338]=[[Multi-Dimension Crystal]],
[50339]=[[Multi-Dimension Crystal]],
[50340]=[[Multi-Dimension Crystal]],
[50341]=[[Multi-Dimension Crystal]],
[50342]=[[Multi-Dimension Crystal]],
[50343]=[[Multi-Dimension Crystal]],
[50344]=[[Chord Crystal]],
[50345]=[[Chord Crystal]],
[50346]=[[Chord Crystal]],
[50347]=[[Chord Crystal]],
[50348]=[[Chord Crystal]],
[50349]=[[Chord Crystal]],
[50350]=[[Chord Crystal]],
[50351]=[[Soulheal Crystal]],
[50352]=[[Soulheal Crystal]],
[50353]=[[Soulheal Crystal]],
[50354]=[[Soulheal Crystal]],
[50355]=[[Soulheal Crystal]],
[50356]=[[Soulheal Crystal]],
[50357]=[[Soulheal Crystal]],
[50358]=[[Faith Crystal]],
[50359]=[[Faith Crystal]],
[50360]=[[Faith Crystal]],
[50361]=[[Faith Crystal]],
[50362]=[[Faith Crystal]],
[50363]=[[Faith Crystal]],
[50364]=[[Faith Crystal]],
[50365]=[[Control Crystal]],
[50366]=[[Control Crystal]],
[50367]=[[Control Crystal]],
[50368]=[[Control Crystal]],
[50369]=[[Control Crystal]],
[50370]=[[Control Crystal]],
[50371]=[[Control Crystal]],
[50372]=[[Soul Warden Crystal]],
[50373]=[[Soul Warden Crystal]],
[50374]=[[Soul Warden Crystal]],
[50375]=[[Soul Warden Crystal]],
[50376]=[[Soul Warden Crystal]],
[50377]=[[Soul Warden Crystal]],
[50378]=[[Soul Warden Crystal]],
[50379]=[[Fearless Crystal]],
[50380]=[[Fearless Crystal]],
[50381]=[[Fearless Crystal]],
[50382]=[[Fearless Crystal]],
[50383]=[[Fearless Crystal]],
[50384]=[[Fearless Crystal]],
[50385]=[[Fearless Crystal]],
[50386]=[[Maniac Crystal]],
[50387]=[[Maniac Crystal]],
[50388]=[[Maniac Crystal]],
[50389]=[[Maniac Crystal]],
[50390]=[[Maniac Crystal]],
[50391]=[[Maniac Crystal]],
[50392]=[[Maniac Crystal]],
[50393]=[[Illusion Crystal]],
[50394]=[[Illusion Crystal]],
[50395]=[[Illusion Crystal]],
[50396]=[[Illusion Crystal]],
[50397]=[[Illusion Crystal]],
[50398]=[[Illusion Crystal]],
[50399]=[[Illusion Crystal]],
[50496]=[[Gather 30 to merge a T3 Crystal.]],
[50497]=[[Gather 30 to merge a T4 Crystal.]],
[50498]=[[Gather 30 to merge a piece of T3 Gear.]],
[50499]=[[Gather 50 to merge a piece of T4 Gear.]],
[50500]=[[Used to purchase T7 Crystals.]],
[50501]=[[Random Stats]],
[50502]=[[HP +300]],
[50503]=[[HP +450]],
[50504]=[[HP +675]],
[50505]=[[HP +1386]],
[50506]=[[HP +2600]],
[50507]=[[HP +5460]],
[50508]=[[HP +11701]],
[50509]=[[HP +11701]],
[50510]=[[HP +11701]],
[50511]=[[ATK +50]],
[50512]=[[ATK +75]],
[50513]=[[ATK +112]],
[50514]=[[ATK +173]],
[50515]=[[ATK +260]],
[50516]=[[ATK +390]],
[50517]=[[ATK +585]],
[50518]=[[ATK +585]],
[50519]=[[ATK +585]],
[50551]=[[HP +6400]],
[50552]=[[ATK +640]],
[50553]=[[SPD +4]],
[50554]=[[CRIT +2%]],
[50555]=[[CRIT DMG +4%]],
[50556]=[[PEN +4%]],
[50557]=[[Block +4%]],
[50558]=[[Armor PEN +2%]],
[50559]=[[DMG Reduction +2%]],
[50560]=[[HP Ratio +2%]],
[50561]=[[ATK Ratio +2%]],
[50562]=[[Skill DMG +3%]],
[50563]=[[Energy +4]],
[50564]=[[Holy DMG +3%]],
[50565]=[[T7 Crystal Shard]],
[50566]=[[Gather 50 to merge a T7 Crystal.]],
[50567]=[[Select Set]],
[50568]=[[ATK +1778]],
[50569]=[[CRIT +8%]],
[50570]=[[CRIT DMG +15%]],
[50571]=[[ATK Ratio +2%]],
[50572]=[[ATK Ratio +3%]],
[50573]=[[ATK Ratio +1%]],
[50574]=[[ATK Ratio +3%]],
[50575]=[[ATK +392]],
[50576]=[[ATK +509]],
[50577]=[[ATK +662]],
[50578]=[[ATK +861]],
[50579]=[[ATK +1119]],
[50580]=[[ATK +1679]],
[50581]=[[ATK +2518]],
[50582]=[[HP +2744]],
[50583]=[[HP +3567]],
[50584]=[[HP +4635]],
[50585]=[[HP +6027]],
[50586]=[[HP +7835]],
[50587]=[[HP +11755]],
[50588]=[[HP +17630]],
[50589]=[[ATK +2.8%]],
[50590]=[[ATK +3.5%]],
[50591]=[[ATK +4.9%]],
[50592]=[[ATK +6.3%]],
[50593]=[[ATK +8%]],
[50594]=[[ATK +13%]],
[50595]=[[ATK +20%]],
[50596]=[[ATK +1.4%]],
[50597]=[[ATK +1.4%]],
[50598]=[[ATK +2.1%]],
[50599]=[[ATK +2.8%]],
[50600]=[[ATK +4%]],
[50601]=[[ATK +6%]],
[50602]=[[ATK +10%]],
[50603]=[[HP +3.5%]],
[50604]=[[HP +4.2%]],
[50605]=[[HP +5.6%]],
[50606]=[[HP +7.7%]],
[50607]=[[HP +11%]],
[50608]=[[HP +15%]],
[50609]=[[HP +24%]],
[50610]=[[HP +1.4%]],
[50611]=[[HP +2.1%]],
[50612]=[[HP +2.8%]],
[50613]=[[HP +3.5%]],
[50614]=[[HP +5%]],
[50615]=[[HP +8%]],
[50616]=[[HP +12%]],
[50617]=[[CRIT +1.4%]],
[50618]=[[CRIT +2.1%]],
[50619]=[[CRIT +2.8%]],
[50620]=[[CRIT +3.5%]],
[50621]=[[CRIT +4%]],
[50622]=[[CRIT +7%]],
[50623]=[[CRIT +11%]],
[50624]=[[CRIT DMG +4.9%]],
[50625]=[[CRIT DMG +7%]],
[50626]=[[CRIT DMG +9.1%]],
[50627]=[[CRIT DMG +11.9%]],
[50628]=[[CRIT DMG +15%]],
[50629]=[[CRIT DMG +23%]],
[50630]=[[CRIT DMG +35%]],
[50631]=[[Armor PEN +2.8%]],
[50632]=[[Armor PEN +4.2%]],
[50633]=[[Armor PEN +5.6%]],
[50634]=[[Armor PEN +7%]],
[50635]=[[Armor PEN +9%]],
[50636]=[[Armor PEN +14%]],
[50637]=[[Armor PEN +21%]],
[50638]=[[Holy DMG +2.8%]],
[50639]=[[Holy DMG +4.2%]],
[50640]=[[Holy DMG +5.6%]],
[50641]=[[Holy DMG +7%]],
[50642]=[[Holy DMG +9%]],
[50643]=[[Holy DMG +14%]],
[50644]=[[Holy DMG +21%]],
[50645]=[[Skill DMG +2.8%]],
[50646]=[[Skill DMG +4.2%]],
[50647]=[[Skill DMG +5.6%]],
[50648]=[[Skill DMG +7%]],
[50649]=[[Skill DMG +9%]],
[50650]=[[Skill DMG +14%]],
[50651]=[[Skill DMG +21%]],
[50652]=[[ATK +110]],
[50653]=[[ATK +143]],
[50654]=[[ATK +286]],
[50655]=[[ATK +371]],
[50656]=[[ATK +483]],
[50657]=[[ATK +628]],
[50658]=[[ATK +816]],
[50659]=[[ATK +1061]],
[50660]=[[ATK +1380]],
[50661]=[[ATK +1794]],
[50662]=[[ATK +2871]],
[50663]=[[ATK +4307]],
[50664]=[[HP +660]],
[50665]=[[HP +858]],
[50666]=[[HP +1716]],
[50667]=[[HP +2974]],
[50668]=[[HP +3866]],
[50669]=[[HP +6283]],
[50670]=[[HP +8168]],
[50671]=[[HP +10618]],
[50672]=[[HP +13804]],
[50673]=[[HP +17946]],
[50674]=[[HP +28713]],
[50675]=[[HP +43070]],
[50676]=[[HP +432]],
[50677]=[[HP +561]],
[50678]=[[HP +1123]],
[50679]=[[HP +1946]],
[50680]=[[HP +2530]],
[50681]=[[HP +4112]],
[50682]=[[HP +5346]],
[50683]=[[HP +6950]],
[50684]=[[HP +9035]],
[50685]=[[HP +11746]],
[50686]=[[HP +18794]],
[50687]=[[HP +28191]],
[50688]=[[ATK +72]],
[50689]=[[ATK +93]],
[50690]=[[ATK +187]],
[50691]=[[ATK +243]],
[50692]=[[ATK +316]],
[50693]=[[ATK +411]],
[50694]=[[ATK +534]],
[50695]=[[ATK +695]],
[50696]=[[ATK +903]],
[50697]=[[ATK +1174]],
[50698]=[[ATK +1879]],
[50699]=[[ATK +2819]],
[50700]=[[ATK +2.1%]],
[50701]=[[ATK +2.8%]],
[50702]=[[ATK +3.5%]],
[50703]=[[ATK +4.9%]],
[50704]=[[ATK +6%]],
[50705]=[[ATK +10%]],
[50706]=[[ATK +15%]],
[50707]=[[HP +2.8%]],
[50708]=[[HP +3.5%]],
[50709]=[[HP +4.2%]],
[50710]=[[HP +6.3%]],
[50711]=[[HP +8%]],
[50712]=[[HP +12%]],
[50713]=[[HP +18%]],
[50714]=[[DMG to Warriors +10.5%]],
[50715]=[[DMG to Warriors +14%]],
[50716]=[[DMG to Warriors +18.2%]],
[50717]=[[DMG to Warriors +23.8%]],
[50718]=[[DMG to Warriors +31%]],
[50719]=[[DMG to Warriors +46%]],
[50720]=[[DMG to Warriors +70%]],
[50721]=[[DMG to Mages +10.5%]],
[50722]=[[DMG to Mages +14%]],
[50723]=[[DMG to Mages +18.2%]],
[50724]=[[DMG to Mages +23.8%]],
[50725]=[[DMG to Mages +31%]],
[50726]=[[DMG to Mages +46%]],
[50727]=[[DMG to Mages +70%]],
[50728]=[[DMG to Hunters +10.5%]],
[50729]=[[DMG to Hunters +14%]],
[50730]=[[DMG to Hunters +18.2%]],
[50731]=[[DMG to Hunters +23.8%]],
[50732]=[[DMG to Hunters +31%]],
[50733]=[[DMG to Hunters +46%]],
[50734]=[[DMG to Hunters +70%]],
[50735]=[[DMG to Assassins +10.5%]],
[50736]=[[DMG to Assassins +14%]],
[50737]=[[DMG to Assassins +18.2%]],
[50738]=[[DMG to Assassins +23.8%]],
[50739]=[[DMG to Assassins +31%]],
[50740]=[[DMG to Assassins +46%]],
[50741]=[[DMG to Assassins +70%]],
[50742]=[[DMG to Priests +10.5%]],
[50743]=[[DMG to Priests +14%]],
[50744]=[[DMG to Priests +18.2%]],
[50745]=[[DMG to Priests +23.8%]],
[50746]=[[DMG to Priests +31%]],
[50747]=[[DMG to Priests +46%]],
[50748]=[[DMG to Priests +70%]],
[50749]=[[Skill DMG +4.9%]],
[50750]=[[Skill DMG +7%]],
[50751]=[[Skill DMG +9.1%]],
[50752]=[[Skill DMG +11.9%]],
[50753]=[[Skill DMG +15%]],
[50754]=[[Skill DMG +23%]],
[50755]=[[Skill DMG +35%]],
[50756]=[[Energy +4]],
[50757]=[[Energy +7]],
[50758]=[[Energy +9]],
[50759]=[[Energy +11]],
[50760]=[[Energy +15]],
[50761]=[[Energy +23]],
[50762]=[[Energy +35]],
[50763]=[[SPD +4]],
[50764]=[[SPD +7]],
[50765]=[[SPD +9]],
[50766]=[[SPD +11]],
[50767]=[[SPD +15]],
[50768]=[[SPD +23]],
[50769]=[[SPD +35]],
[50770]=[[DMG Reduction +2.8%]],
[50771]=[[DMG Reduction +4.2%]],
[50772]=[[DMG Reduction +4.9%]],
[50773]=[[DMG Reduction +7%]],
[50774]=[[DMG Reduction +9%]],
[50775]=[[DMG Reduction +14%]],
[50776]=[[DMG Reduction +21%]],
[50777]=[[Block +4.9%]],
[50778]=[[Block +7%]],
[50779]=[[Block +9.1%]],
[50780]=[[Block +11.9%]],
[50781]=[[Block +15%]],
[50782]=[[Block +23%]],
[50783]=[[Block +35%]],
[50784]=[[CRIT +1.4%]],
[50785]=[[CRIT +2.1%]],
[50786]=[[CRIT +2.8%]],
[50787]=[[CRIT +3.5%]],
[50788]=[[CRIT +4%]],
[50789]=[[CRIT +7%]],
[50790]=[[CRIT +11%]],
[50791]=[[CRIT DMG +4.9%]],
[50792]=[[CRIT DMG +7%]],
[50793]=[[CRIT DMG +9.1%]],
[50794]=[[CRIT DMG +11.9%]],
[50795]=[[CRIT DMG +15%]],
[50796]=[[CRIT DMG +23%]],
[50797]=[[CRIT DMG +35%]],
[50798]=[[Accuracy +7%]],
[50799]=[[Accuracy +9.8%]],
[50800]=[[Accuracy +12.6%]],
[50801]=[[Accuracy +16.8%]],
[50802]=[[Accuracy +22%]],
[50803]=[[Accuracy +32%]],
[50804]=[[Accuracy +49%]],
[50805]=[[Armor PEN +4.9%]],
[50806]=[[Armor PEN +6.3%]],
[50807]=[[Armor PEN +8.4%]],
[50808]=[[Armor PEN +11.2%]],
[50809]=[[Armor PEN +15%]],
[50810]=[[Armor PEN +22%]],
[50811]=[[Armor PEN +33%]],
[50812]=[[T6 Destruction Crystal Shard]],
[50813]=[[T7 Destruction Crystal Shard]],
[50814]=[[T8 Destruction Crystal Shard]],
[50815]=[[T9 Destruction Crystal Shard]],
[50816]=[[T10 Destruction Crystal Shard]],
[50817]=[[T11 Destruction Crystal Shard]],
[50818]=[[T12 Destruction Crystal Shard]],
[50819]=[[T6 Creation Crystal Shard]],
[50820]=[[T7 Creation Crystal Shard]],
[50821]=[[T8 Creation Crystal Shard]],
[50822]=[[T9 Creation Crystal Shard]],
[50823]=[[T10 Creation Crystal Shard]],
[50824]=[[T11 Creation Crystal Shard]],
[50825]=[[T12 Creation Crystal Shard]],
[50826]=[[Gather 30 shards to merge a T6 Destruction Crystal.]],
[50827]=[[Gather 30 shards to merge a T7 Destruction Crystal.]],
[50828]=[[Gather 30 shards to merge a T8 Destruction Crystal.]],
[50829]=[[Gather 50 shards to merge a T9 Destruction Crystal.]],
[50830]=[[Gather 50 shards to merge a T10 Destruction Crystal.]],
[50831]=[[Gather 50 shards to merge a T11 Destruction Crystal.]],
[50832]=[[Gather 50 shards to merge a T12 Destruction Crystal.]],
[50833]=[[Gather 30 shards to merge a T6 Creation Crystal.]],
[50834]=[[Gather 30 shards to merge a T7 Creation Crystal.]],
[50835]=[[Gather 30 shards to merge a T8 Creation Crystal.]],
[50836]=[[Gather 50 shards to merge a T9 Creation Crystal.]],
[50837]=[[Gather 50 shards to merge a T10 Creation Crystal.]],
[50838]=[[Gather 50 shards to merge a T11 Creation Crystal.]],
[50839]=[[Gather 50 shards to merge a T12 Creation Crystal.]],
[51001]=[[Energy Amulet]],
[51002]=[[Icy Amulet]],
[51003]=[[Shiny Amulet]],
[51004]=[[Lava Amulet]],
[51005]=[[Time Amulet]],
[51006]=[[Sun Amulet]],
[51007]=[[Angel Headband]],
[51008]=[[Arcane Ring]],
[51009]=[[Eternal Cloak]],
[51010]=[[Spiritsong Necklace]],
[51011]=[[Holy Wings]],
[51012]=[[Sacred Splendor]],
[51013]=[[Devil's Claw]],
[51014]=[[Devil's Bonespike]],
[51015]=[[Devil's Fangs]],
[51016]=[[Eye of Destruction]],
[51017]=[[Dragon Scale]],
[51018]=[[Devil's Horn]],
[51019]=[[Devil's Wings]],
[51020]=[[Devil's Flag]],
[51021]=[[Devil's Vambrace]],
[51022]=[[Devil's Heart]],
[51023]=[[Devil's Lance]],
[51024]=[[Devil's Bonesword]],
[51025]=[[Silvermoon Amulet]],
[51026]=[[Ocean Amulet]],
[51027]=[[Mountain Amulet]],
[51028]=[[Abyssal Amulet]],
[51029]=[[Thunder Amulet]],
[51031]=[[Holy Horn]],
[51032]=[[Cursed Book]],
[51033]=[[Devil's Shield]],
[51038]=[[Twilight Staff]],
[51101]=[[Soul Whip]],
[51102]=[[Primal Relic]],
[51103]=[[Jasper Fang]],
[51104]=[[Doom Hook]],
[51105]=[[Darkspawn Horn]],
[51106]=[[Scepter of Hatred]],
[51107]=[[Terror Hook]],
[51108]=[[Terror Blade]],
[51109]=[[Phantom Hat]],
[51110]=[[Astrological Sphere]],
[51111]=[[Alchemy Pot]],
[51112]=[[Alchemic Blade]],
[51113]='Mandolin',
[51114]=[[Dragon Spear]],
[51115]=[[Thunder Dragonhorn]],
[51116]=[[Starlight Lance]],
[51117]=[[Dragonbone Shield]],
[51118]=[[Azure Ring]],
[51119]=[[Primal Organ]],
[51120]=[[Starmoon Staff]],
[51121]=[[Prideful Gloves]],
[51122]=[[Starlight Scepter]],
[51123]=[[Jungle Crystal]],
[51124]=[[Willow Sickle]],
[51125]=[[Grass Scepter]],
[51126]=[[Soulwood Horn]],
[51127]=[[Forest Heart]],
[51128]=[[Moth Lamp]],
[51129]=[[Living Cannon]],
[51130]=[[Supernova Radiance]],
[51131]=[[Transcendent Sanctum]],
[51132]=[[Twin Stars]],
[51133]=[[Thunderous Wrath]],
[51134]='Necronomicon',
[51135]=[[Ocean God's Trident]],
[51136]=[[Flames of Destruction]],
[51137]=[[Undying Flame]],
[51138]=[[Darkin Soul]],
[51139]=[[Bloody Edge]],
[51140]=[[Bloody Edge]],
[51141]=[[Heart of Nature]],
[51142]=[[Heart of Nature]],
[51143]=[[Fox Tails]],
[51144]=[[Fox Tails]],
[51145]=[[Bloodthirsty Spear]],
[51146]=[[Bloodthirsty Spear]],
[51147]=[[Death Reaper's Banner]],
[51148]=[[The Evil Spirit, which the Mihia family has worshipped since ancient times, resonates with Mirana, accompanying and guiding her as the future Lord of the Underworld.]],
[51151]=[[Shield & Spear of Judgment]],
[51152]=[[The holy shield and spear of the Valiant Warrior have accompanied her through countless battles. They are said to come from someone of great significance to the Valiant Warrior.]],
[51153]=[[Tome of Souls]],
[51155]=[[Void Origin]],
[51157]=[[Universal Scroll]],
[51159]=[[Royal Serpent Harp]],
[51160]=[[Royal Serpent Harp]],
[51161]=[[Light the Way]],
[51162]=[[Crystal's Soul Lamp guides wandering souls. All desires, strong or weak, melt away before its presence.]],
[51163]=[[Eye of the Forest]],
[51164]=[[The Eye of the Forest is imbued with an abundant reserve of natural energy, granting Irene the ability to protect her allies at all times.]],
[51165]=[[Lunar Bow]],
[51166]=[[Blessed by Luna, the Lunar Bow is the source of Spirit of Ripples' speed and strength.]],
[51167]=[[The Four Guards]],
[51168]=[[The Four Guards are the blessings from the Wilderness, and also the source of Wilderness God's power.]],
[51173]=[[EMP Crossbow]],
[51174]=[[Holy Cross]],
[51175]=[[Thorny Vine]],
[51176]=[[Auspicious Sacred Ganoderma]],
[51177]=[[Black Hole Core]],
[51178]=[[Bloody Claw]],
[51179]=[[Bloody Embrace]],
[51180]=[[Wolfbane Blades]],
[51181]=[[Frost Grip]],
[51182]=[[Ankh Staff]],
[51185]=[[Yale Shield]],
[51186]=[[Imbued with the power of divine protection, Gate Shield stands indestructible.]],
[51187]=[[Hammer of Annihilation]],
[51188]=[[A mighty hammer forged from precious desert ore. It aids Monica in smashing her way to victory.]],
[51195]=[[Rift of Coexistence]],
[51196]=[[The rift between the sky and the abyss is where Isolde transforms, and the source of her strength.]],
[51199]=[[Dragon-Patterned Ring]],
[51200]=[[The ring possesses the immense power of the dragon souls within, granting Porcelina the ability to transform into a mighty dragon.]],
[51201]=[[Contains the elemental power of Yggdrasil. This amulet grants bonus energy.]],
[51202]=[[Created from a fragment of Yggdrasil that fell into a polar icefield, this amulet contains the essence of ice and protects you against enemies with its glacial power.]],
[51203]=[[Created from a fragment of Yggdrasil that fell into a realm of stars, this amulet contains the essence of the stars and allows you to break through all obstacles with the power of the galaxies themselves.]],
[51204]=[[Created from a fragment of Yggdrasil that fell into a realm of lava, this amulet contains the essence of heat and destroys everything that blocks your path with the power of molten fire.]],
[51205]=[[Created from a fragment of Yggdrasil that fell into a space-time fissure, this amulet contains the essence of time and protects you from damage with the power of eternity.]],
[51206]=[[Created from a fragment of Yggdrasil that absorbed the power of the sun, this amulet contains the essence of life and cleanses all evil with the power of the divine.]],
[51207]=[[A headband forged by the Holy Goddess to ward off evil. This artifact brings divine blessings to the devout.]],
[51208]=[[Only the most devout disciples of the Holy Goddess can obtain this ring. It grants one the power to withstand all misfortunes and adversities.]],
[51209]=[[Blessed with the protection of the Holy Goddess, this cloak carries her shadow, which watches over the faithful at all times.]],
[51210]=[[The power of faith in the Holy Goddess grants immense healing.]],
[51211]=[[This artifact has the power to take the purest of hearts to the heavens themselves, where they can meet their god.]],
[51212]=[[The most scared artifact of the Holy Goddess. The dazzling aura surrounding it is powerful enough to turn all enemies into ash.]],
[51214]=[[Bonespikes cover every inch of the abyss devil's body, sending out a chill that can easily pierce through one's very soul.]],
[51215]=[[Devil fangs contain the power to pierce through everything. The edges are made even sharper with every struggling victim.]],
[51216]=[[Even after their demise, the devil's pupil still rests in the heart of Abyss City, gazing upon all warriors who dare seek adventure.]],
[51213]=[[The sharp claws of the abyss devil gathers the souls of the undead and possesses the power to tear everything to shreds.]],
[51217]=[[The hardest scales from the dragon steed of the abyss devil. Capable of resisting all forms of damage.]],
[51218]=[[A long horn from the abyss devil's head. Its acute sense of perception allows one to identify weak spots with ease.]],
[51219]=[[The abyss devil's wings are filled with evil power, giving off a terrifying aura.]],
[51220]=[[This flag was once by the Abyss Devil's army. It possesses almighty inspiring power and has heralded countless victories.]],
[51221]=[[These vambraces equipped by the abyss devil in battles can easily tear enemies asunder.]],
[51222]=[[The heart of the Abyss Devil. It still beats slowly, even after the devil was felled for good.]],
[51223]=[[A weapon once used by the Abyss Devil. Nothing can withstand its devastating charge.]],
[51224]=[[A sword forged from the spine of an abyss devil, granting its wielder destructive power.]],
[51225]=[[Created from a fragment of Yggdrasil that absorbed the power of the moon, this amulet contains the mighty lunar power and empowers attacks.]],
[51226]=[[Created from a fragment of Yggdrasil that fell into the vast sea, this amulet contains the essence of the ocean and empowers you with the power of the deep seas.]],
[51227]=[[The Amulet is formed by the fragments of Yggdrasil scattered within a sprawling mountain range, containing the formidable power of stone. Possessing this amulet allows one to harness the power of the mountains to protect themselves from attack.]],
[51228]=[[Transformed from the shards of Yggdrasil scattered in the abyss, it holds the mighty Abyss Power. Owning this amulet allows you to enhance your CC abilities via the Abyss Power.]],
[51229]=[[Transformed from Yggdrasil shards scattered around the Thunder Realm and contains the almighty Power of Thunder. Anyone who owns this amulet can deal heavy damage to enemies with the Power of Thunder.]],
[51231]=[[The horn, infused with the power of Holy Goddess' faith, can aid righteous warriors in their battle against evil.]],
[51232]=[[An ancient book imbued with the resentful spirit of the Demon King, brimming with a potent curse.]],
[51233]=[[Crafted from the toughest abyssal metal and refined countless times, this shield stands unbreakable even after enduring the test of time.]],
[51238]=[[An artifact that belongs to the Holy Goddess, infused with the power of nature. It is said that only those who are acknowledged by her can truly unleash the power of the staff.]],
[51301]=[[Soul Whip]],
[51302]=[[Primal Relic]],
[51303]=[[Jasper Fang]],
[51304]=[[Doom Hook]],
[51305]=[[Darkspawn Horn]],
[51306]=[[Scepter of Hatred]],
[51307]=[[Terror Hook]],
[51308]=[[Terror Blade]],
[51309]=[[Phantom Hat]],
[51310]=[[Astrological Sphere]],
[51311]=[[Alchemy Pot]],
[51312]=[[Darkin Soul]],
[51313]='Mandolin',
[51314]=[[Dragon Spear]],
[51315]=[[Thunder Dragonhorn]],
[51316]=[[Starlight Lance]],
[51317]=[[Dragonbone Shield]],
[51318]=[[Azure Ring]],
[51319]=[[Primal Organ]],
[51320]=[[Starmoon Staff]],
[51321]=[[Prideful Gloves]],
[51322]=[[Starlight Scepter]],
[51323]=[[Jungle Crystal]],
[51324]=[[Willow Sickle]],
[51325]=[[Grass Scepter]],
[51326]=[[Soulwood Horn]],
[51327]=[[Forest Heart]],
[51328]=[[Moth Lamp]],
[51329]=[[Living Cannon]],
[51330]=[[Supernova Radiance]],
[51331]=[[Transcendent Sanctum]],
[51332]=[[Twin Stars]],
[51333]=[[Thunderous Wrath]],
[51334]='Necronomicon',
[51335]=[[Ocean God's Trident]],
[51336]=[[Flames of Destruction]],
[51337]=[[Undying Flame]],
[51338]=[[Darkin Soul]],
[51369]=[[EMP Crossbow]],
[51370]=[[Holy Cross]],
[51371]=[[Thorny Vine]],
[51372]=[[Auspicious Sacred Ganoderma]],
[51373]=[[Bloody Claw]],
[51374]=[[Bloody Embrace]],
[51375]=[[Wolfbane Blades]],
[51376]=[[Frost Grip]],
[51377]=[[Ankh Staff]],
[51378]=[[Black Hole Core]],
[53301]=[[Moth Soul]],
[53302]=[[Bloodthirsty Sickle]],
[53303]=[[Spiteful Spirit]],
[53304]=[[Pain's Embrace]],
[53305]=[[Shark Blademail]],
[53306]=[[Doom Returns]],
[53307]=[[Serpent's Embrace]],
[53308]=[[Dragonblood Soul]],
[53309]='Resonance',
[53310]=[[Tick Tock]],
[53311]=[[Dangerous Goods]],
[53312]=[[Fortuna's Blessing]],
[53313]=[[Mana Reserves]],
[53314]=[[Shadow Assault]],
[53315]=[[Power Confrontation]],
[53316]=[[Lunar Blessing]],
[53317]=[[Chord Barrier]],
[53318]=[[Rapid Whirl]],
[53319]=[[Defensive Position]],
[53320]=[[Starlight Prayer]],
[53321]=[[Thunderous Rampage]],
[53322]=[[Forest's Protection]],
[53323]=[[Earth Shield]],
[53324]='Restore',
[53325]=[[Rune Blade]],
[53326]=[[Forest Radiance]],
[53327]=[[Dazzling Starlight]],
[53328]=[[Nebula Cross]],
[53329]=[[Nebula Dispersal]],
[53330]=[[Ocean God]],
[53331]=[[Underworld Lord]],
[53332]=[[Primal Seer]],
[53333]='Thundershake',
[53334]=[[Otherworld Shadows]],
[53335]=[[Living Armor]],
[53336]=[[Hellfire Scourge]],
[53337]=[[Phoenix Blood]],
[53338]=[[Flame Mastery]],
[53339]=[[Blood Desire]],
[53340]=[[Soul Link]],
[53341]=[[Blazing Fox Fire]],
[53342]=[[Bloodthirsty Heart]],
[53345]=[[War Order]],
[53346]=[[Soul Explosion]],
[53347]=[[Draw Void Power]],
[53348]=[[Four Symbols]],
[53349]=[[Snake Rhythm]],
[53350]='Guide',
[53351]=[[Forest's Protection]],
[53352]=[[Piercing Moon]],
[53353]=[[Barren Rock Mark]],
[53357]=[[Divine Protection]],
[53358]=[[Battle Fervor]],
[53362]=[[Rift of Light and Shadow]],
[53364]=[[Unwavering Spirit]],
[53371]=[[Hunting Hour]],
[53372]=[[Sacred Faith]],
[53373]=[[Rapid Growth]],
[53374]='Immortality',
[53375]=[[Concentrate Energy]],
[53376]=[[Eternal Curse]],
[53377]=[[Scarlet Covenant]],
[53378]=[[Wolfsoul Totem]],
[53379]=[[Sheep Spirit]],
[53380]=[[Styxian Rebirth]],
[53401]=[[Combo Slash]],
[53402]=[[Piercing Strike]],
[53403]=[[Thick Armor]],
[53404]=[[Tenacious Armor]],
[53405]=[[Unyielding Will]],
[53406]=[[Fatal Blow]],
[53407]=[[Giant Killer]],
[53408]=[[Weak Point]],
[53409]=[[Vital Armor]],
[53410]=[[Selfless Protection]],
[53411]=[[Precision Strike]],
[53412]=[[Shadow Assault]],
[53413]=[[Esper Shield]],
[53414]=[[Battle Knowledge]],
[53415]=[[Ode of Death]],
[53416]=[[Excess Heal]],
[53417]=[[Wraithful Curse]],
[53418]='Cathexis',
[53419]='Perseverance',
[53420]=[[Violent Recovery]],
[53421]='Overpower',
[53422]='Shieldbreaker',
[53423]=[[Courageous Barrier]],
[53424]=[[Tactical Shield]],
[53425]=[[Potent Revival]],
[53426]=[[Soul Gaze]],
[53427]=[[Rapid Circulation]],
[53428]=[[Soul Mastery]],
[53429]=[[Violent Smash]],
[53430]='Rampart',
[53901]='Fearlessness',
[53902]=[[Battle Roar]],
[53903]=[[Magic Aggregation]],
[53904]='Charge',
[53905]=[[Firsthand Assassin]],
[53906]=[[Wind of Rebirth]],
[55500]='CP',
[55501]='HP',
[55502]='ATK',
[55503]='Armor',
[55504]='Speed',
[55505]='CRIT',
[55506]=[[CRIT DMG]],
[55507]=[[Debuff Hit]],
[55508]=[[Debuff RES]],
[55509]='Hit',
[55510]='Dodge',
[55511]='Accuracy',
[55512]='Block',
[55513]=[[Armor PEN]],
[55514]=[[DMG Reduction]],
[55515]='HP',
[55516]='ATK',
[55517]='Armor',
[55518]='Speed',
[55519]=[[Skill DMG]],
[55520]='Energy',
[55521]=[[Holy DMG]],
[55522]='HP',
[55523]='ATK',
[55524]='Armor',
[55525]='Speed',
[55526]='MP',
[55527]=[[DMG to Warriors]],
[55528]=[[DMG to Mages]],
[55529]=[[DMG to Hunters]],
[55530]=[[DMG to Assassins]],
[55531]=[[DMG to Priests]],
[55532]='HP',
[55533]='ATK',
[55534]='Armor',
[55535]='Speed',
[55536]=[[Crit DMG Reduction]],
[55537]=[[Damage to the dark system]],
[55538]=[[Harmful to humans]],
[55539]=[[Damage to orcs]],
[55540]=[[Damage to forests]],
[55541]=[[Damage to void system]],
[55542]=[[Damage to gods]],
[55543]=[[Faction restraint damage]],
[55544]=[[Faction restraint damage]],
[55545]=[[CRIT RES]],
[55546]=[[CRIT DMG RES]],
[55547]=[[Armor combat power bonus]],
[55548]=[[Attack power bonus]],
[55699]=[[place reservation]],
[58001]='Hide',
[58002]='Afghanistan',
[58003]='Armenia',
[58004]='Azerbaijan',
[58005]='Bahrain',
[58006]='Bangladesh',
[58007]='Bhutan',
[58008]='Brunei',
[58009]='Cambodia',
[58010]='China',
[58011]='Cyprus',
[58012]='Georgia',
[58013]='India',
[58014]='Indonesia',
[58015]='Iran',
[58016]='Iraq',
[58017]='Israel',
[58018]='Japan',
[58019]='Jordan',
[58020]='Kazakhstan',
[58021]=[[North Korea]],
[58022]=[[South Korea]],
[58023]='Kuwait',
[58024]='Kyrgyzstan',
[58025]='Laos',
[58026]='Lebanon',
[58027]='Malaysia',
[58028]='Maldives',
[58029]='Mongolia',
[58030]='Myanmar',
[58031]='Nepal',
[58032]='Oman',
[58033]='Pakistan',
[58034]='Palestine',
[58035]='Philippines',
[58036]='Qatar',
[58037]=[[Saudi Arabia]],
[58038]='Singapore',
[58039]=[[Sri Lanka]],
[58040]='Syria',
[58041]='Tajikistan',
[58042]='Thailand',
[58043]='Timor-Leste',
[58044]='Turkey',
[58045]='Turkmenistan',
[58046]=[[The United Arab Emirates]],
[58047]='Uzbekistan',
[58048]='Vietnam',
[58049]='Yemen',
[58050]='Australia',
[58051]='Fiji',
[58052]='Kiribati',
[58053]=[[Marshall Islands]],
[58054]=[[Federated States of Micronesia]],
[58055]='Nauru',
[58056]=[[New Zealand]],
[58057]='Palau',
[58058]=[[Papua New Guinea]],
[58059]='Samoa',
[58060]=[[Solomon Islands]],
[58061]='Tonga',
[58062]='Tuvalu',
[58063]='Vanuatu',
[58064]='Argentina',
[58065]='Bolivia',
[58066]='Brazil',
[58067]='Chile',
[58068]='Colombia',
[58069]='Ecuador',
[58070]='Guyana',
[58071]='Paraguay',
[58072]='Peru',
[58073]='Suriname',
[58074]='Uruguay',
[58075]='Venezuela',
[58076]=[[Antigua and Barbuda ]],
[58077]=[[The Bahamas]],
[58078]='Barbados',
[58079]=[[Belize ]],
[58080]='Canada',
[58081]=[[Costa Rica]],
[58082]='Cuba',
[58083]='Dominica',
[58084]=[[Dominican Republic]],
[58085]='Grenada',
[58086]='Guatemala',
[58087]='Haiti',
[58088]='Honduras',
[58089]='Jamaica',
[58090]='Mexico',
[58091]='Nicaragua',
[58092]='Panama',
[58093]=[[Saint Lucia ]],
[58094]=[[Saint Vincent and the Grenadines]],
[58095]=[[Trinidad and Tobago ]],
[58096]='U.S.A.',
[58097]='Albania',
[58098]='Andorra',
[58099]='Austria',
[58100]='Belarus',
[58101]='Belgium',
[58102]='BiH',
[58103]='Bulgaria',
[58104]='Croatia',
[58105]=[[Czech Republic]],
[58106]='Denmark',
[58107]='Estonia',
[58108]='Finland',
[58109]='France',
[58110]='Germany',
[58111]='Greece',
[58112]='Hungary',
[58113]='Iceland',
[58114]='Ireland',
[58115]='Italy',
[58116]='Latvia',
[58117]='Liechtenstein',
[58118]='Lithuania',
[58119]='Luxembourg',
[58120]=[[North Macedonia]],
[58121]='Malta',
[58122]='Moldova',
[58123]='Monaco',
[58124]=[[Montenegro ]],
[58125]='Netherlands',
[58126]='Norway',
[58127]='Poland',
[58128]='Portugal',
[58129]='Romania',
[58130]='Russia',
[58131]=[[San Marino]],
[58132]='Serbia',
[58133]='Slovakia',
[58134]='Slovenia',
[58135]='Spain',
[58136]='Sweden',
[58137]='Switzerland',
[58138]='Ukraine',
[58139]='Britain',
[58140]=[[Vatican City]],
[58141]='Algeria',
[58142]='Angola',
[58143]='Benin',
[58144]='Botswana',
[58145]=[[Burkina faso ]],
[58146]='Burundi',
[58147]='Cameroon',
[58148]=[[Cape Verde]],
[58149]=[[Central African Republic]],
[58150]='Chad',
[58151]='Comoros',
[58152]=[[Democratic Republic of the Congo]],
[58153]=[[Republic of the Congo]],
[58154]=[[Côte d'Ivoire]],
[58155]='Djibouti',
[58156]='Egypt',
[58157]=[[Equatorial Guinea]],
[58158]='Eritrea',
[58159]='Ethiopia',
[58160]='Gabon',
[58161]=[[The Gambia]],
[58162]='Ghana',
[58163]='Guinea',
[58164]='Guinea-Bissau',
[58165]='Kenya',
[58166]='Lesotho',
[58167]='Liberia',
[58168]='Libya',
[58169]='Madagascar',
[58170]='Malawi',
[58171]='Mali',
[58172]='Mauritania',
[58173]='Mauritius',
[58174]='Morocco',
[58175]='Mozambique',
[58176]='Namibia',
[58177]='Niger',
[58178]='Nigeria',
[58179]='Rwanda',
[58180]=[[Sao Tome and Principe]],
[58181]='Senegal',
[58182]='Seychelles',
[58183]=[[Sierra Leone]],
[58184]='Somalia',
[58185]=[[South Africa]],
[58186]='Sudan',
[58187]='Eswatini',
[58188]='Tanzania',
[58189]='Togo',
[58190]='Tunisia',
[58191]='Uganda',
[58192]='Zambia',
[58193]='Zimbabwe',
[58194]=[[Anguilla ]],
[58195]='Aruba',
[58196]=[[Cayman Islands]],
[58197]='Bermuda',
[58198]=[[French Polynesia]],
[58199]=[[Norfolk Island]],
[58200]='Åland',
[58201]='Gibraltar',
[58202]=[[Saint Kitts and Nevis]],
[58203]=[[El Salvador]],
[58204]=[[Puerto Rico]],
[58205]='Scotland',
[58206]=[[Turks and Caicos Islands ]],
[58207]=[[United States Virgin Islands]],
[58208]=[[Faroe Islands]],
[58209]=[[Wallis and Futuna]],
[58210]=[[British Virgin Islands]],
[58211]=[[Falkland Islands]],
[58212]='Wales',
[58213]=[[Pitcairn Islands]],
[58214]='England',
[58215]=[[European Union]],
[58216]=[[Olympic Games]],
[58217]=[[United Nations]],
[58301]=[[Select Country/Region]],
[58302]='Country/Region',
[58303]=[[The country/region is currently selected]],
[58304]='banner',
[58401]=[[South Korea]],
[58402]=[[Asia Server]],
[60001]='Star',
[60002]='Rankup',
[60003]=[[%s successful!]],
[60004]=[[No qualified heroes from the same faction]],
[60005]='Tips',
[60006]=[[Are you sure you want to descend this hero?]],
[60007]=[[Are you sure you want to substitute this hero?]],
[60008]=[[Insufficient materials. Cannot substitute!]],
[60009]=[[Insufficient materials. Cannot descend!]],
[60010]=[[Cannot substitute the same hero!]],
[60011]=[[Hero obtained]],
[60012]=[[Max Level]],
[60013]=[[You cannot descend as your hero capacity is full!]],
[60014]='Tier',
[60015]='Unequip',
[60016]=[[Quick Equip]],
[60017]=[[Set Stats]],
[60018]=[[<color=#ffffff>Insufficient Rankup Stones. Purchase more from the Giant Merchant or obtain them from the Proving Grounds.</color>]],
[60019]='MP',
[60020]='Level',
[60021]='Sort',
[60022]=[[Upgrade to Lv. %d to unlock.]],
[60023]=[[Hero Awakening]],
[60024]=[[Hero Material]],
[60025]=[[Unlocks at Lv. %s]],
[60026]=[[Quick Equip]],
[60027]=[[Equip the selected set first:]],
[60028]='Assemble',
[60029]='Substitute',
[60030]=[[10-Star Substitution]],
[60031]=[[Hero Descension]],
[60032]='Descend',
[60033]=[[Spend %d Diamonds to increase Hero Capacity by %d.]],
[60034]=[[Tap and hold the Level Up button to continuously level up your hero!]],
[60035]='Hero',
[60036]='Skills',
[60037]=[[Hero Roster]],
[60038]=[[There are no skills to equip!]],
[60039]=[[Mythic Heroes]],
[60040]=[[Legendary Heroes]],
[60041]=[[Common Heroes]],
[60042]='Rare',
[60043]='Rare+',
[60044]='Excellent',
[60045]='Epic',
[60046]='Epic+',
[60047]='Legendary',
[60048]='Legendary+',
[60049]='Mythic',
[60050]=[[Enhancement Level]],
[60051]='Nightfall',
[60052]='Human',
[60053]='Beastkin',
[60054]='Forest',
[60055]='Mythic+',
[60056]='Transcendent',
[60057]='Transcendent+',
[60058]='Transcendent++',
[60059]='Eternal',
[60100]=[[Hero Rankup]],
[60101]=[[Enhance Max Level to:]],
[60102]=[[Max Level:]],
[60103]='HP:',
[60104]='ATK:',
[60105]='Armor:',
[60106]='Speed:',
[60107]=[[New skill acquired.]],
[60108]=[[Upgrade Skill]],
[60109]=[[Tap to continue.]],
[60110]='Common',
[60111]=[[Tap to continue (%ds).]],
[60112]='Excellent+',
[70002]=[[Insufficient level-up materials. Purchase more in the store.]],
[70003]=[[Max Level]],
[70004]=[[No skills available.]],
[73361]=[[You must form a lineup for Team 1.]],
[73362]=[[You must form a lineup for Team 2.]],
[73363]=[[You must form a lineup for Team 3.]],
[73364]=[[You must form a team lineup.]],
[73370]=[[This hero is already in another team]],
[80001]=[[Arena Celebration]],
[80002]=[[During the event, 2 points are granted for each victory in the Daily Arena or Arena Weekly Battle, while 1 point is granted for each defeat. Rewards will be sent via mail.]],
[80005]=[[Exchange successful!]],
[80006]=[[Tavern Celebration]],
[80007]=[[Complete the required number of Acorn Tavern quests during the event to win the following rewards. The rewards will be sent to you via mail.]],
[80008]=[[Hero Awakening]],
[80009]=[[Obtain heroes of the designated star level during the event to win rewards. The rewards will be sent via mail.]],
[80010]=[[Consume the required type and number of heroes from the designated faction to obtain Elite heroes and gear of the same faction.]],
[80011]=[[Consume the required type and number of heroes from the designated faction to obtain Elite heroes of the same faction.]],
[80012]=[[Event Pack]],
[80013]=[[Joker's Blessings]],
[80014]=[[During the event, players can earn 1 point each time they use Common Chips to draw in the Casino. The rewards will be sent via mail.]],
[80015]=[[Faction Summon Event]],
[80016]=[[During the event, players can earn 1 point each time they use Excellent Faction Scrolls to perform summons. The rewards will be sent via mail.]],
[80017]=[[Scroll Mark]],
[80018]=[[During the event, players can earn 1 point each time they perform a Hero Summon. The rewards will be sent via mail.]],
[80019]=[[Hero Rally]],
[80020]=[[Players can earn rewards by completing the following achievements. The rewards will be sent via mail.]],
[80021]=[[The Gray Gnome's Blessing]],
[80022]=[[During the event, gear above 4-Star can be upgraded to higher-level sets, while 6-Star gear can be upgraded to exclusive class sets (only 1 upgrade is allowed).]],
[80023]=[[You must provide the corresponding hero materials.]],
[80024]=[[Obtain 50 points.]],
[80025]=[[Obtain 100 points.]],
[80026]=[[Obtain 200 points.]],
[80027]=[[Novice Scroll Mark]],
[80029]=[[Complete a 4-Star Acorn Tavern quest.]],
[80030]=[[Complete a 5-Star Acorn Tavern quest.]],
[80032]=[[Complete a 6-Star Acorn Tavern quest.]],
[80033]=[[Complete a 7-Star Acorn Tavern quest.]],
[80034]=[[Complete all Acorn Tavern quests.]],
[80035]=[[Obtain 50 points in Daily Arena.]],
[80036]=[[Obtain 100 points in Daily Arena.]],
[80037]=[[Obtain 150 points in the Daily Arena.]],
[80038]=[[Obtain 200 points in the Daily Arena.]],
[80039]=[[Obtain 50 points in an Arena Weekly Battle.]],
[80040]=[[Obtain 100 points in an Arena Weekly Battle.]],
[80041]=[[Obtain 150 points in an Arena Weekly Battle.]],
[80042]=[[Obtain 200 points in an Arena Weekly Battle.]],
[80043]=[[Complete all quests.]],
[80044]=[[Awaken an Excellent hero 1 time.]],
[80045]=[[Awaken an Epic hero 1 time.]],
[80046]=[[Awaken a Legendary+ hero 1 time.]],
[80047]=[[Awaken a Mythic hero 1 time.]],
[80048]=[[Obtain 300 points.]],
[80049]=[[Obtain 300 points.]],
[80050]=[[Obtain 400 points.]],
[80051]=[[Obtain 500 points.]],
[80052]=[[Perform Faction Summon 10 times.]],
[80053]=[[Perform Faction Summon 20 times.]],
[80054]=[[Perform Faction Summon 30 times.]],
[80055]=[[Perform Faction Summon 40 times.]],
[80056]=[[Perform Faction Summon 50 times.]],
[80057]=[[Perform Faction Summon 60 times.]],
[80058]=[[Perform Faction Summon 80 times.]],
[80059]=[[Obtain 50 points.]],
[80060]=[[Obtain 100 points.]],
[80061]=[[Obtain 200 points.]],
[80062]=[[Obtain 300 points.]],
[80063]=[[Obtain 400 points.]],
[80064]=[[Obtain 500 points.]],
[80065]=[[Obtain 450 points.]],
[80066]=[[Obtain 2 God Excellent heroes.]],
[80067]=[[Obtain 2 Voider Excellent heroes.]],
[80068]=[[Obtain 3 Forest Excellent heroes.]],
[80069]=[[Obtain 3 Beastkin Excellent heroes.]],
[80070]=[[Obtain 3 Human Excellent heroes.]],
[80071]=[[Obtain 3 Nightfall Excellent heroes.]],
[80072]=[[Complete all quests.]],
[80085]=[[Exchange Pack]],
[80086]=[[Hunting Battle]],
[80087]=[[Elite Exchange]],
[80088]=[[Skill Exchange]],
[80089]=[[Sacred Gear Fusion]],
[80090]=[[Elite Exchange Rules]],
[80091]=[[The Gray Gnome's Blessing Rules]],
[80092]=[[Skill Substitution Rules]],
[80093]=[[T10 gear can be fused into T11 gear, which can be fused into T12 gear.]],
[80094]=[[You must provide the designated hero materials.]],
[80095]=[[You must provide the designated T5 Nucleus Crystals.]],
[80096]=[[Obtain 500 points.]],
[80097]=[[Obtain 550 points.]],
[80098]=[[Obtain 600 points.]],
[80099]=[[Hero Rebirth]],
[80100]=[[Ruins Chest]],
[80101]=[[Sacred Gear Fusion]],
[80102]=[[Monthly Pack]],
[80103]=[[Weekly Pack]],
[80104]=[[4 T6 Nucleus Crystals can be smelted into 50 T7 Nucleus Crystal Shards.]],
[80105]=[[The Alchemy Store will have 2 Elite heroes available for purchase.]],
[80106]=[[Consume designated heroes to acquire corresponding skills.]],
[80107]=[[Summon Pack]],
[80108]=[[Earn corresponding rewards by obtaining designated Excellent heroes. The rewards can be claimed repeatedly.]],
[80109]=[[Ends in ]],
[80110]=[[Weekly Events]],
[80111]=[[Monthly Events]],
[80112]=[[Exchange Code]],
[80113]=[[You must enter an exchange code.]],
[80114]='Permanent',
[80115]=[[LvUP Gift Rewards]],
[80116]=[[Lv. 10]],
[80117]=[[Lv. 20]],
[80118]=[[Lv. 30]],
[80119]=[[Lv. 40]],
[80120]=[[Lv. 50]],
[80121]=[[Lv. 60]],
[80122]=[[Lv. 70]],
[80123]=[[Lv. 80]],
[80124]='Exchange',
[80125]=[[Elite Exchange]],
[80126]=[[You must provide the designated materials.]],
[80127]=[[Nucleus Crystal Material]],
[80128]=[[No Nucleus Crystal material found.]],
[80129]=[[T Nucleus Crystal]],
[80130]=[[Perform Faction Summons to Earn Points]],
[80131]=[[Perform Hero Summons to Earn Points]],
[80132]=[[Earn points in the Casino.]],
[80133]=[[Summon designated heroes.]],
[80134]=[[Elite Excellent Hero Exchange]],
[80135]=[[Purchase chests with Star Diamonds.]],
[80136]=[[Obtain T7 Nucleus Crystal shards.]],
[80137]=[[Exchange for Designated Skill]],
[80138]=[[Exchange for Advanced Gear]],
[80139]=[[Elite Excellent heroes are available in the Alchemy Store for a limited time.]],
[80140]=[[Earn points in the Arena.]],
[80141]=[[Complete Tavern Quests to earn points.]],
[80142]=[[Awaken and Ascend heroes to earn rewards.]],
[80143]=[[Attack the Domination to earn rewards.]],
[80144]=[[Summon designated heroes to earn rewards.]],
[80145]=[[Level Fund]],
[80146]=[[Total Recharge Gift]],
[80147]=[[Clear Chapter 2 to claim.]],
[80148]=[[Clear Chapter 5 to claim.]],
[80149]=[[Clear Chapter 7 to claim.]],
[80150]=[[Clear Chapter 9 to claim.]],
[80151]=[[Clear Chapter 10 to claim.]],
[80152]=[[Clear Chapter 11 to claim.]],
[80153]=[[Clear Stage 12-20 to claim.]],
[80154]=[[Clear Chapter 12 to claim.]],
[80155]=[[Clear Stage 13-20 to claim.]],
[80156]=[[Clear Chapter 13 to claim.]],
[80157]=[[Clear Stage 14-20 to claim.]],
[80158]=[[Clear Chapter 14 to claim.]],
[80159]=[[Accumulate %s VIP EXP during the event]],
[80160]=[[Reach a total recharge of $13.50.]],
[80161]=[[Recharge $27 in total.]],
[80162]=[[Recharge $46 in total.]],
[80163]=[[Recharge $90 in total.]],
[80164]=[[Recharge $180 in total.]],
[80165]='Selected',
[80166]='Enemy',
[80167]=[[Enter the quantity of Proving Grounds Tokens.]],
[80168]=[[Substitution failed.]],
[80169]=[[You have recharged $460.]],
[80170]=[[Recharge $966 in total.]],
[80171]=[[Recharge Event]],
[80172]=[[Purchase to claim]],
[80173]='Benefits',
[80174]=[[Are you sure you want to substitute?]],
[80177]=[[Summoning Hall — Hero Summon]],
[80178]=[[Perform 15 Hero Summons.]],
[80179]=[[Perform 30 Hero Summons.]],
[80180]=[[Perform 50 Hero Summons.]],
[80181]=[[Perform 75 Hero Summons.]],
[80182]=[[Perform 100 Hero Summons.]],
[80183]=[[Perform 150 Hero Summons.]],
[80184]=[[Chest Preview]],
[80185]=[[Below Lv. %d]],
[80186]=[[Perform 200 Hero Summons.]],
[80187]=[[Lv. 85]],
[80188]=[[Lv. 90]],
[80189]=[[Event Preview]],
[80190]=[[The following events will open on Friday:]],
[80191]=[[Perform 300 Hero Summons.]],
[80192]=[[Perform 400 Hero Summons.]],
[80193]=[[Perform 500 Hero Summons.]],
[80194]=[[Halloween Event]],
[80195]=[[Haunted Carnival Nightfall Hero Limited Exchange]],
[80300]=[[Monthly Card]],
[80301]='Pack',
[80302]=[[Growth Pack]],
[80303]=[[Daily Pack]],
[80304]=[[Daily Value Pack]],
[80305]=[[Monthly Pack]],
[80306]=[[Weekly Pack]],
[80310]=[[Purchase Rewards]],
[80311]=[[Log in every day to claim]],
[80312]=[[Total Rewards]],
[80313]=[[Refreshes after %s.]],
[80314]=[[Purchase packs and clear designated chapters to get abundant Diamonds, Summon Scrolls, and 1000 VIP EXP.]],
[80315]='Inactive',
[80316]=[[Daily Pack <color=#ee3135>(Recommended)</color>]],
[80317]=[[Weekly Pack <color=#ee3135>(Recommended)</color>]],
[80318]=[[Monthly Pack <color=#ee3135>(Recommended)</color>]],
[80319]=[[Monthly Card <color=#ee3135>(Recommended)</color>]],
[80320]=[[Growth Pack <color=#ee3135>(Recommended)</color>]],
[80321]=[[Super Value Pack <color=#ee3135>(Recommended)</color>]],
[80322]=[[Diamond Pack]],
[80323]=[[First recharge grants 2x Diamonds]],
[80324]=[[Limited Offer <color=#ee3135>(Event Exclusive)</color>]],
[80325]=[[Limited Summon not available yet. Coming soon!]],
[80326]=[[Hero Order <color=#ee3135>(Event Exclusive)</color>]],
[80327]=[[Total Rewards: %d]],
[80500]=[[1st Recharge Pack]],
[80501]=[[Super Value Pack]],
[80502]=[[Consumption Gift]],
[80503]=[[Game Perks]],
[80504]=[[40x Rebate]],
[80505]=[[Purchase Level Fund]],
[80506]=[[Total value of rewards: <color=#FEEA43FF>40000</color>]],
[80507]=[[Recharge the designated amount]],
[80508]=[[Get Exclusive Gear of Excellent Elite Heroes]],
[80509]=[[%d diamonds in total]],
[80510]=[[Total: (%s)]],
[80511]='9',
[80512]='10',
[80513]='11',
[80514]='12',
[80515]='13',
[80516]=[[Deluxe Monthly Card]],
[80517]=[[Purchase and Claim]],
[80518]=[[Obtain every day]],
[80519]=[[750 Diamonds and 750 VIP EXP]],
[80520]=[[375 Diamonds]],
[80521]=[[12000 Diamonds]],
[80522]=[[Common Monthly Card]],
[80523]=[[250 Diamonds and 250 VIP EXP]],
[80524]=[[85 Diamonds]],
[80525]=[[2800 Diamonds]],
[80526]=[[Spend Now]],
[80527]=[[Novice Premium Pack]],
[80528]=[[Novice Deluxe Pack]],
[80529]=[[(S+) Epic Hero Pack]],
[80530]=[[(S+) Super Deluxe Pack]],
[80531]=[[Novice Incentive Pack]],
[80532]=[[Resource Pack 2]],
[80533]=[[Resource Pack 3]],
[80534]=[[Resource Pack 4]],
[80535]=[[Recharge Now]],
[80536]=[[Total Rewards:]],
[80537]=[[Cannot claim rewards as you haven't purchased the Growth Pack!
Purchase the Growth Pack to get all rewards of cleared chapters.]],
[80538]=[[Buy Now]],
[80539]=[[Super Value: 1000%]],
[80540]=[[Super Value: 1300%]],
[80541]='Claimed',
[80542]=[[You must enter a valid exchange code.]],
[80543]=[[Growth Pack]],
[80544]=[[Available Heroes]],
[80545]=[[Online Gift]],
[80546]=[[Obtain the required number of heroes form the same faction during the event to win rewards. The rewards will be sent via mail.]],
[80547]=[[Assembly of The Brave]],
[80548]=[[Send the designated amount of flowers to earn rewards.]],
[80549]=[[Diamond Rebate]],
[80550]=[[Spend diamonds to earn bonus rewards.]],
[80632]=[[Players can earn 1 point each time they use Gold Chips to attempt a Heartbeat Roulette lucky draw. Rewards can be obtained at specific point milestones.]],
[80633]=[[Current Round: %d/4]],
[80634]=[[Total Points: %d]],
[80635]=[[Obtain %d points.]],
[80636]=[[Ruins Chest]],
[80637]=[[Players can purchase Ruins Chests with Diamonds during the event.]],
[80638]=[[Diamond Rebate]],
[80639]=[[Players can obtain Gold Chips by reaching specific Diamond consumption milestones. The daily quest chests will also give out additional Gold Chips.]],
[80640]=[[Total Cost: %d]],
[80641]=[[Cost: %d Diamonds]],
[80642]=[[Event Pack]],
[80643]=[[Value Pack that contains Diamonds, Golden Chips, and Excellent Faction Scrolls.]],
[80644]=[[Login Gift]],
[80645]=[[Log in during the event to obtain a new login gift each day.]],
[80646]=[[Esper Badge]],
[80647]=[[During the event, players can obtain additional rewards by collecting badges.]],
[80648]=[[During the event, players can obtain additional rewards by collecting badges.]],
[80649]=[[Badge Store]],
[80650]=[[During the event, players can exchange badges for rewards in the Badge Store.]],
[80651]=[[During the event, players can exchange badges for rewards in the Badge Store.]],
[80652]=[[Total Badges: %d]],
[80653]=[[Obtain %d Badges in total.]],
[80654]=[[Event Pack]],
[80655]=[[Value Pack that contains Diamonds and Esper Badges.]],
[80656]=[[Elite Exchange]],
[80657]=[[During the event, players can spend the required number of Heroes, Gold, and Diamonds to complete Elite Exchange quests and obtain lucrative rewards.]],
[80658]=[[Event Pack]],
[80659]=[[Value Pack that contains Diamonds, Common Scrolls, and Excellent Faction Scrolls.]],
[80660]=[[Login Gift]],
[80661]=[[Log in every day to claim abundant rewards!]],
[80662]=[[Hero Rebirth]],
[80663]=[[You can exchange for the 2 heroes in the Dismissal Store during the event.]],
[80664]=[[Events available on Fridays will be available for preview from Monday.]],
[80665]=[[You can exchange for heroes in the Dismissal Store.]],
[80805]=[[Payment failed. Contact customer service.]],
[80806]=[[Cancel and Re-Order]],
[80807]=[[Special Offer]],
[80808]=[[Extra Benefits]],
[80900]=[[Do you like X-Hero?]],
[80901]=[[I do.]],
[80902]=[[It still needs to be improved.]],
[80903]=[[Ask me later]],
[80904]=[[Would you like to give X-Hero a rating?]],
[80905]='Yes.',
[80906]='No.',
[80907]=[[Thanks for your rating!]],
[80908]=[[You've been issued %s Diamonds as thanks!]],
[80909]=[[Thanks for your feedback!]],
[80910]=[[Rate now to get amazing rewards.]],
[80911]=[[%s diamonds are waiting for you!]],
[80912]=[[Your rating is very important to us.]],
[80913]=[[Rate now to get amazing rewards.]],
[80914]=[[We will keep improving the game to deliver you a better experience, so stay tuned!]],
[80915]='Close',
[80916]=[[Do you like Hero Clash?]],
[80917]=[[Rate later]],
[80918]=[[Rate us on the App Store!]],
[80930]=[[Total recharge reaches]],
[80931]=[[Total VIP EXP from recharges during the event: %s]],
[80932]=[[Are you sure you want to close the game?]],
[80933]=[[Ultimate
Artifact]],
[80934]='Guaranteed',
[80935]='Heroes',
[80936]=[[Claimed today]],
[80937]=[[Daily Pack]],
[80938]=[[This Artifact had already been obtained and cannot be exchanged again.]],
[80939]=[[Do you like X-Clash?]],
[80940]=[[Tap the star to rate us on the App Store.]],
[80941]=[[Leave us a rating!]],
[80942]=[[Not now]],
[80943]=[[Your comment is public and will display your Google profile name and photo.]],
[80944]='X-Clash',
[80950]=[[Faction Scroll*4 Pack]],
[80951]=[[Contains Faction Scrolls and abundant Gold.]],
[80952]=[[Awaken any hero to Epic 1 time.]],
[80953]=[[Awaken any hero to Legendary 1 time.]],
[80954]=[[Perform 20 Arena challenges.]],
[80955]=[[Spend 50 Stamina in the Garden.]],
[80956]=[[Perform 10 Summons in the Summoning Hall.]],
[80957]=[[Perform 10 Faction Summons in the Summoning Hall.]],
[80958]=[[Perform Quick AFK 5 times.]],
[80959]=[[Accept 10 quests in the Acorn Tavern.]],
[80960]=[[Complete 5 Ancient Summon challenges.]],
[80961]=[[Decorate the Christmas Tree]],
[80962]=[[Christmas Stocking Presents]],
[80963]=[[Jungle Ruins]],
[80964]=[[Christmas Quests]],
[80965]=[[Christmas Pack]],
[80966]=[[Current Progress: %d]],
[80967]='Decorate',
[80968]=[[Insufficient Candies.]],
[80969]=[[During the Christmas event, use Candy to increase your decoration progress and receive random rewards. You will also receive respective phase rewards upon completion.]],
[80970]=[[During the event, you can unlock 1 Christmas Stocking each day. Accumulate a total of 14 Christmas Stockings rewards through purchases]],
[80971]=[[Rewards for a Common and a Deluxe Christmas Stockings are shown below. Purchase to unlock them.]],
[80972]=[[Daily Rewards]],
[80973]='Deluxe',
[80974]=[[<color=#dd3c2dFF>Total Christmas Stockings that can be obtained</color>]],
[80975]=[[Total Deluxe Christmas Stockings that can be obtained]],
[80976]=[[Christmas Stockings and Deluxe Christmas Stockings can be purchased at the same time.]],
[80977]=[[Jungle Ruins %dF]],
[80978]=[[Floor Ultimate Treasure]],
[80979]=[[Rewards Preview]],
[80980]=[[God Key obtained.]],
[80981]='Owned:',
[80982]=[[Tap on the titles to look for treasure.]],
[80983]=[[Insufficient God Keys.]],
[80984]=[[Complete quests during the event to obtain God Keys, Christmas Candy, and Christmas Cards. Each quest can be completed multiple times.]],
[80985]=[[Value Packs that contain Christmas Candy and God Keys will be available.]],
[80986]=[[Phase Rewards]],
[80987]=[[Decoration Rewards]],
[80988]=[[Ultimate Treasure]],
[80989]=[[Claim x%d]],
[80990]=[[Available Next Day]],
[80991]='Claimed',
[80992]='Purchased',
[80993]=[[Reach Jungle Ruins %dF to unlock]],
[80994]=[[Claim 2h of AFK Rewards]],
[80995]=[[Tap and hold to use automatically.]],
[80996]=[[Spend 20 Stamina in the Garden.]],
[80997]=[[Perform 10 Arena challenges.]],
[80998]=[[Floor Cost:]],
[80999]=[[Time-Limited Diamond Purchase Pack]],
[81000]=[[Spirit Chest*50 Pack]],
[81001]=[[Includes 50 Deity Chests used to enhance Artifacts and a large amount of Gold.]],
[81002]=[[Super Value]],
[81003]=[[Value Fund]],
[81004]=[[Day %d]],
[81005]=[[Deluxe Monthly Card User Exclusive]],
[81006]=[[You must activate the Deluxe Monthly Card first.]],
[81007]=[[25x Rebate]],
[81008]='25000',
[81009]=[[Deluxe Fund]],
[81010]=[[28x Rebate]],
[81011]='70000',
[81012]=[[Thanksgiving Store]],
[81013]=[[Witch's Adventure]],
[81014]=[[Thanksgiving Pack]],
[81015]=[[Thanksgiving Collection]],
[81016]=[[During the Thanksgiving Event, Travelers can exchange Pumpkin Pies for rewards.]],
[81017]=[[Travelers can use the dice to control how the witch moves. Collect stars to obtain a huge amount of rewards!]],
[81018]=[[Dice Packs and Pumpkin Pie Packs will be available for purchase.]],
[81019]=[[Obtained: 
%d/%d]],
[81020]=[[Rewards: Round %d]],
[81021]=[[Skip ]],
[81022]=[[Purchase Dice]],
[81023]=[[Normal Dice 
x%d]],
[81024]=[[Lucky Dice 
x%d]],
[81025]=[[Star Rewards]],
[81026]=[[Stars: %d]],
[81027]=[[Lucky Card]],
[81028]=[[Star Card]],
[81029]=[[Storm Card]],
[81030]=[[Energy Card]],
[81031]=[[You receive a blessing from the Goddess of Luck. The value of your dice will be multiplied by 2 for your next roll.]],
[81032]=[[When you land on or pass a Star Tile, you will receive double the amount of Stars!]],
[81033]=[[The rewards for your next roll will be devoured by stormy clouds.]],
[81034]=[[Tada! Congratulations on obtaining 1 Resource Chest!]],
[81035]=[[Limit: %d/%d]],
[81036]=[[Lucky Dice]],
[81037]=[[Select a value for the dice roll!]],
[81038]=[[Select a Card of Fate.]],
[81039]=[[Thanksgiving Store <color=#dd3c2dFF>(November 26-December 2)</color>]],
[81040]=[[Witch's Adventure <color=#dd3c2dFF>(November 26-December 2)</color>]],
[81041]=[[Thanksgiving Pack <color=#dd3c2dFF>(November 26-December 2)</color>]],
[81042]=[[Thanksgiving Collection <color=#dd3c2dFF>(November 26-December 2)</color>]],
[81043]=[[Players can complete Collection Quests to earn Collection EXP and unlock more rewards. Players can also purchase levels directly to unlock rewards and the Advanced Collection even faster. It's just $29.99!]],
[81044]=[[Select a value for the dice roll!]],
[81045]=[[December 24: Decorate the Christmas Tree]],
[81046]=[[December 24: Christmas Stocking Presents]],
[81047]=[[December 24: Jungle Ruins]],
[81048]=[[December 24: Christmas Quests]],
[81049]=[[December 24: Christmas Packs]],
[81050]=[[Proceed to the next floor? You can still use God Keys to unlock treasures, even after obtaining the current round's prize.]],
[81051]=[[Tip: Tap and hold to use automatically.]],
[81052]=[[Ruins Quests]],
[81053]=[[Pack containing God Keys and Common Scrolls.]],
[81054]=[[Event Pack]],
[81055]=[[Complete quests to gain God Keys. Every quest can be completed multiple times.]],
[81056]=[[During the event, players can use God Keys to uncover treasures in the Jungle Ruins]],
[81057]=[[Adventure Pack]],
[81058]=[[Witch's Adventure]],
[81059]=[[Witch's Collection]],
[81060]=[[Pack containing Dice and Common Scrolls.]],
[81061]=[[Travelers can use the dice to control how the witch moves. Collect stars to obtain a huge amount of rewards!]],
[81062]=[[Players can complete Collection Quests to earn Collection EXP and unlock more rewards. Players can also purchase levels directly to unlock rewards and the Advanced Collection even faster. It's just $24.99!]],
[81063]=[[2. During the event, players can use God Keys to uncover treasures in the Jungle Ruins.]],
[81064]=[[3. Complete quests to gain God Keys. Every quest can be completed multiple times.]],
[81065]=[[4. Value Packs containing Diamonds and God Keys will be available for purchase.]],
[81066]=[[5. Players can purchase Ruins Chests with Diamonds.]],
[81067]=[[Complete quests to gain God Keys. Every quest can be completed multiple times.]],
[81068]=[[Value Packs containing Diamonds, God Keys, and Common Scrolls will be available for purchase.]],
[81069]=[[Players can purchase Ruins Chests with Diamonds.]],
[81070]=[[1. During the event, Travelers can control the witch's movement with Normal Dice and Lucky Dice, and obtain rewards on the corresponding tiles. Collect stars to earn more rewards!]],
[81071]=[[2. Control the witch's movement by rolling dice.]],
[81072]=[[①Normal Dice: Roll to yield a random number. The witch will move tiles based on the number rolled.]],
[81073]=[[①Lucky Dice: Roll to pick a specific number. The witch will move tiles based on the number chosen.]],
[81074]=[[3. Different effects and consequences will occur based on the tiles the witch lands on or passes by.]],
[81075]=[[① Resource Tile: When the witch lands on a Resource Tile, the Traveler will obtain the resource shown on the tile. The tile will then be upgraded, up to a maximum of Lv. 3. The tile will yield resources equal to its current level.]],
[81076]=[[② Star Tile: When the witch lands on or passes by a Star Tile, traveler will obtain the number of stars shown on the tile. The tile will be upgraded to increase the number of stars you will get next time if the witch lands on it, up to a maximum of Lv. 3.]],
[81077]=[[③ Normal Dice Tile: The Traveler will obtain 1 Normal Dice when the witch lands on a Normal Dice Tile. These tiles cannot be upgraded.]],
[81078]=[[③ Lucky Dice Tile: The Traveler will obtain 1 Lucky Dice when the witch lands on a Lucky Dice Tile. These tiles cannot be upgraded.]],
[81079]=[[③Card of Fate Tile: Activate a random card effect when the witch lands on a Card of Fate Tile. These tiles cannot be upgraded.]],
[81080]=[[③Crossroad of Fate Tile: After landing on a Crossroad of Fate Tile, the witch will move forward if the next dice roll is even, or backwards if it is odd. These tiles cannot be upgraded.]],
[81081]=[[4. Cards of Fate: Will have different effects depending on the result of the Random Summon.]],
[81082]=[[① Lucky Card: You receive a blessing from the Goddess of Luck. The value of your dice will be multiplied by 2 for your next roll.]],
[81083]=[[② Star Card: When you land on or pass a Star Tile, you will receive double the amount of Stars!]],
[81084]=[[③ Storm Card: The rewards for your next roll will be devoured by stormy clouds.]],
[81085]=[[④ Energy Card: Tada! Congratulations on obtaining 1 Resource Chest!]],
[81086]=[[5. After accumulating a certain number of Stars, you will obtain the respective stage rewards.]],
[81087]=[[1. Witch's Adventure: Travelers can use the dice to control how the witch moves. Collect stars to obtain a huge amount of rewards!]],
[81088]=[[2. Event Packs containing Dice and Common Scrolls will be available for purchase.]],
[81089]=[[3. Players can complete Tome Quests to earn Tome EXP and unlock more rewards. Players can also purchase levels directly to unlock rewards and the Adv. Collection even faster. It's just $29.99, so what are you waiting for?]],
[81090]=[[4. The Alchemy Store will have 2 Elite heroes available for purchase.]],
[81091]=[[Packs containing Dice and Common Scrolls will be available for purchase.]],
[81092]=[[Witch's Adventure: Travelers can use the dice to control how the witch moves. Collect stars to obtain a huge amount of rewards!]],
[81093]='Gifts',
[81094]=[[Cupid's Arrow]],
[81095]=[[Valentine's Day Login]],
[81096]=[[Valentine's Day Quest]],
[81097]=[[Valentine's Day Pack]],
[81098]=[[Give gifts to heroes during the event to increase Intimacy and receive return gifts.]],
[81099]=[[Rewards Preview]],
[81100]='Intimacy',
[81101]=[[Level %s Intimacy Rewards]],
[81102]='Send',
[81103]=[[Tap and hold to keep gifting.]],
[81104]=[[Intimacy Rewards]],
[81105]=[[Reach the corresponding Intimacy Level to obtain the rewards.]],
[81106]=[[Total Intimacy reaches]],
[81107]=[[Event ends in: <color=#db2e2e>%s</color>]],
[81108]=[[Maximum Intimacy reached.]],
[81109]=[[Help Cupid practice shooting to receive gifts.]],
[81110]='Score',
[81111]='Arrow',
[81112]=[[Tap Start to begin Cupid's Shooting Practice.]],
[81113]=[[Tap and hold to change directions. Release to shoot the arrow.]],
[81114]=[[Rewards Preview]],
[81115]=[[Remaining Challenge(s)]],
[81116]='Start',
[81117]='Challenging',
[81118]='Ring!',
[81119]=[[Remaining Purchases: %s]],
[81120]=[[Total Ring Score: %s]],
[81121]=[[Get 1st prize!]],
[81122]='Started',
[81123]=[[You have reached the max number of purchases.]],
[81124]=[[You can collect a Valentine's Day login gift each day.]],
[81125]=[[Complete quests to obtain gifts that can increase the Intimacy of heroes.]],
[81126]=[[Packs that can raise the Intimacy of heroes will be available for purchase.]],
[81127]=[[You have used up all attempts.]],
[81128]=[[Time Remaining: <color=#87F425>%s</color>]],
[81129]=[[Complete Cupid's Arrow 2 times.]],
[81130]=[[Claim Daily Quest Chest 1.]],
[81131]=[[Claim 2h of AFK Rewards]],
[81132]=[[Don't test the patience of a witch, for you cannot bear the consequences of her temptation.]],
[81133]=[[My tinder heralds from hell itself. The slumbering mortal realm shall burn with my arrival.]],
[81134]=[[Don't test the patience of a witch, for you cannot bear the consequences of her temptation.]],
[81135]=[[My tinder heralds from hell itself. The slumbering mortal realm shall burn with my arrival.]],
[81136]=[[The most beautiful temptations are the most deadly. Are you ready to accept my invitation?]],
[81137]=[[You're an interesting human. How about a glass of fine wine from hell?]],
[81138]=[[Desires have torn your soul. Let me see whom you long for in your heart~!]],
[81139]=[[You're an interesting human. How about a glass of fine wine from hell?]],
[81140]=[[Desires have torn your soul. Let me see whom you long for in your heart~!]],
[81141]=[[There is no need to conceal your thoughts. I am well aware of what you humans think...]],
[81142]=[[Love need not be spoken. The eyes of humans are much more honest than their mouths.]],
[81143]=[[The winter of hell is a frosty one... might you be the one to warm me up?]],
[81144]=[[Spending the winter with humans can be a decent experience at times.]],
[81145]=[[In the world of humans, there is no better way to keep warm than to embrace each other.]],
[81146]=[[There is nothing impressive about being unrivaled. I'm awaiting your challenge...]],
[81147]=[[Battle is the only thing in the world that can bring me a thrill.]],
[81148]=[[There is nothing impressive about being unrivaled. I'm awaiting your challenge...]],
[81149]=[[Battle is the only thing in the world that can bring me a thrill.]],
[81150]=[[A swift blade can cut through running water, but it cannot sever human feelings. Love and affection are the greatest taboos of the wanderer.]],
[81151]=[[A swift blade can cut through running water, but it cannot sever human feelings. Love and affection are the greatest taboos of the wanderer.]],
[81152]=[[My blade can catch up with the driving winds, but it cannot bring back feelings that have already dissipated.]],
[81153]=[[Don't tempt the heart of a swordmaster carelessly, for theirs will be as cold and sharp as their blade.]],
[81154]=[[Being undefeated is the most lonely feeling in the world... Why? Is it that hard to believe that I could be a lonely woman as well?]],
[81155]=[[On my climb to the top, I've also wished to have someone to talk to.]],
[81156]=[[Thanks to you, I now understand that communicating with others involves more than exchanging blows and blades.]],
[81157]=[[In this world, you are one of the rare few who could bring me thrills outside of battle.]],
[81158]=[[It's easy to look for precarious peaks, but hard to find ultimate intentions. Even before I started seeking such things, I already started to miss you.]],
[81159]=[[Cutting through water is the highest level of swordmanship, but from the bottom of my heart, I hope that our bond is one that can never be severed..]],
[81160]=[[Steel is a loyal partner, the sound of the wind is a bosom friend, and the bullet of a gun a welcome gift.]],
[81161]=[[Though cold-hearted, steel is more trustworthy than flesh and blood, soft and warm though it may be.]],
[81162]=[[Steel is a loyal partner, the sound of the wind is a bosom friend, and the bullet of a gun a welcome gift.]],
[81163]=[[Though cold-hearted, steel is more trustworthy than flesh and blood, soft and warm though it may be.]],
[81164]=[[Do you also enjoy the feeling of flying? I’ll be waiting for you in the blue sky and white clouds!]],
[81165]=[[The only thing that prevents humans from flying is a fear of heights. You're not afraid, are you?]],
[81166]=[[Do you also enjoy the feeling of flying? I’ll be waiting for you in the blue sky and white clouds!]],
[81167]=[[Living like the free wind is my dream. Might it be yours too?]],
[81168]=[[Sixty percent of my body is made of steel. Are you willing to be my weakness?]],
[81169]=[[If you think steel is cold, then warm her up.]],
[81170]=[[Might you never betray me... like my steel wings?]],
[81171]=[[My bullets never miss their target, but the one I shot at you found its way to my heart.]],
[81172]=[[When I meet you, I felt lucky that my heart was made of flesh and blood.]],
[81173]=[[Flying with you and fighting side by side is the best ending I could ask for.]],
[81174]=[[Foolish mortals, stay away. The power of lightning is not something you can bear.]],
[81175]=[[Lightning is the incarnation of God's will, and I will descend alongside it.]],
[81176]=[[Foolish mortals, stay away. The power of lightning is not something you can bear.]],
[81177]=[[Lightning is the incarnation of God's will, and I will descend alongside it.]],
[81178]=[[Have you felt the spark of my plasma field?]],
[81179]=[[What does it feel like when the current passes through your body?]],
[81180]=[[Have you felt the spark of my plasma field?]],
[81181]=[[You look quite adorable when the sparks run through you! Oh... why are you blushing?]],
[81182]=[[Your blood is a conductor, and I can even hear the tremble of your heart.]],
[81183]=[[There's something luckier than being chosen by lightning... being chosen by me, of course!]],
[81184]=[[If emotions can be transmitted through currents, our communication would be much easier.]],
[81185]=[[Would you embrace me in the current and enjoy every moment of madness?]],
[81186]=[[If the current is a rope, I can surely tie you closely to me.]],
[81187]=[[You are the first person who has dared to hold my hand, and you look like you're enjoying it.]],
[81188]=[[Final Rewards]],
[81189]=[[Round %d]],
[81190]=[[Round Prize]],
[81191]=[[Star Tile]],
[81193]=[[Cards of Fate]],
[81194]=[[Randomly drawn Cards of Fate will have different effects.
①Lucky Card: You receive a blessing from the Goddess of Luck. The value of your dice will be multiplied by 2 for your next roll.
②Star Card: When you land on or pass a Star Tile, you will receive double the amount of stars!
③Storm Card: The rewards for your next roll will be devoured by stormy clouds.
④Energy Card: Tada! Congratulations on obtaining 1 Resource Chest!]],
[81195]=[[Crossroad of Fate]],
[81197]=[[Light Crystal*1000 Pack]],
[81198]=[[Light Crystal*5000 Pack]],
[81199]=[[The pack contains Light Crystal x1000, Union Coin x5000, Gold x200000, Diamonds x500, and VIP EXP.]],
[81200]=[[The pack contains Light Crystal x5000, Union Coin x25000, Gold x1000000, Diamonds x2500, and VIP EXP.]],
[81201]=[[Obtain 2nd class rewards!]],
[81202]=[[Obtain 3rd class rewards!]],
[81203]=[[Giveaway <color=#dd3c2dFF>(September 17- September 23)</color>]],
[81204]=[[Mid-Autumn Festival Archery <color=#dd3c2dFF>(September 17- September 23)</color>]],
[81205]=[[Mid-Autumn Festival Login <color=#dd3c2dFF>(September 17- September 23)</color>]],
[81206]=[[Mid-Autumn Festival Quests <color=#dd3c2dFF>(September 17- September 23)</color>]],
[81207]=[[Mid-Autumn Pack <color=#dd3c2dFF>(September 17 - September 23)</color>]],
[81208]='Gifts',
[81209]=[[Cupid's Arrow]],
[81210]=[[Valentine's Day Login]],
[81211]=[[Valentine's Day Quest]],
[81212]=[[Valentine's Day Pack]],
[81213]=[[The new Moon Phoenix Skin: Lethal Silverwing will be sold at half price for a limited time.]],
[81214]=[[Moon Phoenix Skin Pack]],
[81215]=[[Normal Dice]],
[81216]=[[Obtain 1 Normal Dice. Roll to yield a random number. The witch will move tiles based on the number rolled.]],
[81217]=[[Lucky Dice]],
[81218]=[[You have obtained a Lucky Dice. Roll to pick a specific number. The witch will move tiles based on the number chosen.]],
[81219]=[[Dice Purchase]],
[81220]=[[Adventure Sign-In]],
[81221]=[[Adventure Quest]],
[81222]=[[During the event, you can collect a login reward every day.]],
[81223]=[[During the event, you can obtain additional rewards by completing quests.]],
[81224]=[[3. You can collect a login reward every day.]],
[81225]=[[4. You can obtain additional rewards by completing quests.]],
[81226]=[[5. The Alchemy Store will have 2 Elite heroes available for purchase.]],
[81227]=[[Claim 2h of AFK Rewards]],
[81228]=[[Perform 5 Arena challenges.]],
[81229]=[[Spend 30 Stamina in the Garden.]],
[81230]=[[Perform 10 Summons in the Summoning Hall.]],
[81231]=[[Awaken any hero to Epic 1 time.]],
[81232]=[[Awaken any hero to Legendary 1 time.]],
[81233]=[[Spend 1000 Diamonds.]],
[81234]=[[Dragon Boat Event]],
[81235]=[[Rice Dumplings Bingo]],
[81236]=[[Login Gift]],
[81237]=[[Bingo Quest]],
[81238]=[[Skin Pack]],
[81239]=[[During the event, use Rice Dumplings to activate Bingo spaces and form horizontal or vertical lines to unlock Bingo rewards. Activate all areas to receive the grand prize: Hero - Alvarez.]],
[81240]=[[During the event, you can collect a login reward every day.]],
[81241]=[[Complete quests to gain Rice Dumplings. Every quest can be completed multiple times.]],
[81242]=[[The new Dragon Warrior Skin Pack will be available for purchase.]],
[81243]=[[Do you want to spend %s?]],
[81244]=[[Unlock Bingo Tile]],
[81245]=[[(Unlock Bingo Tile to collect random rewards)]],
[81246]=[[Clear to unlock higher discounts]],
[81247]=[[Are you sure you want to purchase?]],
[81248]=[[Claim the Bingo Event Prize to unlock this discount]],
[81249]=[[Tap the Go button to skip]],
[81250]=[[Clear to unlock higher discounts]],
[81251]=[[Bingo Exclusive]],
[81252]='Claimed',
[81253]='Activated',
[81254]=[[Stat Bonus: +3% ATK, +3% HP, +6% CRIT DMG]],
[81255]=[[Rice Dumplings Bingo <color=#dd3c2dFF>(June 11 - June 17)</color>]],
[81256]=[[Login Gift <color=#dd3c2dFF>(June 11 - June 17)</color>]],
[81257]=[[Bingo Quest <color=#dd3c2dFF>(June 11 - June 17)</color>]],
[81258]=[[Skin Pack <color=#dd3c2dFF>(June 11 - June 17)</color>]],
[81259]=[[Rice Dumplings Bingo <color=#dd3c2dFF>(June 11 - June 17)</color>]],
[81260]=[[Login Gift <color=#dd3c2dFF>(June 11 - June 17)</color>]],
[81261]=[[Bingo Quest <color=#dd3c2dFF>(June 11 - June 17)</color>]],
[81262]=[[Skin Pack <color=#dd3c2dFF>(June 11 - June 17)</color>]],
[81263]=[[Recharge Gift]],
[81264]=[[VIP EXP
%s]],
[81265]=[[[Total Recharge %s%s VIP EXP] Moon Phoenix Gear Pack]],
[81266]=[[[Total Recharge %s%s VIP EXP] Moon Phoenix Guaranteed Epic Pack]],
[81267]=[[[Total Recharge %s%s VIP EXP] Moon Phoenix Boost Legend Pack]],
[81268]=[[[Total Recharge %s%s VIP EXP] Moon Phoenix Exclusive Artifact Pack]],
[81269]=[[Recharge %s]],
[81270]=[[Recommended Pack]],
[81271]=[[Warrior Trial]],
[81272]=[[Permanent Card]],
[81273]=[[Growth Pack]],
[81274]=[[Summon Pack]],
[81275]=[[Daily Rewards]],
[81276]=[[Complete the Warrior Trial Quest and purchase a Warrior Trial Chest to claim <color=#E6D95F>S/S+ Hero Shard</color> rewards.]],
[81277]=[[Purchase the Permanent Card to claim <color=#E6D95F>200 Diamonds and 2H of AFK Rewards</color> every day (permanent validity).]],
[81278]=[[Purchase the Growth Pack to claim all rewards for cleared chapters, with a total value of <color=#E6D95F>40000 Diamonds</color>.]],
[81279]=[[After purchasing the Summon Pack, log in every day to unlock <color=#E6D95F>Common Scroll x150 and Faction Summon Scroll x15</color>.]],
[81280]=[[Gain VIP EXP from recharges]],
[81281]=[[Free Monthly Card]],
[81282]=[[Recommended Pack]],
[81283]=[[Subscription Pack]],
[81284]=[[Hero Pass]],
[81285]=[[Purchase the Subscription Pack to enjoy <color=#E6D95F>7 perks</color> for <color=#E6D95F>30 days</color>.]],
[81286]=[[Complete daily quests and accumulate hero badges to unlock Pass rewards and obtain additional gifts after purchase.]],
[81287]=[[Additional Garden Draw Energy: %s]],
[81288]=[[Additional Ancient Summon Dungeon Potions: %s]],
[81289]=[[Additional Rune Cave I/II Stamina: %s]],
[81290]=[[Additional Acorn Tavern Daily Quests: %s]],
[81291]=[[Additional Free Quick AFKs: %s]],
[81292]=[[Additional AFK Resource Income: %s]],
[81293]=[[Unlock Sweep for Ancient Summon and Rune Cave I/II]],
[81294]=[[Subscription Perks]],
[81295]=[[Subscription Perks]],
[81296]=[[Effective after purchase for <color=#ffff00><size=40><i>30</i></size></color> days.]],
[81297]=[[Enjoy<color=#ffff00><size=40> <i>7 Perks </i></size></color>]],
[81298]=[[Additional Daily Garden Draw Energy:]],
[81299]=[[Additional Daily Potions of Refreshment (Ancient Summon Dungeon):]],
[81300]=[[Additional Daily Rune Cave I/II Stamina:]],
[81301]=[[Additional Daily Acorn Tavern Quests:]],
[81302]=[[Additional Daily Free Quick AFKs:]],
[81303]=[[Additional AFK Resource Income:]],
[81304]=[[Unlock Sweep for Ancient Summon and Rune Cave I/II]],
[81305]=[[Remaining time in effect: %s ]],
[81306]=[[Purchase to add 30 days of Subscription Perks]],
[81307]=[[Subscription Perks activated. Additional purchases will add 30 more days.]],
[81308]=[[Event ends in:]],
[81309]=[[Gain %s VIP EXP from recharges to start for free]],
[81310]=[[Common Scroll*10 Pack]],
[81311]=[[Common Scroll*20 Pack]],
[81312]=[[Exclusive Stone Chest*100 Pack]],
[81313]=[[Faction Summon Scroll*5 Pack]],
[81314]=[[T2 Celestial Stone*10 Pack]],
[81315]=[[Faction Summon Scroll*8 Pack]],
[81316]=[[Faction Summon Scroll*15 Pack]],
[81317]=[[Origin of Doom*2000 Pack]],
[81318]=[[T2 Celestial Stone*25 Pack]],
[81319]=[[Common Scroll*5 Pack]],
[81320]=[[Common Scroll*5 Pack]],
[81321]=[[Common Scroll*10 Pack]],
[81322]=[[Common Scroll*15 Pack]],
[81323]=[[Faction Summon Scroll*10 Pack]],
[81324]=[[Faction Summon Scroll*20 Pack]],
[81325]=[[Includes 10 Common Scrolls for summoning Heroes and Gold.]],
[81326]=[[Includes 20 Common Scrolls for summoning Heroes and Hero EXP.]],
[81327]=[[Includes 100 Exclusive Stone Chests to enhance heroes and Diamonds.]],
[81328]=[[Includes 5 Faction Scrolls for summoning Heroes, 5 Common Scrolls, and AFK resources.]],
[81329]=[[Includes 10 Tier 2 Celestial Stones for Gear Rankup, Gold, and Geolite.]],
[81330]=[[Includes 8 Faction Scrolls for summoning Heroes, 13 Common Scrolls, and a large quantity of Rankup Stones.]],
[81331]=[[Includes 15 Faction Scrolls for summoning Heroes, 25 Common Scrolls, and a large quantity of Rankup Stones.]],
[81332]=[[Includes 2000 Origin of Doom for ultimate artifact upgrades and Gold.]],
[81333]=[[Includes Tier 2 Celestial Stones for Gear Rankup, Gold, and Geolite.]],
[81334]=[[Includes 5 Common Scrolls for summoning Heroes and Diamonds.]],
[81335]=[[Includes 5 Common Scrolls for summoning Heroes, Diamonds, and Rankup Stones.]],
[81336]=[[Includes 10 Common Scrolls for summoning Heroes and a Rare Hero Selection Card.]],
[81337]=[[Includes 15 Common Scrolls for summoning Heroes, 50 shards for merging Excellent Heroes, and Gold.]],
[81338]=[[Includes 10 Faction Scrolls for summoning Heroes, 15 Common Scrolls, and Diamonds.]],
[81339]=[[Includes 20 Faction Scrolls for summoning Heroes, 30 Common Scrolls, and Diamonds.]],
[81340]=[[[First Recharge Pack] Moon Phoenix First Recharge Super Value Pack]],
[81341]=[[Total Rewards]],
[81342]=[[Total Rewards]],
[81343]=[[Insufficient Proving Grounds sweep charges.]],
[81344]=[[Wish Summon]],
[81345]=[[Guaranteed in %s summons.]],
[81346]=[[Summon %s time(s)]],
[81347]=[[Guaranteed in the next summon]],
[81348]=[[Rewards List]],
[81349]=[[Summon Rates Preview]],
[81350]=[[Value Weekly Card]],
[81351]=[[Event Carnival]],
[81352]=[[Purchase to instantly obtain a large number of diamonds]],
[81353]=[[Purchase the Weekly Pass to obtain daily rewards]],
[81354]=[[Daily Rewards: Issued continuously for <size=32><color=#fff000>%s</color></size> days.]],
[81355]=[[Weekly Pass Grand Pack]],
[81356]=[[Weekly Pass]],
[81358]=[[Purchase the Weekly Pass to obtain a wealth of rewards and exclusive pass rewards every day for 7 days.]],
[81359]=[[Wish Weekly Pass]],
[81360]=[[Wish Summon]],
[81361]=[[Claim Now]],
[81362]=[[Wishing Room]],
[81363]=[[Wishing Sign-In]],
[81364]=[[Wishing Quest]],
[81365]=[[Event Pack]],
[81366]=[[Weekly Wish Card]],
[81367]=[[Rewards List]],
[81368]=[[<color=#ffea00><size=40>%s</size></color><color=#ecb440> <size=30>to guarantee a</size></color><color=#69e7a7> <size=40>6-Star Super ATK Rune</size></color>.]],
[81369]=[[<color=#ffea00><size=40>%s</size></color><color=#ecb440> <size=30>to guarantee a</size></color><color=#69e7a7> <size=40>4-Star Super ATK Rune</size></color>.]],
[81370]=[[<color=#ffea00><size=40>%s</size></color><color=#ecb440> <size=30>to guarantee a</size></color><color=#69e7a7> <size=40>3-Star Super ATK Rune</size></color>.]],
[81371]=[[<color=#ffea00><size=40>Draw</size></color><color=#ecb440> <size=30>to guarantee a</size></color><color=#69e7a7><size=40>6-Star Super ATK Rune</size></color>.]],
[81372]=[[<color=#ffea00><size=40>Draw</size></color><color=#ecb440> <size=30>to guarantee a</size></color><color=#69e7a7><size=40>4-Star Super ATK Rune</size></color>.]],
[81373]=[[<color=#ffea00><size=40>Draw</size></color><color=#ecb440> <size=30>to guarantee a</size></color><color=#69e7a7><size=40>3-Star Super ATK Rune</size></color>.]],
[81374]='1x',
[81375]='10x',
[81376]=[[Event ends in: %s]],
[81377]=[[Basic Astrology]],
[81378]=[[Intermediate Astrology]],
[81379]=[[Advanced Astrology]],
[81380]=[[Claim daily sign-in rewards!]],
[81381]=[[Complete quests to obtain Divination Tickets, which can be used in the Astrology House.]],
[81382]=[[Packs containing Divination Tickets, Enhancement Materials, and Diamonds will be available for purchase.  ]],
[81383]=[[You have used up all attempts.]],
[81384]=[[Claim Daily Quest Chest 5.]],
[81385]=[[Claim Daily Quest Chest 1.]],
[81386]=[[Perform 10 Faction Summons in the Summoning Hall.]],
[81387]=[[Claim 2h of AFK Rewards]],
[81388]=[[Defeat the Tower of Fate boss 1 time.]],
[81389]=[[Perform Quick AFK 5 times.]],
[81390]=[[Accept 10 quests in the Acorn Tavern.]],
[81391]=[[Purchase the weekly pass to earn a wealth of rewards.]],
[81392]=[[Rune Carnival]],
[81393]=[[Instantly claim upon purchase]],
[81394]=[[Purchase to obtain a 4-Star Rune each day.]],
[81395]=[[Issued continuously for 7 days.]],
[81396]=[[Weekly Pass Grand Pack]],
[81397]=[[Weekly Pass]],
[81398]=[[Original Price: %s]],
[81399]=[[Current Price: <size=30>%s</size>]],
[81400]=[[Complete quests to obtain <color=#E8D946>heaps of rewards</color>.
Quests can be repeated <color=#E8D946>multiple times</color>.]],
[81401]=[[Wish Summon Cards, Diamonds, VIP EXP, and other packs are available for purchase.]],
[81402]=[[Travelers can use different Divination Tickets obtained during the event to perform Divination with different Divination Beads.  ]],
[81403]=[[Wish Pack]],
[81404]=[[Wish Quest]],
[81405]=[[Spend Wish Summon Cards to make wish draws. Perform a specified number of draws to guarantee a designated Wish Hero.]],
[81406]=[[Completing designated quests to earn Wish Summon Cards.]],
[81408]=[[Rune Carnival]],
[81409]=[[Claim rewards now]],
[81410]=[[Daily Rewards]],
[81411]=[[The Weekly Pass grants rewards for 7 consecutive days.]],
[81412]='Go',
[81413]=[[Daily gain of VIP EXP from recharges reached %s]],
[81414]=[[Claim Free Excellent Hero]],
[81415]=[[Claim by accumulating 150 VIP EXP via recharge for 1 day.]],
[81416]=[[Claim by accumulating 150 VIP EXP via recharge for 2 days.]],
[81417]=[[Claim by accumulating 150 VIP EXP via recharge for 3 days.]],
[81418]=[[Claim by accumulating 150 VIP EXP via recharge for 4 days.]],
[81419]=[[Claim by accumulating 150 VIP EXP via recharge for 5 days.]],
[81420]=[[Claim by accumulating 150 VIP EXP via recharge for 6 days.]],
[81421]=[[Claim by accumulating 150 VIP EXP via recharge for 7 days.]],
[81422]=[[Claim by accumulating 150 VIP EXP via recharge for 8 days.]],
[81423]=[[Claim by accumulating 150 VIP EXP via recharge for 9 days.]],
[81424]=[[Claim by accumulating 150 VIP EXP via recharge for 10 days.]],
[81425]=[[Claim by accumulating 150 VIP EXP via recharge for 11 days.]],
[81426]=[[Claim by accumulating 150 VIP EXP via recharge for 12 days.]],
[81427]=[[Claim by accumulating 150 VIP EXP via recharge for 13 days.]],
[81428]=[[Claim by accumulating 150 VIP EXP via recharge for 14 days.]],
[81429]=[[Claim by accumulating 150 VIP EXP via recharge for 15 days.]],
[81430]=[[Public Welfare Gift]],
[81431]=[[Daily Special Gift]],
[81432]=[[Daily Boost Gift]],
[81433]=[[Daily Featured Gift]],
[81434]=[[Quick Purchase]],
[81435]=[[Everyday Login]],
[81436]=[[Daily Pack]],
[81437]=[[Daily Featured]],
[81438]=[[Super Value Pack]],
[81439]=[[Exclusive Pack]],
[81440]=[[Hero Ascension]],
[81441]=[[Exclusive Pack]],
[81442]=[[Exclusive Wish Hero Selection]],
[81443]=[[1. Select a wish hero for the corresponding exclusive pack to appear.]],
[81444]=[[2. You can swap to a random hero.]],
[81445]=[[3. Once you confirm your selection, you will not be able to change your wish hero. You can now purchase the selected hero's exclusive pack.]],
[81446]=[[Boss Trial]],
[81447]=[[Boss Trial Ranking]],
[81448]=[[Highest DMG:]],
[81449]=[[Current Ranking: %s]],
[81450]=[[Can claim after %s]],
[81451]='Challenge',
[81452]=[[Challenge Quest]],
[81453]=[[Ranking Rewards]],
[81454]=[[Challenge Quest]],
[81455]=[[Boss Info]],
[81456]=[[Daily Challenge]],
[81457]=[[First Trial]],
[81458]=[[Stats Challenge]],
[81459]=[[Deploy 2 God and Voider Heroes.]],
[81460]=[[Survival Challenge]],
[81461]=[[Have 3 Heroes survive to the end of the battle.]],
[81462]=[[DMG Challenge]],
[81463]=[[Achieve %s DMG in a single challenge.]],
[81464]='Unachieved',
[81465]='Claim',
[81466]='Claimed',
[81467]=[[Challenge the trial boss to earn heaps of rewards.]],
[81468]=[[Exclusive Hero Awakening Packs will be available for purchase.]],
[81469]=[[%s unlocked]],
[81470]=[[Once you confirm your selection, you will not be able to change your wish hero. You can now purchase the selected hero's exclusive pack.]],
[81471]=[[Quick Summon Scrolls:]],
[81472]=[[Unable to purchase. Perks will expire in %s days.]],
[81473]=[[Purchase Subscription Perks to increase Daily Garden Draw Energy.]],
[81474]=[[Purchase Subscription Perks to increase Rune Cave I/II Stamina.]],
[81475]=[[Purchase Subscription Perks to increase Ancient Summon Dungeon Fuel.]],
[81476]=[[Purchase Subscription Perks to increase Daily Free Quick AFKs.]],
[81477]=[[Purchase Subscription Perks to unlock Sweep for Ancient Summon and Rune Cave I/II.]],
[81478]=[[Mid-Autumn Festival Event]],
[81479]=[[Mid-Autumn Festival Word Collection]],
[81480]=[[Mid-Autumn Festival Login]],
[81481]=[[Event Pack]],
[81482]=[[Mid-Autumn Festival Archery]],
[81483]='Gifts',
[81484]=[[Mid-Autumn Festival Quests]],
[81485]='Intimacy',
[81486]=[[Tap Start to begin archery practice.]],
[81487]=[[Are you sure you want to use %s?]],
[81488]=[[You can collect a Mid-Autumn login gift each day.]],
[81489]=[[Complete quests to obtain lanterns and calligraphy. Quests can be completed multiple times.]],
[81490]=[[Packs containing Mooncakes, Summon Scrolls, and Diamonds will be available for purchase.]],
[81491]=[[Give gifts to Heroes to increase Intimacy and receive return gifts.]],
[81492]=[[Complete the Shooting event to obtain item rewards.]],
[81493]=[[Complete quests to obtain Calligraphy, which can be redeemed for various rewards. The Mid-Autumn Universal Card can be used to obtain all Calligraphy available during the Mid-Autumn event.]],
[81494]=[[National Day Event]],
[81495]=[[National Day Word Collection]],
[81496]=[[National Day Login]],
[81497]=[[Event Value Pack]],
[81498]=[[National Day Bingo]],
[81499]=[[National Day Quests]],
[81500]=[[You can collect a National Day login gift each day.]],
[81501]=[[Complete specified quests to obtain lanterns and random calligraphy rewards. You can repeat each quest multiple times.]],
[81502]=[[Value Packs containing National Day Wild Cards, Summon Scroll Coupons, and Diamonds will be available for purchase.]],
[81503]=[[Use Lanterns to activate Bingo spaces and form horizontal or vertical lines to unlock Bingo rewards. Activate all areas to receive the grand prize: S+ Hero Selection Card.]],
[81504]=[[Complete quests to obtain Calligraphy, which can be redeemed for various rewards. The National Day Universal Card can be used to obtain all Calligraphy available during the National Day event.]],
[81505]=[[National Day Free Pack]],
[81506]=[[National Day Value Pack]],
[81507]=[[Reward Exchange]],
[81508]=[[Reached Exchange Limit]],
[81509]='Friendship',
[81510]=[[Friendship Rewards]],
[81511]=[[Total Friendship reached]],
[81512]=[[You can obtain level rewards upon reaching the corresponding Friendship Level.]],
[81513]=[[Level %s Friendship Reward]],
[81515]=[[Swords are loyal companions, while the sound of the wind is my sanctuary.]],
[81516]=[[Swords may be cold and emotionless, but their reliability is unparalleled.]]}