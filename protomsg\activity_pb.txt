-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local error_code_pb=require("error_code_pb")
local common_new_pb=require("common_new_pb")
local allSaintsDay_pb=require("allSaintsDay_pb")
local common_pb=require("common_pb")
local sandbox_pb=require("sandbox_pb")
module('activity_pb')


V1M=V(4,"enActivityHeadingCodeType_Task",0,101)
V2M=V(4,"enActivityHeadingCodeType_PaidTask",1,102)
V3M=V(4,"enActivityHeadingCodeType_MiniGameActivity",2,106)
V4M=V(4,"enActivityHeadingCodeType_ActivityRank",3,107)
V5M=V(4,"enActivityHeadingCodeType_SlotMachineDraw",4,108)
V6M=V(4,"enActivityHeadingCodeType_WorldBoss",5,201)
V7M=V(4,"enActivityHeadingCodeType_ArmsRace",6,202)
V8M=V(4,"enActivityHeadingCodeType_GeneralTrial",7,203)
V9M=V(4,"enActivityHeadingCodeType_AllianceMilitaryDrill",8,204)
V10M=V(4,"enActivityHeadingCodeType_MassBattle",9,205)
V11M=V(4,"enActivityHeadingCodeType_ZombieInvasion",10,206)
V12M=V(4,"enActivityHeadingCodeType_Arena",11,207)
V13M=V(4,"enActivityHeadingCodeType_SkyFall",12,217)
V14M=V(4,"enActivityHeadingCodeType_CampTrial",13,210)
V15M=V(4,"enActivityHeadingCodeType_UrbanRace",14,211)
V16M=V(4,"enActivityHeadingCodeType_AllianceDuel",15,212)
V17M=V(4,"enActivityHeadingCodeType_AllianceDuelBattle",16,213)
V18M=V(4,"enActivityHeadingCodeType_Carriage",17,214)
V19M=V(4,"enActivityHeadingCodeType_ZoneBattleDuel",18,215)
V20M=V(4,"enActivityHeadingCodeType_CommunityGift",19,218)
V21M=V(4,"enActivityHeadingCodeType_SevenDayLogin",20,219)
V22M=V(4,"enActivityHeadingCodeType_ZombieApocalypse",21,221)
V23M=V(4,"enActivityHeadingCodeType_MiniLevelActivity",22,222)
V24M=V(4,"enActivityHeadingCodeType_DualBattlePass",23,1001)
V25M=V(4,"enActivityHeadingCodeType_TripleBattlePass",24,1002)
V26M=V(4,"enActivityHeadingCodeType_NoMissionBattlePass",25,1003)
V27M=V(4,"enActivityHeadingCodeType_VanguardObjective",26,1004)
V28M=V(4,"enActivityHeadingCodeType_VanguardCommander",27,1005)
V29M=V(4,"enActivityHeadingCodeType_StrongestCommander",28,1008)
V30M=V(4,"enActivityHeadingCodeType_AccumulatedRecharge",29,1009)
V31M=V(4,"enActivityHeadingCodeType_WeeklyCard",30,1011)
V32M=V(4,"enActivityHeadingCodeType_MonthlyCard",31,1012)
V33M=V(4,"enActivityHeadingCodeType_LoginReward",32,1017)
V34M=V(4,"enActivityHeadingCodeType_GearSupply",33,1021)
V35M=V(4,"enActivityHeadingCodeType_ChooseWeekCard",34,1022)
V36M=V(4,"enActivityHeadingCodeType_MonthlyCard2",35,1024)
V37M=V(4,"enActivityHeadingCodeType_lordObjective",36,1026)
V38M=V(4,"enActivityHeadingCodeType_AllianceDropChests",37,1031)
E1M=E(3,"enActivityHeadingCodeType",".CSMsg.enActivityHeadingCodeType")
V39M=V(4,"WEEKLY",0,1)
V40M=V(4,"MONTHLY",1,2)
V41M=V(4,"RMB",2,3)
V42M=V(4,"WELFARE",3,4)
V43M=V(4,"FRISTRECHATGE",4,5)
V44M=V(4,"BORKENTIME",5,6)
V45M=V(4,"UPGRADE",6,7)
V46M=V(4,"FESTIAVL",7,8)
E2M=E(3,"ActivityType",".CSMsg.ActivityType")
V47M=V(4,"ACTIVITY_TYPE_WEEKLY",0,1)
V48M=V(4,"ACTIVITY_TYPE_MONTHLY",1,2)
V49M=V(4,"ACTIVITY_TYPE_RMB",2,3)
V50M=V(4,"ACTIVITY_TYPE_WELFARE",3,4)
V51M=V(4,"ACTIVITY_TYPE_SPECIALGIFT",4,5)
V52M=V(4,"ACTIVITY_TYPE_FESTIVAL",5,6)
V53M=V(4,"ACTIVITY_TYPE_NEW",6,7)
E3M=E(3,"enActivityType",".CSMsg.enActivityType")
V54M=V(4,"ALREADY_SIGN",0,1)
V55M=V(4,"CAN_SIGN",1,2)
V56M=V(4,"WILL_SIGN",2,3)
V57M=V(4,"NOT_SIGN",3,4)
E4M=E(3,"SignInState",".CSMsg.SignInState")
V58M=V(4,"INSERT",0,1)
V59M=V(4,"REMOVE",1,2)
V60M=V(4,"UPDATE",2,3)
E5M=E(3,"ActivityOperat",".CSMsg.ActivityOperat")
V61M=V(4,"ACTTYPE_MAKE_FOOD",0,1)
V62M=V(4,"ACTTYPE_LABOURDAY",1,2)
V63M=V(4,"ACTTYPE_ALLSAINTSDAY_UPVOTE",2,3)
V64M=V(4,"ACTTYPE_ALLSAINTSDAY_SCORE",3,4)
V65M=V(4,"ACTTYPE_ALLSAINTSDAY_UPVOTE_SUMMARY",4,5)
V66M=V(4,"ACTTYPE_WORLDBOSS_DAMAGE",5,101)
V67M=V(4,"ACTTYPE_ARMSRACE",6,102)
V68M=V(4,"ACTTYPE_ALLIANCEBOSS",7,103)
V69M=V(4,"ACTTYPE_ZOMBIECOMING",8,104)
V70M=V(4,"ACTTYPE_CAMPTRIAL_FORESET",9,105)
V71M=V(4,"ACTTYPE_CAMPTRIAL_HUMANS",10,106)
V72M=V(4,"ACTTYPE_CAMPTRIAL_NIGHT",11,107)
V73M=V(4,"ACTTYPE_ALLIANCEDUEL",12,108)
V74M=V(4,"ACTTYPE_STRONGEST_COMMANDER",13,109)
V75M=V(4,"ACTTYPE_NCRANKING",14,110)
V76M=V(4,"ACTTYPE_WORLDBOSS_ALLIANCE_DAMAGE",15,111)
V77M=V(4,"ACTTYPE_SKYFALL",16,112)
V78M=V(4,"ACTTYPE_LORD_TOUR",17,113)
V79M=V(4,"ACTTYPE_ENDLESS_MINILEVEL",18,114)
V80M=V(4,"ACTTYPE_ALLIANCEDUELBATTLE",19,115)
V81M=V(4,"ACTTYPE_PARTY_PERSON",20,116)
V82M=V(4,"ACTTYPE_PARTY_ALLIANCE",21,117)
V83M=V(4,"ACTTYPE_PERSONAL_POWER",22,301)
V84M=V(4,"ACTTYPE_PERSONAL_MAINCITY_LEVEL",23,302)
V85M=V(4,"ACTTYPE_PERSONAL_KILL_NUM",24,303)
V86M=V(4,"ACTTYPE_PERSONAL_MAX_PAL_POWER",25,304)
V87M=V(4,"ACTTYPE_PERSONAL_PASS_CHECKPOINT",26,305)
V88M=V(4,"ACTTYPE_PERSONAL_SELF_POWER",27,306)
V89M=V(4,"ACTTYPE_COMMON_MAX",28,401)
V90M=V(4,"ACTTYPE_ALLIANCE_POWER",29,501)
V91M=V(4,"ACTTYPE_ALLIANCE_KILLNUM",30,502)
V92M=V(4,"ACTTYPE_ZONE_BATTLE_PERSON_SCORE",31,601)
V93M=V(4,"ACTTYPE_ZONE_BATTLE_ALLIANCE_SCORE",32,602)
V94M=V(4,"ACTTYPE_ZONE_WORLD_BATTLE_TOTAL_SCORE",33,605)
V95M=V(4,"ACTTYPE_VIOLENT_MONSTER_LEADER_PERSON",34,620)
V96M=V(4,"ACTTYPE_VIOLENT_MONSTER_LEADER_ALLIANCE",35,621)
V97M=V(4,"ACTTYPE_VIOLENT_MONSTER_LEADER_TYPE_DAMAGE",36,622)
E6M=E(3,"eRankActivityType",".CSMsg.eRankActivityType")
V98M=V(4,"enActivityCommonLotteryType_LuckyDraw",0,1007)
V99M=V(4,"enActivityCommonLotteryType_MiracleBox",1,1020)
E7M=E(3,"ActivityCommonLotteryType",".CSMsg.ActivityCommonLotteryType")
V100M=V(4,"SwapOrderStatus_Idle",0,1)
V101M=V(4,"SwapOrderStatus_Transacted",1,2)
V102M=V(4,"SwapOrderStatus_Finish",2,3)
V103M=V(4,"SwapOrderStatus_Cancel",3,4)
V104M=V(4,"SwapOrderStatus_Transacting",4,5)
V105M=V(4,"SwapOrderStatus_Recycled",5,6)
E8M=E(3,"SwapOrderStatusType",".CSMsg.SwapOrderStatusType")
V106M=V(4,"emZombieApocalypseScheduleState_None",0,0)
V107M=V(4,"emZombieApocalypseScheduleState_AppointmentOpen",1,1)
V108M=V(4,"emZombieApocalypseScheduleState_Running",2,2)
V109M=V(4,"emZombieApocalypseScheduleState_CoolDowm",3,3)
E9M=E(3,"emZombieApocalypseScheduleState",".CSMsg.emZombieApocalypseScheduleState")
V110M=V(4,"emZombieApocalypseScheduleRunningState_None",0,0)
V111M=V(4,"emZombieApocalypseScheduleRunningState_Prepare",1,1)
V112M=V(4,"emZombieApocalypseScheduleRunningState_Attack",2,2)
E10M=E(3,"emZombieApocalypseScheduleRunningState",".CSMsg.emZombieApocalypseScheduleRunningState")
V113M=V(4,"CommunityGiftStatus_Init",0,0)
V114M=V(4,"CommunityGiftStatus_Reward",1,1)
V115M=V(4,"CommunityGiftStatus_Finish",2,2)
E11M=E(3,"CommunityGiftStatus",".CSMsg.CommunityGiftStatus")
V116M=V(4,"AtyNoticePeriord_StrongestCommander",0,121)
V117M=V(4,"AtyNoticePeriord_ZombieApocalypse",1,220)
E12M=E(3,"AtyNoticePeriord",".CSMsg.AtyNoticePeriord")
F1D=F(2,"itemID",".CSMsg.MopUpReward.itemID",1,0,2,false,0,5,1)
F2D=F(2,"itemNum",".CSMsg.MopUpReward.itemNum",2,1,2,false,0,5,1)
M1G=D(1,"MopUpReward",".CSMsg.MopUpReward",false,{},{},nil,{})
F3D=F(2,"index",".CSMsg.TBrokenSTMonster.index",1,0,2,false,0,5,1)
F4D=F(2,"hpMul",".CSMsg.TBrokenSTMonster.hpMul",2,1,2,false,0,5,1)
F5D=F(2,"hpMod",".CSMsg.TBrokenSTMonster.hpMod",3,2,2,false,0,5,1)
F6D=F(2,"maxHpMul",".CSMsg.TBrokenSTMonster.maxHpMul",4,3,2,false,0,5,1)
F7D=F(2,"maxHpMod",".CSMsg.TBrokenSTMonster.maxHpMod",5,4,2,false,0,5,1)
F8D=F(2,"energy",".CSMsg.TBrokenSTMonster.energy",6,5,2,false,0,5,1)
M2G=D(1,"TBrokenSTMonster",".CSMsg.TBrokenSTMonster",false,{},{},nil,{})
F9D=F(2,"mopUpErr",".CSMsg.TMSG_BROKENST_MOPUP_REWARD_NTF.mopUpErr",1,0,2,false,nil,14,8)
F10D=F(2,"bType",".CSMsg.TMSG_BROKENST_MOPUP_REWARD_NTF.bType",2,1,2,false,nil,14,8)
F11D=F(2,"nStage",".CSMsg.TMSG_BROKENST_MOPUP_REWARD_NTF.nStage",3,2,2,false,0,5,1)
F12D=F(2,"remainHP",".CSMsg.TMSG_BROKENST_MOPUP_REWARD_NTF.remainHP",4,3,2,false,0,5,1)
F13D=F(2,"rewardInfo",".CSMsg.TMSG_BROKENST_MOPUP_REWARD_NTF.rewardInfo",5,4,3,false,{},11,10)
F14D=F(2,"battleType",".CSMsg.TMSG_BROKENST_MOPUP_REWARD_NTF.battleType",6,5,2,false,nil,14,8)
F15D=F(2,"teamID",".CSMsg.TMSG_BROKENST_MOPUP_REWARD_NTF.teamID",7,6,2,false,0,5,1)
F16D=F(2,"arrMonsters",".CSMsg.TMSG_BROKENST_MOPUP_REWARD_NTF.arrMonsters",8,7,3,false,{},11,10)
M3G=D(1,"TMSG_BROKENST_MOPUP_REWARD_NTF",".CSMsg.TMSG_BROKENST_MOPUP_REWARD_NTF",false,{},{},nil,{})
F17D=F(2,"badgesNum",".CSMsg.TMSG_BROKENST_UPDATA_BADGES_NTF.badgesNum",1,0,2,false,0,5,1)
M7G=D(1,"TMSG_BROKENST_UPDATA_BADGES_NTF",".CSMsg.TMSG_BROKENST_UPDATA_BADGES_NTF",false,{},{},nil,{})
F18D=F(2,"type",".CSMsg.TMSG_ACTIVITY_COMPLETE_NTF.type",1,0,2,false,0,5,1)
F19D=F(2,"ID",".CSMsg.TMSG_ACTIVITY_COMPLETE_NTF.ID",2,1,2,false,0,5,1)
F20D=F(2,"content",".CSMsg.TMSG_ACTIVITY_COMPLETE_NTF.content",3,2,2,false,0,5,1)
M8G=D(1,"TMSG_ACTIVITY_COMPLETE_NTF",".CSMsg.TMSG_ACTIVITY_COMPLETE_NTF",false,{},{},nil,{})
F21D=F(2,"isActived",".CSMsg.TMSG_ACTIVITY_GROW_GIFT2_STATE.isActived",1,0,2,false,false,8,7)
F22D=F(2,"timeValid",".CSMsg.TMSG_ACTIVITY_GROW_GIFT2_STATE.timeValid",2,1,2,false,0,13,3)
M9G=D(1,"TMSG_ACTIVITY_GROW_GIFT2_STATE",".CSMsg.TMSG_ACTIVITY_GROW_GIFT2_STATE",false,{},{},nil,{})
F23D=F(2,"lvID",".CSMsg.TMSG_XYX_PASS_LV_REQ.lvID",1,0,2,false,0,5,1)
F24D=F(2,"LevelType",".CSMsg.TMSG_XYX_PASS_LV_REQ.LevelType",2,1,2,false,0,5,1)
F25D=F(2,"unlockType",".CSMsg.TMSG_XYX_PASS_LV_REQ.unlockType",3,2,2,false,0,5,1)
F26D=F(2,"nGameType",".CSMsg.TMSG_XYX_PASS_LV_REQ.nGameType",4,3,2,false,0,5,1)
F27D=F(2,"nCollectionID",".CSMsg.TMSG_XYX_PASS_LV_REQ.nCollectionID",5,4,2,false,0,5,1)
F28D=F(2,"bDelayGot",".CSMsg.TMSG_XYX_PASS_LV_REQ.bDelayGot",6,5,1,false,false,8,7)
M10G=D(1,"TMSG_XYX_PASS_LV_REQ",".CSMsg.TMSG_XYX_PASS_LV_REQ",false,{},{},nil,{})
F29D=F(2,"errorcode",".CSMsg.TMSG_XYX_PASS_LV_RSP.errorcode",1,0,2,false,nil,14,8)
F30D=F(2,"lvID",".CSMsg.TMSG_XYX_PASS_LV_RSP.lvID",2,1,2,false,0,5,1)
F31D=F(2,"IsPass",".CSMsg.TMSG_XYX_PASS_LV_RSP.IsPass",3,2,2,false,0,5,1)
F32D=F(2,"ItemNums",".CSMsg.TMSG_XYX_PASS_LV_RSP.ItemNums",4,3,2,false,0,5,1)
F33D=F(2,"rewardIDs",".CSMsg.TMSG_XYX_PASS_LV_RSP.rewardIDs",5,4,3,false,{},5,1)
M11G=D(1,"TMSG_XYX_PASS_LV_RSP",".CSMsg.TMSG_XYX_PASS_LV_RSP",false,{},{},nil,{})
M12G=D(1,"TMSG_XYX_GOT_DELAYAWARD_REQ",".CSMsg.TMSG_XYX_GOT_DELAYAWARD_REQ",false,{},{},{},{})
F34D=F(2,"errorcode",".CSMsg.TMSG_XYX_GOT_DELAYAWARD_RSP.errorcode",1,0,2,false,nil,14,8)
F35D=F(2,"arrDelayRewardInfo",".CSMsg.TMSG_XYX_GOT_DELAYAWARD_RSP.arrDelayRewardInfo",2,1,3,false,{},11,10)
M13G=D(1,"TMSG_XYX_GOT_DELAYAWARD_RSP",".CSMsg.TMSG_XYX_GOT_DELAYAWARD_RSP",false,{},{},nil,{})
M15G=D(1,"TMSG_XYX_GOT_GAME_REWARD_REQ",".CSMsg.TMSG_XYX_GOT_GAME_REWARD_REQ",false,{},{},{},{})
F36D=F(2,"errorcode",".CSMsg.TMSG_XYX_GOT_GAME_REWARD_RSP.errorcode",1,0,2,false,nil,14,8)
F37D=F(2,"arrRewardInfo",".CSMsg.TMSG_XYX_GOT_GAME_REWARD_RSP.arrRewardInfo",2,1,3,false,{},11,10)
M16G=D(1,"TMSG_XYX_GOT_GAME_REWARD_RSP",".CSMsg.TMSG_XYX_GOT_GAME_REWARD_RSP",false,{},{},nil,{})
F38D=F(2,"nGameType",".CSMsg.TMSG_XYX_SET_FAILSTAGE_REQ.nGameType",1,0,2,false,0,5,1)
F39D=F(2,"nStageID",".CSMsg.TMSG_XYX_SET_FAILSTAGE_REQ.nStageID",2,1,2,false,0,5,1)
M17G=D(1,"TMSG_XYX_SET_FAILSTAGE_REQ",".CSMsg.TMSG_XYX_SET_FAILSTAGE_REQ",false,{},{},nil,{})
F40D=F(2,"nGameType",".CSMsg.TMSG_XYX_SET_FAILSTAGE_RSP.nGameType",1,0,2,false,0,5,1)
F41D=F(2,"nStageID",".CSMsg.TMSG_XYX_SET_FAILSTAGE_RSP.nStageID",2,1,2,false,0,5,1)
F42D=F(2,"errorcode",".CSMsg.TMSG_XYX_SET_FAILSTAGE_RSP.errorcode",3,2,2,false,nil,14,8)
M18G=D(1,"TMSG_XYX_SET_FAILSTAGE_RSP",".CSMsg.TMSG_XYX_SET_FAILSTAGE_RSP",false,{},{},nil,{})
F43D=F(2,"arrHeroIDs",".CSMsg.TMSG_XYX_ENDLESS_SET_HEROPOWER_REQ.arrHeroIDs",1,0,3,false,{},5,1)
F44D=F(2,"nUnlockType",".CSMsg.TMSG_XYX_ENDLESS_SET_HEROPOWER_REQ.nUnlockType",2,1,2,false,0,5,1)
F45D=F(2,"nStartStageID",".CSMsg.TMSG_XYX_ENDLESS_SET_HEROPOWER_REQ.nStartStageID",3,2,2,false,0,5,1)
M19G=D(1,"TMSG_XYX_ENDLESS_SET_HEROPOWER_REQ",".CSMsg.TMSG_XYX_ENDLESS_SET_HEROPOWER_REQ",false,{},{},nil,{})
F46D=F(2,"nStartStageID",".CSMsg.TMSG_XYX_ENDLESS_SET_HEROPOWER_RSP.nStartStageID",1,0,2,false,0,5,1)
F47D=F(2,"nUnlockType",".CSMsg.TMSG_XYX_ENDLESS_SET_HEROPOWER_RSP.nUnlockType",2,1,2,false,0,5,1)
F48D=F(2,"errorcode",".CSMsg.TMSG_XYX_ENDLESS_SET_HEROPOWER_RSP.errorcode",3,2,2,false,nil,14,8)
M20G=D(1,"TMSG_XYX_ENDLESS_SET_HEROPOWER_RSP",".CSMsg.TMSG_XYX_ENDLESS_SET_HEROPOWER_RSP",false,{},{},nil,{})
M21G=D(1,"TMSG_XYX_GET_DATA_REQ",".CSMsg.TMSG_XYX_GET_DATA_REQ",false,{},{},{},{})
F49D=F(2,"arrGameType",".CSMsg.TMSG_XYX_INITGAMEDATA_REQ.arrGameType",1,0,3,false,{},5,1)
M22G=D(1,"TMSG_XYX_INITGAMEDATA_REQ",".CSMsg.TMSG_XYX_INITGAMEDATA_REQ",false,{},{},nil,{})
F50D=F(2,"arrGameType",".CSMsg.TMSG_XYX_INITGAMEDATA_RSP.arrGameType",1,0,3,false,{},5,1)
F51D=F(2,"errorcode",".CSMsg.TMSG_XYX_INITGAMEDATA_RSP.errorcode",2,1,2,false,nil,14,8)
M23G=D(1,"TMSG_XYX_INITGAMEDATA_RSP",".CSMsg.TMSG_XYX_INITGAMEDATA_RSP",false,{},{},nil,{})
F52D=F(2,"nGameCfgType",".CSMsg.TMSG_MINIGAME_ACTIVITY_JION_REQ.nGameCfgType",1,0,2,false,0,5,1)
F53D=F(2,"nGameCfgID",".CSMsg.TMSG_MINIGAME_ACTIVITY_JION_REQ.nGameCfgID",2,1,2,false,0,5,1)
F54D=F(2,"nActivityID",".CSMsg.TMSG_MINIGAME_ACTIVITY_JION_REQ.nActivityID",3,2,2,false,0,5,1)
M24G=D(1,"TMSG_MINIGAME_ACTIVITY_JION_REQ",".CSMsg.TMSG_MINIGAME_ACTIVITY_JION_REQ",false,{},{},nil,{})
F55D=F(2,"nGameCfgType",".CSMsg.TMSG_MINIGAME_ACTIVITY_JION_RSP.nGameCfgType",1,0,2,false,0,5,1)
F56D=F(2,"nGameCfgID",".CSMsg.TMSG_MINIGAME_ACTIVITY_JION_RSP.nGameCfgID",2,1,2,false,0,5,1)
F57D=F(2,"nActivityID",".CSMsg.TMSG_MINIGAME_ACTIVITY_JION_RSP.nActivityID",3,2,2,false,0,5,1)
F58D=F(2,"errorcode",".CSMsg.TMSG_MINIGAME_ACTIVITY_JION_RSP.errorcode",4,3,2,false,nil,14,8)
M25G=D(1,"TMSG_MINIGAME_ACTIVITY_JION_RSP",".CSMsg.TMSG_MINIGAME_ACTIVITY_JION_RSP",false,{},{},nil,{})
F59D=F(2,"data",".CSMsg.TMSG_XYX_SET_LINK_REQ.data",1,0,2,false,0,5,1)
M26G=D(1,"TMSG_XYX_SET_LINK_REQ",".CSMsg.TMSG_XYX_SET_LINK_REQ",false,{},{},nil,{})
F60D=F(2,"errorcode",".CSMsg.TMSG_XYX_SET_LINK_RSP.errorcode",1,0,2,false,nil,14,8)
M27G=D(1,"TMSG_XYX_SET_LINK_RSP",".CSMsg.TMSG_XYX_SET_LINK_RSP",false,{},{},nil,{})
F61D=F(2,"data",".CSMsg.TMSG_XYX_LINK_DATA_NTF.data",1,0,2,false,0,5,1)
M28G=D(1,"TMSG_XYX_LINK_DATA_NTF",".CSMsg.TMSG_XYX_LINK_DATA_NTF",false,{},{},nil,{})
F62D=F(2,"type",".CSMsg.TMSG_XYX_SET_LEVEL_REQ.type",1,0,2,false,0,5,1)
F63D=F(2,"data",".CSMsg.TMSG_XYX_SET_LEVEL_REQ.data",2,1,2,false,0,5,1)
M29G=D(1,"TMSG_XYX_SET_LEVEL_REQ",".CSMsg.TMSG_XYX_SET_LEVEL_REQ",false,{},{},nil,{})
F64D=F(2,"errorcode",".CSMsg.TMSG_XYX_SET_LEVEL_RSP.errorcode",1,0,2,false,nil,14,8)
M30G=D(1,"TMSG_XYX_SET_LEVEL_RSP",".CSMsg.TMSG_XYX_SET_LEVEL_RSP",false,{},{},nil,{})
F65D=F(2,"type",".CSMsg.TMSG_XYX_LEVEL_DATA_NTF.type",1,0,2,false,0,5,1)
F66D=F(2,"data",".CSMsg.TMSG_XYX_LEVEL_DATA_NTF.data",2,1,2,false,0,5,1)
M31G=D(1,"TMSG_XYX_LEVEL_DATA_NTF",".CSMsg.TMSG_XYX_LEVEL_DATA_NTF",false,{},{},nil,{})
F67D=F(2,"gametype",".CSMsg.TMSG_XYX_SET_PROP_REQ.gametype",1,0,2,false,"",9,9)
F68D=F(2,"key",".CSMsg.TMSG_XYX_SET_PROP_REQ.key",2,1,2,false,"",9,9)
F69D=F(2,"data",".CSMsg.TMSG_XYX_SET_PROP_REQ.data",3,2,2,false,"",9,9)
M32G=D(1,"TMSG_XYX_SET_PROP_REQ",".CSMsg.TMSG_XYX_SET_PROP_REQ",false,{},{},nil,{})
F70D=F(2,"errorcode",".CSMsg.TMSG_XYX_SET_PROP_RSP.errorcode",1,0,2,false,nil,14,8)
M33G=D(1,"TMSG_XYX_SET_PROP_RSP",".CSMsg.TMSG_XYX_SET_PROP_RSP",false,{},{},nil,{})
F71D=F(2,"gametype",".CSMsg.TMSG_XYX_GET_PROP_REQ.gametype",1,0,2,false,"",9,9)
F72D=F(2,"key",".CSMsg.TMSG_XYX_GET_PROP_REQ.key",2,1,2,false,"",9,9)
M34G=D(1,"TMSG_XYX_GET_PROP_REQ",".CSMsg.TMSG_XYX_GET_PROP_REQ",false,{},{},nil,{})
F73D=F(2,"errorcode",".CSMsg.TMSG_XYX_GET_PROP_RSP.errorcode",1,0,2,false,nil,14,8)
F74D=F(2,"gametype",".CSMsg.TMSG_XYX_GET_PROP_RSP.gametype",2,1,1,false,"",9,9)
F75D=F(2,"key",".CSMsg.TMSG_XYX_GET_PROP_RSP.key",3,2,1,false,"",9,9)
F76D=F(2,"data",".CSMsg.TMSG_XYX_GET_PROP_RSP.data",4,3,1,false,"",9,9)
M35G=D(1,"TMSG_XYX_GET_PROP_RSP",".CSMsg.TMSG_XYX_GET_PROP_RSP",false,{},{},nil,{})
F77D=F(2,"gametype",".CSMsg.TMSG_XYX_PROP_NTF.gametype",1,0,1,false,"",9,9)
F78D=F(2,"key",".CSMsg.TMSG_XYX_PROP_NTF.key",2,1,1,false,"",9,9)
F79D=F(2,"data",".CSMsg.TMSG_XYX_PROP_NTF.data",3,2,1,false,"",9,9)
M36G=D(1,"TMSG_XYX_PROP_NTF",".CSMsg.TMSG_XYX_PROP_NTF",false,{},{},nil,{})
F80D=F(2,"type",".CSMsg.TMSG_XYX_ITEMACTIVE_REQ.type",1,0,2,false,0,5,1)
M37G=D(1,"TMSG_XYX_ITEMACTIVE_REQ",".CSMsg.TMSG_XYX_ITEMACTIVE_REQ",false,{},{},nil,{})
F81D=F(2,"errorcode",".CSMsg.TMSG_XYX_ITEMACTIVE_RSP.errorcode",1,0,2,false,nil,14,8)
F82D=F(2,"type",".CSMsg.TMSG_XYX_ITEMACTIVE_RSP.type",2,1,2,false,0,5,1)
M38G=D(1,"TMSG_XYX_ITEMACTIVE_RSP",".CSMsg.TMSG_XYX_ITEMACTIVE_RSP",false,{},{},nil,{})
F83D=F(2,"lvID",".CSMsg.TMSG_XYX_ADVRW_REQ.lvID",1,0,2,false,0,5,1)
F84D=F(2,"LevelType",".CSMsg.TMSG_XYX_ADVRW_REQ.LevelType",2,1,2,false,0,5,1)
F85D=F(2,"unlockType",".CSMsg.TMSG_XYX_ADVRW_REQ.unlockType",3,2,2,false,0,5,1)
M39G=D(1,"TMSG_XYX_ADVRW_REQ",".CSMsg.TMSG_XYX_ADVRW_REQ",false,{},{},nil,{})
F86D=F(2,"errorcode",".CSMsg.TMSG_XYX_ADVRW_RSP.errorcode",1,0,2,false,nil,14,8)
F87D=F(2,"lvID",".CSMsg.TMSG_XYX_ADVRW_RSP.lvID",2,1,2,false,0,5,1)
M40G=D(1,"TMSG_XYX_ADVRW_RSP",".CSMsg.TMSG_XYX_ADVRW_RSP",false,{},{},nil,{})
F88D=F(2,"chapterid",".CSMsg.TMSG_XYX_STAGE_REWARD_REQ.chapterid",1,0,2,false,0,5,1)
F89D=F(2,"stageNum",".CSMsg.TMSG_XYX_STAGE_REWARD_REQ.stageNum",2,1,2,false,0,5,1)
F90D=F(2,"ChaptersNum",".CSMsg.TMSG_XYX_STAGE_REWARD_REQ.ChaptersNum",3,2,2,false,0,5,1)
F91D=F(2,"nCollectionID",".CSMsg.TMSG_XYX_STAGE_REWARD_REQ.nCollectionID",4,3,1,false,0,5,1)
F92D=F(2,"nStageIndex",".CSMsg.TMSG_XYX_STAGE_REWARD_REQ.nStageIndex",5,4,1,false,0,5,1)
F93D=F(2,"nGameType",".CSMsg.TMSG_XYX_STAGE_REWARD_REQ.nGameType",6,5,1,false,0,5,1)
M41G=D(1,"TMSG_XYX_STAGE_REWARD_REQ",".CSMsg.TMSG_XYX_STAGE_REWARD_REQ",false,{},{},nil,{})
F94D=F(2,"chapterid",".CSMsg.TMSG_XYX_STAGE_REWARD_RSP.chapterid",1,0,2,false,0,5,1)
F95D=F(2,"stageNum",".CSMsg.TMSG_XYX_STAGE_REWARD_RSP.stageNum",2,1,2,false,0,5,1)
F96D=F(2,"errorcode",".CSMsg.TMSG_XYX_STAGE_REWARD_RSP.errorcode",3,2,2,false,nil,14,8)
M42G=D(1,"TMSG_XYX_STAGE_REWARD_RSP",".CSMsg.TMSG_XYX_STAGE_REWARD_RSP",false,{},{},nil,{})
M43G=D(1,"TMSG_XYX_LEVEL_REQ",".CSMsg.TMSG_XYX_LEVEL_REQ",false,{},{},{},{})
F97D=F(2,"errorcode",".CSMsg.TMSG_XYX_LEVEL_RSP.errorcode",1,0,2,false,nil,14,8)
F98D=F(2,"lv",".CSMsg.TMSG_XYX_LEVEL_RSP.lv",2,1,2,false,0,5,1)
M44G=D(1,"TMSG_XYX_LEVEL_RSP",".CSMsg.TMSG_XYX_LEVEL_RSP",false,{},{},nil,{})
M45G=D(1,"TMSG_FACEBOOK_GUIDE_REQ",".CSMsg.TMSG_FACEBOOK_GUIDE_REQ",false,{},{},{},{})
F99D=F(2,"errorcode",".CSMsg.TMSG_FACEBOOK_GUIDE_RSP.errorcode",1,0,2,false,nil,14,8)
M46G=D(1,"TMSG_FACEBOOK_GUIDE_RSP",".CSMsg.TMSG_FACEBOOK_GUIDE_RSP",false,{},{},nil,{})
F100D=F(2,"sellTimes",".CSMsg.oneBookinfo.sellTimes",1,0,2,false,0,5,1)
F101D=F(2,"sellType",".CSMsg.oneBookinfo.sellType",2,1,2,false,0,5,1)
F102D=F(2,"inCome",".CSMsg.oneBookinfo.inCome",3,2,2,false,0,5,1)
F103D=F(2,"carrotNums",".CSMsg.oneBookinfo.carrotNums",4,3,1,false,0,5,1)
F104D=F(2,"jadeNums",".CSMsg.oneBookinfo.jadeNums",5,4,1,false,0,5,1)
M47G=D(1,"oneBookinfo",".CSMsg.oneBookinfo",false,{},{},nil,{})
F105D=F(2,"oneInfo",".CSMsg.oneDayBookinfo.oneInfo",1,0,3,false,{},11,10)
M48G=D(1,"oneDayBookinfo",".CSMsg.oneDayBookinfo",false,{},{},nil,{})
F106D=F(2,"TodayIncome",".CSMsg.accountBookinfo.TodayIncome",1,0,2,false,0,5,1)
F107D=F(2,"oneDayinfo",".CSMsg.accountBookinfo.oneDayinfo",2,1,3,false,{},11,10)
M49G=D(1,"accountBookinfo",".CSMsg.accountBookinfo",false,{},{},nil,{})
F108D=F(2,"day",".CSMsg.TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_REQ.day",1,0,1,false,0,5,1)
M50G=D(1,"TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_REQ",".CSMsg.TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_REQ",false,{},{},nil,{})
F109D=F(2,"errCode",".CSMsg.TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP.errCode",1,0,2,false,0,5,1)
F110D=F(2,"bookInfo",".CSMsg.TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP.bookInfo",2,1,3,false,{},11,10)
F111D=F(2,"day",".CSMsg.TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP.day",3,2,1,false,0,5,1)
F112D=F(2,"total",".CSMsg.TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP.total",4,3,1,false,0,5,1)
M51G=D(1,"TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP",".CSMsg.TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP",false,{},{},nil,{})
F113D=F(2,"chapterid",".CSMsg.StageRewardReqInfo.chapterid",1,0,2,false,0,5,1)
F114D=F(2,"stageNum",".CSMsg.StageRewardReqInfo.stageNum",2,1,2,false,0,5,1)
F115D=F(2,"ChaptersNum",".CSMsg.StageRewardReqInfo.ChaptersNum",3,2,2,false,0,5,1)
F116D=F(2,"bGetAdsReward",".CSMsg.StageRewardReqInfo.bGetAdsReward",4,3,1,false,false,8,7)
F117D=F(2,"nCollectionID",".CSMsg.StageRewardReqInfo.nCollectionID",5,4,1,false,0,5,1)
F118D=F(2,"nStageIndex",".CSMsg.StageRewardReqInfo.nStageIndex",6,5,1,false,0,5,1)
F119D=F(2,"nGameType",".CSMsg.StageRewardReqInfo.nGameType",7,6,1,false,0,5,1)
M52G=D(1,"StageRewardReqInfo",".CSMsg.StageRewardReqInfo",false,{},{},nil,{})
F120D=F(2,"reqInfo",".CSMsg.TMSG_XYX_STAGE_REWARD_MULTI_REQ.reqInfo",1,0,3,false,{},11,10)
M53G=D(1,"TMSG_XYX_STAGE_REWARD_MULTI_REQ",".CSMsg.TMSG_XYX_STAGE_REWARD_MULTI_REQ",false,{},{},nil,{})
F121D=F(2,"rspInfo",".CSMsg.TMSG_XYX_STAGE_REWARD_MULTI_RSP.rspInfo",1,0,3,false,{},11,10)
F122D=F(2,"errorcode",".CSMsg.TMSG_XYX_STAGE_REWARD_MULTI_RSP.errorcode",2,1,2,false,nil,14,8)
M54G=D(1,"TMSG_XYX_STAGE_REWARD_MULTI_RSP",".CSMsg.TMSG_XYX_STAGE_REWARD_MULTI_RSP",false,{},{},nil,{})
F123D=F(2,"rank",".CSMsg.CommActRankinfo.rank",1,0,2,false,0,5,1)
F124D=F(2,"dbid",".CSMsg.CommActRankinfo.dbid",2,1,2,false,0,5,1)
F125D=F(2,"score",".CSMsg.CommActRankinfo.score",3,2,2,false,0,3,2)
F126D=F(2,"roleLv",".CSMsg.CommActRankinfo.roleLv",4,3,1,false,0,13,3)
F127D=F(2,"faceID",".CSMsg.CommActRankinfo.faceID",5,4,1,false,0,13,3)
F128D=F(2,"frameID",".CSMsg.CommActRankinfo.frameID",6,5,1,false,0,13,3)
F129D=F(2,"sex",".CSMsg.CommActRankinfo.sex",7,6,1,false,0,13,3)
F130D=F(2,"name",".CSMsg.CommActRankinfo.name",8,7,1,false,"",9,9)
F131D=F(2,"worldId",".CSMsg.CommActRankinfo.worldId",9,8,1,false,0,13,3)
F132D=F(2,"leagueid",".CSMsg.CommActRankinfo.leagueid",10,9,1,false,0,5,1)
F133D=F(2,"leagueFlag",".CSMsg.CommActRankinfo.leagueFlag",11,10,1,false,0,5,1)
F134D=F(2,"leagueName",".CSMsg.CommActRankinfo.leagueName",12,11,1,false,"",9,9)
F135D=F(2,"leagueShortName",".CSMsg.CommActRankinfo.leagueShortName",13,12,1,false,"",9,9)
F136D=F(2,"extradata",".CSMsg.CommActRankinfo.extradata",14,13,1,false,"",9,9)
F137D=F(2,"ceoname",".CSMsg.CommActRankinfo.ceoname",15,14,1,false,"",9,9)
F138D=F(2,"faceStr",".CSMsg.CommActRankinfo.faceStr",16,15,1,false,"",9,9)
M55G=D(1,"CommActRankinfo",".CSMsg.CommActRankinfo",false,{},{},nil,{})
F139D=F(2,"actRankType",".CSMsg.TMSG_COMM_ACTIVITY_RANK_REQ.actRankType",1,0,2,false,nil,14,8)
F140D=F(2,"actid",".CSMsg.TMSG_COMM_ACTIVITY_RANK_REQ.actid",2,1,1,false,0,5,1)
F141D=F(2,"themeId",".CSMsg.TMSG_COMM_ACTIVITY_RANK_REQ.themeId",3,2,1,false,0,5,1)
F142D=F(2,"toWorldId",".CSMsg.TMSG_COMM_ACTIVITY_RANK_REQ.toWorldId",4,3,1,false,0,5,1)
F143D=F(2,"paramInt",".CSMsg.TMSG_COMM_ACTIVITY_RANK_REQ.paramInt",5,4,3,false,{},5,1)
F144D=F(2,"paramStr",".CSMsg.TMSG_COMM_ACTIVITY_RANK_REQ.paramStr",6,5,3,false,{},9,9)
M56G=D(1,"TMSG_COMM_ACTIVITY_RANK_REQ",".CSMsg.TMSG_COMM_ACTIVITY_RANK_REQ",false,{},{},nil,{})
F145D=F(2,"errCode",".CSMsg.TMSG_COMM_ACTIVITY_RANK_RSP.errCode",1,0,2,false,0,5,1)
F146D=F(2,"selfRank",".CSMsg.TMSG_COMM_ACTIVITY_RANK_RSP.selfRank",2,1,1,false,nil,11,10)
F147D=F(2,"rankInfo",".CSMsg.TMSG_COMM_ACTIVITY_RANK_RSP.rankInfo",3,2,3,false,{},11,10)
F148D=F(2,"actRankType",".CSMsg.TMSG_COMM_ACTIVITY_RANK_RSP.actRankType",4,3,1,false,nil,14,8)
F149D=F(2,"pumpkinInfos",".CSMsg.TMSG_COMM_ACTIVITY_RANK_RSP.pumpkinInfos",5,4,3,false,{},11,10)
F150D=F(2,"themeId",".CSMsg.TMSG_COMM_ACTIVITY_RANK_RSP.themeId",6,5,1,false,0,5,1)
M58G=D(1,"TMSG_COMM_ACTIVITY_RANK_RSP",".CSMsg.TMSG_COMM_ACTIVITY_RANK_RSP",false,{},{},nil,{})
F151D=F(2,"index",".CSMsg.TXyxPassLvData.index",1,0,2,false,0,5,1)
F152D=F(2,"bitFlag",".CSMsg.TXyxPassLvData.bitFlag",2,1,2,false,0,5,1)
M60G=D(1,"TXyxPassLvData",".CSMsg.TXyxPassLvData",false,{},{},nil,{})
F153D=F(2,"index",".CSMsg.TPbXyxStageRewardData.index",1,0,2,false,0,5,1)
F154D=F(2,"bitFlag",".CSMsg.TPbXyxStageRewardData.bitFlag",2,1,2,false,0,5,1)
M61G=D(1,"TPbXyxStageRewardData",".CSMsg.TPbXyxStageRewardData",false,{},{},nil,{})
F155D=F(2,"gameType",".CSMsg.TPbXyxPassLvPartData.gameType",1,0,2,false,0,5,1)
F156D=F(2,"passlvdata",".CSMsg.TPbXyxPassLvPartData.passlvdata",2,1,3,false,{},11,10)
F157D=F(2,"nFailStageID",".CSMsg.TPbXyxPassLvPartData.nFailStageID",3,2,1,false,0,5,1)
M62G=D(1,"TPbXyxPassLvPartData",".CSMsg.TPbXyxPassLvPartData",false,{},{},nil,{})
F158D=F(2,"arrPassLvData",".CSMsg.TBXyxBasePartData.arrPassLvData",1,0,3,false,{},11,10)
F159D=F(2,"stageReward",".CSMsg.TBXyxBasePartData.stageReward",2,1,3,false,{},11,10)
F160D=F(2,"nCurGameID",".CSMsg.TBXyxBasePartData.nCurGameID",3,2,2,false,0,5,1)
F161D=F(2,"bMergeGame",".CSMsg.TBXyxBasePartData.bMergeGame",4,3,2,false,0,5,1)
F162D=F(2,"sUnlockGameID",".CSMsg.TBXyxBasePartData.sUnlockGameID",5,4,1,false,"",9,9)
F163D=F(2,"nCollectionID",".CSMsg.TBXyxBasePartData.nCollectionID",6,5,2,false,0,5,1)
F164D=F(2,"nTotalPassNum",".CSMsg.TBXyxBasePartData.nTotalPassNum",7,6,2,false,0,5,1)
F165D=F(2,"firstSelectID",".CSMsg.TBXyxBasePartData.firstSelectID",8,7,1,false,0,5,1)
F166D=F(2,"nLastSingleID",".CSMsg.TBXyxBasePartData.nLastSingleID",9,8,1,false,0,5,1)
F167D=F(2,"bTurnToSingle",".CSMsg.TBXyxBasePartData.bTurnToSingle",10,9,1,false,0,5,1)
F168D=F(2,"arrDelayRewardInfo",".CSMsg.TBXyxBasePartData.arrDelayRewardInfo",11,10,3,false,{},11,10)
F169D=F(2,"arrWaitToGetRewardInfo",".CSMsg.TBXyxBasePartData.arrWaitToGetRewardInfo",12,11,3,false,{},11,10)
M63G=D(1,"TBXyxBasePartData",".CSMsg.TBXyxBasePartData",false,{},{},nil,{})
F170D=F(2,"data",".CSMsg.TMSG_XYX_DATA_NTF.data",1,0,2,false,"",12,9)
M64G=D(1,"TMSG_XYX_DATA_NTF",".CSMsg.TMSG_XYX_DATA_NTF",false,{},{},nil,{})
M65G=D(1,"TMSG_RESERVATION_EMAIL_BIND_REQ",".CSMsg.TMSG_RESERVATION_EMAIL_BIND_REQ",false,{},{},{},{})
M66G=D(1,"TMSG_RESERVATION_EMAIL_UNBIND_REQ",".CSMsg.TMSG_RESERVATION_EMAIL_UNBIND_REQ",false,{},{},{},{})
M67G=D(1,"TMSG_RESERVATION_EMAIL_SENDREWARD_REQ",".CSMsg.TMSG_RESERVATION_EMAIL_SENDREWARD_REQ",false,{},{},{},{})
F171D=F(2,"errorcode",".CSMsg.TMSG_RESERVATION_EMAIL_SENDREWARD_RSP.errorcode",1,0,2,false,nil,14,8)
M68G=D(1,"TMSG_RESERVATION_EMAIL_SENDREWARD_RSP",".CSMsg.TMSG_RESERVATION_EMAIL_SENDREWARD_RSP",false,{},{},nil,{})
M69G=D(1,"TMSG_RESERVATION_EMAIL_BGETREWARD_REQ",".CSMsg.TMSG_RESERVATION_EMAIL_BGETREWARD_REQ",false,{},{},{},{})
F172D=F(2,"bGetRewards",".CSMsg.TMSG_RESERVATION_EMAIL_BGETREWARD_RSP.bGetRewards",1,0,2,false,0,5,1)
F173D=F(2,"errorcode",".CSMsg.TMSG_RESERVATION_EMAIL_BGETREWARD_RSP.errorcode",2,1,2,false,nil,14,8)
M70G=D(1,"TMSG_RESERVATION_EMAIL_BGETREWARD_RSP",".CSMsg.TMSG_RESERVATION_EMAIL_BGETREWARD_RSP",false,{},{},nil,{})
F174D=F(2,"nGatherID",".CSMsg.TMSG_GATHERING_AWARD_REQ.nGatherID",1,0,2,false,0,5,1)
F175D=F(2,"termID",".CSMsg.TMSG_GATHERING_AWARD_REQ.termID",2,1,2,false,0,5,1)
M71G=D(1,"TMSG_GATHERING_AWARD_REQ",".CSMsg.TMSG_GATHERING_AWARD_REQ",false,{},{},nil,{})
F176D=F(2,"errorcode",".CSMsg.TMSG_GATHERING_AWARD_RSP.errorcode",1,0,2,false,nil,14,8)
F177D=F(2,"uRewardId",".CSMsg.TMSG_GATHERING_AWARD_RSP.uRewardId",2,1,1,false,0,5,1)
M72G=D(1,"TMSG_GATHERING_AWARD_RSP",".CSMsg.TMSG_GATHERING_AWARD_RSP",false,{},{},nil,{})
F178D=F(2,"nGatherID",".CSMsg.TMSG_GATHERING_NTF.nGatherID",1,0,2,false,0,5,1)
F179D=F(2,"nTermID",".CSMsg.TMSG_GATHERING_NTF.nTermID",2,1,2,false,0,5,1)
F180D=F(2,"nEndTime",".CSMsg.TMSG_GATHERING_NTF.nEndTime",3,2,2,false,0,5,1)
F181D=F(2,"nState",".CSMsg.TMSG_GATHERING_NTF.nState",4,3,2,false,0,5,1)
F182D=F(2,"nCnt",".CSMsg.TMSG_GATHERING_NTF.nCnt",5,4,2,false,0,5,1)
F183D=F(2,"nLastMassTime",".CSMsg.TMSG_GATHERING_NTF.nLastMassTime",6,5,2,false,0,5,1)
F184D=F(2,"nJoinMassCnt",".CSMsg.TMSG_GATHERING_NTF.nJoinMassCnt",7,6,2,false,0,5,1)
F185D=F(2,"nLastJoinMassTime",".CSMsg.TMSG_GATHERING_NTF.nLastJoinMassTime",8,7,2,false,0,5,1)
M73G=D(1,"TMSG_GATHERING_NTF",".CSMsg.TMSG_GATHERING_NTF",false,{},{},nil,{})
F186D=F(2,"playerName",".CSMsg.TGatheringChatPushinfo.playerName",1,0,1,false,"",9,9)
F187D=F(2,"killMonsterNum",".CSMsg.TGatheringChatPushinfo.killMonsterNum",2,1,1,false,0,5,1)
F188D=F(2,"rewardID",".CSMsg.TGatheringChatPushinfo.rewardID",3,2,1,false,0,5,1)
F189D=F(2,"rewardNum",".CSMsg.TGatheringChatPushinfo.rewardNum",4,3,1,false,0,5,1)
F190D=F(2,"rewardType",".CSMsg.TGatheringChatPushinfo.rewardType",5,4,1,false,0,5,1)
M74G=D(1,"TGatheringChatPushinfo",".CSMsg.TGatheringChatPushinfo",false,{},{},nil,{})
F191D=F(2,"bossid",".CSMsg.WordBossData.bossid",1,0,2,false,0,5,1)
F192D=F(2,"bornstamp",".CSMsg.WordBossData.bornstamp",2,1,2,false,0,5,1)
F193D=F(2,"deathstamp",".CSMsg.WordBossData.deathstamp",3,2,2,false,0,5,1)
F194D=F(2,"position",".CSMsg.WordBossData.position",4,3,1,false,nil,11,10)
M75G=D(1,"WordBossData",".CSMsg.WordBossData",false,{},{},nil,{})
M77G=D(1,"TMSG_WORDBOSS_INFO_REQ",".CSMsg.TMSG_WORDBOSS_INFO_REQ",false,{},{},{},{})
F195D=F(2,"actid",".CSMsg.TMSG_WORDBOSS_INFO_NTF.actid",1,0,2,false,0,5,1)
F196D=F(2,"boss",".CSMsg.TMSG_WORDBOSS_INFO_NTF.boss",2,1,1,false,nil,11,10)
F197D=F(2,"attackcnt",".CSMsg.TMSG_WORDBOSS_INFO_NTF.attackcnt",3,2,1,false,0,5,1)
F198D=F(2,"remaincnt",".CSMsg.TMSG_WORDBOSS_INFO_NTF.remaincnt",4,3,1,false,0,5,1)
F199D=F(2,"achieve",".CSMsg.TMSG_WORDBOSS_INFO_NTF.achieve",5,4,1,false,0,4,4)
F200D=F(2,"worldDateInfo",".CSMsg.TMSG_WORDBOSS_INFO_NTF.worldDateInfo",6,5,3,false,{},11,10)
M78G=D(1,"TMSG_WORDBOSS_INFO_NTF",".CSMsg.TMSG_WORDBOSS_INFO_NTF",false,{},{},nil,{})
F201D=F(2,"worldId",".CSMsg.WorldDate.worldId",1,0,2,false,0,5,1)
F202D=F(2,"totalDamage",".CSMsg.WorldDate.totalDamage",2,1,1,false,0,3,2)
M79G=D(1,"WorldDate",".CSMsg.WorldDate",false,{},{},nil,{})
F203D=F(2,"id",".CSMsg.WordBossHero.id",1,0,2,false,0,5,1)
F204D=F(2,"lv",".CSMsg.WordBossHero.lv",2,1,2,false,0,5,1)
F205D=F(2,"star",".CSMsg.WordBossHero.star",3,2,2,false,0,5,1)
M80G=D(1,"WordBossHero",".CSMsg.WordBossHero",false,{},{},nil,{})
F206D=F(2,"rank",".CSMsg.TMSG_WORDBOSS_LINE_INFO_REQ.rank",1,0,2,false,0,5,1)
M81G=D(1,"TMSG_WORDBOSS_LINE_INFO_REQ",".CSMsg.TMSG_WORDBOSS_LINE_INFO_REQ",false,{},{},nil,{})
F207D=F(2,"rank",".CSMsg.TMSG_WORDBOSS_LINE_INFO_RSP.rank",1,0,2,false,0,5,1)
F208D=F(2,"dbid",".CSMsg.TMSG_WORDBOSS_LINE_INFO_RSP.dbid",2,1,2,false,0,5,1)
F209D=F(2,"power",".CSMsg.TMSG_WORDBOSS_LINE_INFO_RSP.power",3,2,2,false,0,5,1)
F210D=F(2,"heros",".CSMsg.TMSG_WORDBOSS_LINE_INFO_RSP.heros",4,3,3,false,{},11,10)
M82G=D(1,"TMSG_WORDBOSS_LINE_INFO_RSP",".CSMsg.TMSG_WORDBOSS_LINE_INFO_RSP",false,{},{},nil,{})
F211D=F(2,"bossid",".CSMsg.TMSG_WORDBOSS_DMGRECORD_NTF.bossid",1,0,2,false,0,5,1)
F212D=F(2,"damage",".CSMsg.TMSG_WORDBOSS_DMGRECORD_NTF.damage",2,1,2,false,0,5,1)
F213D=F(2,"rankidx",".CSMsg.TMSG_WORDBOSS_DMGRECORD_NTF.rankidx",3,2,2,false,0,5,1)
M83G=D(1,"TMSG_WORDBOSS_DMGRECORD_NTF",".CSMsg.TMSG_WORDBOSS_DMGRECORD_NTF",false,{},{},nil,{})
F214D=F(2,"errorcode",".CSMsg.TMSG_WORDBOSS_ERR_NTF.errorcode",1,0,1,false,nil,14,8)
M84G=D(1,"TMSG_WORDBOSS_ERR_NTF",".CSMsg.TMSG_WORDBOSS_ERR_NTF",false,{},{},nil,{})
F215D=F(2,"AtyID",".CSMsg.TMSG_FULLBATTLE_EXCHANGE_REQ.AtyID",1,0,2,false,0,5,1)
F216D=F(2,"ContentID",".CSMsg.TMSG_FULLBATTLE_EXCHANGE_REQ.ContentID",2,1,2,false,0,5,1)
F217D=F(2,"ExchangeNum",".CSMsg.TMSG_FULLBATTLE_EXCHANGE_REQ.ExchangeNum",3,2,2,false,0,5,1)
M85G=D(1,"TMSG_FULLBATTLE_EXCHANGE_REQ",".CSMsg.TMSG_FULLBATTLE_EXCHANGE_REQ",false,{},{},nil,{})
F218D=F(2,"errorcode",".CSMsg.TMSG_FULLBATTLE_EXCHANGE_RSP.errorcode",1,0,2,false,nil,14,8)
F219D=F(2,"AtyID",".CSMsg.TMSG_FULLBATTLE_EXCHANGE_RSP.AtyID",2,1,1,false,0,5,1)
F220D=F(2,"ContentID",".CSMsg.TMSG_FULLBATTLE_EXCHANGE_RSP.ContentID",3,2,1,false,0,5,1)
F221D=F(2,"RewardIDs",".CSMsg.TMSG_FULLBATTLE_EXCHANGE_RSP.RewardIDs",4,3,1,false,0,5,1)
F222D=F(2,"ExchangeNum",".CSMsg.TMSG_FULLBATTLE_EXCHANGE_RSP.ExchangeNum",5,4,1,false,0,5,1)
M86G=D(1,"TMSG_FULLBATTLE_EXCHANGE_RSP",".CSMsg.TMSG_FULLBATTLE_EXCHANGE_RSP",false,{},{},nil,{})
F223D=F(2,"ActivityContentId",".CSMsg.FullBattle_Goods.ActivityContentId",1,0,2,false,0,13,3)
F224D=F(2,"goodsNum",".CSMsg.FullBattle_Goods.goodsNum",2,1,2,false,0,13,3)
M87G=D(1,"FullBattle_Goods",".CSMsg.FullBattle_Goods",false,{},{},nil,{})
F225D=F(2,"ContentID",".CSMsg.ExchangeGoodInfo.ContentID",1,0,2,false,0,5,1)
F226D=F(2,"ExchangeNum",".CSMsg.ExchangeGoodInfo.ExchangeNum",2,1,2,false,0,5,1)
F227D=F(2,"residueNum",".CSMsg.ExchangeGoodInfo.residueNum",3,2,2,false,0,5,1)
M88G=D(1,"ExchangeGoodInfo",".CSMsg.ExchangeGoodInfo",false,{},{},nil,{})
F228D=F(2,"AtyID",".CSMsg.TMSG_EXCHANGE_SHOP_INFO_NTF.AtyID",1,0,2,false,0,5,1)
F229D=F(2,"exchangeInfos",".CSMsg.TMSG_EXCHANGE_SHOP_INFO_NTF.exchangeInfos",2,1,3,false,{},11,10)
M89G=D(1,"TMSG_EXCHANGE_SHOP_INFO_NTF",".CSMsg.TMSG_EXCHANGE_SHOP_INFO_NTF",false,{},{},nil,{})
F230D=F(2,"AtyID",".CSMsg.TMSG_EXCHANGE_SHOP_GETITEM_NTF.AtyID",1,0,2,false,0,5,1)
F231D=F(2,"ContentID",".CSMsg.TMSG_EXCHANGE_SHOP_GETITEM_NTF.ContentID",2,1,2,false,0,5,1)
F232D=F(2,"RechargeID",".CSMsg.TMSG_EXCHANGE_SHOP_GETITEM_NTF.RechargeID",3,2,2,false,0,5,1)
F233D=F(2,"FixRewardIDs",".CSMsg.TMSG_EXCHANGE_SHOP_GETITEM_NTF.FixRewardIDs",4,3,3,false,{},5,1)
F234D=F(2,"RandomRewardIDs",".CSMsg.TMSG_EXCHANGE_SHOP_GETITEM_NTF.RandomRewardIDs",5,4,3,false,{},5,1)
M90G=D(1,"TMSG_EXCHANGE_SHOP_GETITEM_NTF",".CSMsg.TMSG_EXCHANGE_SHOP_GETITEM_NTF",false,{},{},nil,{})
F235D=F(2,"AtyID",".CSMsg.TMSG_FULLBATTLE_REWARD_NTF.AtyID",1,0,2,false,0,5,1)
F236D=F(2,"goods",".CSMsg.TMSG_FULLBATTLE_REWARD_NTF.goods",2,1,3,false,{},11,10)
M91G=D(1,"TMSG_FULLBATTLE_REWARD_NTF",".CSMsg.TMSG_FULLBATTLE_REWARD_NTF",false,{},{},nil,{})
F237D=F(2,"indexId",".CSMsg.TMiracleBoxData.indexId",1,0,1,false,0,5,1)
F238D=F(2,"miracleboxId",".CSMsg.TMiracleBoxData.miracleboxId",2,1,1,false,0,5,1)
F239D=F(2,"rewardId",".CSMsg.TMiracleBoxData.rewardId",3,2,1,false,0,5,1)
M92G=D(1,"TMiracleBoxData",".CSMsg.TMiracleBoxData",false,{},{},nil,{})
F240D=F(2,"boxDatas",".CSMsg.TMiracleBoxDataList.boxDatas",1,0,3,false,{},11,10)
M93G=D(1,"TMiracleBoxDataList",".CSMsg.TMiracleBoxDataList",false,{},{},nil,{})
F241D=F(2,"AtyID",".CSMsg.TMSG_LUCKY_DRAW_LOTTERY_REQ.AtyID",1,0,2,false,0,5,1)
F242D=F(2,"iLotteryID",".CSMsg.TMSG_LUCKY_DRAW_LOTTERY_REQ.iLotteryID",2,1,2,false,0,5,1)
F243D=F(2,"iLotteryType",".CSMsg.TMSG_LUCKY_DRAW_LOTTERY_REQ.iLotteryType",3,2,2,false,0,5,1)
M94G=D(1,"TMSG_LUCKY_DRAW_LOTTERY_REQ",".CSMsg.TMSG_LUCKY_DRAW_LOTTERY_REQ",false,{},{},nil,{})
F244D=F(2,"errorcode",".CSMsg.TMSG_LUCKY_DRAW_LOTTERY_RSP.errorcode",1,0,2,false,nil,14,8)
F245D=F(2,"AtyID",".CSMsg.TMSG_LUCKY_DRAW_LOTTERY_RSP.AtyID",2,1,1,false,0,5,1)
F246D=F(2,"iLotteryID",".CSMsg.TMSG_LUCKY_DRAW_LOTTERY_RSP.iLotteryID",3,2,1,false,0,5,1)
F247D=F(2,"iLotteryType",".CSMsg.TMSG_LUCKY_DRAW_LOTTERY_RSP.iLotteryType",4,3,1,false,0,5,1)
F248D=F(2,"iRewardIds",".CSMsg.TMSG_LUCKY_DRAW_LOTTERY_RSP.iRewardIds",5,4,3,false,{},5,1)
F249D=F(2,"boxDataList",".CSMsg.TMSG_LUCKY_DRAW_LOTTERY_RSP.boxDataList",6,5,1,false,nil,11,10)
M95G=D(1,"TMSG_LUCKY_DRAW_LOTTERY_RSP",".CSMsg.TMSG_LUCKY_DRAW_LOTTERY_RSP",false,{},{},nil,{})
F250D=F(2,"AtyID",".CSMsg.TMSG_LUCKY_DRAW_DAILYGIFT_NTF.AtyID",1,0,1,false,0,5,1)
F251D=F(2,"lotteryNum",".CSMsg.TMSG_LUCKY_DRAW_DAILYGIFT_NTF.lotteryNum",2,1,1,false,0,5,1)
F252D=F(2,"alreadyNum",".CSMsg.TMSG_LUCKY_DRAW_DAILYGIFT_NTF.alreadyNum",3,2,1,false,0,5,1)
F253D=F(2,"boxDataList",".CSMsg.TMSG_LUCKY_DRAW_DAILYGIFT_NTF.boxDataList",4,3,1,false,nil,11,10)
F254D=F(2,"freeNum",".CSMsg.TMSG_LUCKY_DRAW_DAILYGIFT_NTF.freeNum",5,4,1,false,0,5,1)
M96G=D(1,"TMSG_LUCKY_DRAW_DAILYGIFT_NTF",".CSMsg.TMSG_LUCKY_DRAW_DAILYGIFT_NTF",false,{},{},nil,{})
F255D=F(2,"atyID",".CSMsg.TMSG_STRONGEST_COMMANDER_REQ.atyID",1,0,2,false,0,5,1)
F256D=F(2,"stage",".CSMsg.TMSG_STRONGEST_COMMANDER_REQ.stage",2,1,2,false,0,5,1)
M97G=D(1,"TMSG_STRONGEST_COMMANDER_REQ",".CSMsg.TMSG_STRONGEST_COMMANDER_REQ",false,{},{},nil,{})
F257D=F(2,"errorCode",".CSMsg.TMSG_STRONGEST_COMMANDER_RSP.errorCode",1,0,2,false,nil,14,8)
F258D=F(2,"atyID",".CSMsg.TMSG_STRONGEST_COMMANDER_RSP.atyID",2,1,1,false,0,5,1)
F259D=F(2,"stage",".CSMsg.TMSG_STRONGEST_COMMANDER_RSP.stage",3,2,1,false,0,5,1)
F260D=F(2,"endTime",".CSMsg.TMSG_STRONGEST_COMMANDER_RSP.endTime",4,3,1,false,0,5,1)
F261D=F(2,"rank",".CSMsg.TMSG_STRONGEST_COMMANDER_RSP.rank",5,4,1,false,0,5,1)
F262D=F(2,"integral",".CSMsg.TMSG_STRONGEST_COMMANDER_RSP.integral",6,5,1,false,0,5,1)
M98G=D(1,"TMSG_STRONGEST_COMMANDER_RSP",".CSMsg.TMSG_STRONGEST_COMMANDER_RSP",false,{},{},nil,{})
F263D=F(2,"atyID",".CSMsg.TMSG_STRONGEST_COMMANDER_NTF.atyID",1,0,2,false,0,5,1)
F264D=F(2,"todayStage",".CSMsg.TMSG_STRONGEST_COMMANDER_NTF.todayStage",2,1,2,false,0,5,1)
F265D=F(2,"integral",".CSMsg.TMSG_STRONGEST_COMMANDER_NTF.integral",3,2,1,false,0,5,1)
M99G=D(1,"TMSG_STRONGEST_COMMANDER_NTF",".CSMsg.TMSG_STRONGEST_COMMANDER_NTF",false,{},{},nil,{})
F266D=F(2,"atyID",".CSMsg.TNewActivityData.atyID",1,0,1,false,0,13,3)
F267D=F(2,"startTime",".CSMsg.TNewActivityData.startTime",2,1,1,false,0,13,3)
F268D=F(2,"endTime",".CSMsg.TNewActivityData.endTime",3,2,1,false,0,13,3)
F269D=F(2,"nextStartTime",".CSMsg.TNewActivityData.nextStartTime",4,3,1,false,0,13,3)
F270D=F(2,"nextEndTime",".CSMsg.TNewActivityData.nextEndTime",5,4,1,false,0,13,3)
F271D=F(2,"currentPeriod",".CSMsg.TNewActivityData.currentPeriod",6,5,1,false,0,13,3)
M100G=D(1,"TNewActivityData",".CSMsg.TNewActivityData",false,{},{},nil,{})
F272D=F(2,"headingCode",".CSMsg.TMSG_ACTIVITY_DATA_REQ.headingCode",1,0,3,false,{},5,1)
M101G=D(1,"TMSG_ACTIVITY_DATA_REQ",".CSMsg.TMSG_ACTIVITY_DATA_REQ",false,{},{},nil,{})
F273D=F(2,"errorCode",".CSMsg.TMSG_ACTIVITY_DATA_RSP.errorCode",1,0,1,false,0,5,1)
F274D=F(2,"datas",".CSMsg.TMSG_ACTIVITY_DATA_RSP.datas",2,1,3,false,{},11,10)
M102G=D(1,"TMSG_ACTIVITY_DATA_RSP",".CSMsg.TMSG_ACTIVITY_DATA_RSP",false,{},{},nil,{})
F275D=F(2,"AtyID",".CSMsg.TMSG_OPEN_MIRACLE_BOX_REQ.AtyID",1,0,2,false,0,5,1)
F276D=F(2,"indexId",".CSMsg.TMSG_OPEN_MIRACLE_BOX_REQ.indexId",2,1,2,false,0,5,1)
M103G=D(1,"TMSG_OPEN_MIRACLE_BOX_REQ",".CSMsg.TMSG_OPEN_MIRACLE_BOX_REQ",false,{},{},nil,{})
F277D=F(2,"errorcode",".CSMsg.TMSG_OPEN_MIRACLE_BOX_RSP.errorcode",1,0,2,false,nil,14,8)
F278D=F(2,"AtyID",".CSMsg.TMSG_OPEN_MIRACLE_BOX_RSP.AtyID",2,1,1,false,0,5,1)
F279D=F(2,"indexId",".CSMsg.TMSG_OPEN_MIRACLE_BOX_RSP.indexId",3,2,1,false,0,5,1)
F280D=F(2,"miracleboxId",".CSMsg.TMSG_OPEN_MIRACLE_BOX_RSP.miracleboxId",4,3,1,false,0,5,1)
F281D=F(2,"rewardId",".CSMsg.TMSG_OPEN_MIRACLE_BOX_RSP.rewardId",5,4,1,false,0,5,1)
F282D=F(2,"isDobule",".CSMsg.TMSG_OPEN_MIRACLE_BOX_RSP.isDobule",6,5,1,false,0,5,1)
M104G=D(1,"TMSG_OPEN_MIRACLE_BOX_RSP",".CSMsg.TMSG_OPEN_MIRACLE_BOX_RSP",false,{},{},nil,{})
F283D=F(2,"AtyID",".CSMsg.TMSG_DEL_MIRACLE_BOX_DATA_REQ.AtyID",1,0,2,false,0,5,1)
F284D=F(2,"indexId",".CSMsg.TMSG_DEL_MIRACLE_BOX_DATA_REQ.indexId",2,1,2,false,0,5,1)
M105G=D(1,"TMSG_DEL_MIRACLE_BOX_DATA_REQ",".CSMsg.TMSG_DEL_MIRACLE_BOX_DATA_REQ",false,{},{},nil,{})
F285D=F(2,"errorcode",".CSMsg.TMSG_DEL_MIRACLE_BOX_DATA_RSP.errorcode",1,0,2,false,nil,14,8)
F286D=F(2,"AtyID",".CSMsg.TMSG_DEL_MIRACLE_BOX_DATA_RSP.AtyID",2,1,1,false,0,5,1)
F287D=F(2,"indexId",".CSMsg.TMSG_DEL_MIRACLE_BOX_DATA_RSP.indexId",3,2,1,false,0,5,1)
M106G=D(1,"TMSG_DEL_MIRACLE_BOX_DATA_RSP",".CSMsg.TMSG_DEL_MIRACLE_BOX_DATA_RSP",false,{},{},nil,{})
F288D=F(2,"orderId",".CSMsg.SwapOrderData.orderId",1,0,1,false,"",9,9)
F289D=F(2,"createId",".CSMsg.SwapOrderData.createId",2,1,1,false,0,5,1)
F290D=F(2,"createPlayerInfo",".CSMsg.SwapOrderData.createPlayerInfo",3,2,1,false,nil,11,10)
F291D=F(2,"createTime",".CSMsg.SwapOrderData.createTime",4,3,1,false,0,13,3)
F292D=F(2,"status",".CSMsg.SwapOrderData.status",5,4,1,false,0,5,1)
F293D=F(2,"consumeItemId",".CSMsg.SwapOrderData.consumeItemId",6,5,1,false,0,5,1)
F294D=F(2,"consumeNum",".CSMsg.SwapOrderData.consumeNum",7,6,1,false,0,5,1)
F295D=F(2,"targetItemId",".CSMsg.SwapOrderData.targetItemId",8,7,1,false,0,5,1)
F296D=F(2,"targetItemNum",".CSMsg.SwapOrderData.targetItemNum",9,8,1,false,0,5,1)
F297D=F(2,"tradeRoleId",".CSMsg.SwapOrderData.tradeRoleId",10,9,1,false,0,5,1)
F298D=F(2,"tranPlayerInfo",".CSMsg.SwapOrderData.tranPlayerInfo",11,10,1,false,nil,11,10)
F299D=F(2,"tradeTime",".CSMsg.SwapOrderData.tradeTime",12,11,1,false,0,13,3)
F300D=F(2,"isPraise",".CSMsg.SwapOrderData.isPraise",13,12,1,false,false,8,7)
M107G=D(1,"SwapOrderData",".CSMsg.SwapOrderData",false,{},{},nil,{})
F301D=F(2,"headingcode",".CSMsg.ScheduleAtyData.headingcode",1,0,2,false,0,5,1)
F302D=F(2,"startTime",".CSMsg.ScheduleAtyData.startTime",2,1,2,false,0,5,1)
F303D=F(2,"endTime",".CSMsg.ScheduleAtyData.endTime",3,2,2,false,0,5,1)
F304D=F(2,"nextStartTime",".CSMsg.ScheduleAtyData.nextStartTime",4,3,2,false,0,5,1)
F305D=F(2,"nextEndTime",".CSMsg.ScheduleAtyData.nextEndTime",5,4,2,false,0,5,1)
F306D=F(2,"atyID",".CSMsg.ScheduleAtyData.atyID",6,5,1,false,0,5,1)
M109G=D(1,"ScheduleAtyData",".CSMsg.ScheduleAtyData",false,{},{},nil,{})
F307D=F(2,"nState",".CSMsg.TZombieApocalypseSchedule.nState",1,0,1,false,nil,14,8)
F308D=F(2,"nRunningState",".CSMsg.TZombieApocalypseSchedule.nRunningState",2,1,1,false,nil,14,8)
F309D=F(2,"nWave",".CSMsg.TZombieApocalypseSchedule.nWave",3,2,1,false,0,5,1)
F310D=F(2,"nOpenTime",".CSMsg.TZombieApocalypseSchedule.nOpenTime",4,3,1,false,0,5,1)
M110G=D(1,"TZombieApocalypseSchedule",".CSMsg.TZombieApocalypseSchedule",false,{},{},nil,{})
F311D=F(2,"atyID",".CSMsg.TMSG_COMMONATY_VIEWORDER_REQ.atyID",1,0,1,false,0,5,1)
M113G=D(1,"TMSG_COMMONATY_VIEWORDER_REQ",".CSMsg.TMSG_COMMONATY_VIEWORDER_REQ",false,{},{},nil,{})
F312D=F(2,"errorcode",".CSMsg.TMSG_COMMONATY_VIEWORDER_RSP.errorcode",1,0,2,false,nil,14,8)
F313D=F(2,"datas",".CSMsg.TMSG_COMMONATY_VIEWORDER_RSP.datas",2,1,3,false,{},11,10)
M114G=D(1,"TMSG_COMMONATY_VIEWORDER_RSP",".CSMsg.TMSG_COMMONATY_VIEWORDER_RSP",false,{},{},nil,{})
F314D=F(2,"atyID",".CSMsg.TMSG_COMMONATY_SHELF_GOODS_REQ.atyID",1,0,1,false,0,5,1)
F315D=F(2,"consumeItemId",".CSMsg.TMSG_COMMONATY_SHELF_GOODS_REQ.consumeItemId",2,1,1,false,0,5,1)
F316D=F(2,"consumeNum",".CSMsg.TMSG_COMMONATY_SHELF_GOODS_REQ.consumeNum",3,2,1,false,0,5,1)
F317D=F(2,"targetItemId",".CSMsg.TMSG_COMMONATY_SHELF_GOODS_REQ.targetItemId",4,3,1,false,0,5,1)
F318D=F(2,"targetItemNum",".CSMsg.TMSG_COMMONATY_SHELF_GOODS_REQ.targetItemNum",5,4,1,false,0,5,1)
M115G=D(1,"TMSG_COMMONATY_SHELF_GOODS_REQ",".CSMsg.TMSG_COMMONATY_SHELF_GOODS_REQ",false,{},{},nil,{})
F319D=F(2,"errorcode",".CSMsg.TMSG_COMMONATY_SHELF_GOODS_RSP.errorcode",1,0,2,false,nil,14,8)
F320D=F(2,"order",".CSMsg.TMSG_COMMONATY_SHELF_GOODS_RSP.order",2,1,1,false,nil,11,10)
M116G=D(1,"TMSG_COMMONATY_SHELF_GOODS_RSP",".CSMsg.TMSG_COMMONATY_SHELF_GOODS_RSP",false,{},{},nil,{})
F321D=F(2,"atyID",".CSMsg.TMSG_COMMONATY_EXCHANGE_GOODS_REQ.atyID",1,0,1,false,0,5,1)
F322D=F(2,"orderId",".CSMsg.TMSG_COMMONATY_EXCHANGE_GOODS_REQ.orderId",2,1,1,false,"",9,9)
M117G=D(1,"TMSG_COMMONATY_EXCHANGE_GOODS_REQ",".CSMsg.TMSG_COMMONATY_EXCHANGE_GOODS_REQ",false,{},{},nil,{})
F323D=F(2,"errorcode",".CSMsg.TMSG_COMMONATY_EXCHANGE_GOODS_RSP.errorcode",1,0,2,false,nil,14,8)
F324D=F(2,"order",".CSMsg.TMSG_COMMONATY_EXCHANGE_GOODS_RSP.order",2,1,1,false,nil,11,10)
M118G=D(1,"TMSG_COMMONATY_EXCHANGE_GOODS_RSP",".CSMsg.TMSG_COMMONATY_EXCHANGE_GOODS_RSP",false,{},{},nil,{})
F325D=F(2,"atyID",".CSMsg.TMSG_COMMONATY_CANCLE_EXCHANGE_REQ.atyID",1,0,1,false,0,5,1)
F326D=F(2,"orderId",".CSMsg.TMSG_COMMONATY_CANCLE_EXCHANGE_REQ.orderId",2,1,1,false,"",9,9)
M119G=D(1,"TMSG_COMMONATY_CANCLE_EXCHANGE_REQ",".CSMsg.TMSG_COMMONATY_CANCLE_EXCHANGE_REQ",false,{},{},nil,{})
F327D=F(2,"errorcode",".CSMsg.TMSG_COMMONATY_CANCLE_EXCHANGE_RSP.errorcode",1,0,2,false,nil,14,8)
F328D=F(2,"orderId",".CSMsg.TMSG_COMMONATY_CANCLE_EXCHANGE_RSP.orderId",2,1,1,false,"",9,9)
M120G=D(1,"TMSG_COMMONATY_CANCLE_EXCHANGE_RSP",".CSMsg.TMSG_COMMONATY_CANCLE_EXCHANGE_RSP",false,{},{},nil,{})
F329D=F(2,"atyID",".CSMsg.TMSG_COMMONATY_EXCHANGE_ORDER_LIST_REQ.atyID",1,0,1,false,0,5,1)
M121G=D(1,"TMSG_COMMONATY_EXCHANGE_ORDER_LIST_REQ",".CSMsg.TMSG_COMMONATY_EXCHANGE_ORDER_LIST_REQ",false,{},{},nil,{})
F330D=F(2,"errorcode",".CSMsg.TMSG_COMMONATY_EXCHANGE_ORDER_LIST_RSP.errorcode",1,0,2,false,nil,14,8)
F331D=F(2,"datas",".CSMsg.TMSG_COMMONATY_EXCHANGE_ORDER_LIST_RSP.datas",2,1,3,false,{},11,10)
M122G=D(1,"TMSG_COMMONATY_EXCHANGE_ORDER_LIST_RSP",".CSMsg.TMSG_COMMONATY_EXCHANGE_ORDER_LIST_RSP",false,{},{},nil,{})
F332D=F(2,"atyID",".CSMsg.TMSG_COMMONATY_VIEW_ORDER_RECORD_REQ.atyID",1,0,1,false,0,5,1)
F333D=F(2,"isAlliance",".CSMsg.TMSG_COMMONATY_VIEW_ORDER_RECORD_REQ.isAlliance",2,1,1,false,0,5,1)
M123G=D(1,"TMSG_COMMONATY_VIEW_ORDER_RECORD_REQ",".CSMsg.TMSG_COMMONATY_VIEW_ORDER_RECORD_REQ",false,{},{},nil,{})
F334D=F(2,"errorcode",".CSMsg.TMSG_COMMONATY_VIEW_ORDER_RECORD_RSP.errorcode",1,0,2,false,nil,14,8)
F335D=F(2,"datas",".CSMsg.TMSG_COMMONATY_VIEW_ORDER_RECORD_RSP.datas",2,1,3,false,{},11,10)
M124G=D(1,"TMSG_COMMONATY_VIEW_ORDER_RECORD_RSP",".CSMsg.TMSG_COMMONATY_VIEW_ORDER_RECORD_RSP",false,{},{},nil,{})
F336D=F(2,"orderId",".CSMsg.TMSG_ACORNPUB_TREASURE_PARASE_REQ.orderId",1,0,2,false,"",9,9)
F337D=F(2,"atyID",".CSMsg.TMSG_ACORNPUB_TREASURE_PARASE_REQ.atyID",2,1,1,false,0,5,1)
M125G=D(1,"TMSG_ACORNPUB_TREASURE_PARASE_REQ",".CSMsg.TMSG_ACORNPUB_TREASURE_PARASE_REQ",false,{},{},nil,{})
F338D=F(2,"errorcode",".CSMsg.TMSG_ACORNPUB_TREASURE_PARASE_RSP.errorcode",1,0,2,false,nil,14,8)
F339D=F(2,"data",".CSMsg.TMSG_ACORNPUB_TREASURE_PARASE_RSP.data",2,1,1,false,nil,11,10)
M126G=D(1,"TMSG_ACORNPUB_TREASURE_PARASE_RSP",".CSMsg.TMSG_ACORNPUB_TREASURE_PARASE_RSP",false,{},{},nil,{})
M127G=D(1,"TMSG_SCHEDULE_LIST_REQ",".CSMsg.TMSG_SCHEDULE_LIST_REQ",false,{},{},{},{})
F340D=F(2,"errorcode",".CSMsg.TMSG_SCHEDULE_LIST_RSP.errorcode",1,0,2,false,nil,14,8)
F341D=F(2,"data",".CSMsg.TMSG_SCHEDULE_LIST_RSP.data",2,1,3,false,{},11,10)
F342D=F(2,"zombieSchedule",".CSMsg.TMSG_SCHEDULE_LIST_RSP.zombieSchedule",3,2,1,false,nil,11,10)
M128G=D(1,"TMSG_SCHEDULE_LIST_RSP",".CSMsg.TMSG_SCHEDULE_LIST_RSP",false,{},{},nil,{})
M129G=D(1,"TMSG_GEAR_SUPPLY_GETACTIVITYDATA_REQ",".CSMsg.TMSG_GEAR_SUPPLY_GETACTIVITYDATA_REQ",false,{},{},{},{})
F343D=F(2,"arrUnreceiveReward",".CSMsg.TMSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP.arrUnreceiveReward",1,0,3,false,{},5,1)
F344D=F(2,"arrReceiveReward",".CSMsg.TMSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP.arrReceiveReward",2,1,3,false,{},5,1)
F345D=F(2,"nTotalDrawCnt",".CSMsg.TMSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP.nTotalDrawCnt",3,2,2,false,0,5,1)
F346D=F(2,"nBigRewardGridID",".CSMsg.TMSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP.nBigRewardGridID",4,3,2,false,0,5,1)
F347D=F(2,"nCurLayerID",".CSMsg.TMSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP.nCurLayerID",5,4,2,false,0,5,1)
F348D=F(2,"arrGridRewardID",".CSMsg.TMSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP.arrGridRewardID",6,5,3,false,{},5,1)
F349D=F(2,"nDefaultSelect",".CSMsg.TMSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP.nDefaultSelect",7,6,2,false,0,5,1)
M130G=D(1,"TMSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP",".CSMsg.TMSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP",false,{},{},nil,{})
M131G=D(1,"TMSG_GEAR_SUPPLY_RECEIVEAWARD_REQ",".CSMsg.TMSG_GEAR_SUPPLY_RECEIVEAWARD_REQ",false,{},{},{},{})
F350D=F(2,"errorcode",".CSMsg.TMSG_GEAR_SUPPLY_RECEIVEAWARD_RSP.errorcode",1,0,2,false,nil,14,8)
F351D=F(2,"arrGotAward",".CSMsg.TMSG_GEAR_SUPPLY_RECEIVEAWARD_RSP.arrGotAward",2,1,3,false,{},5,1)
M132G=D(1,"TMSG_GEAR_SUPPLY_RECEIVEAWARD_RSP",".CSMsg.TMSG_GEAR_SUPPLY_RECEIVEAWARD_RSP",false,{},{},nil,{})
F352D=F(2,"nGridIdx",".CSMsg.TMSG_GEAR_SUPPLY_DRAWAWARD_REQ.nGridIdx",1,0,2,false,0,5,1)
M133G=D(1,"TMSG_GEAR_SUPPLY_DRAWAWARD_REQ",".CSMsg.TMSG_GEAR_SUPPLY_DRAWAWARD_REQ",false,{},{},nil,{})
F353D=F(2,"errorcode",".CSMsg.TMSG_GEAR_SUPPLY_DRAWAWARD_RSP.errorcode",1,0,2,false,nil,14,8)
F354D=F(2,"nResultRewardID",".CSMsg.TMSG_GEAR_SUPPLY_DRAWAWARD_RSP.nResultRewardID",2,1,2,false,0,5,1)
F355D=F(2,"nResultGridIdx",".CSMsg.TMSG_GEAR_SUPPLY_DRAWAWARD_RSP.nResultGridIdx",3,2,2,false,0,5,1)
M134G=D(1,"TMSG_GEAR_SUPPLY_DRAWAWARD_RSP",".CSMsg.TMSG_GEAR_SUPPLY_DRAWAWARD_RSP",false,{},{},nil,{})
F356D=F(2,"nBigRewardGridID",".CSMsg.TMSG_GEAR_SUPPLY_SETREWARD_REQ.nBigRewardGridID",1,0,2,false,0,5,1)
F357D=F(2,"nDefaultSelect",".CSMsg.TMSG_GEAR_SUPPLY_SETREWARD_REQ.nDefaultSelect",2,1,2,false,0,5,1)
M135G=D(1,"TMSG_GEAR_SUPPLY_SETREWARD_REQ",".CSMsg.TMSG_GEAR_SUPPLY_SETREWARD_REQ",false,{},{},nil,{})
F358D=F(2,"errorcode",".CSMsg.TMSG_GEAR_SUPPLY_SETREWARD_RSP.errorcode",1,0,2,false,nil,14,8)
F359D=F(2,"nBigRewardGridID",".CSMsg.TMSG_GEAR_SUPPLY_SETREWARD_RSP.nBigRewardGridID",2,1,2,false,0,5,1)
F360D=F(2,"nDefaultSelect",".CSMsg.TMSG_GEAR_SUPPLY_SETREWARD_RSP.nDefaultSelect",3,2,2,false,0,5,1)
M136G=D(1,"TMSG_GEAR_SUPPLY_SETREWARD_RSP",".CSMsg.TMSG_GEAR_SUPPLY_SETREWARD_RSP",false,{},{},nil,{})
F361D=F(2,"sid",".CSMsg.ThreatMonData.sid",1,0,2,false,0,3,2)
F362D=F(2,"monId",".CSMsg.ThreatMonData.monId",2,1,2,false,0,3,2)
F363D=F(2,"endTime",".CSMsg.ThreatMonData.endTime",3,2,2,false,0,5,1)
F364D=F(2,"lineSid",".CSMsg.ThreatMonData.lineSid",4,3,2,false,0,5,1)
M137G=D(1,"ThreatMonData",".CSMsg.ThreatMonData",false,{},{},nil,{})
F365D=F(2,"dbid",".CSMsg.FirstRankData.dbid",1,0,2,false,0,5,1)
F366D=F(2,"name",".CSMsg.FirstRankData.name",2,1,2,false,"",9,9)
F367D=F(2,"faceStr",".CSMsg.FirstRankData.faceStr",3,2,2,false,"",9,9)
F368D=F(2,"frameID",".CSMsg.FirstRankData.frameID",4,3,2,false,0,5,1)
F369D=F(2,"leagueid",".CSMsg.FirstRankData.leagueid",5,4,2,false,0,5,1)
F370D=F(2,"leagueName",".CSMsg.FirstRankData.leagueName",6,5,2,false,"",9,9)
F371D=F(2,"leagueShortName",".CSMsg.FirstRankData.leagueShortName",7,6,2,false,"",9,9)
F372D=F(2,"totalDamage",".CSMsg.FirstRankData.totalDamage",8,7,2,false,0,3,2)
M138G=D(1,"FirstRankData",".CSMsg.FirstRankData",false,{},{},nil,{})
M139G=D(1,"TMSG_SKY_FALL_THREAT_MONSTER_REQ",".CSMsg.TMSG_SKY_FALL_THREAT_MONSTER_REQ",false,{},{},{},{})
F373D=F(2,"errorcode",".CSMsg.TMSG_SKY_FALL_THREAT_MONSTER_RSP.errorcode",1,0,2,false,nil,14,8)
F374D=F(2,"nextRefTime",".CSMsg.TMSG_SKY_FALL_THREAT_MONSTER_RSP.nextRefTime",2,1,2,false,0,5,1)
F375D=F(2,"allianceThreat",".CSMsg.TMSG_SKY_FALL_THREAT_MONSTER_RSP.allianceThreat",3,2,3,false,{},11,10)
F376D=F(2,"battleAreaThreat",".CSMsg.TMSG_SKY_FALL_THREAT_MONSTER_RSP.battleAreaThreat",4,3,3,false,{},11,10)
F377D=F(2,"firstRank",".CSMsg.TMSG_SKY_FALL_THREAT_MONSTER_RSP.firstRank",5,4,1,false,nil,11,10)
M140G=D(1,"TMSG_SKY_FALL_THREAT_MONSTER_RSP",".CSMsg.TMSG_SKY_FALL_THREAT_MONSTER_RSP",false,{},{},nil,{})
F378D=F(2,"type",".CSMsg.TaskPro.type",1,0,2,false,0,5,1)
F379D=F(2,"proVal",".CSMsg.TaskPro.proVal",2,1,2,false,0,5,1)
M141G=D(1,"TaskPro",".CSMsg.TaskPro",false,{},{},nil,{})
F380D=F(2,"taskPros",".CSMsg.TMSG_SKY_FALL_TASK_PRO_NTF.taskPros",1,0,3,false,{},11,10)
M142G=D(1,"TMSG_SKY_FALL_TASK_PRO_NTF",".CSMsg.TMSG_SKY_FALL_TASK_PRO_NTF",false,{},{},nil,{})
F381D=F(2,"doomsDayIdx",".CSMsg.TMSG_SKY_FALL_DOOMSDAY_NTF.doomsDayIdx",1,0,2,false,0,5,1)
M143G=D(1,"TMSG_SKY_FALL_DOOMSDAY_NTF",".CSMsg.TMSG_SKY_FALL_DOOMSDAY_NTF",false,{},{},nil,{})
M144G=D(1,"TMSG_COMMUNITYGIFT_REWARDSTATUS_REQ",".CSMsg.TMSG_COMMUNITYGIFT_REWARDSTATUS_REQ",false,{},{},{},{})
F382D=F(2,"errorcode",".CSMsg.TMSG_COMMUNITYGIFT_REWARDSTATUS_RSP.errorcode",1,0,2,false,nil,14,8)
F383D=F(2,"rewardStatusList",".CSMsg.TMSG_COMMUNITYGIFT_REWARDSTATUS_RSP.rewardStatusList",2,1,3,false,{},5,1)
M145G=D(1,"TMSG_COMMUNITYGIFT_REWARDSTATUS_RSP",".CSMsg.TMSG_COMMUNITYGIFT_REWARDSTATUS_RSP",false,{},{},nil,{})
F384D=F(2,"index",".CSMsg.TMSG_COMMUNITYGIFT_JUMP_REQ.index",1,0,2,false,0,5,1)
M146G=D(1,"TMSG_COMMUNITYGIFT_JUMP_REQ",".CSMsg.TMSG_COMMUNITYGIFT_JUMP_REQ",false,{},{},nil,{})
F385D=F(2,"errorcode",".CSMsg.TMSG_COMMUNITYGIFT_JUMP_RSP.errorcode",1,0,2,false,nil,14,8)
F386D=F(2,"index",".CSMsg.TMSG_COMMUNITYGIFT_JUMP_RSP.index",2,1,2,false,0,5,1)
M147G=D(1,"TMSG_COMMUNITYGIFT_JUMP_RSP",".CSMsg.TMSG_COMMUNITYGIFT_JUMP_RSP",false,{},{},nil,{})
F387D=F(2,"index",".CSMsg.TMSG_COMMUNITYGIFT_GETREWARD_REQ.index",1,0,2,false,0,5,1)
M148G=D(1,"TMSG_COMMUNITYGIFT_GETREWARD_REQ",".CSMsg.TMSG_COMMUNITYGIFT_GETREWARD_REQ",false,{},{},nil,{})
F388D=F(2,"errorcode",".CSMsg.TMSG_COMMUNITYGIFT_GETREWARD_RSP.errorcode",1,0,2,false,nil,14,8)
F389D=F(2,"index",".CSMsg.TMSG_COMMUNITYGIFT_GETREWARD_RSP.index",2,1,2,false,0,5,1)
M149G=D(1,"TMSG_COMMUNITYGIFT_GETREWARD_RSP",".CSMsg.TMSG_COMMUNITYGIFT_GETREWARD_RSP",false,{},{},nil,{})
F390D=F(2,"weekCardIdx",".CSMsg.WeekCard.weekCardIdx",1,0,2,false,0,5,1)
F391D=F(2,"buyFlag",".CSMsg.WeekCard.buyFlag",2,1,2,false,0,5,1)
F392D=F(2,"getDay",".CSMsg.WeekCard.getDay",3,2,2,false,0,5,1)
F393D=F(2,"chooseItems",".CSMsg.WeekCard.chooseItems",4,3,3,false,{},5,1)
M150G=D(1,"WeekCard",".CSMsg.WeekCard",false,{},{},nil,{})
F394D=F(2,"atyID",".CSMsg.OneAtyChooseWeekCard.atyID",1,0,2,false,0,5,1)
F395D=F(2,"weekCard",".CSMsg.OneAtyChooseWeekCard.weekCard",2,1,3,false,{},11,10)
M151G=D(1,"OneAtyChooseWeekCard",".CSMsg.OneAtyChooseWeekCard",false,{},{},nil,{})
F396D=F(2,"oneAtyChooseWeekCard",".CSMsg.TMSG_All_ATY_CHOOSE_WEEK_CARD_NTF.oneAtyChooseWeekCard",1,0,3,false,{},11,10)
M152G=D(1,"TMSG_All_ATY_CHOOSE_WEEK_CARD_NTF",".CSMsg.TMSG_All_ATY_CHOOSE_WEEK_CARD_NTF",false,{},{},nil,{})
F397D=F(2,"oneAtyChooseWeekCard",".CSMsg.TMSG_ONE_ATY_CHOOSE_WEEK_CARD_NTF.oneAtyChooseWeekCard",1,0,2,false,nil,11,10)
M153G=D(1,"TMSG_ONE_ATY_CHOOSE_WEEK_CARD_NTF",".CSMsg.TMSG_ONE_ATY_CHOOSE_WEEK_CARD_NTF",false,{},{},nil,{})
F398D=F(2,"atyID",".CSMsg.TMSG_BUY_CHOOSE_WEEK_CARD_CHECK_REQ.atyID",1,0,2,false,0,5,1)
F399D=F(2,"weekCardIdx",".CSMsg.TMSG_BUY_CHOOSE_WEEK_CARD_CHECK_REQ.weekCardIdx",2,1,2,false,0,5,1)
F400D=F(2,"chooseItems",".CSMsg.TMSG_BUY_CHOOSE_WEEK_CARD_CHECK_REQ.chooseItems",3,2,3,false,{},5,1)
M154G=D(1,"TMSG_BUY_CHOOSE_WEEK_CARD_CHECK_REQ",".CSMsg.TMSG_BUY_CHOOSE_WEEK_CARD_CHECK_REQ",false,{},{},nil,{})
F401D=F(2,"errorcode",".CSMsg.TMSG_BUY_CHOOSE_WEEK_CARD_CHECK_RSP.errorcode",1,0,2,false,nil,14,8)
F402D=F(2,"atyID",".CSMsg.TMSG_BUY_CHOOSE_WEEK_CARD_CHECK_RSP.atyID",2,1,1,false,0,5,1)
F403D=F(2,"weekCardIdx",".CSMsg.TMSG_BUY_CHOOSE_WEEK_CARD_CHECK_RSP.weekCardIdx",3,2,1,false,0,5,1)
M155G=D(1,"TMSG_BUY_CHOOSE_WEEK_CARD_CHECK_RSP",".CSMsg.TMSG_BUY_CHOOSE_WEEK_CARD_CHECK_RSP",false,{},{},nil,{})
F404D=F(2,"errorcode",".CSMsg.TMSG_ACTIVE_CHOOSE_WEEK_CARD_NTF.errorcode",1,0,2,false,nil,14,8)
F405D=F(2,"atyID",".CSMsg.TMSG_ACTIVE_CHOOSE_WEEK_CARD_NTF.atyID",2,1,1,false,0,5,1)
F406D=F(2,"weekCardIdx",".CSMsg.TMSG_ACTIVE_CHOOSE_WEEK_CARD_NTF.weekCardIdx",3,2,1,false,0,5,1)
F407D=F(2,"weekCard",".CSMsg.TMSG_ACTIVE_CHOOSE_WEEK_CARD_NTF.weekCard",4,3,1,false,nil,11,10)
M156G=D(1,"TMSG_ACTIVE_CHOOSE_WEEK_CARD_NTF",".CSMsg.TMSG_ACTIVE_CHOOSE_WEEK_CARD_NTF",false,{},{},nil,{})
F408D=F(2,"atyID",".CSMsg.TMSG_GET_CHOOSE_WEEK_CARD_REWARD_REQ.atyID",1,0,2,false,0,5,1)
F409D=F(2,"weekCardIdx",".CSMsg.TMSG_GET_CHOOSE_WEEK_CARD_REWARD_REQ.weekCardIdx",2,1,2,false,0,5,1)
M157G=D(1,"TMSG_GET_CHOOSE_WEEK_CARD_REWARD_REQ",".CSMsg.TMSG_GET_CHOOSE_WEEK_CARD_REWARD_REQ",false,{},{},nil,{})
F410D=F(2,"errorcode",".CSMsg.TMSG_GET_CHOOSE_WEEK_CARD_REWARD_RSP.errorcode",1,0,2,false,nil,14,8)
F411D=F(2,"atyID",".CSMsg.TMSG_GET_CHOOSE_WEEK_CARD_REWARD_RSP.atyID",2,1,1,false,0,5,1)
F412D=F(2,"weekCardIdx",".CSMsg.TMSG_GET_CHOOSE_WEEK_CARD_REWARD_RSP.weekCardIdx",3,2,1,false,0,5,1)
F413D=F(2,"weekCard",".CSMsg.TMSG_GET_CHOOSE_WEEK_CARD_REWARD_RSP.weekCard",4,3,1,false,nil,11,10)
M158G=D(1,"TMSG_GET_CHOOSE_WEEK_CARD_REWARD_RSP",".CSMsg.TMSG_GET_CHOOSE_WEEK_CARD_REWARD_RSP",false,{},{},nil,{})
F414D=F(2,"nActivityID",".CSMsg.TMSG_SEVENDAY_LOGIN_RECEIVEAWARD_REQ.nActivityID",1,0,2,false,0,5,1)
M159G=D(1,"TMSG_SEVENDAY_LOGIN_RECEIVEAWARD_REQ",".CSMsg.TMSG_SEVENDAY_LOGIN_RECEIVEAWARD_REQ",false,{},{},nil,{})
F415D=F(2,"errorcode",".CSMsg.TMSG_SEVENDAY_LOGIN_RECEIVEAWARD_RSP.errorcode",1,0,2,false,nil,14,8)
F416D=F(2,"nActivityID",".CSMsg.TMSG_SEVENDAY_LOGIN_RECEIVEAWARD_RSP.nActivityID",2,1,2,false,0,5,1)
F417D=F(2,"arrGotDays",".CSMsg.TMSG_SEVENDAY_LOGIN_RECEIVEAWARD_RSP.arrGotDays",3,2,3,false,{},5,1)
F418D=F(2,"arrItemInfo",".CSMsg.TMSG_SEVENDAY_LOGIN_RECEIVEAWARD_RSP.arrItemInfo",4,3,3,false,{},11,10)
M160G=D(1,"TMSG_SEVENDAY_LOGIN_RECEIVEAWARD_RSP",".CSMsg.TMSG_SEVENDAY_LOGIN_RECEIVEAWARD_RSP",false,{},{},nil,{})
F419D=F(2,"nActivityID",".CSMsg.TMSG_BATTLE_PASS_GET_ALL_REWARD_REQ.nActivityID",1,0,2,false,0,5,1)
M162G=D(1,"TMSG_BATTLE_PASS_GET_ALL_REWARD_REQ",".CSMsg.TMSG_BATTLE_PASS_GET_ALL_REWARD_REQ",false,{},{},nil,{})
F420D=F(2,"errorcode",".CSMsg.TMSG_BATTLE_PASS_GET_ALL_REWARD_RSP.errorcode",1,0,2,false,nil,14,8)
M163G=D(1,"TMSG_BATTLE_PASS_GET_ALL_REWARD_RSP",".CSMsg.TMSG_BATTLE_PASS_GET_ALL_REWARD_RSP",false,{},{},nil,{})
M164G=D(1,"TMSG_GETBINDEMAIL_REWARD_REQ",".CSMsg.TMSG_GETBINDEMAIL_REWARD_REQ",false,{},{},{},{})
F421D=F(2,"errorcode",".CSMsg.TMSG_GETBINDEMAIL_REWARD_RSP.errorcode",1,0,2,false,0,5,1)
F422D=F(2,"rewardIDs",".CSMsg.TMSG_GETBINDEMAIL_REWARD_RSP.rewardIDs",2,1,3,false,{},5,1)
M165G=D(1,"TMSG_GETBINDEMAIL_REWARD_RSP",".CSMsg.TMSG_GETBINDEMAIL_REWARD_RSP",false,{},{},nil,{})
F423D=F(2,"AtyID",".CSMsg.TAtyNoticePeriod.AtyID",1,0,2,false,0,5,1)
F424D=F(2,"beginTime",".CSMsg.TAtyNoticePeriod.beginTime",2,1,2,false,0,5,1)
F425D=F(2,"endTime",".CSMsg.TAtyNoticePeriod.endTime",3,2,2,false,0,5,1)
M166G=D(1,"TAtyNoticePeriod",".CSMsg.TAtyNoticePeriod",false,{},{},nil,{})
F426D=F(2,"notices",".CSMsg.TMSG_ACTIVITY_NOTICEPERIOD_NTF.notices",1,0,3,false,{},11,10)
M167G=D(1,"TMSG_ACTIVITY_NOTICEPERIOD_NTF",".CSMsg.TMSG_ACTIVITY_NOTICEPERIOD_NTF",false,{},{},nil,{})
F427D=F(2,"score",".CSMsg.TMSG_GOOGLE_COMMENT_SET_REQ.score",1,0,2,false,0,5,1)
M168G=D(1,"TMSG_GOOGLE_COMMENT_SET_REQ",".CSMsg.TMSG_GOOGLE_COMMENT_SET_REQ",false,{},{},nil,{})
F428D=F(2,"err",".CSMsg.TMSG_GOOGLE_COMMENT_SET_RSP.err",1,0,2,false,nil,14,8)
F429D=F(2,"score",".CSMsg.TMSG_GOOGLE_COMMENT_SET_RSP.score",2,1,1,false,0,5,1)
M169G=D(1,"TMSG_GOOGLE_COMMENT_SET_RSP",".CSMsg.TMSG_GOOGLE_COMMENT_SET_RSP",false,{},{},nil,{})
F430D=F(2,"nActivityID",".CSMsg.TMSG_SLOT_MACHINE_DRAW_REQ.nActivityID",1,0,1,false,0,5,1)
F431D=F(2,"nDrawsCnt",".CSMsg.TMSG_SLOT_MACHINE_DRAW_REQ.nDrawsCnt",2,1,1,false,0,5,1)
M170G=D(1,"TMSG_SLOT_MACHINE_DRAW_REQ",".CSMsg.TMSG_SLOT_MACHINE_DRAW_REQ",false,{},{},nil,{})
F432D=F(2,"errorcode",".CSMsg.TMSG_SLOT_MACHINE_DRAW_RSP.errorcode",1,0,2,false,nil,14,8)
F433D=F(2,"nIndex",".CSMsg.TMSG_SLOT_MACHINE_DRAW_RSP.nIndex",2,1,1,false,0,5,1)
F434D=F(2,"nActivityID",".CSMsg.TMSG_SLOT_MACHINE_DRAW_RSP.nActivityID",3,2,1,false,0,5,1)
F435D=F(2,"nDrawsCnt",".CSMsg.TMSG_SLOT_MACHINE_DRAW_RSP.nDrawsCnt",4,3,1,false,0,5,1)
M171G=D(1,"TMSG_SLOT_MACHINE_DRAW_RSP",".CSMsg.TMSG_SLOT_MACHINE_DRAW_RSP",false,{},{},nil,{})
F436D=F(2,"nActivityID",".CSMsg.TMSG_GHOST_PARTY_HOLD_REQ.nActivityID",1,0,1,false,0,5,1)
M172G=D(1,"TMSG_GHOST_PARTY_HOLD_REQ",".CSMsg.TMSG_GHOST_PARTY_HOLD_REQ",false,{},{},nil,{})
F437D=F(2,"errorcode",".CSMsg.TMSG_GHOST_PARTY_HOLD_RSP.errorcode",1,0,2,false,nil,14,8)
F438D=F(2,"nActivityID",".CSMsg.TMSG_GHOST_PARTY_HOLD_RSP.nActivityID",2,1,1,false,0,5,1)
F439D=F(2,"pos",".CSMsg.TMSG_GHOST_PARTY_HOLD_RSP.pos",3,2,1,false,nil,11,10)
F440D=F(2,"nBoxSid",".CSMsg.TMSG_GHOST_PARTY_HOLD_RSP.nBoxSid",4,3,1,false,0,5,1)
M173G=D(1,"TMSG_GHOST_PARTY_HOLD_RSP",".CSMsg.TMSG_GHOST_PARTY_HOLD_RSP",false,{},{},nil,{})
M175G=D(1,"TMSG_GHOST_PARTY_HOLD_LIST_REQ",".CSMsg.TMSG_GHOST_PARTY_HOLD_LIST_REQ",false,{},{},{},{})
F441D=F(2,"errCode",".CSMsg.TMSG_GHOST_PARTY_HOLD_LIST_RSP.errCode",1,0,2,false,nil,14,8)
F442D=F(2,"stBoxData",".CSMsg.TMSG_GHOST_PARTY_HOLD_LIST_RSP.stBoxData",2,1,3,false,{},11,10)
M176G=D(1,"TMSG_GHOST_PARTY_HOLD_LIST_RSP",".CSMsg.TMSG_GHOST_PARTY_HOLD_LIST_RSP",false,{},{},nil,{})
M178G=D(1,"TMSG_HALLOWEEN_PARTY_GETINFO_REQ",".CSMsg.TMSG_HALLOWEEN_PARTY_GETINFO_REQ",false,{},{},{},{})
F443D=F(2,"actId",".CSMsg.TMSG_HALLOWEEN_PARTY_GETINFO_RSP.actId",1,0,2,false,0,5,1)
F444D=F(2,"partyExp",".CSMsg.TMSG_HALLOWEEN_PARTY_GETINFO_RSP.partyExp",2,1,2,false,0,5,1)
F445D=F(2,"receiveStatus",".CSMsg.TMSG_HALLOWEEN_PARTY_GETINFO_RSP.receiveStatus",3,2,3,false,{},5,1)
M179G=D(1,"TMSG_HALLOWEEN_PARTY_GETINFO_RSP",".CSMsg.TMSG_HALLOWEEN_PARTY_GETINFO_RSP",false,{},{},nil,{})
F446D=F(2,"actId",".CSMsg.TMSG_HALLOWEEN_PARTY_SUBMIT_REQ.actId",1,0,2,false,0,5,1)
F447D=F(2,"submitCnt",".CSMsg.TMSG_HALLOWEEN_PARTY_SUBMIT_REQ.submitCnt",2,1,2,false,0,5,1)
M180G=D(1,"TMSG_HALLOWEEN_PARTY_SUBMIT_REQ",".CSMsg.TMSG_HALLOWEEN_PARTY_SUBMIT_REQ",false,{},{},nil,{})
F448D=F(2,"errorcode",".CSMsg.TMSG_HALLOWEEN_PARTY_SUBMIT_RSP.errorcode",1,0,2,false,nil,14,8)
F449D=F(2,"actId",".CSMsg.TMSG_HALLOWEEN_PARTY_SUBMIT_RSP.actId",2,1,2,false,0,5,1)
F450D=F(2,"partyExp",".CSMsg.TMSG_HALLOWEEN_PARTY_SUBMIT_RSP.partyExp",3,2,2,false,0,5,1)
F451D=F(2,"receiveStatus",".CSMsg.TMSG_HALLOWEEN_PARTY_SUBMIT_RSP.receiveStatus",4,3,3,false,{},5,1)
F452D=F(2,"submitRewards",".CSMsg.TMSG_HALLOWEEN_PARTY_SUBMIT_RSP.submitRewards",5,4,3,false,{},5,1)
M181G=D(1,"TMSG_HALLOWEEN_PARTY_SUBMIT_RSP",".CSMsg.TMSG_HALLOWEEN_PARTY_SUBMIT_RSP",false,{},{},nil,{})
F453D=F(2,"actId",".CSMsg.TMSG_HALLOWEEN_PARTY_GET_REWARD_REQ.actId",1,0,2,false,0,5,1)
F454D=F(2,"id",".CSMsg.TMSG_HALLOWEEN_PARTY_GET_REWARD_REQ.id",2,1,2,false,0,5,1)
M182G=D(1,"TMSG_HALLOWEEN_PARTY_GET_REWARD_REQ",".CSMsg.TMSG_HALLOWEEN_PARTY_GET_REWARD_REQ",false,{},{},nil,{})
F455D=F(2,"errorcode",".CSMsg.TMSG_HALLOWEEN_PARTY_GET_REWARD_RSP.errorcode",1,0,2,false,nil,14,8)
F456D=F(2,"actId",".CSMsg.TMSG_HALLOWEEN_PARTY_GET_REWARD_RSP.actId",2,1,2,false,0,5,1)
F457D=F(2,"receiveStatus",".CSMsg.TMSG_HALLOWEEN_PARTY_GET_REWARD_RSP.receiveStatus",3,2,3,false,{},5,1)
F458D=F(2,"rewards",".CSMsg.TMSG_HALLOWEEN_PARTY_GET_REWARD_RSP.rewards",4,3,3,false,{},5,1)
M183G=D(1,"TMSG_HALLOWEEN_PARTY_GET_REWARD_RSP",".CSMsg.TMSG_HALLOWEEN_PARTY_GET_REWARD_RSP",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M,V24M,V25M,V26M,V27M,V28M,V29M,V30M,V31M,V32M,V33M,V34M,V35M,V36M,V37M,V38M}
E2M.values = {V39M,V40M,V41M,V42M,V43M,V44M,V45M,V46M}
E3M.values = {V47M,V48M,V49M,V50M,V51M,V52M,V53M}
E4M.values = {V54M,V55M,V56M,V57M}
E5M.values = {V58M,V59M,V60M}
E6M.values = {V61M,V62M,V63M,V64M,V65M,V66M,V67M,V68M,V69M,V70M,V71M,V72M,V73M,V74M,V75M,V76M,V77M,V78M,V79M,V80M,V81M,V82M,V83M,V84M,V85M,V86M,V87M,V88M,V89M,V90M,V91M,V92M,V93M,V94M,V95M,V96M,V97M}
E7M.values = {V98M,V99M}
E8M.values = {V100M,V101M,V102M,V103M,V104M,V105M}
E9M.values = {V106M,V107M,V108M,V109M}
E10M.values = {V110M,V111M,V112M}
E11M.values = {V113M,V114M,V115M}
E12M.values = {V116M,V117M}
M1G.fields={F1D, F2D}
M2G.fields={F3D, F4D, F5D, F6D, F7D, F8D}
F9D.enum_type=error_code_pb.E1M
F10D.enum_type=common_new_pb.E4M
F13D.message_type=M1G
F14D.enum_type=common_new_pb.E5M
F16D.message_type=M2G
M3G.fields={F9D, F10D, F11D, F12D, F13D, F14D, F15D, F16D}
M7G.fields={F17D}
M8G.fields={F18D, F19D, F20D}
M9G.fields={F21D, F22D}
M10G.fields={F23D, F24D, F25D, F26D, F27D, F28D}
F29D.enum_type=error_code_pb.E1M
M11G.fields={F29D, F30D, F31D, F32D, F33D}
F34D.enum_type=error_code_pb.E1M
F35D.message_type=common_new_pb.M9G
M13G.fields={F34D, F35D}
F36D.enum_type=error_code_pb.E1M
F37D.message_type=common_new_pb.M9G
M16G.fields={F36D, F37D}
M17G.fields={F38D, F39D}
F42D.enum_type=error_code_pb.E1M
M18G.fields={F40D, F41D, F42D}
M19G.fields={F43D, F44D, F45D}
F48D.enum_type=error_code_pb.E1M
M20G.fields={F46D, F47D, F48D}
M22G.fields={F49D}
F51D.enum_type=error_code_pb.E1M
M23G.fields={F50D, F51D}
M24G.fields={F52D, F53D, F54D}
F58D.enum_type=error_code_pb.E1M
M25G.fields={F55D, F56D, F57D, F58D}
M26G.fields={F59D}
F60D.enum_type=error_code_pb.E1M
M27G.fields={F60D}
M28G.fields={F61D}
M29G.fields={F62D, F63D}
F64D.enum_type=error_code_pb.E1M
M30G.fields={F64D}
M31G.fields={F65D, F66D}
M32G.fields={F67D, F68D, F69D}
F70D.enum_type=error_code_pb.E1M
M33G.fields={F70D}
M34G.fields={F71D, F72D}
F73D.enum_type=error_code_pb.E1M
M35G.fields={F73D, F74D, F75D, F76D}
M36G.fields={F77D, F78D, F79D}
M37G.fields={F80D}
F81D.enum_type=error_code_pb.E1M
M38G.fields={F81D, F82D}
M39G.fields={F83D, F84D, F85D}
F86D.enum_type=error_code_pb.E1M
M40G.fields={F86D, F87D}
M41G.fields={F88D, F89D, F90D, F91D, F92D, F93D}
F96D.enum_type=error_code_pb.E1M
M42G.fields={F94D, F95D, F96D}
F97D.enum_type=error_code_pb.E1M
M44G.fields={F97D, F98D}
F99D.enum_type=error_code_pb.E1M
M46G.fields={F99D}
M47G.fields={F100D, F101D, F102D, F103D, F104D}
F105D.message_type=M47G
M48G.fields={F105D}
F107D.message_type=M48G
M49G.fields={F106D, F107D}
M50G.fields={F108D}
F110D.message_type=M49G
M51G.fields={F109D, F110D, F111D, F112D}
M52G.fields={F113D, F114D, F115D, F116D, F117D, F118D, F119D}
F120D.message_type=M52G
M53G.fields={F120D}
F121D.message_type=M52G
F122D.enum_type=error_code_pb.E1M
M54G.fields={F121D, F122D}
M55G.fields={F123D, F124D, F125D, F126D, F127D, F128D, F129D, F130D, F131D, F132D, F133D, F134D, F135D, F136D, F137D, F138D}
F139D.enum_type=M57G
M56G.fields={F139D, F140D, F141D, F142D, F143D, F144D}
F146D.message_type=M55G
F147D.message_type=M55G
F148D.enum_type=M57G
F149D.message_type=allSaintsDay_pb.M3G
M58G.fields={F145D, F146D, F147D, F148D, F149D, F150D}
M60G.fields={F151D, F152D}
M61G.fields={F153D, F154D}
F156D.message_type=M60G
M62G.fields={F155D, F156D, F157D}
F158D.message_type=M62G
F159D.message_type=M61G
F168D.message_type=common_new_pb.M9G
F169D.message_type=common_new_pb.M9G
M63G.fields={F158D, F159D, F160D, F161D, F162D, F163D, F164D, F165D, F166D, F167D, F168D, F169D}
M64G.fields={F170D}
F171D.enum_type=error_code_pb.E1M
M68G.fields={F171D}
F173D.enum_type=error_code_pb.E1M
M70G.fields={F172D, F173D}
M71G.fields={F174D, F175D}
F176D.enum_type=error_code_pb.E1M
M72G.fields={F176D, F177D}
M73G.fields={F178D, F179D, F180D, F181D, F182D, F183D, F184D, F185D}
M74G.fields={F186D, F187D, F188D, F189D, F190D}
F194D.message_type=common_pb.M1G
M75G.fields={F191D, F192D, F193D, F194D}
F196D.message_type=M75G
F200D.message_type=M79G
M78G.fields={F195D, F196D, F197D, F198D, F199D, F200D}
M79G.fields={F201D, F202D}
M80G.fields={F203D, F204D, F205D}
M81G.fields={F206D}
F210D.message_type=M80G
M82G.fields={F207D, F208D, F209D, F210D}
M83G.fields={F211D, F212D, F213D}
F214D.enum_type=error_code_pb.E1M
M84G.fields={F214D}
M85G.fields={F215D, F216D, F217D}
F218D.enum_type=error_code_pb.E1M
M86G.fields={F218D, F219D, F220D, F221D, F222D}
M87G.fields={F223D, F224D}
M88G.fields={F225D, F226D, F227D}
F229D.message_type=M88G
M89G.fields={F228D, F229D}
M90G.fields={F230D, F231D, F232D, F233D, F234D}
F236D.message_type=M87G
M91G.fields={F235D, F236D}
M92G.fields={F237D, F238D, F239D}
F240D.message_type=M92G
M93G.fields={F240D}
M94G.fields={F241D, F242D, F243D}
F244D.enum_type=error_code_pb.E1M
F249D.message_type=M93G
M95G.fields={F244D, F245D, F246D, F247D, F248D, F249D}
F253D.message_type=M93G
M96G.fields={F250D, F251D, F252D, F253D, F254D}
M97G.fields={F255D, F256D}
F257D.enum_type=error_code_pb.E1M
M98G.fields={F257D, F258D, F259D, F260D, F261D, F262D}
M99G.fields={F263D, F264D, F265D}
M100G.fields={F266D, F267D, F268D, F269D, F270D, F271D}
M101G.fields={F272D}
F274D.message_type=M100G
M102G.fields={F273D, F274D}
M103G.fields={F275D, F276D}
F277D.enum_type=error_code_pb.E1M
M104G.fields={F277D, F278D, F279D, F280D, F281D, F282D}
M105G.fields={F283D, F284D}
F285D.enum_type=error_code_pb.E1M
M106G.fields={F285D, F286D, F287D}
F290D.message_type=common_new_pb.M1G
F298D.message_type=common_new_pb.M1G
M107G.fields={F288D, F289D, F290D, F291D, F292D, F293D, F294D, F295D, F296D, F297D, F298D, F299D, F300D}
M109G.fields={F301D, F302D, F303D, F304D, F305D, F306D}
F307D.enum_type=M111G
F308D.enum_type=M112G
M110G.fields={F307D, F308D, F309D, F310D}
M113G.fields={F311D}
F312D.enum_type=error_code_pb.E1M
F313D.message_type=M107G
M114G.fields={F312D, F313D}
M115G.fields={F314D, F315D, F316D, F317D, F318D}
F319D.enum_type=error_code_pb.E1M
F320D.message_type=M107G
M116G.fields={F319D, F320D}
M117G.fields={F321D, F322D}
F323D.enum_type=error_code_pb.E1M
F324D.message_type=M107G
M118G.fields={F323D, F324D}
M119G.fields={F325D, F326D}
F327D.enum_type=error_code_pb.E1M
M120G.fields={F327D, F328D}
M121G.fields={F329D}
F330D.enum_type=error_code_pb.E1M
F331D.message_type=M107G
M122G.fields={F330D, F331D}
M123G.fields={F332D, F333D}
F334D.enum_type=error_code_pb.E1M
F335D.message_type=M107G
M124G.fields={F334D, F335D}
M125G.fields={F336D, F337D}
F338D.enum_type=error_code_pb.E1M
F339D.message_type=M107G
M126G.fields={F338D, F339D}
F340D.enum_type=error_code_pb.E1M
F341D.message_type=M109G
F342D.message_type=M110G
M128G.fields={F340D, F341D, F342D}
M130G.fields={F343D, F344D, F345D, F346D, F347D, F348D, F349D}
F350D.enum_type=error_code_pb.E1M
M132G.fields={F350D, F351D}
M133G.fields={F352D}
F353D.enum_type=error_code_pb.E1M
M134G.fields={F353D, F354D, F355D}
M135G.fields={F356D, F357D}
F358D.enum_type=error_code_pb.E1M
M136G.fields={F358D, F359D, F360D}
M137G.fields={F361D, F362D, F363D, F364D}
M138G.fields={F365D, F366D, F367D, F368D, F369D, F370D, F371D, F372D}
F373D.enum_type=error_code_pb.E1M
F375D.message_type=M137G
F376D.message_type=M137G
F377D.message_type=M138G
M140G.fields={F373D, F374D, F375D, F376D, F377D}
M141G.fields={F378D, F379D}
F380D.message_type=M141G
M142G.fields={F380D}
M143G.fields={F381D}
F382D.enum_type=error_code_pb.E1M
M145G.fields={F382D, F383D}
M146G.fields={F384D}
F385D.enum_type=error_code_pb.E1M
M147G.fields={F385D, F386D}
M148G.fields={F387D}
F388D.enum_type=error_code_pb.E1M
M149G.fields={F388D, F389D}
M150G.fields={F390D, F391D, F392D, F393D}
F395D.message_type=M150G
M151G.fields={F394D, F395D}
F396D.message_type=M151G
M152G.fields={F396D}
F397D.message_type=M151G
M153G.fields={F397D}
M154G.fields={F398D, F399D, F400D}
F401D.enum_type=error_code_pb.E1M
M155G.fields={F401D, F402D, F403D}
F404D.enum_type=error_code_pb.E1M
F407D.message_type=M150G
M156G.fields={F404D, F405D, F406D, F407D}
M157G.fields={F408D, F409D}
F410D.enum_type=error_code_pb.E1M
F413D.message_type=M150G
M158G.fields={F410D, F411D, F412D, F413D}
M159G.fields={F414D}
F415D.enum_type=error_code_pb.E1M
F418D.message_type=common_new_pb.M19G
M160G.fields={F415D, F416D, F417D, F418D}
M162G.fields={F419D}
F420D.enum_type=error_code_pb.E1M
M163G.fields={F420D}
M165G.fields={F421D, F422D}
M166G.fields={F423D, F424D, F425D}
F426D.message_type=M166G
M167G.fields={F426D}
M168G.fields={F427D}
F428D.enum_type=error_code_pb.E1M
M169G.fields={F428D, F429D}
M170G.fields={F430D, F431D}
F432D.enum_type=error_code_pb.E1M
M171G.fields={F432D, F433D, F434D, F435D}
M172G.fields={F436D}
F437D.enum_type=error_code_pb.E1M
F439D.message_type=sandbox_pb.M1G
M173G.fields={F437D, F438D, F439D, F440D}
F441D.enum_type=error_code_pb.E1M
F442D.message_type=sandbox_pb.M142G
M176G.fields={F441D, F442D}
M179G.fields={F443D, F444D, F445D}
M180G.fields={F446D, F447D}
F448D.enum_type=error_code_pb.E1M
M181G.fields={F448D, F449D, F450D, F451D, F452D}
M182G.fields={F453D, F454D}
F455D.enum_type=error_code_pb.E1M
M183G.fields={F455D, F456D, F457D, F458D}

ACTIVITY_TYPE_FESTIVAL = 6
ACTIVITY_TYPE_MONTHLY = 2
ACTIVITY_TYPE_NEW = 7
ACTIVITY_TYPE_RMB = 3
ACTIVITY_TYPE_SPECIALGIFT = 5
ACTIVITY_TYPE_WEEKLY = 1
ACTIVITY_TYPE_WELFARE = 4
ACTTYPE_ALLIANCEBOSS = 103
ACTTYPE_ALLIANCEDUEL = 108
ACTTYPE_ALLIANCEDUELBATTLE = 115
ACTTYPE_ALLIANCE_KILLNUM = 502
ACTTYPE_ALLIANCE_POWER = 501
ACTTYPE_ALLSAINTSDAY_SCORE = 4
ACTTYPE_ALLSAINTSDAY_UPVOTE = 3
ACTTYPE_ALLSAINTSDAY_UPVOTE_SUMMARY = 5
ACTTYPE_ARMSRACE = 102
ACTTYPE_CAMPTRIAL_FORESET = 105
ACTTYPE_CAMPTRIAL_HUMANS = 106
ACTTYPE_CAMPTRIAL_NIGHT = 107
ACTTYPE_COMMON_MAX = 401
ACTTYPE_ENDLESS_MINILEVEL = 114
ACTTYPE_LABOURDAY = 2
ACTTYPE_LORD_TOUR = 113
ACTTYPE_MAKE_FOOD = 1
ACTTYPE_NCRANKING = 110
ACTTYPE_PARTY_ALLIANCE = 117
ACTTYPE_PARTY_PERSON = 116
ACTTYPE_PERSONAL_KILL_NUM = 303
ACTTYPE_PERSONAL_MAINCITY_LEVEL = 302
ACTTYPE_PERSONAL_MAX_PAL_POWER = 304
ACTTYPE_PERSONAL_PASS_CHECKPOINT = 305
ACTTYPE_PERSONAL_POWER = 301
ACTTYPE_PERSONAL_SELF_POWER = 306
ACTTYPE_SKYFALL = 112
ACTTYPE_STRONGEST_COMMANDER = 109
ACTTYPE_VIOLENT_MONSTER_LEADER_ALLIANCE = 621
ACTTYPE_VIOLENT_MONSTER_LEADER_PERSON = 620
ACTTYPE_VIOLENT_MONSTER_LEADER_TYPE_DAMAGE = 622
ACTTYPE_WORLDBOSS_ALLIANCE_DAMAGE = 111
ACTTYPE_WORLDBOSS_DAMAGE = 101
ACTTYPE_ZOMBIECOMING = 104
ACTTYPE_ZONE_BATTLE_ALLIANCE_SCORE = 602
ACTTYPE_ZONE_BATTLE_PERSON_SCORE = 601
ACTTYPE_ZONE_WORLD_BATTLE_TOTAL_SCORE = 605
ALREADY_SIGN = 1
AtyNoticePeriord_StrongestCommander = 121
AtyNoticePeriord_ZombieApocalypse = 220
BORKENTIME = 6
CAN_SIGN = 2
CommActRankinfo =M(M55G)
CommunityGiftStatus_Finish = 2
CommunityGiftStatus_Init = 0
CommunityGiftStatus_Reward = 1
ExchangeGoodInfo =M(M88G)
FESTIAVL = 8
FRISTRECHATGE = 5
FirstRankData =M(M138G)
FullBattle_Goods =M(M87G)
INSERT = 1
MONTHLY = 2
MopUpReward =M(M1G)
NOT_SIGN = 4
OneAtyChooseWeekCard =M(M151G)
REMOVE = 2
RMB = 3
ScheduleAtyData =M(M109G)
StageRewardReqInfo =M(M52G)
SwapOrderData =M(M107G)
SwapOrderStatus_Cancel = 4
SwapOrderStatus_Finish = 3
SwapOrderStatus_Idle = 1
SwapOrderStatus_Recycled = 6
SwapOrderStatus_Transacted = 2
SwapOrderStatus_Transacting = 5
TAtyNoticePeriod =M(M166G)
TBXyxBasePartData =M(M63G)
TBrokenSTMonster =M(M2G)
TGatheringChatPushinfo =M(M74G)
TMSG_ACORNPUB_TREASURE_PARASE_REQ =M(M125G)
TMSG_ACORNPUB_TREASURE_PARASE_RSP =M(M126G)
TMSG_ACTIVE_CHOOSE_WEEK_CARD_NTF =M(M156G)
TMSG_ACTIVITY_COMPLETE_NTF =M(M8G)
TMSG_ACTIVITY_DATA_REQ =M(M101G)
TMSG_ACTIVITY_DATA_RSP =M(M102G)
TMSG_ACTIVITY_GROW_GIFT2_STATE =M(M9G)
TMSG_ACTIVITY_NOTICEPERIOD_NTF =M(M167G)
TMSG_All_ATY_CHOOSE_WEEK_CARD_NTF =M(M152G)
TMSG_BATTLE_PASS_GET_ALL_REWARD_REQ =M(M162G)
TMSG_BATTLE_PASS_GET_ALL_REWARD_RSP =M(M163G)
TMSG_BROKENST_MOPUP_REWARD_NTF =M(M3G)
TMSG_BROKENST_UPDATA_BADGES_NTF =M(M7G)
TMSG_BUY_CHOOSE_WEEK_CARD_CHECK_REQ =M(M154G)
TMSG_BUY_CHOOSE_WEEK_CARD_CHECK_RSP =M(M155G)
TMSG_COMMONATY_CANCLE_EXCHANGE_REQ =M(M119G)
TMSG_COMMONATY_CANCLE_EXCHANGE_RSP =M(M120G)
TMSG_COMMONATY_EXCHANGE_GOODS_REQ =M(M117G)
TMSG_COMMONATY_EXCHANGE_GOODS_RSP =M(M118G)
TMSG_COMMONATY_EXCHANGE_ORDER_LIST_REQ =M(M121G)
TMSG_COMMONATY_EXCHANGE_ORDER_LIST_RSP =M(M122G)
TMSG_COMMONATY_SHELF_GOODS_REQ =M(M115G)
TMSG_COMMONATY_SHELF_GOODS_RSP =M(M116G)
TMSG_COMMONATY_VIEWORDER_REQ =M(M113G)
TMSG_COMMONATY_VIEWORDER_RSP =M(M114G)
TMSG_COMMONATY_VIEW_ORDER_RECORD_REQ =M(M123G)
TMSG_COMMONATY_VIEW_ORDER_RECORD_RSP =M(M124G)
TMSG_COMMUNITYGIFT_GETREWARD_REQ =M(M148G)
TMSG_COMMUNITYGIFT_GETREWARD_RSP =M(M149G)
TMSG_COMMUNITYGIFT_JUMP_REQ =M(M146G)
TMSG_COMMUNITYGIFT_JUMP_RSP =M(M147G)
TMSG_COMMUNITYGIFT_REWARDSTATUS_REQ =M(M144G)
TMSG_COMMUNITYGIFT_REWARDSTATUS_RSP =M(M145G)
TMSG_COMM_ACTIVITY_RANK_REQ =M(M56G)
TMSG_COMM_ACTIVITY_RANK_RSP =M(M58G)
TMSG_DEL_MIRACLE_BOX_DATA_REQ =M(M105G)
TMSG_DEL_MIRACLE_BOX_DATA_RSP =M(M106G)
TMSG_EXCHANGE_SHOP_GETITEM_NTF =M(M90G)
TMSG_EXCHANGE_SHOP_INFO_NTF =M(M89G)
TMSG_FACEBOOK_GUIDE_REQ =M(M45G)
TMSG_FACEBOOK_GUIDE_RSP =M(M46G)
TMSG_FULLBATTLE_EXCHANGE_REQ =M(M85G)
TMSG_FULLBATTLE_EXCHANGE_RSP =M(M86G)
TMSG_FULLBATTLE_REWARD_NTF =M(M91G)
TMSG_GATHERING_AWARD_REQ =M(M71G)
TMSG_GATHERING_AWARD_RSP =M(M72G)
TMSG_GATHERING_NTF =M(M73G)
TMSG_GEAR_SUPPLY_DRAWAWARD_REQ =M(M133G)
TMSG_GEAR_SUPPLY_DRAWAWARD_RSP =M(M134G)
TMSG_GEAR_SUPPLY_GETACTIVITYDATA_REQ =M(M129G)
TMSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP =M(M130G)
TMSG_GEAR_SUPPLY_RECEIVEAWARD_REQ =M(M131G)
TMSG_GEAR_SUPPLY_RECEIVEAWARD_RSP =M(M132G)
TMSG_GEAR_SUPPLY_SETREWARD_REQ =M(M135G)
TMSG_GEAR_SUPPLY_SETREWARD_RSP =M(M136G)
TMSG_GETBINDEMAIL_REWARD_REQ =M(M164G)
TMSG_GETBINDEMAIL_REWARD_RSP =M(M165G)
TMSG_GET_CHOOSE_WEEK_CARD_REWARD_REQ =M(M157G)
TMSG_GET_CHOOSE_WEEK_CARD_REWARD_RSP =M(M158G)
TMSG_GHOST_PARTY_HOLD_LIST_REQ =M(M175G)
TMSG_GHOST_PARTY_HOLD_LIST_RSP =M(M176G)
TMSG_GHOST_PARTY_HOLD_REQ =M(M172G)
TMSG_GHOST_PARTY_HOLD_RSP =M(M173G)
TMSG_GOOGLE_COMMENT_SET_REQ =M(M168G)
TMSG_GOOGLE_COMMENT_SET_RSP =M(M169G)
TMSG_HALLOWEEN_PARTY_GETINFO_REQ =M(M178G)
TMSG_HALLOWEEN_PARTY_GETINFO_RSP =M(M179G)
TMSG_HALLOWEEN_PARTY_GET_REWARD_REQ =M(M182G)
TMSG_HALLOWEEN_PARTY_GET_REWARD_RSP =M(M183G)
TMSG_HALLOWEEN_PARTY_SUBMIT_REQ =M(M180G)
TMSG_HALLOWEEN_PARTY_SUBMIT_RSP =M(M181G)
TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_REQ =M(M50G)
TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP =M(M51G)
TMSG_LUCKY_DRAW_DAILYGIFT_NTF =M(M96G)
TMSG_LUCKY_DRAW_LOTTERY_REQ =M(M94G)
TMSG_LUCKY_DRAW_LOTTERY_RSP =M(M95G)
TMSG_MINIGAME_ACTIVITY_JION_REQ =M(M24G)
TMSG_MINIGAME_ACTIVITY_JION_RSP =M(M25G)
TMSG_ONE_ATY_CHOOSE_WEEK_CARD_NTF =M(M153G)
TMSG_OPEN_MIRACLE_BOX_REQ =M(M103G)
TMSG_OPEN_MIRACLE_BOX_RSP =M(M104G)
TMSG_RESERVATION_EMAIL_BGETREWARD_REQ =M(M69G)
TMSG_RESERVATION_EMAIL_BGETREWARD_RSP =M(M70G)
TMSG_RESERVATION_EMAIL_BIND_REQ =M(M65G)
TMSG_RESERVATION_EMAIL_SENDREWARD_REQ =M(M67G)
TMSG_RESERVATION_EMAIL_SENDREWARD_RSP =M(M68G)
TMSG_RESERVATION_EMAIL_UNBIND_REQ =M(M66G)
TMSG_SCHEDULE_LIST_REQ =M(M127G)
TMSG_SCHEDULE_LIST_RSP =M(M128G)
TMSG_SEVENDAY_LOGIN_RECEIVEAWARD_REQ =M(M159G)
TMSG_SEVENDAY_LOGIN_RECEIVEAWARD_RSP =M(M160G)
TMSG_SKY_FALL_DOOMSDAY_NTF =M(M143G)
TMSG_SKY_FALL_TASK_PRO_NTF =M(M142G)
TMSG_SKY_FALL_THREAT_MONSTER_REQ =M(M139G)
TMSG_SKY_FALL_THREAT_MONSTER_RSP =M(M140G)
TMSG_SLOT_MACHINE_DRAW_REQ =M(M170G)
TMSG_SLOT_MACHINE_DRAW_RSP =M(M171G)
TMSG_STRONGEST_COMMANDER_NTF =M(M99G)
TMSG_STRONGEST_COMMANDER_REQ =M(M97G)
TMSG_STRONGEST_COMMANDER_RSP =M(M98G)
TMSG_WORDBOSS_DMGRECORD_NTF =M(M83G)
TMSG_WORDBOSS_ERR_NTF =M(M84G)
TMSG_WORDBOSS_INFO_NTF =M(M78G)
TMSG_WORDBOSS_INFO_REQ =M(M77G)
TMSG_WORDBOSS_LINE_INFO_REQ =M(M81G)
TMSG_WORDBOSS_LINE_INFO_RSP =M(M82G)
TMSG_XYX_ADVRW_REQ =M(M39G)
TMSG_XYX_ADVRW_RSP =M(M40G)
TMSG_XYX_DATA_NTF =M(M64G)
TMSG_XYX_ENDLESS_SET_HEROPOWER_REQ =M(M19G)
TMSG_XYX_ENDLESS_SET_HEROPOWER_RSP =M(M20G)
TMSG_XYX_GET_DATA_REQ =M(M21G)
TMSG_XYX_GET_PROP_REQ =M(M34G)
TMSG_XYX_GET_PROP_RSP =M(M35G)
TMSG_XYX_GOT_DELAYAWARD_REQ =M(M12G)
TMSG_XYX_GOT_DELAYAWARD_RSP =M(M13G)
TMSG_XYX_GOT_GAME_REWARD_REQ =M(M15G)
TMSG_XYX_GOT_GAME_REWARD_RSP =M(M16G)
TMSG_XYX_INITGAMEDATA_REQ =M(M22G)
TMSG_XYX_INITGAMEDATA_RSP =M(M23G)
TMSG_XYX_ITEMACTIVE_REQ =M(M37G)
TMSG_XYX_ITEMACTIVE_RSP =M(M38G)
TMSG_XYX_LEVEL_DATA_NTF =M(M31G)
TMSG_XYX_LEVEL_REQ =M(M43G)
TMSG_XYX_LEVEL_RSP =M(M44G)
TMSG_XYX_LINK_DATA_NTF =M(M28G)
TMSG_XYX_PASS_LV_REQ =M(M10G)
TMSG_XYX_PASS_LV_RSP =M(M11G)
TMSG_XYX_PROP_NTF =M(M36G)
TMSG_XYX_SET_FAILSTAGE_REQ =M(M17G)
TMSG_XYX_SET_FAILSTAGE_RSP =M(M18G)
TMSG_XYX_SET_LEVEL_REQ =M(M29G)
TMSG_XYX_SET_LEVEL_RSP =M(M30G)
TMSG_XYX_SET_LINK_REQ =M(M26G)
TMSG_XYX_SET_LINK_RSP =M(M27G)
TMSG_XYX_SET_PROP_REQ =M(M32G)
TMSG_XYX_SET_PROP_RSP =M(M33G)
TMSG_XYX_STAGE_REWARD_MULTI_REQ =M(M53G)
TMSG_XYX_STAGE_REWARD_MULTI_RSP =M(M54G)
TMSG_XYX_STAGE_REWARD_REQ =M(M41G)
TMSG_XYX_STAGE_REWARD_RSP =M(M42G)
TMiracleBoxData =M(M92G)
TMiracleBoxDataList =M(M93G)
TNewActivityData =M(M100G)
TPbXyxPassLvPartData =M(M62G)
TPbXyxStageRewardData =M(M61G)
TXyxPassLvData =M(M60G)
TZombieApocalypseSchedule =M(M110G)
TaskPro =M(M141G)
ThreatMonData =M(M137G)
UPDATE = 3
UPGRADE = 7
WEEKLY = 1
WELFARE = 4
WILL_SIGN = 3
WeekCard =M(M150G)
WordBossData =M(M75G)
WordBossHero =M(M80G)
WorldDate =M(M79G)
accountBookinfo =M(M49G)
emZombieApocalypseScheduleRunningState_Attack = 2
emZombieApocalypseScheduleRunningState_None = 0
emZombieApocalypseScheduleRunningState_Prepare = 1
emZombieApocalypseScheduleState_AppointmentOpen = 1
emZombieApocalypseScheduleState_CoolDowm = 3
emZombieApocalypseScheduleState_None = 0
emZombieApocalypseScheduleState_Running = 2
enActivityCommonLotteryType_LuckyDraw = 1007
enActivityCommonLotteryType_MiracleBox = 1020
enActivityHeadingCodeType_AccumulatedRecharge = 1009
enActivityHeadingCodeType_ActivityRank = 107
enActivityHeadingCodeType_AllianceDropChests = 1031
enActivityHeadingCodeType_AllianceDuel = 212
enActivityHeadingCodeType_AllianceDuelBattle = 213
enActivityHeadingCodeType_AllianceMilitaryDrill = 204
enActivityHeadingCodeType_Arena = 207
enActivityHeadingCodeType_ArmsRace = 202
enActivityHeadingCodeType_CampTrial = 210
enActivityHeadingCodeType_Carriage = 214
enActivityHeadingCodeType_ChooseWeekCard = 1022
enActivityHeadingCodeType_CommunityGift = 218
enActivityHeadingCodeType_DualBattlePass = 1001
enActivityHeadingCodeType_GearSupply = 1021
enActivityHeadingCodeType_GeneralTrial = 203
enActivityHeadingCodeType_LoginReward = 1017
enActivityHeadingCodeType_MassBattle = 205
enActivityHeadingCodeType_MiniGameActivity = 106
enActivityHeadingCodeType_MiniLevelActivity = 222
enActivityHeadingCodeType_MonthlyCard = 1012
enActivityHeadingCodeType_MonthlyCard2 = 1024
enActivityHeadingCodeType_NoMissionBattlePass = 1003
enActivityHeadingCodeType_PaidTask = 102
enActivityHeadingCodeType_SevenDayLogin = 219
enActivityHeadingCodeType_SkyFall = 217
enActivityHeadingCodeType_SlotMachineDraw = 108
enActivityHeadingCodeType_StrongestCommander = 1008
enActivityHeadingCodeType_Task = 101
enActivityHeadingCodeType_TripleBattlePass = 1002
enActivityHeadingCodeType_UrbanRace = 211
enActivityHeadingCodeType_VanguardCommander = 1005
enActivityHeadingCodeType_VanguardObjective = 1004
enActivityHeadingCodeType_WeeklyCard = 1011
enActivityHeadingCodeType_WorldBoss = 201
enActivityHeadingCodeType_ZombieApocalypse = 221
enActivityHeadingCodeType_ZombieInvasion = 206
enActivityHeadingCodeType_ZoneBattleDuel = 215
enActivityHeadingCodeType_lordObjective = 1026
oneBookinfo =M(M47G)
oneDayBookinfo =M(M48G)

