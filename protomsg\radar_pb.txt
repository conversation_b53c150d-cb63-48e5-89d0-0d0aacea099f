-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local error_code_pb=require("error_code_pb")
module('radar_pb')


V1M=V(4,"EnumRadarMission_DemonCastle",0,1)
V2M=V(4,"EnumRadarMission_BeastInvasion",1,2)
V3M=V(4,"EnumRadarMission_EliteDemon",2,3)
V4M=V(4,"EnumRadarMission_Collection",3,4)
V5M=V(4,"EnumRadarMission_EnvironmentExplorate",4,5)
V6M=V(4,"EnumRadarMission_RescueSurvivor",5,6)
V7M=V(4,"EnumRadarMission_Challenge",6,7)
V8M=V(4,"EnumRadarMission_DigTreasure",7,8)
V9M=V(4,"EnumRadarMission_AssistAllies",8,9)
V10M=V(4,"EnumRadarMission_DoomsdayWander",9,10)
E1M=E(3,"EnRadarMissionType",".CSMsg.EnRadarMissionType")
V11M=V(4,"EnumRadarMissionQuality_General",0,1)
V12M=V(4,"EnumRadarMissionQuality_Excellent",1,2)
V13M=V(4,"EnumRadarMissionQuality_Rare",2,3)
V14M=V(4,"EnumRadarMissionQuality_MuchRare",3,4)
V15M=V(4,"EnumRadarMissionQuality_Epic",4,5)
V16M=V(4,"EnumRadarMissionQuality_Legend",5,6)
E2M=E(3,"EnRadarMissionQuality",".CSMsg.EnRadarMissionQuality")
V17M=V(4,"EnumRadarMissionState_InitialState",0,0)
V18M=V(4,"EnumRadarMissionState_Doing",1,1)
V19M=V(4,"EnumRadarMissionState_NotTroops",2,2)
V20M=V(4,"EnumRadarMissionState_CanReceive",3,3)
V21M=V(4,"EnumRadarMissionState_Finished",4,4)
E3M=E(3,"EnRadarMissionState",".CSMsg.EnRadarMissionState")
F1D=F(2,"dbid",".CSMsg.AssistorInfo.dbid",1,0,2,false,0,5,1)
F2D=F(2,"playerName",".CSMsg.AssistorInfo.playerName",2,1,2,false,"",9,9)
M1G=D(1,"AssistorInfo",".CSMsg.AssistorInfo",false,{},{},nil,{})
F3D=F(2,"itemId",".CSMsg.RewardItem.itemId",1,0,2,false,0,5,1)
F4D=F(2,"itemNum",".CSMsg.RewardItem.itemNum",2,1,2,false,0,5,1)
M2G=D(1,"RewardItem",".CSMsg.RewardItem",false,{},{},nil,{})
F5D=F(2,"dbid",".CSMsg.LeagueInfo.dbid",1,0,2,false,0,5,1)
F6D=F(2,"playerName",".CSMsg.LeagueInfo.playerName",2,1,2,false,"",9,9)
F7D=F(2,"faceId",".CSMsg.LeagueInfo.faceId",3,2,2,false,0,5,1)
F8D=F(2,"frameId",".CSMsg.LeagueInfo.frameId",4,3,2,false,0,5,1)
F9D=F(2,"allianceShortName",".CSMsg.LeagueInfo.allianceShortName",5,4,2,false,"",9,9)
F10D=F(2,"schloss",".CSMsg.LeagueInfo.schloss",6,5,1,false,0,5,1)
F11D=F(2,"faceStr",".CSMsg.LeagueInfo.faceStr",7,6,2,false,"",9,9)
F12D=F(2,"schlossEffectId",".CSMsg.LeagueInfo.schlossEffectId",8,7,1,false,0,5,1)
M3G=D(1,"LeagueInfo",".CSMsg.LeagueInfo",false,{},{},nil,{})
F13D=F(2,"sid",".CSMsg.SandboxEntity.sid",1,0,2,false,0,4,4)
F14D=F(2,"posX",".CSMsg.SandboxEntity.posX",2,1,2,false,0,5,1)
F15D=F(2,"posY",".CSMsg.SandboxEntity.posY",3,2,2,false,0,5,1)
M4G=D(1,"SandboxEntity",".CSMsg.SandboxEntity",false,{},{},nil,{})
F16D=F(2,"missionId",".CSMsg.RadarMissionInfo.missionId",1,0,2,false,0,4,4)
F17D=F(2,"taskId",".CSMsg.RadarMissionInfo.taskId",2,1,2,false,0,5,1)
F18D=F(2,"typeID",".CSMsg.RadarMissionInfo.typeID",3,2,2,false,0,5,1)
F19D=F(2,"missionEndTime",".CSMsg.RadarMissionInfo.missionEndTime",4,3,1,false,0,5,1)
F20D=F(2,"missionState",".CSMsg.RadarMissionInfo.missionState",5,4,2,false,nil,14,8)
F21D=F(2,"leagueInfo",".CSMsg.RadarMissionInfo.leagueInfo",6,5,1,false,nil,11,10)
F22D=F(2,"sandboxEntity",".CSMsg.RadarMissionInfo.sandboxEntity",7,6,1,false,nil,11,10)
M5G=D(1,"RadarMissionInfo",".CSMsg.RadarMissionInfo",false,{},{},nil,{})
F23D=F(2,"radarLevel",".CSMsg.RadarTotalInfo.radarLevel",1,0,2,false,0,5,1)
F24D=F(2,"curFinishedMissionCount",".CSMsg.RadarTotalInfo.curFinishedMissionCount",2,1,2,false,0,5,1)
F25D=F(2,"remainMisionCount",".CSMsg.RadarTotalInfo.remainMisionCount",3,2,2,false,0,5,1)
F26D=F(2,"refreshEndTime",".CSMsg.RadarTotalInfo.refreshEndTime",4,3,2,false,0,5,1)
F27D=F(2,"todayBuyCount",".CSMsg.RadarTotalInfo.todayBuyCount",5,4,2,false,0,5,1)
F28D=F(2,"canOneClickExecution",".CSMsg.RadarTotalInfo.canOneClickExecution",6,5,1,false,false,8,7)
F29D=F(2,"levelMission",".CSMsg.RadarTotalInfo.levelMission",7,6,2,false,false,8,7)
M7G=D(1,"RadarTotalInfo",".CSMsg.RadarTotalInfo",false,{},{},nil,{})
F30D=F(2,"param",".CSMsg.TMSG_RADAR_INFO_REQ.param",1,0,1,false,0,5,1)
M8G=D(1,"TMSG_RADAR_INFO_REQ",".CSMsg.TMSG_RADAR_INFO_REQ",false,{},{},nil,{})
F31D=F(2,"errCode",".CSMsg.TMSG_RADAR_INFO_RSP.errCode",1,0,2,false,nil,14,8)
F32D=F(2,"radarTotalInfo",".CSMsg.TMSG_RADAR_INFO_RSP.radarTotalInfo",2,1,2,false,nil,11,10)
F33D=F(2,"missionInfoList",".CSMsg.TMSG_RADAR_INFO_RSP.missionInfoList",3,2,3,false,{},11,10)
M9G=D(1,"TMSG_RADAR_INFO_RSP",".CSMsg.TMSG_RADAR_INFO_RSP",false,{},{},nil,{})
F34D=F(2,"radarTotalInfo",".CSMsg.TMSG_RADAR_INFO_NTF.radarTotalInfo",1,0,2,false,nil,11,10)
F35D=F(2,"missionInfoList",".CSMsg.TMSG_RADAR_INFO_NTF.missionInfoList",2,1,3,false,{},11,10)
M11G=D(1,"TMSG_RADAR_INFO_NTF",".CSMsg.TMSG_RADAR_INFO_NTF",false,{},{},nil,{})
F36D=F(2,"radarLevel",".CSMsg.TMSG_RADAR_INFO_UPDATE_NTF.radarLevel",1,0,2,false,0,5,1)
F37D=F(2,"curFinishedMissionCount",".CSMsg.TMSG_RADAR_INFO_UPDATE_NTF.curFinishedMissionCount",2,1,2,false,0,5,1)
F38D=F(2,"remainMisionCount",".CSMsg.TMSG_RADAR_INFO_UPDATE_NTF.remainMisionCount",3,2,2,false,0,5,1)
F39D=F(2,"refreshEndTime",".CSMsg.TMSG_RADAR_INFO_UPDATE_NTF.refreshEndTime",4,3,2,false,0,5,1)
F40D=F(2,"todayBuyCount",".CSMsg.TMSG_RADAR_INFO_UPDATE_NTF.todayBuyCount",5,4,2,false,0,5,1)
F41D=F(2,"canOneClickExecution",".CSMsg.TMSG_RADAR_INFO_UPDATE_NTF.canOneClickExecution",6,5,1,false,false,8,7)
F42D=F(2,"levelMission",".CSMsg.TMSG_RADAR_INFO_UPDATE_NTF.levelMission",7,6,2,false,false,8,7)
F43D=F(2,"SurvivorBox_AwardNum",".CSMsg.TMSG_RADAR_INFO_UPDATE_NTF.SurvivorBox_AwardNum",8,7,1,false,0,5,1)
M12G=D(1,"TMSG_RADAR_INFO_UPDATE_NTF",".CSMsg.TMSG_RADAR_INFO_UPDATE_NTF",false,{},{},nil,{})
F44D=F(2,"missionIdList",".CSMsg.TMSG_RADAR_DO_MISSION_REQ.missionIdList",1,0,3,false,{},4,4)
F45D=F(2,"callback",".CSMsg.TMSG_RADAR_DO_MISSION_REQ.callback",2,1,1,false,0,5,1)
M13G=D(1,"TMSG_RADAR_DO_MISSION_REQ",".CSMsg.TMSG_RADAR_DO_MISSION_REQ",false,{},{},nil,{})
F46D=F(2,"errCode",".CSMsg.TMSG_RADAR_DO_MISSION_RSP.errCode",1,0,2,false,nil,14,8)
F47D=F(2,"missionInfoList",".CSMsg.TMSG_RADAR_DO_MISSION_RSP.missionInfoList",2,1,3,false,{},11,10)
F48D=F(2,"callback",".CSMsg.TMSG_RADAR_DO_MISSION_RSP.callback",3,2,1,false,0,5,1)
M14G=D(1,"TMSG_RADAR_DO_MISSION_RSP",".CSMsg.TMSG_RADAR_DO_MISSION_RSP",false,{},{},nil,{})
F49D=F(2,"missionId",".CSMsg.TMSG_RADAR_RECEIVE_REWARD_REQ.missionId",1,0,2,false,0,4,4)
M15G=D(1,"TMSG_RADAR_RECEIVE_REWARD_REQ",".CSMsg.TMSG_RADAR_RECEIVE_REWARD_REQ",false,{},{},nil,{})
F50D=F(2,"errCode",".CSMsg.TMSG_RADAR_RECEIVE_REWARD_RSP.errCode",1,0,2,false,nil,14,8)
F51D=F(2,"rewardIdList",".CSMsg.TMSG_RADAR_RECEIVE_REWARD_RSP.rewardIdList",2,1,3,false,{},11,10)
F52D=F(2,"assistorInfo",".CSMsg.TMSG_RADAR_RECEIVE_REWARD_RSP.assistorInfo",3,2,3,false,{},11,10)
F53D=F(2,"taskId",".CSMsg.TMSG_RADAR_RECEIVE_REWARD_RSP.taskId",4,3,2,false,0,5,1)
F54D=F(2,"missionId",".CSMsg.TMSG_RADAR_RECEIVE_REWARD_RSP.missionId",5,4,2,false,0,4,4)
F55D=F(2,"rewards",".CSMsg.TMSG_RADAR_RECEIVE_REWARD_RSP.rewards",6,5,3,false,{},5,1)
M16G=D(1,"TMSG_RADAR_RECEIVE_REWARD_RSP",".CSMsg.TMSG_RADAR_RECEIVE_REWARD_RSP",false,{},{},nil,{})
F56D=F(2,"param",".CSMsg.TMSG_RADAR_MISSION_REQ.param",1,0,1,false,0,5,1)
M17G=D(1,"TMSG_RADAR_MISSION_REQ",".CSMsg.TMSG_RADAR_MISSION_REQ",false,{},{},nil,{})
F57D=F(2,"errCode",".CSMsg.TMSG_RADAR_MISSION_RSP.errCode",1,0,2,false,nil,14,8)
M18G=D(1,"TMSG_RADAR_MISSION_RSP",".CSMsg.TMSG_RADAR_MISSION_RSP",false,{},{},nil,{})
F58D=F(2,"missionInfoList",".CSMsg.TMSG_RADAR_GENERATE_NEW_MISSION_NTF.missionInfoList",1,0,3,false,{},11,10)
M19G=D(1,"TMSG_RADAR_GENERATE_NEW_MISSION_NTF",".CSMsg.TMSG_RADAR_GENERATE_NEW_MISSION_NTF",false,{},{},nil,{})
F59D=F(2,"missionInfoList",".CSMsg.TMSG_RADAR_UPDATE_MISSION_INFO_NTF.missionInfoList",1,0,3,false,{},11,10)
M20G=D(1,"TMSG_RADAR_UPDATE_MISSION_INFO_NTF",".CSMsg.TMSG_RADAR_UPDATE_MISSION_INFO_NTF",false,{},{},nil,{})
F60D=F(2,"missionIdList",".CSMsg.TMSG_RADAR_DESTROY_MISSION_NTF.missionIdList",1,0,3,false,{},4,4)
M21G=D(1,"TMSG_RADAR_DESTROY_MISSION_NTF",".CSMsg.TMSG_RADAR_DESTROY_MISSION_NTF",false,{},{},nil,{})
F61D=F(2,"missionType",".CSMsg.TMSG_RADAR_DO_MISSION_RPT.missionType",1,0,2,false,nil,14,8)
F62D=F(2,"params",".CSMsg.TMSG_RADAR_DO_MISSION_RPT.params",2,1,3,false,{},5,1)
M22G=D(1,"TMSG_RADAR_DO_MISSION_RPT",".CSMsg.TMSG_RADAR_DO_MISSION_RPT",false,{},{},nil,{})
M24G=D(1,"TMSG_RADAR_LEVEL_MISSION_REQ",".CSMsg.TMSG_RADAR_LEVEL_MISSION_REQ",false,{},{},{},{})
F63D=F(2,"missionId",".CSMsg.RadarLevelTaskInfo.missionId",1,0,2,false,0,4,4)
F64D=F(2,"taskId",".CSMsg.RadarLevelTaskInfo.taskId",2,1,2,false,0,5,1)
M25G=D(1,"RadarLevelTaskInfo",".CSMsg.RadarLevelTaskInfo",false,{},{},nil,{})
F65D=F(2,"errCode",".CSMsg.TMSG_RADAR_LEVEL_MISSION_RSP.errCode",1,0,2,false,nil,14,8)
F66D=F(2,"taskList",".CSMsg.TMSG_RADAR_LEVEL_MISSION_RSP.taskList",2,1,3,false,{},11,10)
M26G=D(1,"TMSG_RADAR_LEVEL_MISSION_RSP",".CSMsg.TMSG_RADAR_LEVEL_MISSION_RSP",false,{},{},nil,{})
F67D=F(2,"roleid",".CSMsg.TMSG_RADAR_ASSIST_NTF.TAssistInfo.roleid",1,0,2,false,0,5,1)
F68D=F(2,"name",".CSMsg.TMSG_RADAR_ASSIST_NTF.TAssistInfo.name",2,1,2,false,"",9,9)
F69D=F(2,"faceStr",".CSMsg.TMSG_RADAR_ASSIST_NTF.TAssistInfo.faceStr",3,2,2,false,"",9,9)
F70D=F(2,"frameid",".CSMsg.TMSG_RADAR_ASSIST_NTF.TAssistInfo.frameid",4,3,2,false,0,5,1)
M28G=D(1,"TAssistInfo",".CSMsg.TMSG_RADAR_ASSIST_NTF.TAssistInfo",false,{},{},nil,{})
F71D=F(2,"assistInfo",".CSMsg.TMSG_RADAR_ASSIST_NTF.assistInfo",1,0,3,false,{},11,10)
M27G=D(1,"TMSG_RADAR_ASSIST_NTF",".CSMsg.TMSG_RADAR_ASSIST_NTF",false,nil,{},nil,{})
M29G=D(1,"TMSG_RADAR_GRATITUDE_REQ",".CSMsg.TMSG_RADAR_GRATITUDE_REQ",false,{},{},{},{})
F72D=F(2,"errCode",".CSMsg.TMSG_RADAR_GRATITUDE_RSP.errCode",1,0,2,false,nil,14,8)
M30G=D(1,"TMSG_RADAR_GRATITUDE_RSP",".CSMsg.TMSG_RADAR_GRATITUDE_RSP",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M}
E2M.values = {V11M,V12M,V13M,V14M,V15M,V16M}
E3M.values = {V17M,V18M,V19M,V20M,V21M}
M1G.fields={F1D, F2D}
M2G.fields={F3D, F4D}
M3G.fields={F5D, F6D, F7D, F8D, F9D, F10D, F11D, F12D}
M4G.fields={F13D, F14D, F15D}
F20D.enum_type=M6G
F21D.message_type=M3G
F22D.message_type=M4G
M5G.fields={F16D, F17D, F18D, F19D, F20D, F21D, F22D}
M7G.fields={F23D, F24D, F25D, F26D, F27D, F28D, F29D}
M8G.fields={F30D}
F31D.enum_type=error_code_pb.E1M
F32D.message_type=M7G
F33D.message_type=M5G
M9G.fields={F31D, F32D, F33D}
F34D.message_type=M7G
F35D.message_type=M5G
M11G.fields={F34D, F35D}
M12G.fields={F36D, F37D, F38D, F39D, F40D, F41D, F42D, F43D}
M13G.fields={F44D, F45D}
F46D.enum_type=error_code_pb.E1M
F47D.message_type=M5G
M14G.fields={F46D, F47D, F48D}
M15G.fields={F49D}
F50D.enum_type=error_code_pb.E1M
F51D.message_type=M2G
F52D.message_type=M1G
M16G.fields={F50D, F51D, F52D, F53D, F54D, F55D}
M17G.fields={F56D}
F57D.enum_type=error_code_pb.E1M
M18G.fields={F57D}
F58D.message_type=M5G
M19G.fields={F58D}
F59D.message_type=M5G
M20G.fields={F59D}
M21G.fields={F60D}
F61D.enum_type=M23G
M22G.fields={F61D, F62D}
M25G.fields={F63D, F64D}
F65D.enum_type=error_code_pb.E1M
F66D.message_type=M25G
M26G.fields={F65D, F66D}
M28G.fields={F67D, F68D, F69D, F70D}
M28G.containing_type=M27G
F71D.message_type=M28G
M27G.nested_types={M28G}
M27G.fields={F71D}
F72D.enum_type=error_code_pb.E1M
M30G.fields={F72D}

AssistorInfo =M(M1G)
EnumRadarMissionQuality_Epic = 5
EnumRadarMissionQuality_Excellent = 2
EnumRadarMissionQuality_General = 1
EnumRadarMissionQuality_Legend = 6
EnumRadarMissionQuality_MuchRare = 4
EnumRadarMissionQuality_Rare = 3
EnumRadarMissionState_CanReceive = 3
EnumRadarMissionState_Doing = 1
EnumRadarMissionState_Finished = 4
EnumRadarMissionState_InitialState = 0
EnumRadarMissionState_NotTroops = 2
EnumRadarMission_AssistAllies = 9
EnumRadarMission_BeastInvasion = 2
EnumRadarMission_Challenge = 7
EnumRadarMission_Collection = 4
EnumRadarMission_DemonCastle = 1
EnumRadarMission_DigTreasure = 8
EnumRadarMission_DoomsdayWander = 10
EnumRadarMission_EliteDemon = 3
EnumRadarMission_EnvironmentExplorate = 5
EnumRadarMission_RescueSurvivor = 6
LeagueInfo =M(M3G)
RadarLevelTaskInfo =M(M25G)
RadarMissionInfo =M(M5G)
RadarTotalInfo =M(M7G)
RewardItem =M(M2G)
SandboxEntity =M(M4G)
TMSG_RADAR_ASSIST_NTF =M(M27G)
TMSG_RADAR_ASSIST_NTF.TAssistInfo =M(M28G)
TMSG_RADAR_DESTROY_MISSION_NTF =M(M21G)
TMSG_RADAR_DO_MISSION_REQ =M(M13G)
TMSG_RADAR_DO_MISSION_RPT =M(M22G)
TMSG_RADAR_DO_MISSION_RSP =M(M14G)
TMSG_RADAR_GENERATE_NEW_MISSION_NTF =M(M19G)
TMSG_RADAR_GRATITUDE_REQ =M(M29G)
TMSG_RADAR_GRATITUDE_RSP =M(M30G)
TMSG_RADAR_INFO_NTF =M(M11G)
TMSG_RADAR_INFO_REQ =M(M8G)
TMSG_RADAR_INFO_RSP =M(M9G)
TMSG_RADAR_INFO_UPDATE_NTF =M(M12G)
TMSG_RADAR_LEVEL_MISSION_REQ =M(M24G)
TMSG_RADAR_LEVEL_MISSION_RSP =M(M26G)
TMSG_RADAR_MISSION_REQ =M(M17G)
TMSG_RADAR_MISSION_RSP =M(M18G)
TMSG_RADAR_RECEIVE_REWARD_REQ =M(M15G)
TMSG_RADAR_RECEIVE_REWARD_RSP =M(M16G)
TMSG_RADAR_UPDATE_MISSION_INFO_NTF =M(M20G)

