-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
module('commonParts_pb')


V1M=V(4,"ECommonPartModuleId_None",0,100)
V2M=V(4,"ECommonPartModuleId_CampTrial",1,101)
V3M=V(4,"ECommonPartModuleId_Exchange",2,102)
V4M=V(4,"ECommonPartModuleId_Gift",3,103)
V5M=V(4,"ECommonPartModuleId_AllianceDuel",4,104)
V6M=V(4,"ECommonPartModuleId_Lottery",5,105)
V7M=V(4,"ECommonPartModuleId_Activity",6,106)
V8M=V(4,"ECommonPartModuleId_Commander",7,107)
V9M=V(4,"ECommonPartModuleId_FuncitonOpen",8,108)
V10M=V(4,"ECommonPartModuleId_Troop",9,109)
V11M=V(4,"ECommonPartModuleId_GearSupply",10,110)
V12M=V(4,"ECommonPartModuleId_ChooseWeekCard",11,111)
V13M=V(4,"ECommonPartModuleId_BuildVisitor",12,112)
V14M=V(4,"ECommonPartModuleId_MonsterComingNew",13,113)
V15M=V(4,"EcommonPartModuleId_StrayDog",14,114)
V16M=V(4,"ECommonPartModuleId_RoleFrame",15,115)
V17M=V(4,"EcommonPartModuleId_ExtraLoot",16,116)
V18M=V(4,"ECommonPartModuleId_Max",17,117)
E1M=E(3,"ECommonPartModuleId",".CSMsg.ECommonPartModuleId")
F1D=F(2,"moduleId",".CSMsg.TPbModuleData.moduleId",1,0,2,false,0,13,3)
F2D=F(2,"moduleValue",".CSMsg.TPbModuleData.moduleValue",2,1,2,false,"",9,9)
F3D=F(2,"lastValue",".CSMsg.TPbModuleData.lastValue",3,2,1,false,"",9,9)
M1G=D(1,"TPbModuleData",".CSMsg.TPbModuleData",false,{},{},nil,{})
F4D=F(2,"part",".CSMsg.TPbCommonPart.part",1,0,3,false,{},11,10)
M2G=D(1,"TPbCommonPart",".CSMsg.TPbCommonPart",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M}
M1G.fields={F1D, F2D, F3D}
F4D.message_type=M1G
M2G.fields={F4D}

ECommonPartModuleId_Activity = 106
ECommonPartModuleId_AllianceDuel = 104
ECommonPartModuleId_BuildVisitor = 112
ECommonPartModuleId_CampTrial = 101
ECommonPartModuleId_ChooseWeekCard = 111
ECommonPartModuleId_Commander = 107
ECommonPartModuleId_Exchange = 102
ECommonPartModuleId_FuncitonOpen = 108
ECommonPartModuleId_GearSupply = 110
ECommonPartModuleId_Gift = 103
ECommonPartModuleId_Lottery = 105
ECommonPartModuleId_Max = 117
ECommonPartModuleId_MonsterComingNew = 113
ECommonPartModuleId_None = 100
ECommonPartModuleId_RoleFrame = 115
ECommonPartModuleId_Troop = 109
EcommonPartModuleId_ExtraLoot = 116
EcommonPartModuleId_StrayDog = 114
TPbCommonPart =M(M2G)
TPbModuleData =M(M1G)

