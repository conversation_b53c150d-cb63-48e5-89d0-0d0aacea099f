local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform
local RawImage = CS.UnityEngine.UI.RawImage


module("ui_land_revival_login_binding")

UIPath = "ui/prefabs/gw/gw_landrevival/uilandrevivallogin.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	txt_time = { path = "Panel/txt_time", type = Text, },
	btn_goTo = { path = "Panel/btn_goTo", type = Button, event_name = "OnBtnGoToClickedProxy"},
	rtf_bigReward = { path = "Panel/rtf_bigReward", type = RectTransform, },
	rImg_bigBubble = { path = "Panel/rtf_bigReward/rImg_bigBubble", type = RawImage, },
	rtf_smallReward = { path = "Panel/rtf_smallReward", type = RectTransform, },
	rImg_smallBubble = { path = "Panel/rtf_smallReward/rImg_smallBubble", type = RawImage, },
	txt_tips1 = { path = "Panel/txt_tips1", type = Text, },
	txt_tips2 = { path = "Panel/txt_tips2", type = Text, },

}
