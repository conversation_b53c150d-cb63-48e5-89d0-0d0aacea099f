---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by <PERSON><PERSON><PERSON>.
--- DateTime: 2025/1/7 15:58
---
local require = require
local string_util = require "string_util"
local gw_const = require "gw_const"
local print = print
local cfg_util = require "cfg_util"
local ui_window_mgr = require "ui_window_mgr"
local time_util = require "time_util"
local flow_text = require "flow_text"
local event = require "event"
local typeof = typeof
local table = table
local math = math
local string = string
local pairs = pairs
local ipairs = ipairs
local tonumber = tonumber
local lang = require "lang"
local game_scheme = require "game_scheme"
local util = require "util"
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local chat_mgr_new = require "chat_mgr_new"
local mq_common_pb = require "mq_common_pb"
local ui_chat_define_gw = require "ui_chat_define_gw"
local chat_mgr_pro = require "chat_mgr_pro"
-- 开启log日志
local logger = require("logger").new("ui_chat_data_gw", 4)
local Warning = logger.Warning

---@class ui_chat_data_gw
local M = {}
local ePageState = chat_mgr_new.enum_pState
M.maxLimitLen = nil
M.maxAllianceNoticeStrCount=nil

local checkNewDataAdd = {
    [ePageState.allianceNotice] = function(msg)
        return msg.sType == mq_common_pb.enSpeak_Announcement or msg.sType == mq_common_pb.enSpeak_UrgentAnnouncement
    end,
}

--新增的数据是否有效
function M.CheckNewDataAddChatList(pState, msg)
    if checkNewDataAdd[pState] then
        return checkNewDataAdd[pState](msg)
    else
        return true
    end
end

--特殊处理的函数
function M.TryGetChatDataByPageState(pState, msg)
    local chatData = nil
    if pState == ePageState.guide and( msg.sType == mq_common_pb.enSpeak_UrgentAnnouncement or msg.sType==mq_common_pb.enSpeak_Announcement) then
        --聊天公告替换公告类型数据
        local guideData = chat_mgr_new.GetMsgByType(pState)
        for i, v in pairs(guideData) do
            if v.szChatID == msg.szChatID and v.sType ~= msg.sType then
                chatData = v
                break
            end
        end
    end
    return chatData
end


--根据页面状态过滤数据
function M.FilterDataByPageState_Fun(pState, itemData, itemStyle)
    if not pState then
        return
    end

    if M.FilterDataByPageState[pState] then
        M.FilterDataByPageState[pState](itemData, itemStyle)
    end
end

M.FilterDataByPageState = {
    [ePageState.allianceNotice] = function(itemData, itemStyle)
        M.FilterChatData(itemData, itemStyle, function(tmpData)
            return tmpData.sType ~= mq_common_pb.enSpeak_Announcement and tmpData.sType ~= mq_common_pb.enSpeak_UrgentAnnouncement
        end)
    end,

    [ePageState.guide] = function(itemData, itemStyle)
        --剔除公告类型数据
        M.FilterChatData(itemData, itemStyle, function(tmpData)
            return tmpData.sType == mq_common_pb.enSpeak_Announcement or tmpData.sType == mq_common_pb.enSpeak_UrgentAnnouncement
        end)
    end
}

function M.FilterChatData(itemData, itemStyle, filterFunc)
    local data = nil
    for i = #itemData, 1, -1 do
        data = itemData[i]
        if filterFunc(data) then
            table.remove(itemData, i)
            table.remove(itemStyle, i)
        end
    end
end
--检查是否可以发布紧急公告
function M.CheckUrgentCanRelease()
    local constCfg = game_scheme:InitBattleProp_0(8214) --紧急公告配置
    if constCfg and constCfg.szParam.data and #constCfg.szParam.data > 0 then

        local urgentCDLast = chat_mgr_new.GetUrgentAnnounceTime()
        local offsetTime=urgentCDLast+constCfg.szParam.data[1] * 60-time_util.GetServerTime_GW()
        if offsetTime > 0 then
            local countStr=time_util.FormatTime5(math.round(offsetTime))
            flow_text.Add(string.format2(lang.Get(130131),countStr))
            return false
        end

        if chat_mgr_new.GetUrgentAnnounceNum() >= constCfg.szParam.data[0] then
            flow_text.Add(lang.Get(670027))
            return false
        end

        return true
    else
        return false
    end
end


--是否展示联盟聊天界面置顶公告
function M.IsShowAllianceTopNotice()
    --当前最新置顶公告id是否已经被关闭
    local pState = chat_mgr_new.GetPageState()
    return pState == chat_mgr_new.enum_pState.guide
end

--设置联盟公告item数据
--ojbTable={
--    data={
--        instance=nil,
--        clickTurnUrgent=nil,
--        clickClose=nil,
--        clickDelete=nil,
--    }
--}

function M.ShowTurnUrgentMessageBox(context)
    --二次确认
    local message_box = require "message_box"
    message_box.SetUISkin("ui/prefabs/uimessageboxyestips.prefab")
    --发布紧急公告提示确认
    local remainNum = string.format2(lang.Get(654024), chat_mgr_new.GetUrgentRemainingNum())
    message_box.OpenYesTipBox(remainNum, lang.Get(670029), message_box.STYLE_YESNO, function(d, r)
        if r == message_box.RESULT_YES then
            local ui_chat_mgr_gw = require "ui_chat_mgr_gw"
            ui_chat_mgr_gw.TryReleaseUrgentNotice(context)
            print("普通公告转紧急公告")
        end
    end, 0, lang.KEY_OK, lang.KEY_CANCEL, lang.KEY_SYSTEM_TIPS)
end

function M.DeleteFunc(szChatID, callBack)
    --二次弹窗确认
    local message_box = require "message_box"
    message_box.Open(lang.Get(670030), message_box.STYLE_YESNO, function(d, r)
        if r == message_box.RESULT_YES then
            local net_chat_module_new = require "net_chat_module_new"
            local chat_pb = require "chat_pb"
            net_chat_module_new.Recv_CHAT_RECALL_MSG_REQ(chat_pb.Channel_Guild, szChatID)
            if callBack then
                callBack()
            end
        end
    end, 0, lang.KEY_OK, lang.KEY_CANCEL, lang.KEY_SYSTEM_TIPS)
end

function M.SetAllianceNoticeItemData(objTable, data)
    local objData = objTable.data
    local item = objData.instance:GetComponent(typeof(ScrollRectItem))
    objData.scrollItem = item
    objData.btn_translate = item:Get("btn_translate") --额外函数处理翻译
    objData.btn_turnUrgent = objData.btn_turnUrgent or item:Get("btn_turnUrgent")
    objData.btn_close = objData.btn_close or item:Get("btn_close")
    objData.btn_delete = objData.btn_delete or item:Get("btn_delete")
    objData.btn_self = objData.btn_self or item:Get("btn_self")
    objData.txt_content_translate = item:Get("txt_content_translate")
    objData.txt_content = objData.content or item:Get("txt_content")
    objData.line = item:Get("line")
    objData.txt_bottomInfo = item:Get("txt_bottomInfo")
    objData.txt_title = item:Get("txt_title")
    objData.topBG = item:Get("topBG")
    objData.selfContentSize = item:Get("selfContentSize")
    objData.maskContentSize = item:Get("maskContentSize")

    objData.txt_content_translate:SetActive(false)
    objData.line:SetActive(false)

    --正文内容
    local contentStr = data.context
    objData.txt_content.text = contentStr
    --有翻译内容则打开分界线
    objData.line:SetActive(false)
    objData.txt_bottomInfo.text = time_util.ConvertStamp2Time(data.chatTime) .. " " .. data.name--时间及归属

    local alliance_mgr = require "alliance_mgr"
    if data.sType == mq_common_pb.enSpeak_Announcement then
        objData.txt_title.text = lang.Get(670018)--普通公告
        objData.topBG:Switch(0)
        --普通转紧急公告按钮（条件显示）
        local isSelfR4R5 = alliance_mgr.IsSelfR4R5()
        objData.btn_turnUrgent:SetActive(isSelfR4R5)
        if isSelfR4R5 then
            --二次确认界面扩展
            objData.clickTurnUrgent = nil
            objData.clickTurnUrgent = function()
                M.ShowTurnUrgentMessageBox(contentStr)
            end
        end
    elseif data.sType == mq_common_pb.enSpeak_UrgentAnnouncement then
        objData.btn_turnUrgent:SetActive(false)
        objData.txt_title.text = lang.Get(670019)--紧急公告
        objData.topBG:Switch(1)
    end

    --objData.selfContentSize.enabled = false
    --objData.maskContentSize.enabled = false

    --关闭按钮（在联盟聊天页签，且没有被关闭记录）
    local isShowCloseBtn = M.IsShowAllianceTopNotice() and not objData.hideCloseBtn
    objData.btn_close:SetActive(isShowCloseBtn)
    --外部传入关闭按钮事件

    --删除开关(公告频道条件)
    --local isShowDeleteBtn = chat_mgr_new.GetPageState() == chat_mgr_new.enum_pState.allianceNotice and alliance_mgr.IsSelfR4R5()
    local isShowDeleteBtn = objData.isShowDeleteBtn ~= nil and objData.isShowDeleteBtn or false
    objData.btn_delete:SetActive(isShowDeleteBtn)
    if isShowDeleteBtn then
        objData.clickDelete = function()
            M.DeleteFunc(data.szChatID, objData.deleteCB)
        end
    end

end

function M.RegisterAllianceNoticeSelfBtn_Jump2Detail(objTable, msg)
    objTable.data.clickSelf = function()
        ui_window_mgr:ShowModule("ui_alliance_notice_detail_panel", nil, nil, msg)
    end
end

function M.TrySetShowDeleteBtn(data)
    local alliance_mgr = require "alliance_mgr"
    data.isShowDeleteBtn = alliance_mgr.IsSelfR4R5() and true or false
end

local MainSlgChatIndexCur = nil

--主界面聊天定义
M.MainSlgChatData_Index = {
    world = 1,
    guide = 2,
}

M.MainSlgChatData = {
    [M.MainSlgChatData_Index.world] = {
        channel = ui_chat_define_gw.e_channel.WORLD,
        pState = ui_chat_define_gw.e_pState.world,
    },
    [M.MainSlgChatData_Index.guide] = {
        channel = ui_chat_define_gw.e_channel.GUIDE,
        pState = ui_chat_define_gw.e_pState.guide,
    }
}

--region 获取主界面聊天字符（根据类型定义入口）
--默认获取主界面聊天内容函数
local function GetMainChatStr_Default(msg)
    local contentStr = msg.context
    if string.IsNullOrEmpty(contentStr) then
        contentStr = table.concat({ "adaptation---sType：", msg.sType })
    end
    return contentStr
end

local function GetMainChatStr_ZombieApocalypseCard(msg)
    local value = chat_mgr_new.GetZombieApocalypseCard(msg.extendinfopb)
    if value.nType then
        if value.nType == gw_const.EnZombieApocalypseCardType.enZombieApocalypseCardType_MutantTruck then --变异卡车
            return lang.Get(1006568)
        elseif value.nType == gw_const.EnZombieApocalypseCardType.enZombieApocalypseCardType_Pass then --通关
            return lang.Get(1006573)
        elseif value.nType == gw_const.EnZombieApocalypseCardType.enZombieApocalypseCardType_Prepare then -- 准备开始
            return lang.Get(1006551)
        elseif value.nType == gw_const.EnZombieApocalypseCardType.enZombieApocalypseCardType_PassFail then -- 失败
            return lang.Get(1006575)
        end
    end
    return table.concat({ "adaptation---sType：", msg.sType })
end

local function GetMainChatStr_ZombieApocalypseDetail(msg)
    local value = chat_mgr_new.GetZombieApocalypseDetail(msg.extendinfopb)
    if value.nType then
        if value.nType == gw_const.EnZombieApocalypseDetailType.enZombieApocalypseDetailType_DetoxifyReward then --解毒奖励详情
            return lang.Get(1006559)
        elseif value.nType == gw_const.EnZombieApocalypseDetailType.enZombieApocalypseDetailType_MutantTruck then --卡车
            return lang.Get(1006570)
        end
    end
    return table.concat({ "adaptation---sType：", msg.sType })
end

local function GetMainChatStr_ZombieApocalypseList(msg)
    local value = chat_mgr_new.GetZombieApocalypseList(msg.extendinfopb)
    if value.nType then
        if value.nType == gw_const.EnZombieApocalypseListType.enZombieApocalypseListType_AssistDefence then --协助防御
            return lang.Get(1006566)
        elseif value.nType == gw_const.EnZombieApocalypseListType.enZombieApocalypseListType_AssistDetoxification then --协助解毒
            return lang.Get(1006556)
        end
    end
    return table.concat({ "adaptation---sType：", msg.sType })
end

local function GetMainChatStr_CityFire(msg)
    return lang.Get(1007706)
end

local function GetMainChatStr_ReportShare(msg)
    return lang.Get(660003)
end

local function GetMainChatStr_DetectReportShare(msg)
    return lang.Get(604515)
end

--region 获取主界面聊天字符（根据类型定义入口）

local function GetMainChatStr_SandboxMarkPos(msg)
    local contentText = ""
    if not msg.sandboxmarkData then
        return table.concat({ contentText, msg.sType })
    end

    local sandboxMarkData = msg.sandboxmarkData
    local ui_util = require "ui_util"
    local posText = string.format("[<a href=w>%s #%d X:%d Y:%d</a>]", lang.Get(602159), ui_util.GetWorldIDToShowWorldID(sandboxMarkData.sandboxSid, nil, ui_util.WorldIDRangeType.Normal), sandboxMarkData.x, sandboxMarkData.y)
    local share_define = require "share_define"
    local intercity_trucks_enum = require "intercity_trucks_enum"
    local cfg = game_scheme:ChatSharing_0(share_define.eChatShareType.CarriagePos)
    if sandboxMarkData.playerName and not string.empty(sandboxMarkData.playerName) then
        contentText = sandboxMarkData.playerName
    end
    if sandboxMarkData.quality and sandboxMarkData.quality ~= 0 and cfg then
        --货车
        contentText = string.formatL(cfg.ShareContent, contentText, intercity_trucks_enum.enQuality[sandboxMarkData.quality], posText)
    elseif sandboxMarkData.tavernTaskID and sandboxMarkData.tavernTaskID ~= 0 then
        --酒馆任务
        contentText = string.format2(lang.Get(668070), contentText)
        contentText = string.format("%s %s", contentText, posText)
    else
        if not string.empty(sandboxMarkData.entityLevel) then
            contentText = string.format("%s %s", contentText, sandboxMarkData.entityLevel)
        end
        if sandboxMarkData.entityName ~= 0 then
            contentText = string.format("%s %s", contentText, lang.Get(sandboxMarkData.entityName))
        end
        contentText = string.format("%s %s", contentText, posText)
    end
    return contentText
end

local function GetMainChatStr_sandboxTreasureData(msg)
    return msg.sandboxTreasureData.context
end

local function GetMainChatStr_CarriageTruckShare(msg)
    return msg.sandboxCarriageTruckShare.context
end

local function GetMainChatStr_AllianceTrainShare(msg)
    local str = ""
    if msg.extendinfopb then
        -- log.Warning("msg", Edump(msg))
        local intercity_alliance_train_mgr = require "intercity_alliance_train_mgr"
        local data = chat_mgr_new.GetAllianceTrainMsg(msg.extendinfopb)
        if data then
            local cfg = intercity_alliance_train_mgr.GetAllianceTrainCfgByID(data.trainType)
            if cfg then
                local posText = data.pos and string.format("X:%s Y:%s", data.pos.posX or 0, data.pos.posY or 0) or ""
                local sandBoxText = "#" .. (data.sandboxSid or 0)
                local nameText = lang.Get(cfg.name) or ""
                str = string.formatL(675104, data.roleName or "", nameText, sandBoxText, posText)
            end
        end
    end
    return str
end

local function GetMainChatStr_AllianceTrainPush(msg)
    local str = ""
    if msg.extendinfopb then
        -- log.Warning("msg", Edump(msg))
        local intercity_alliance_train_mgr = require "intercity_alliance_train_mgr"
        local data = chat_mgr_new.GetAllianceTrainMsg(msg.extendinfopb)
        if data then
            local cfg = intercity_alliance_train_mgr.GetAllianceTrainCfgByID(data.trainType)
            if cfg then
                local langID = 675105
                local allianceTrain_pb = require "allianceTrain_pb"
                if data.pushType == allianceTrain_pb.ALLIANCETRAIN_CHAT_PUSH_TYPE_Prepare then
                    langID = 675105
                elseif data.pushType == allianceTrain_pb.ALLIANCETRAIN_CHAT_PUSH_TYPE_Running then
                    langID = 675106
                elseif data.pushType == allianceTrain_pb.ALLIANCETRAIN_CHAT_PUSH_TYPE_Arrived then
                    langID = 675107
                end
                str = string.formatL(langID, data.roleName or "")
            end
        end
    end
    return str
end

local function GetMainChatStr_AllianceTrainPlunder(msg)
    local str = ""
    if msg.extendinfopb then
        --log.Warning("msg", Edump(msg))
        local intercity_alliance_train_mgr = require "intercity_alliance_train_mgr"
        local data = chat_mgr_new.GetAllianceTrainMsg(msg.extendinfopb)
        if data then
            local cfg = intercity_alliance_train_mgr.GetAllianceTrainCfgByID(data.trainType)
            if cfg then
                str = string.formatL(675109, data.beAttackName or "", data.roleName or "")
            end
        end
    end
    return str
end

local function GetMainChatStr_enSpeak_Like(msg)
    return lang.Get(650047)--点赞
end

--攻占城池聊天
local function GetMainChatStr_sandboxNCOccupied(msg)
    if not msg.sandboxNCOccupied then
        return nil
    end

    local tempData = msg.sandboxNCOccupied
    local str = ""

    local regionCfg = game_scheme:SandMapRegion_0(tempData.regionID)
    if not regionCfg then
        logger.Warning(4, "[Occupy NeutralCity Chat Error] regionCfg is nil, regionID: ", tempData.regionID)
        return
    end
    local cityCfg = game_scheme:SandMapCity_0(regionCfg.cityID)
    if not cityCfg then
        logger.Warning(4, "[Occupy NeutralCity Chat Error] cityCfg is nil, cityID: ", regionCfg.cityID)
        return
    end

    local cityPos = cfg_util.StringToNumberArray(regionCfg.CityPos)
    if string.IsNullOrEmpty(tempData.sShort0) then
        local langID = tempData.isFirst == 1 and 667091 or 667092
        str = string.format2(lang.Get(langID), string.format("[%s]", tempData.sShort),
                string.format("<a href=w>Lv.%d%s(X:%d, Y:%d)</a>", cityCfg.cityLevel, lang.Get(cityCfg.name), cityPos[1], cityPos[2]))
    else
        str = string.format2(lang.Get(667093), string.format("[%s]", tempData.sShort0),
                string.format("<a href=w>[Lv.%d%s](X:%d, Y:%d)</a>", cityCfg.cityLevel, lang.Get(cityCfg.name), cityPos[1], cityPos[2]),
                string.format("[%s]", tempData.sShort))
    end
    return str, cityCfg, regionCfg
end

local function GetMainChatStr_sandboxNCAbandon(msg)
    local str = ""
    if not string.IsNullOrEmpty(msg.extendinfo) then
        local json = require "dkjson"
        local luaInfo = json.decode(msg.extendinfo)

        local cityId = luaInfo.cityID or 0
        local cityCfg = game_scheme:SandMapCity_0(cityId)
        if cityCfg then
            local allianceName = string.format("[%s]", luaInfo.allianceName)
            local cityName = string.format("Lv.%d%s", cityCfg.cityLevel, lang.Get(cityCfg.name))
            str = string.format2(lang.Get(667181), allianceName or "", cityName)
        end
    end
    return str
end

local function GetMainChatStr_ShareBattle(msg)
    local context = util.SplitString(msg.context, "#")
    local contentText = ""
    if #context == 1 or (#context > 1 and (context[2] == "1" or context[2] == "")) then
        contentText = lang.Get(18513)
    else
        local opponentName = context[2]
        local winFlag = tonumber(context[3])
        contentText = string.format(lang.Get(winFlag == 1 and 18501 or 18502), opponentName)
    end
    return contentText
end

---@private 联盟成就聊天在主界面现实样式
local function GetMainChatStr_AllianceAchievement(msg)
    local contentText = ""
    local tempData = msg.leagueAchievementData
    if tempData and tempData.achID then
        local cfg = game_scheme:LeagueAchievements_0(tempData.achID)
        if cfg then
            contentText = lang.Get(cfg.AchieveName)
        end
    end
    return contentText
end

---@private 集结分享聊天在主界面现实样式
local function GetMainChatStr_SandboxMass(msg)
    local contentText = ""
    local data = msg.sandboxmassData
    contentText = string.format2(lang.Get(560364), data.strName)
    return contentText
end

---@private 集结推送聊天在主界面现实样式
local function GetMainChatStr_SandboxMassPush(msg)
    local contentText = ""
    local extendinfopb = msg.extendinfopb
    if not extendinfopb then
        return
    end
    local warRallyData = chat_mgr_new.GetGatheringChatMsg(extendinfopb)
    if not warRallyData then
        return
    end
    contentText = string.format2(lang.Get(663028), warRallyData.strName)
    return contentText
end

local function GetMainChatStr_GoldenEggsMaxReward(msg)
    local contentText = ""
    local surprise_bag_data  =  require "surprise_bag_data"
    local extendinfopb = surprise_bag_data.GetGoldenEggsMaxRewardMsg(msg.extendinfopb)
    if extendinfopb.launchName and not string.empty(extendinfopb.launchName) then
        contentText = string.format2(lang.Get(1005270),extendinfopb.launchName)
    end
    return contentText
end

local function GetMainChatStr_GoldenEggs(msg)
    local contentText = lang.Get(1005261)
    return contentText
end

--获取主界面聊天字符（根据类型定义入口）
M._getMainChatStr = {
    [mq_common_pb.enSpeak_SandboxMarkPos] = GetMainChatStr_SandboxMarkPos,
    [mq_common_pb.enSpeak_SandboxTreasure] = GetMainChatStr_sandboxTreasureData,
    [mq_common_pb.enSpeak_CarriageTruckShare] = GetMainChatStr_CarriageTruckShare,
    [mq_common_pb.enSpeak_Like] = GetMainChatStr_enSpeak_Like,
    [mq_common_pb.enSpeak_NC_Occupied] = GetMainChatStr_sandboxNCOccupied,
    [mq_common_pb.enSpeak_NC_Abandon] = GetMainChatStr_sandboxNCAbandon,
    [mq_common_pb.enSpeak_ShareBattle] = GetMainChatStr_ShareBattle,
    [mq_common_pb.enSpeak_AllianceTrain] = GetMainChatStr_AllianceTrainShare,
    [mq_common_pb.enSpeak_AllianceTrainPush] = GetMainChatStr_AllianceTrainPush,
    [mq_common_pb.enSpeak_AllianceTrainPlunder] = GetMainChatStr_AllianceTrainPlunder,
    [mq_common_pb.enSpeak_LeagueAchievement] = GetMainChatStr_AllianceAchievement,
    [mq_common_pb.enSpeak_SandboxMass] = GetMainChatStr_SandboxMass,
    [mq_common_pb.enSpeak_Gathering] = GetMainChatStr_SandboxMassPush,
    [mq_common_pb.enSpeak_GoldenEggs] = GetMainChatStr_GoldenEggs,
    [mq_common_pb.enSpeak_GoldenEggsMaxReward] = GetMainChatStr_GoldenEggsMaxReward,
    [mq_common_pb.enSpeak_CityFireShare] = GetMainChatStr_CityFire,
    [mq_common_pb.enSpeak_ZombieApocalypseCard] = GetMainChatStr_ZombieApocalypseCard,
    [mq_common_pb.enSpeak_ZombieApocalypseDetail] = GetMainChatStr_ZombieApocalypseDetail,
    [mq_common_pb.enSpeak_ZombieApocalypseList] = GetMainChatStr_ZombieApocalypseList,
    [mq_common_pb.enSpeak_BattleVicReportShare] = GetMainChatStr_ReportShare,
    [mq_common_pb.enSpeak_BattleDefReportShare] = GetMainChatStr_ReportShare,
    [mq_common_pb.enSpeak_DetectReportShare] = GetMainChatStr_DetectReportShare,
}

local function GetMainChatStr_Name_NC_Occupied(msg)
    return lang.Get(671056)--系统消息
end

M._getMainChatStr_Name = {
    [mq_common_pb.enSpeak_NC_Occupied] = GetMainChatStr_Name_NC_Occupied,
    [mq_common_pb.enSpeak_NC_Abandon] = GetMainChatStr_Name_NC_Occupied,
    [mq_common_pb.enSpeak_AllianceTrainPush] = GetMainChatStr_Name_NC_Occupied,
    [mq_common_pb.enSpeak_AllianceTrainPlunder] = GetMainChatStr_Name_NC_Occupied,
    [mq_common_pb.enSpeak_GoldenEggsMaxReward] = GetMainChatStr_Name_NC_Occupied,
}

--主界面聊天信息摘要剔除table
M._getMainChatStr_External = {
    [mq_common_pb.enSpeak_AnnounceForChannel] = true,
    [mq_common_pb.enSpeak_UrgentAnnounceForChannel] = true,
    [mq_common_pb.enSpeak_SandboxTreasureFinish] = true,
    [mq_common_pb.enSpeak_SandboxKastenboxMultipleReward] = true,
    [mq_common_pb.enSpeak_AcornPubTreasure] = true,
    [mq_common_pb.enSpeak_AllianceShare] = true,
}
--endregion

---获取主界面聊天字符函数
function M.GetMainChatStr(msg)
    local tmpStr = ""
    if M._getMainChatStr[msg.sType] then
        tmpStr = M._getMainChatStr[msg.sType](msg)
    end

    if string.IsNullOrEmpty(tmpStr) then
        tmpStr = GetMainChatStr_Default(msg)
    end

    local name = ""
    if M._getMainChatStr_Name[msg.sType] then
        name = M._getMainChatStr_Name[msg.sType](msg)
    end
    if string.IsNullOrEmpty(name) then
        name = msg.name
    end

    tmpStr = table.concat({ name, " : ", tmpStr })
    tmpStr = M.RemoveUnderlineTags(tmpStr)
    tmpStr = M.RemoveLineFeed(tmpStr)
    return tmpStr
end

--endregion

local function GetIndexByPageState(pState)
    for k, v in pairs(M.MainSlgChatData) do
        if v.pState == pState then
            return k
        end
    end
    return nil
end

--设置主界面页签状态
function M.TrySetMainChatPageStateAndRefresh(pState)
    local index = GetIndexByPageState(pState)
    if index or MainSlgChatIndexCur == index then
        M.SetMainChatIndexAndRefresh(index)
    end
end

--左右滑动切换主界面页签状态数据(左滑减一，右滑加一)
function M.ChangeMainChatPageState(indexOffset)
    if not indexOffset then
        indexOffset = 0
        MainSlgChatIndexCur = ui_chat_define_gw.MainSlgChatIndexDefault
    end
    local tmpIndex = MainSlgChatIndexCur + indexOffset
    --越界处理
    if tmpIndex <= 0 or tmpIndex > util.get_len(M.MainSlgChatData_Index) then
        return
    end
    M.SetMainChatIndexAndRefresh(tmpIndex)
end

function M.SetMainChatIndexAndRefresh(index)
    MainSlgChatIndexCur = index
    M.RefreshMainContent()--刷新主界面聊天框内容
    event.Trigger(event.UPDATE_CHANNEL_DOT)
end

--刷新主界面聊天框内容
function M.RefreshMainContent()
    local strData = M.GetMainChatContentData()
    if strData and util.get_len(strData) > 0 then
        M.ReverseTable(strData)
    end
    -- 发事件刷新展示框内容 
    event.Trigger(ui_chat_define_gw.Evt_RefreshMainChatContent, strData)
end

--反转表
function M.ReverseTable(t)
    local n = #t  -- 获取表的长度
    for i = 1, math.floor(n / 2) do
        -- 交换位置：头尾对称交换
        t[i], t[n - i + 1] = t[n - i + 1], t[i]
    end
end

--插入数据到表头
function M.ReverseTable_Insert(t)
    local tmpData = {}
    for i, v in ipairs(t) do
        table.insert(tmpData, 1, v)
    end
end


--获取主界面最新的三条聊天数据
function M.GetMainChatContentData()
    --根据当前存储的主界面频道页签数据获取相关频道数据，倒序取3个
    local mainChatDataCur = M.MainSlgChatData[MainSlgChatIndexCur]
    local tmpData = nil
    if mainChatDataCur then
        tmpData = {}
        local chatData = chat_mgr_new.GetMsgByType(mainChatDataCur.channel)
        local tmpStr = ""
        local tmpOneData = nil
        for i = #chatData, 1, -1 do
            tmpOneData = chatData[i]
            --剔除聊天公告类型和聊天紧急公告类型数据（这两种数据为客户端虚拟的，如果取出，则跟原始数据重复。客户端会显示两条一样的聊天数据）
            if tmpOneData and not M._getMainChatStr_External[tmpOneData.sType] and not chat_mgr_pro.CheckRemoveData(tmpOneData) then
                tmpStr = M.GetMainChatStr(tmpOneData)
                tmpStr = M.TryChangeMainChatStrColor(tmpStr, tmpOneData)
                table.insert(tmpData, tmpStr)
                if #tmpData >= ui_chat_define_gw.MainChatContentDataCountMax then
                    break
                end
            end
        end
    end
    return tmpData
end

local normalColor = "#FFFFFF"
local blueColor = "#2BF3FF"

function M.TryChangeMainChatStrColor(str, msg)
    --是同盟蓝色字体，否则白色字体
    local alliance_mgr = require "alliance_mgr"
    if alliance_mgr.GetAllianceRoleInfo(msg.roleid) then
        str = util.SetColor(blueColor, str)
    else
        str = util.SetColor(normalColor, str)
    end
    return str
end

local _removeSymbols = {
    "<u>",
    "</u>",
    "<a href=w>",
    "</a>",
}
--- 剔除字符串中的 <u> 和 </u> 标签
---@param text string 原始字符串
---@return string 剔除标签后的字符串
function M.RemoveUnderlineTags(text)
    if not text then
        return "" -- 如果输入为空，返回空字符串
    end
    -- 使用 string.gsub 全局替换 <u> 标签为空字符串
    local tmpStr = text
    for i, v in pairs(_removeSymbols) do
        tmpStr = string.gsub(tmpStr, v, "")
    end
    return tmpStr
end

--剔除字符串中的换行符
function M.RemoveLineFeed(text)
    if not text then
        return "" -- 如果输入为空，返回空字符串
    end
    -- 使用 string.gsub 全局替换 "/n" 标签为空字符串
    local textWithoutStartTag = string.gsub(text, "\n", " ")
    return textWithoutStartTag
end


--是否当前主界面聊天频道
function M.IsCurMainChatChanel(channel)
    local mainChatDataCur = M.MainSlgChatData[MainSlgChatIndexCur]
    if mainChatDataCur then
        return mainChatDataCur.channel == channel
    end
    return false
end

--获取当前主界面页签状态
function M.GetCurMainPageState()
    local mainChatDataCur = M.MainSlgChatData[MainSlgChatIndexCur]
    if mainChatDataCur then
        return mainChatDataCur.pState
    else
        return ePageState.world
    end
end

--region 聊天顶部提示数据
--只有一条数据，无数据队列
function M.SetChatTopTipsData(data)
    if data then
        if M.chatTopTipsData then
            M.chatTopTipsData = nil
        end
        M.chatTopTipsData = {
            data = data,
            isShow = true
        }
        event.Trigger(event.CHAT_SET_TOP_TIPS_AND_SHOW, data)
    end
end

--获取顶部提示数据
function M.GetChatTopTipsData()
    return M.chatTopTipsData
end

--设置顶部提示数据是否显示
function M.SetChatTopTipsDataShow(isShow)
    if M.chatTopTipsData then
        M.chatTopTipsData.isShow = isShow
    end
end

function M.GetMaxAllianceNoticeStrCount()
    if not M.maxAllianceNoticeStrCount then
        local cfg = game_scheme:InitBattleProp_0(8319)
        if cfg and cfg.szParam and cfg.szParam.data[0] then
            M.maxAllianceNoticeStrCount = cfg.szParam.data[0]
        else
            M.maxAllianceNoticeStrCount = 800
        end
    end
    return M.maxAllianceNoticeStrCount
end

function M.GetSendMsgMaxLimitLen()
    if not M.maxLimitLen then
        local cfg = game_scheme:InitBattleProp_0(990)
        if cfg and cfg.szParam and cfg.szParam.data[0] then
            M.maxLimitLen = cfg.szParam.data[0]
        else
            M.maxLimitLen = 400
        end
    end
    return M.maxLimitLen
end

--endregion
return M