-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local error_code_pb=require("error_code_pb")
local city_pb=require("city_pb")
local prop_pb=require("prop_pb")
module('login_pb')


V1M=V(4,"enLoginPartnerID_Test",0,0)
V2M=V(4,"enLoginPartnerID_BingChuan",1,1)
V3M=V(4,"enLoginPartnerID_Google",2,2)
V4M=V(4,"enLoginPartnerID_Facebook",3,3)
V5M=V(4,"enLoginPartnerID_Visitor",4,4)
V6M=V(4,"enLoginPartnerID_TestVisitor",5,5)
V7M=V(4,"enLoginPartnerID_GameCenter",6,6)
V8M=V(4,"enLoginPartnerID_Apple",7,7)
V9M=V(4,"enLoginPartnerID_HuaWei",8,8)
V10M=V(4,"enLoginPartnerID_Amazon",9,9)
V11M=V(4,"enLoginPartnerID_U8",10,10)
V12M=V(4,"enLoginPartnerID_AiWan1",11,11)
V13M=V(4,"enLoginPartnerID_AiWan2",12,12)
V14M=V(4,"enLoginPartnerID_TanWan1",13,13)
V15M=V(4,"enLoginPartnerID_TanWan2",14,14)
V16M=V(4,"enLoginPartnerID_TanWanIos",15,15)
V17M=V(4,"enLoginPartnerID_India",16,16)
V18M=V(4,"enLoginPartnerID_Twitter",17,17)
V19M=V(4,"enLoginPartnerID_Email",18,9999)
E1M=E(3,"EnLoginPartnerID",".CSMsg.EnLoginPartnerID")
V20M=V(4,"enLET_LoginSvr_Login",0,1)
V21M=V(4,"enLET_LoginSvr_AutoLogin",1,2)
V22M=V(4,"enLET_LoginSvr_AutoLoginChkAls",2,3)
V23M=V(4,"enLET_LobbySvr_BuildInfo",3,20)
V24M=V(4,"enLET_LobbySvr_Login",4,21)
V25M=V(4,"enLET_LobbySvr_CheckALS",5,22)
V26M=V(4,"enLET_LobbySvr_Verify",6,23)
V27M=V(4,"enLET_LobbySvr_CreateActorFail",7,24)
V28M=V(4,"enLET_LobbySvr_ExportActorFail",8,25)
V29M=V(4,"enLET_LobbySvr_Create",9,26)
V30M=V(4,"enLET_LobbySvr_RoleList",10,27)
V31M=V(4,"enLET_LobbySvr_Shoot",11,28)
V32M=V(4,"enLET_LobbySvr_Select",12,29)
E2M=E(3,"EnLoginErrorType",".CSMsg.EnLoginErrorType")
V33M=V(4,"enSystemType_Android",0,11)
V34M=V(4,"enSystemType_IOS",1,8)
V35M=V(4,"enSystemType_WebGL",2,12)
E3M=E(3,"EnSystemType",".CSMsg.EnSystemType")
V36M=V(4,"enLoginParam_Unknow",0,0)
V37M=V(4,"enLoginParam_int_IsInQueue",1,1)
V38M=V(4,"enLoginParam_int_CreateDelay",2,2)
E4M=E(3,"EnLoginParam",".CSMsg.EnLoginParam")
V39M=V(4,"enSex_NoShow",0,0)
V40M=V(4,"enSex_Male",1,1)
V41M=V(4,"enSex_Female",2,2)
E5M=E(3,"EnSex",".CSMsg.EnSex")
V42M=V(4,"enLang_ZH",0,1)
V43M=V(4,"enLang_EN",1,2)
V44M=V(4,"enLang_FA",2,3)
V45M=V(4,"enLang_IND",3,4)
V46M=V(4,"enLang_PI",4,5)
V47M=V(4,"enLang_VI",5,6)
V48M=V(4,"enLang_TH",6,7)
V49M=V(4,"enLang_PO",7,8)
V50M=V(4,"enLang_KO",8,9)
V51M=V(4,"enLang_FR",9,10)
V52M=V(4,"enLang_DE",10,11)
V53M=V(4,"enLang_MA",11,12)
V54M=V(4,"enLang_RU",12,13)
V55M=V(4,"enLang_ES",13,14)
V56M=V(4,"enLang_JA",14,16)
V57M=V(4,"enLang_IN",15,17)
V58M=V(4,"enLang_TR",16,18)
V59M=V(4,"enLang_IT",17,19)
V60M=V(4,"enLang_PL",18,20)
V61M=V(4,"enLang_AR",19,21)
V62M=V(4,"enLang_NL",20,22)
E6M=E(3,"EnLang",".CSMsg.EnLang")
V63M=V(4,"enSDKType_CHN",0,1)
V64M=V(4,"enSDKType_SEA",1,2)
V65M=V(4,"enSDKType_USAndCAN",2,3)
E7M=E(3,"EnSDKType",".CSMsg.EnSDKType")
V66M=V(4,"enKickReason_RepeatLogin",0,1)
V67M=V(4,"enKickReason_DataInvalid",1,2)
V68M=V(4,"enKickReason_ShootLobbyFailed",2,3)
V69M=V(4,"enKickReason_GMKick",3,4)
V70M=V(4,"enKickReason_BindAccountSucc",4,5)
E8M=E(3,"EnKickReason",".CSMsg.EnKickReason")
V71M=V(4,"enBanType_GM",0,1)
E9M=E(3,"EnAccountBanType",".CSMsg.EnAccountBanType")
V72M=V(4,"NONE",0,0)
V73M=V(4,"LOGIN_ERROR",1,1)
V74M=V(4,"CREATE_ACTOR_ERROR",2,2)
V75M=V(4,"DELETE_ACTOR_ERROR",3,3)
V76M=V(4,"SELECT_ACTOR_ERROR",4,4)
V77M=V(4,"ACCOUNT_LOCK_ERROR",5,5)
V78M=V(4,"ACCOUNT_UNLOCKING",6,6)
V79M=V(4,"GESTURE_ERROR",7,7)
V80M=V(4,"PARTNER_CHECK_ERROR",8,8)
V81M=V(4,"NAME_USED_ERROR",9,9)
V82M=V(4,"NAME_INVALID_ERROR",10,10)
V83M=V(4,"ACTOR_CREATING_ERROR",11,11)
V84M=V(4,"CREATE_ABNORMAL_ERROR",12,12)
V85M=V(4,"LOGINING_ERROR",13,13)
V86M=V(4,"GET_ROLE_LIST_ERROR",14,14)
V87M=V(4,"LOGIN_ALREADY_ERROR",15,15)
V88M=V(4,"LOGIN_ABNORMAL_ERROR",16,16)
E10M=E(3,"ProtocolError",".CSMsg.TMSG_LOGIN_MESSAGE_NTF.ProtocolError")
F1D=F(2,"errorCode",".CSMsg.TMSG_LOGIN_MESSAGE_NTF.errorCode",1,0,2,false,nil,14,8)
F2D=F(2,"msg",".CSMsg.TMSG_LOGIN_MESSAGE_NTF.msg",2,1,1,false,"",9,9)
M1G=D(1,"TMSG_LOGIN_MESSAGE_NTF",".CSMsg.TMSG_LOGIN_MESSAGE_NTF",false,{},nil,nil,{})
F3D=F(2,"ip",".CSMsg.TMSG_LOGIN_HANDSHAKE_REQ.ip",1,0,1,false,0,5,1)
M3G=D(1,"TMSG_LOGIN_HANDSHAKE_REQ",".CSMsg.TMSG_LOGIN_HANDSHAKE_REQ",false,{},{},nil,{})
F4D=F(2,"errCode",".CSMsg.TMSG_LOGIN_HANDSHAKE_RSP.errCode",1,0,2,false,0,5,1)
F5D=F(2,"gameWorldName",".CSMsg.TMSG_LOGIN_HANDSHAKE_RSP.gameWorldName",2,1,2,false,"",9,9)
F6D=F(2,"worldID",".CSMsg.TMSG_LOGIN_HANDSHAKE_RSP.worldID",3,2,1,false,0,5,1)
M4G=D(1,"TMSG_LOGIN_HANDSHAKE_RSP",".CSMsg.TMSG_LOGIN_HANDSHAKE_RSP",false,{},{},nil,{})
F7D=F(2,"bIsReturnBattle",".CSMsg.TLoginExtraContext.bIsReturnBattle",1,0,2,false,false,8,7)
F8D=F(2,"nLanguage",".CSMsg.TLoginExtraContext.nLanguage",2,1,1,false,nil,14,8)
F9D=F(2,"adid",".CSMsg.TLoginExtraContext.adid",3,2,2,false,"",9,9)
F10D=F(2,"channelmark",".CSMsg.TLoginExtraContext.channelmark",4,3,1,false,"",9,9)
F11D=F(2,"loginParams",".CSMsg.TLoginExtraContext.loginParams",5,4,3,false,{},9,9)
M5G=D(1,"TLoginExtraContext",".CSMsg.TLoginExtraContext",false,{},{},nil,{})
V89M=V(4,"NONE",0,0)
V90M=V(4,"AUTO_LOGIN",1,1)
E11M=E(3,"LoginType",".CSMsg.TLoginBaseData.LoginType")
F12D=F(2,"clientVersion",".CSMsg.TLoginBaseData.clientVersion",1,0,2,false,"",9,9)
F13D=F(2,"userName",".CSMsg.TLoginBaseData.userName",2,1,2,false,"",9,9)
F14D=F(2,"password",".CSMsg.TLoginBaseData.password",3,2,2,false,"",9,9)
F15D=F(2,"macAddress",".CSMsg.TLoginBaseData.macAddress",4,3,2,false,"",9,9)
F16D=F(2,"verifyCode",".CSMsg.TLoginBaseData.verifyCode",5,4,1,false,"",9,9)
F17D=F(2,"gameWorldName",".CSMsg.TLoginBaseData.gameWorldName",6,5,2,false,"",9,9)
F18D=F(2,"loginType",".CSMsg.TLoginBaseData.loginType",7,6,2,false,nil,14,8)
F19D=F(2,"partnerId",".CSMsg.TLoginBaseData.partnerId",8,7,1,false,nil,14,8)
F20D=F(2,"serialNo",".CSMsg.TLoginBaseData.serialNo",9,8,1,false,0,5,1)
F21D=F(2,"session",".CSMsg.TLoginBaseData.session",10,9,1,false,"",9,9)
F22D=F(2,"robotFlag",".CSMsg.TLoginBaseData.robotFlag",11,10,1,false,false,8,7)
F23D=F(2,"uuid",".CSMsg.TLoginBaseData.uuid",12,11,1,false,"",9,9)
F24D=F(2,"channelID",".CSMsg.TLoginBaseData.channelID",13,12,1,false,0,13,3)
F25D=F(2,"operSystem",".CSMsg.TLoginBaseData.operSystem",14,13,1,false,nil,14,8)
F26D=F(2,"sdkType",".CSMsg.TLoginBaseData.sdkType",15,14,1,false,nil,14,8)
F27D=F(2,"fromWorldID",".CSMsg.TLoginBaseData.fromWorldID",16,15,1,false,0,5,1)
F28D=F(2,"SKDChannelID",".CSMsg.TLoginBaseData.SKDChannelID",17,16,1,false,0,5,1)
F29D=F(2,"SDKUserID",".CSMsg.TLoginBaseData.SDKUserID",18,17,1,false,"",9,9)
F30D=F(2,"SDKUserName",".CSMsg.TLoginBaseData.SDKUserName",19,18,1,false,"",9,9)
F31D=F(2,"ChannelUserID",".CSMsg.TLoginBaseData.ChannelUserID",20,19,1,false,"",9,9)
F32D=F(2,"ChannelUserName",".CSMsg.TLoginBaseData.ChannelUserName",21,20,1,false,"",9,9)
F33D=F(2,"age",".CSMsg.TLoginBaseData.age",22,21,1,false,0,5,1)
F34D=F(2,"timestamp",".CSMsg.TLoginBaseData.timestamp",23,22,1,false,0,13,3)
F35D=F(2,"strTimeStamp",".CSMsg.TLoginBaseData.strTimeStamp",24,23,1,false,"",9,9)
F36D=F(2,"appID",".CSMsg.TLoginBaseData.appID",25,24,1,false,"",9,9)
F37D=F(2,"appKey",".CSMsg.TLoginBaseData.appKey",26,25,1,false,"",9,9)
F38D=F(2,"testUserName",".CSMsg.TLoginBaseData.testUserName",27,26,1,false,"",9,9)
M7G=D(1,"TLoginBaseData",".CSMsg.TLoginBaseData",false,{},nil,nil,{})
V91M=V(4,"NONE",0,0)
V92M=V(4,"AUTO_LOGIN",1,1)
E12M=E(3,"LoginType",".CSMsg.TMSG_LOGIN_LOGIN_REQ.LoginType")
F39D=F(2,"stLoginData",".CSMsg.TMSG_LOGIN_LOGIN_REQ.stLoginData",1,0,2,false,nil,11,10)
F40D=F(2,"stLoginExContext",".CSMsg.TMSG_LOGIN_LOGIN_REQ.stLoginExContext",2,1,2,false,nil,11,10)
M12G=D(1,"TMSG_LOGIN_LOGIN_REQ",".CSMsg.TMSG_LOGIN_LOGIN_REQ",false,{},nil,nil,{})
F41D=F(2,"errCode",".CSMsg.TMSG_LOGIN_LOGIN_RSP.errCode",1,0,2,false,nil,14,8)
F42D=F(2,"allowTime",".CSMsg.TMSG_LOGIN_LOGIN_RSP.allowTime",2,1,2,false,0,5,1)
F43D=F(2,"curTime",".CSMsg.TMSG_LOGIN_LOGIN_RSP.curTime",3,2,2,false,0,5,1)
F44D=F(2,"errorInfo",".CSMsg.TMSG_LOGIN_LOGIN_RSP.errorInfo",4,3,1,false,"",9,9)
F45D=F(2,"areaID",".CSMsg.TMSG_LOGIN_LOGIN_RSP.areaID",5,4,1,false,0,5,1)
M13G=D(1,"TMSG_LOGIN_LOGIN_RSP",".CSMsg.TMSG_LOGIN_LOGIN_RSP",false,{},{},nil,{})
F46D=F(2,"name",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.TActor.name",1,0,2,false,"",9,9)
F47D=F(2,"level",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.TActor.level",2,1,2,false,0,5,1)
F48D=F(2,"sex",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.TActor.sex",3,2,2,false,0,5,1)
F49D=F(2,"nation",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.TActor.nation",4,3,2,false,0,5,1)
F50D=F(2,"profession",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.TActor.profession",5,4,2,false,0,5,1)
F51D=F(2,"faceId",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.TActor.faceId",6,5,2,false,0,5,1)
F52D=F(2,"guideFlag",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.TActor.guideFlag",7,6,2,false,0,5,1)
F53D=F(2,"userID",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.TActor.userID",8,7,2,false,0,5,1)
F54D=F(2,"BCUserID",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.TActor.BCUserID",9,8,2,false,"",9,9)
F55D=F(2,"roleID",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.TActor.roleID",10,9,2,false,0,5,1)
F56D=F(2,"areaID",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.TActor.areaID",11,10,1,false,0,5,1)
F57D=F(2,"faceStr",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.TActor.faceStr",12,11,1,false,"",9,9)
M16G=D(1,"TActor",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.TActor",false,{},{},nil,{})
F58D=F(2,"actor",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF.actor",1,0,3,false,{},11,10)
M15G=D(1,"TMSG_LOGIN_ACTOR_INFO_NTF",".CSMsg.TMSG_LOGIN_ACTOR_INFO_NTF",false,nil,{},nil,{})
F59D=F(2,"name",".CSMsg.TMSG_LOGIN_CREATE_ACTOR_REQ.name",1,0,2,false,"",9,9)
F60D=F(2,"faceId",".CSMsg.TMSG_LOGIN_CREATE_ACTOR_REQ.faceId",2,1,2,false,0,5,1)
F61D=F(2,"sex",".CSMsg.TMSG_LOGIN_CREATE_ACTOR_REQ.sex",3,2,2,false,nil,14,8)
F62D=F(2,"nation",".CSMsg.TMSG_LOGIN_CREATE_ACTOR_REQ.nation",4,3,2,false,0,5,1)
F63D=F(2,"profession",".CSMsg.TMSG_LOGIN_CREATE_ACTOR_REQ.profession",5,4,2,false,0,5,1)
F64D=F(2,"serialNo",".CSMsg.TMSG_LOGIN_CREATE_ACTOR_REQ.serialNo",6,5,2,false,0,5,1)
M17G=D(1,"TMSG_LOGIN_CREATE_ACTOR_REQ",".CSMsg.TMSG_LOGIN_CREATE_ACTOR_REQ",false,{},{},nil,{})
F65D=F(2,"name",".CSMsg.TMSG_LOGIN_DELETE_ACTOR_REQ.name",1,0,2,false,"",9,9)
F66D=F(2,"estatePassword",".CSMsg.TMSG_LOGIN_DELETE_ACTOR_REQ.estatePassword",2,1,2,false,"",9,9)
F67D=F(2,"supperPassword",".CSMsg.TMSG_LOGIN_DELETE_ACTOR_REQ.supperPassword",3,2,2,false,"",9,9)
M19G=D(1,"TMSG_LOGIN_DELETE_ACTOR_REQ",".CSMsg.TMSG_LOGIN_DELETE_ACTOR_REQ",false,{},{},nil,{})
F68D=F(2,"name",".CSMsg.TMSG_LOGIN_SELECT_ACTOR_REQ.name",1,0,2,false,"",9,9)
M20G=D(1,"TMSG_LOGIN_SELECT_ACTOR_REQ",".CSMsg.TMSG_LOGIN_SELECT_ACTOR_REQ",false,{},{},nil,{})
V93M=V(4,"LOGIN",0,0)
V94M=V(4,"ACTOR",1,1)
V95M=V(4,"QUEUE",2,2)
V96M=V(4,"GAME",3,3)
V97M=V(4,"MATRIX",4,4)
V98M=V(4,"UNLOCK",5,5)
V99M=V(4,"AREA_SWITCH",6,6)
V100M=V(4,"GESTURE",7,7)
E13M=E(3,"UserState",".CSMsg.TMSG_LOGIN_SWITCH_STATE_REQ.UserState")
F69D=F(2,"state",".CSMsg.TMSG_LOGIN_SWITCH_STATE_REQ.state",1,0,2,false,nil,14,8)
M21G=D(1,"TMSG_LOGIN_SWITCH_STATE_REQ",".CSMsg.TMSG_LOGIN_SWITCH_STATE_REQ",false,{},nil,nil,{})
F70D=F(2,"remainTime",".CSMsg.TMSG_LOGIN_QUEUE_ORDER_NTF.remainTime",1,0,2,false,0,5,1)
F71D=F(2,"order",".CSMsg.TMSG_LOGIN_QUEUE_ORDER_NTF.order",2,1,2,false,0,5,1)
M23G=D(1,"TMSG_LOGIN_QUEUE_ORDER_NTF",".CSMsg.TMSG_LOGIN_QUEUE_ORDER_NTF",false,{},{},nil,{})
F72D=F(2,"partnerId",".CSMsg.TMSG_LOGIN_REGISTERACCOUNT_REQ.partnerId",1,0,2,false,0,5,1)
F73D=F(2,"account",".CSMsg.TMSG_LOGIN_REGISTERACCOUNT_REQ.account",2,1,2,false,"",9,9)
F74D=F(2,"password",".CSMsg.TMSG_LOGIN_REGISTERACCOUNT_REQ.password",3,2,2,false,"",9,9)
F75D=F(2,"realName",".CSMsg.TMSG_LOGIN_REGISTERACCOUNT_REQ.realName",4,3,2,false,"",9,9)
F76D=F(2,"id",".CSMsg.TMSG_LOGIN_REGISTERACCOUNT_REQ.id",5,4,2,false,"",9,9)
F77D=F(2,"verifyCode",".CSMsg.TMSG_LOGIN_REGISTERACCOUNT_REQ.verifyCode",6,5,2,false,"",9,9)
F78D=F(2,"macAddress",".CSMsg.TMSG_LOGIN_REGISTERACCOUNT_REQ.macAddress",7,6,2,false,"",9,9)
M24G=D(1,"TMSG_LOGIN_REGISTERACCOUNT_REQ",".CSMsg.TMSG_LOGIN_REGISTERACCOUNT_REQ",false,{},{},nil,{})
F79D=F(2,"userId",".CSMsg.TMSG_LOGIN_REGISTERACCOUNT_RSP.userId",1,0,2,false,0,5,1)
F80D=F(2,"context",".CSMsg.TMSG_LOGIN_REGISTERACCOUNT_RSP.context",2,1,2,false,"",9,9)
M25G=D(1,"TMSG_LOGIN_REGISTERACCOUNT_RSP",".CSMsg.TMSG_LOGIN_REGISTERACCOUNT_RSP",false,{},{},nil,{})
F81D=F(2,"surviveNum",".CSMsg.TMSG_LOGIN_ENTER_PLAYERNUM_NTF.TPlayerNumInfo.surviveNum",1,0,2,false,0,5,1)
F82D=F(2,"playerNum",".CSMsg.TMSG_LOGIN_ENTER_PLAYERNUM_NTF.TPlayerNumInfo.playerNum",2,1,2,false,0,5,1)
F83D=F(2,"nCampId",".CSMsg.TMSG_LOGIN_ENTER_PLAYERNUM_NTF.TPlayerNumInfo.nCampId",3,2,2,false,0,5,1)
F84D=F(2,"nCampCountryId",".CSMsg.TMSG_LOGIN_ENTER_PLAYERNUM_NTF.TPlayerNumInfo.nCampCountryId",4,3,2,false,0,5,1)
M27G=D(1,"TPlayerNumInfo",".CSMsg.TMSG_LOGIN_ENTER_PLAYERNUM_NTF.TPlayerNumInfo",false,{},{},nil,{})
F85D=F(2,"stPlayerNumInfo",".CSMsg.TMSG_LOGIN_ENTER_PLAYERNUM_NTF.stPlayerNumInfo",1,0,3,false,{},11,10)
M26G=D(1,"TMSG_LOGIN_ENTER_PLAYERNUM_NTF",".CSMsg.TMSG_LOGIN_ENTER_PLAYERNUM_NTF",false,nil,{},nil,{})
F86D=F(2,"clientVersion",".CSMsg.TMSG_LOGIN_AUTO_LOGIN_REQ.clientVersion",1,0,2,false,0,5,1)
F87D=F(2,"loginkey",".CSMsg.TMSG_LOGIN_AUTO_LOGIN_REQ.loginkey",2,1,2,false,"",9,9)
F88D=F(2,"dbid",".CSMsg.TMSG_LOGIN_AUTO_LOGIN_REQ.dbid",3,2,2,false,0,5,1)
F89D=F(2,"lobbysvrid",".CSMsg.TMSG_LOGIN_AUTO_LOGIN_REQ.lobbysvrid",4,3,2,false,0,5,1)
M28G=D(1,"TMSG_LOGIN_AUTO_LOGIN_REQ",".CSMsg.TMSG_LOGIN_AUTO_LOGIN_REQ",false,{},{},nil,{})
F90D=F(2,"iUserDBID",".CSMsg.TAutoLoginSession.iUserDBID",1,0,2,false,0,5,1)
F91D=F(2,"iRoleDBID",".CSMsg.TAutoLoginSession.iRoleDBID",2,1,2,false,0,5,1)
F92D=F(2,"iTime",".CSMsg.TAutoLoginSession.iTime",3,2,2,false,0,5,1)
F93D=F(2,"iLobbySvrID",".CSMsg.TAutoLoginSession.iLobbySvrID",4,3,2,false,0,5,1)
F94D=F(2,"strToken",".CSMsg.TAutoLoginSession.strToken",5,4,2,false,"",9,9)
F95D=F(2,"iBCUserID",".CSMsg.TAutoLoginSession.iBCUserID",6,5,1,false,0,5,1)
M29G=D(1,"TAutoLoginSession",".CSMsg.TAutoLoginSession",false,{},{},nil,{})
F96D=F(2,"stALSess",".CSMsg.TMSG_LOGIN_AUTO_LOGIN_REQ_V2.stALSess",1,0,2,false,nil,11,10)
F97D=F(2,"stLoginExContext",".CSMsg.TMSG_LOGIN_AUTO_LOGIN_REQ_V2.stLoginExContext",2,1,2,false,nil,11,10)
F98D=F(2,"stLoginData",".CSMsg.TMSG_LOGIN_AUTO_LOGIN_REQ_V2.stLoginData",3,2,1,false,nil,11,10)
M30G=D(1,"TMSG_LOGIN_AUTO_LOGIN_REQ_V2",".CSMsg.TMSG_LOGIN_AUTO_LOGIN_REQ_V2",false,{},{},nil,{})
F99D=F(2,"iErrorType",".CSMsg.TMSG_LOGIN_LOGIN_ERROR_NTF.iErrorType",1,0,2,false,0,5,1)
F100D=F(2,"iError",".CSMsg.TMSG_LOGIN_LOGIN_ERROR_NTF.iError",2,1,2,false,0,5,1)
F101D=F(2,"errorInfo",".CSMsg.TMSG_LOGIN_LOGIN_ERROR_NTF.errorInfo",3,2,1,false,"",9,9)
M31G=D(1,"TMSG_LOGIN_LOGIN_ERROR_NTF",".CSMsg.TMSG_LOGIN_LOGIN_ERROR_NTF",false,{},{},nil,{})
F102D=F(2,"stALSess",".CSMsg.TMSG_LOGIN_UPDATE_ALSESSION_NTF.stALSess",1,0,2,false,nil,11,10)
M32G=D(1,"TMSG_LOGIN_UPDATE_ALSESSION_NTF",".CSMsg.TMSG_LOGIN_UPDATE_ALSESSION_NTF",false,{},{},nil,{})
F103D=F(2,"iVersionID",".CSMsg.TMSG_LOGIN_REQUEST_VERSION_UP_REWARD_REQ.iVersionID",1,0,2,false,"",9,9)
M33G=D(1,"TMSG_LOGIN_REQUEST_VERSION_UP_REWARD_REQ",".CSMsg.TMSG_LOGIN_REQUEST_VERSION_UP_REWARD_REQ",false,{},{},nil,{})
F104D=F(2,"kickReason",".CSMsg.TMSG_LOGIN_KICKOUT_NTF.kickReason",1,0,2,false,0,5,1)
M34G=D(1,"TMSG_LOGIN_KICKOUT_NTF",".CSMsg.TMSG_LOGIN_KICKOUT_NTF",false,{},{},nil,{})
F105D=F(2,"accountName",".CSMsg.TMSG_LOGIN_NEW_ACCOUNT_NTF.accountName",1,0,2,false,"",9,9)
M35G=D(1,"TMSG_LOGIN_NEW_ACCOUNT_NTF",".CSMsg.TMSG_LOGIN_NEW_ACCOUNT_NTF",false,{},{},nil,{})
F106D=F(2,"BanType",".CSMsg.TMSG_LOGIN_BAN_ACCOUNT_NTF.BanType",1,0,2,false,0,5,1)
F107D=F(2,"EndTime",".CSMsg.TMSG_LOGIN_BAN_ACCOUNT_NTF.EndTime",2,1,2,false,0,5,1)
F108D=F(2,"PlayerName",".CSMsg.TMSG_LOGIN_BAN_ACCOUNT_NTF.PlayerName",3,2,1,false,"",9,9)
F109D=F(2,"Content_zh",".CSMsg.TMSG_LOGIN_BAN_ACCOUNT_NTF.Content_zh",4,3,1,false,"",9,9)
F110D=F(2,"Content_eh",".CSMsg.TMSG_LOGIN_BAN_ACCOUNT_NTF.Content_eh",5,4,1,false,"",9,9)
F111D=F(2,"BanId",".CSMsg.TMSG_LOGIN_BAN_ACCOUNT_NTF.BanId",6,5,1,false,0,5,1)
M36G=D(1,"TMSG_LOGIN_BAN_ACCOUNT_NTF",".CSMsg.TMSG_LOGIN_BAN_ACCOUNT_NTF",false,{},{},nil,{})
F112D=F(2,"fromWorldID",".CSMsg.TMSG_LOGIN_MODIFY_FROMWORLDID_NTF.fromWorldID",1,0,2,false,0,5,1)
M37G=D(1,"TMSG_LOGIN_MODIFY_FROMWORLDID_NTF",".CSMsg.TMSG_LOGIN_MODIFY_FROMWORLDID_NTF",false,{},{},nil,{})
F113D=F(2,"state",".CSMsg.TMSG_LOGIN_ACTOR_CREATE_QUEUE_NTF.state",1,0,2,false,0,5,1)
M38G=D(1,"TMSG_LOGIN_ACTOR_CREATE_QUEUE_NTF",".CSMsg.TMSG_LOGIN_ACTOR_CREATE_QUEUE_NTF",false,{},{},nil,{})
F114D=F(2,"nLevel",".CSMsg.TMSG_ACTOR_CREATE_DATA_SYNC_REQ.nLevel",1,0,2,false,0,5,1)
F115D=F(2,"nLevelChallenge",".CSMsg.TMSG_ACTOR_CREATE_DATA_SYNC_REQ.nLevelChallenge",2,1,2,false,0,5,1)
F116D=F(2,"arrLevelChallengeReq",".CSMsg.TMSG_ACTOR_CREATE_DATA_SYNC_REQ.arrLevelChallengeReq",3,2,3,false,{},12,9)
F117D=F(2,"arrRepairMapid",".CSMsg.TMSG_ACTOR_CREATE_DATA_SYNC_REQ.arrRepairMapid",4,3,3,false,{},13,3)
F118D=F(2,"arrOpenGiftMapid",".CSMsg.TMSG_ACTOR_CREATE_DATA_SYNC_REQ.arrOpenGiftMapid",5,4,3,false,{},13,3)
M39G=D(1,"TMSG_ACTOR_CREATE_DATA_SYNC_REQ",".CSMsg.TMSG_ACTOR_CREATE_DATA_SYNC_REQ",false,{},{},nil,{})
F119D=F(2,"errCode",".CSMsg.TMSG_ACTOR_CREATE_DATA_SYNC_RSP.errCode",1,0,2,false,nil,14,8)
F120D=F(2,"nLevel",".CSMsg.TMSG_ACTOR_CREATE_DATA_SYNC_RSP.nLevel",2,1,2,false,0,5,1)
F121D=F(2,"nLevelChallenge",".CSMsg.TMSG_ACTOR_CREATE_DATA_SYNC_RSP.nLevelChallenge",3,2,2,false,0,5,1)
F122D=F(2,"arrBuilding",".CSMsg.TMSG_ACTOR_CREATE_DATA_SYNC_RSP.arrBuilding",4,3,3,false,{},11,10)
F123D=F(2,"arrPropData",".CSMsg.TMSG_ACTOR_CREATE_DATA_SYNC_RSP.arrPropData",5,4,3,false,{},11,10)
M40G=D(1,"TMSG_ACTOR_CREATE_DATA_SYNC_RSP",".CSMsg.TMSG_ACTOR_CREATE_DATA_SYNC_RSP",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M,V19M}
E2M.values = {V20M,V21M,V22M,V23M,V24M,V25M,V26M,V27M,V28M,V29M,V30M,V31M,V32M}
E3M.values = {V33M,V34M,V35M}
E4M.values = {V36M,V37M,V38M}
E5M.values = {V39M,V40M,V41M}
E6M.values = {V42M,V43M,V44M,V45M,V46M,V47M,V48M,V49M,V50M,V51M,V52M,V53M,V54M,V55M,V56M,V57M,V58M,V59M,V60M,V61M,V62M}
E7M.values = {V63M,V64M,V65M}
E8M.values = {V66M,V67M,V68M,V69M,V70M}
E9M.values = {V71M}
E10M.values = {V72M,V73M,V74M,V75M,V76M,V77M,V78M,V79M,V80M,V81M,V82M,V83M,V84M,V85M,V86M,V87M,V88M}
F1D.enum_type=M2G
M1G.enum_types={E10M}
M1G.fields={F1D, F2D}
M3G.fields={F3D}
M4G.fields={F4D, F5D, F6D}
F8D.enum_type=M6G
M5G.fields={F7D, F8D, F9D, F10D, F11D}
E11M.values = {V89M,V90M}
F18D.enum_type=M8G
F19D.enum_type=M9G
F25D.enum_type=M10G
F26D.enum_type=M11G
M7G.enum_types={E11M}
M7G.fields={F12D, F13D, F14D, F15D, F16D, F17D, F18D, F19D, F20D, F21D, F22D, F23D, F24D, F25D, F26D, F27D, F28D, F29D, F30D, F31D, F32D, F33D, F34D, F35D, F36D, F37D, F38D}
E12M.values = {V91M,V92M}
F39D.message_type=M7G
F40D.message_type=M5G
M12G.enum_types={E12M}
M12G.fields={F39D, F40D}
F41D.enum_type=error_code_pb.E1M
M13G.fields={F41D, F42D, F43D, F44D, F45D}
M16G.fields={F46D, F47D, F48D, F49D, F50D, F51D, F52D, F53D, F54D, F55D, F56D, F57D}
M16G.containing_type=M15G
F58D.message_type=M16G
M15G.nested_types={M16G}
M15G.fields={F58D}
F61D.enum_type=M18G
M17G.fields={F59D, F60D, F61D, F62D, F63D, F64D}
M19G.fields={F65D, F66D, F67D}
M20G.fields={F68D}
E13M.values = {V93M,V94M,V95M,V96M,V97M,V98M,V99M,V100M}
F69D.enum_type=M22G
M21G.enum_types={E13M}
M21G.fields={F69D}
M23G.fields={F70D, F71D}
M24G.fields={F72D, F73D, F74D, F75D, F76D, F77D, F78D}
M25G.fields={F79D, F80D}
M27G.fields={F81D, F82D, F83D, F84D}
M27G.containing_type=M26G
F85D.message_type=M27G
M26G.nested_types={M27G}
M26G.fields={F85D}
M28G.fields={F86D, F87D, F88D, F89D}
M29G.fields={F90D, F91D, F92D, F93D, F94D, F95D}
F96D.message_type=M29G
F97D.message_type=M5G
F98D.message_type=M7G
M30G.fields={F96D, F97D, F98D}
M31G.fields={F99D, F100D, F101D}
F102D.message_type=M29G
M32G.fields={F102D}
M33G.fields={F103D}
M34G.fields={F104D}
M35G.fields={F105D}
M36G.fields={F106D, F107D, F108D, F109D, F110D, F111D}
M37G.fields={F112D}
M38G.fields={F113D}
M39G.fields={F114D, F115D, F116D, F117D, F118D}
F119D.enum_type=error_code_pb.E1M
F122D.message_type=city_pb.M3G
F123D.message_type=prop_pb.M9G
M40G.fields={F119D, F120D, F121D, F122D, F123D}

TAutoLoginSession =M(M29G)
TLoginBaseData =M(M7G)
TLoginExtraContext =M(M5G)
TMSG_ACTOR_CREATE_DATA_SYNC_REQ =M(M39G)
TMSG_ACTOR_CREATE_DATA_SYNC_RSP =M(M40G)
TMSG_LOGIN_ACTOR_CREATE_QUEUE_NTF =M(M38G)
TMSG_LOGIN_ACTOR_INFO_NTF =M(M15G)
TMSG_LOGIN_ACTOR_INFO_NTF.TActor =M(M16G)
TMSG_LOGIN_AUTO_LOGIN_REQ =M(M28G)
TMSG_LOGIN_AUTO_LOGIN_REQ_V2 =M(M30G)
TMSG_LOGIN_BAN_ACCOUNT_NTF =M(M36G)
TMSG_LOGIN_CREATE_ACTOR_REQ =M(M17G)
TMSG_LOGIN_DELETE_ACTOR_REQ =M(M19G)
TMSG_LOGIN_ENTER_PLAYERNUM_NTF =M(M26G)
TMSG_LOGIN_ENTER_PLAYERNUM_NTF.TPlayerNumInfo =M(M27G)
TMSG_LOGIN_HANDSHAKE_REQ =M(M3G)
TMSG_LOGIN_HANDSHAKE_RSP =M(M4G)
TMSG_LOGIN_KICKOUT_NTF =M(M34G)
TMSG_LOGIN_LOGIN_ERROR_NTF =M(M31G)
TMSG_LOGIN_LOGIN_REQ =M(M12G)
TMSG_LOGIN_LOGIN_RSP =M(M13G)
TMSG_LOGIN_MESSAGE_NTF =M(M1G)
TMSG_LOGIN_MODIFY_FROMWORLDID_NTF =M(M37G)
TMSG_LOGIN_NEW_ACCOUNT_NTF =M(M35G)
TMSG_LOGIN_QUEUE_ORDER_NTF =M(M23G)
TMSG_LOGIN_REGISTERACCOUNT_REQ =M(M24G)
TMSG_LOGIN_REGISTERACCOUNT_RSP =M(M25G)
TMSG_LOGIN_REQUEST_VERSION_UP_REWARD_REQ =M(M33G)
TMSG_LOGIN_SELECT_ACTOR_REQ =M(M20G)
TMSG_LOGIN_SWITCH_STATE_REQ =M(M21G)
TMSG_LOGIN_UPDATE_ALSESSION_NTF =M(M32G)
enBanType_GM = 1
enKickReason_BindAccountSucc = 5
enKickReason_DataInvalid = 2
enKickReason_GMKick = 4
enKickReason_RepeatLogin = 1
enKickReason_ShootLobbyFailed = 3
enLET_LobbySvr_BuildInfo = 20
enLET_LobbySvr_CheckALS = 22
enLET_LobbySvr_Create = 26
enLET_LobbySvr_CreateActorFail = 24
enLET_LobbySvr_ExportActorFail = 25
enLET_LobbySvr_Login = 21
enLET_LobbySvr_RoleList = 27
enLET_LobbySvr_Select = 29
enLET_LobbySvr_Shoot = 28
enLET_LobbySvr_Verify = 23
enLET_LoginSvr_AutoLogin = 2
enLET_LoginSvr_AutoLoginChkAls = 3
enLET_LoginSvr_Login = 1
enLang_AR = 21
enLang_DE = 11
enLang_EN = 2
enLang_ES = 14
enLang_FA = 3
enLang_FR = 10
enLang_IN = 17
enLang_IND = 4
enLang_IT = 19
enLang_JA = 16
enLang_KO = 9
enLang_MA = 12
enLang_NL = 22
enLang_PI = 5
enLang_PL = 20
enLang_PO = 8
enLang_RU = 13
enLang_TH = 7
enLang_TR = 18
enLang_VI = 6
enLang_ZH = 1
enLoginParam_Unknow = 0
enLoginParam_int_CreateDelay = 2
enLoginParam_int_IsInQueue = 1
enLoginPartnerID_AiWan1 = 11
enLoginPartnerID_AiWan2 = 12
enLoginPartnerID_Amazon = 9
enLoginPartnerID_Apple = 7
enLoginPartnerID_BingChuan = 1
enLoginPartnerID_Email = 9999
enLoginPartnerID_Facebook = 3
enLoginPartnerID_GameCenter = 6
enLoginPartnerID_Google = 2
enLoginPartnerID_HuaWei = 8
enLoginPartnerID_India = 16
enLoginPartnerID_TanWan1 = 13
enLoginPartnerID_TanWan2 = 14
enLoginPartnerID_TanWanIos = 15
enLoginPartnerID_Test = 0
enLoginPartnerID_TestVisitor = 5
enLoginPartnerID_Twitter = 17
enLoginPartnerID_U8 = 10
enLoginPartnerID_Visitor = 4
enSDKType_CHN = 1
enSDKType_SEA = 2
enSDKType_USAndCAN = 3
enSex_Female = 2
enSex_Male = 1
enSex_NoShow = 0
enSystemType_Android = 11
enSystemType_IOS = 8
enSystemType_WebGL = 12

