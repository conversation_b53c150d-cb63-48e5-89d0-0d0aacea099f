-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local error_code_pb=require("error_code_pb")
local common_new_pb=require("common_new_pb")
module('ZoneBattleDuel_pb')


V1M=V(4,"enZoneBattleDuelTimeType_ScoreTimeBegin",0,1)
V2M=V(4,"enZoneBattleDuelTimeType_ScoreTimeEnd",1,2)
V3M=V(4,"enZoneBattleDuelTimeType_CrossMoveBegin",2,3)
V4M=V(4,"enZoneBattleDuelTimeType_CrossMoveEnd",3,4)
V5M=V(4,"enZoneBattleDuelTimeType_CongressAtkBegin",4,5)
V6M=V(4,"enZoneBattleDuelTimeType_CongressAtkEnd",5,6)
V7M=V(4,"enZoneBattleDuelTimeType_RoundTimeEnd",6,7)
V8M=V(4,"enZoneBattleDuelTimeType_BattleScoreBegin",7,8)
V9M=V(4,"enZoneBattleDuelTimeType_BattleScoreEnd",8,9)
V10M=V(4,"enZoneBattleDuelTimeType_Max",9,10)
E1M=E(3,"enZoneBattleDuelTimeType",".CSMsg.enZoneBattleDuelTimeType")
V11M=V(4,"enZoneBattleScorePersonal",0,1)
V12M=V(4,"enZoneBattleScoreAlliance",1,2)
V13M=V(4,"enZoneBattleScoreBattle",2,3)
E2M=E(3,"enZoneBattleScoreUpdateRank",".CSMsg.enZoneBattleScoreUpdateRank")
V14M=V(4,"enZoneBattleDuelScoreType_AllianceDuel",0,1)
V15M=V(4,"enZoneBattleDuelScoreType_AllianceDuel_Mvp",1,2)
V16M=V(4,"enZoneBattleDuelScoreType_ArenaFirst",2,3)
V17M=V(4,"enZoneBattleDuelDesertStrom_Alliance_Win",3,4)
V18M=V(4,"enZoneBattleDuelScoreType_WorldBoss",4,5)
V19M=V(4,"enZoneBattleDuelScoreType_WorldHarmFirst",5,6)
V20M=V(4,"enZoneBattleDuelScoreType_LootTruck",6,7)
V21M=V(4,"enZoneBattleDuelScoreType_ArmyRace",7,8)
V22M=V(4,"enZoneBattleDuelDesertStrom_Mvp",8,9)
V23M=V(4,"enZoneBattleDuelScoreType_Max",9,10)
E3M=E(3,"enZoneBattleDuelScoreType",".CSMsg.enZoneBattleDuelScoreType")
V24M=V(4,"eZoneDuel_UnKnow",0,0)
V25M=V(4,"eZoneDuel_TwoMatch",1,2)
V26M=V(4,"eZoneDuel_FourMatch",2,4)
V27M=V(4,"eZoneDuel_EightMatch",3,8)
E4M=E(3,"ZoneBattleType",".CSMsg.ZoneBattleType")
V28M=V(4,"eMatchRule_UnKnow",0,0)
V29M=V(4,"eMatchRule_RandomRule",1,1)
V30M=V(4,"eMatchRule_PowerRule",2,2)
E5M=E(3,"MatchRuleType",".CSMsg.MatchRuleType")
V31M=V(4,"ZoneBattleDuel_Type_UnKnow",0,0)
V32M=V(4,"ZoneBattleDuel_Type_AtyStart",1,1)
V33M=V(4,"ZoneBattleDuel_Type_BattleEnd",2,2)
V34M=V(4,"ZoneBattleDuel_Type_CrossStart",3,3)
V35M=V(4,"ZoneBattleDuel_Type_CongressBattleStart",4,4)
V36M=V(4,"ZoneBattleDuel_Type_CongressBattleSettlement",5,5)
V37M=V(4,"ZoneBattleDuel_Type_CongressBattleEnd",6,6)
V38M=V(4,"ZoneBattleDuel_Type_CrossEnd",7,7)
V39M=V(4,"ZoneBattleDuel_Type_CrossTotalEnd",8,8)
V40M=V(4,"ZoneBattleDuel_Type_AtyEnd",9,9)
E6M=E(3,"ZoneBattleDuelActivityType",".CSMsg.ZoneBattleDuelActivityType")
F1D=F(2,"scoreType",".CSMsg.TZoneBattleDuelVSLog.scoreType",1,0,2,false,0,5,1)
F2D=F(2,"occurTime",".CSMsg.TZoneBattleDuelVSLog.occurTime",2,1,2,false,0,5,1)
F3D=F(2,"addScore",".CSMsg.TZoneBattleDuelVSLog.addScore",3,2,2,false,0,3,2)
F4D=F(2,"worldIDA",".CSMsg.TZoneBattleDuelVSLog.worldIDA",4,3,2,false,0,5,1)
F5D=F(2,"nameA",".CSMsg.TZoneBattleDuelVSLog.nameA",5,4,2,false,"",9,9)
F6D=F(2,"shortNameA",".CSMsg.TZoneBattleDuelVSLog.shortNameA",6,5,2,false,"",9,9)
F7D=F(2,"faceStrA",".CSMsg.TZoneBattleDuelVSLog.faceStrA",7,6,2,false,"",9,9)
F8D=F(2,"frameIDA",".CSMsg.TZoneBattleDuelVSLog.frameIDA",8,7,2,false,0,5,1)
F9D=F(2,"power",".CSMsg.TZoneBattleDuelVSLog.power",14,8,1,false,0,3,2)
F10D=F(2,"winIsA",".CSMsg.TZoneBattleDuelVSLog.winIsA",15,9,1,false,false,8,7)
F11D=F(2,"worldIDB",".CSMsg.TZoneBattleDuelVSLog.worldIDB",16,10,1,false,0,5,1)
F12D=F(2,"nameB",".CSMsg.TZoneBattleDuelVSLog.nameB",17,11,1,false,"",9,9)
F13D=F(2,"shortNameB",".CSMsg.TZoneBattleDuelVSLog.shortNameB",18,12,1,false,"",9,9)
F14D=F(2,"faceStrB",".CSMsg.TZoneBattleDuelVSLog.faceStrB",19,13,1,false,"",9,9)
F15D=F(2,"frameIDB",".CSMsg.TZoneBattleDuelVSLog.frameIDB",20,14,1,false,0,5,1)
M1G=D(1,"TZoneBattleDuelVSLog",".CSMsg.TZoneBattleDuelVSLog",false,{},{},nil,{})
F16D=F(2,"worldID",".CSMsg.TZoneBattleDuelWorldItem.worldID",1,0,2,false,0,5,1)
F17D=F(2,"continueWinTimes",".CSMsg.TZoneBattleDuelWorldItem.continueWinTimes",2,1,2,false,0,5,1)
F18D=F(2,"power",".CSMsg.TZoneBattleDuelWorldItem.power",3,2,2,false,0,3,2)
F19D=F(2,"isSkip",".CSMsg.TZoneBattleDuelWorldItem.isSkip",5,3,1,false,false,8,7)
M2G=D(1,"TZoneBattleDuelWorldItem",".CSMsg.TZoneBattleDuelWorldItem",false,{},{},nil,{})
F20D=F(2,"worldIDA",".CSMsg.TZoneBattleDuelVSItem.worldIDA",1,0,2,false,0,5,1)
F21D=F(2,"worldIDB",".CSMsg.TZoneBattleDuelVSItem.worldIDB",2,1,2,false,0,5,1)
F22D=F(2,"totalScoreA",".CSMsg.TZoneBattleDuelVSItem.totalScoreA",3,2,2,false,0,6,4)
F23D=F(2,"totalScoreB",".CSMsg.TZoneBattleDuelVSItem.totalScoreB",4,3,2,false,0,6,4)
F24D=F(2,"atkWorldID",".CSMsg.TZoneBattleDuelVSItem.atkWorldID",7,4,2,false,0,7,3)
F25D=F(2,"atkScore",".CSMsg.TZoneBattleDuelVSItem.atkScore",8,5,2,false,0,7,3)
F26D=F(2,"defWorldID",".CSMsg.TZoneBattleDuelVSItem.defWorldID",9,6,2,false,0,7,3)
F27D=F(2,"defScore",".CSMsg.TZoneBattleDuelVSItem.defScore",10,7,2,false,0,7,3)
F28D=F(2,"winWorldID",".CSMsg.TZoneBattleDuelVSItem.winWorldID",11,8,2,false,0,7,3)
F29D=F(2,"losWorldID",".CSMsg.TZoneBattleDuelVSItem.losWorldID",12,9,2,false,0,7,3)
F30D=F(2,"isSettle",".CSMsg.TZoneBattleDuelVSItem.isSettle",13,10,2,false,false,8,7)
F31D=F(2,"occupWorldID",".CSMsg.TZoneBattleDuelVSItem.occupWorldID",14,11,2,false,0,5,1)
F32D=F(2,"occupTime",".CSMsg.TZoneBattleDuelVSItem.occupTime",15,12,2,false,0,5,1)
F33D=F(2,"scoreA",".CSMsg.TZoneBattleDuelVSItem.scoreA",16,13,3,false,{},6,4)
F34D=F(2,"scoreB",".CSMsg.TZoneBattleDuelVSItem.scoreB",17,14,3,false,{},6,4)
F35D=F(2,"logs",".CSMsg.TZoneBattleDuelVSItem.logs",20,15,3,false,{},11,10)
M3G=D(1,"TZoneBattleDuelVSItem",".CSMsg.TZoneBattleDuelVSItem",false,{},{},nil,{})
F36D=F(2,"rewarded",".CSMsg.TZoneBattleDuelRound.rewarded",1,0,2,false,false,8,7)
F37D=F(2,"vsItems",".CSMsg.TZoneBattleDuelRound.vsItems",2,1,3,false,{},11,10)
M4G=D(1,"TZoneBattleDuelRound",".CSMsg.TZoneBattleDuelRound",false,{},{},nil,{})
F38D=F(2,"finalRewarded",".CSMsg.TZoneBattleDuelGroup.finalRewarded",1,0,2,false,false,8,7)
F39D=F(2,"rounds",".CSMsg.TZoneBattleDuelGroup.rounds",3,1,3,false,{},11,10)
F40D=F(2,"worlds",".CSMsg.TZoneBattleDuelGroup.worlds",4,2,3,false,{},11,10)
M5G=D(1,"TZoneBattleDuelGroup",".CSMsg.TZoneBattleDuelGroup",false,{},{},nil,{})
F41D=F(2,"timePoints",".CSMsg.TZoneBattleDuelTime.timePoints",1,0,3,false,{},7,3)
M6G=D(1,"TZoneBattleDuelTime",".CSMsg.TZoneBattleDuelTime",false,{},{},nil,{})
F42D=F(2,"seasonTimeBegin",".CSMsg.TDBZoneBattleDuel.seasonTimeBegin",1,0,2,false,0,7,3)
F43D=F(2,"seasonTimeEnd",".CSMsg.TDBZoneBattleDuel.seasonTimeEnd",2,1,2,false,0,7,3)
F44D=F(2,"reqWorldGroupTime",".CSMsg.TDBZoneBattleDuel.reqWorldGroupTime",3,2,2,false,0,7,3)
F45D=F(2,"curRound",".CSMsg.TDBZoneBattleDuel.curRound",4,3,2,false,0,7,3)
F46D=F(2,"roundTimePoints",".CSMsg.TDBZoneBattleDuel.roundTimePoints",5,4,2,false,nil,11,10)
F47D=F(2,"maxCurRound",".CSMsg.TDBZoneBattleDuel.maxCurRound",6,5,2,false,0,7,3)
F48D=F(2,"nMatchType",".CSMsg.TDBZoneBattleDuel.nMatchType",7,6,2,false,0,7,3)
F49D=F(2,"activityId",".CSMsg.TDBZoneBattleDuel.activityId",8,7,2,false,0,7,3)
F50D=F(2,"groups",".CSMsg.TDBZoneBattleDuel.groups",9,8,2,false,nil,11,10)
M7G=D(1,"TDBZoneBattleDuel",".CSMsg.TDBZoneBattleDuel",false,{},{},nil,{})
F51D=F(2,"duels",".CSMsg.TDBZoneBattleDuelTotal.duels",1,0,3,false,{},11,10)
M8G=D(1,"TDBZoneBattleDuelTotal",".CSMsg.TDBZoneBattleDuelTotal",false,{},{},nil,{})
F52D=F(2,"nWorldID",".CSMsg.TMSG_ZONEBATTLEDUEL_GETINFO_REQ.nWorldID",1,0,1,false,0,5,1)
M9G=D(1,"TMSG_ZONEBATTLEDUEL_GETINFO_REQ",".CSMsg.TMSG_ZONEBATTLEDUEL_GETINFO_REQ",false,{},{},nil,{})
F53D=F(2,"errCode",".CSMsg.TMSG_ZONEBATTLEDUEL_GETINFO_RSP.errCode",1,0,2,false,nil,14,8)
F54D=F(2,"seasonTimeBegin",".CSMsg.TMSG_ZONEBATTLEDUEL_GETINFO_RSP.seasonTimeBegin",2,1,1,false,0,5,1)
F55D=F(2,"seasonTimeEnd",".CSMsg.TMSG_ZONEBATTLEDUEL_GETINFO_RSP.seasonTimeEnd",3,2,1,false,0,5,1)
F56D=F(2,"timePoints",".CSMsg.TMSG_ZONEBATTLEDUEL_GETINFO_RSP.timePoints",4,3,1,false,nil,11,10)
F57D=F(2,"groups",".CSMsg.TMSG_ZONEBATTLEDUEL_GETINFO_RSP.groups",5,4,1,false,nil,11,10)
F58D=F(2,"ConfigForeshowBegin",".CSMsg.TMSG_ZONEBATTLEDUEL_GETINFO_RSP.ConfigForeshowBegin",6,5,1,false,0,5,1)
F59D=F(2,"ConfigInForesBegin",".CSMsg.TMSG_ZONEBATTLEDUEL_GETINFO_RSP.ConfigInForesBegin",7,6,1,false,0,5,1)
F60D=F(2,"actCrossInfo",".CSMsg.TMSG_ZONEBATTLEDUEL_GETINFO_RSP.actCrossInfo",8,7,1,false,nil,11,10)
M10G=D(1,"TMSG_ZONEBATTLEDUEL_GETINFO_RSP",".CSMsg.TMSG_ZONEBATTLEDUEL_GETINFO_RSP",false,{},{},nil,{})
F61D=F(2,"nAtyId",".CSMsg.activityCrossInfo.nAtyId",1,0,1,false,0,5,1)
F62D=F(2,"ServerGroup",".CSMsg.activityCrossInfo.ServerGroup",2,1,1,false,"",9,9)
F63D=F(2,"nNoticeTime",".CSMsg.activityCrossInfo.nNoticeTime",3,2,1,false,0,5,1)
F64D=F(2,"nStartTime",".CSMsg.activityCrossInfo.nStartTime",4,3,1,false,0,5,1)
F65D=F(2,"nEndTime",".CSMsg.activityCrossInfo.nEndTime",5,4,1,false,0,5,1)
M12G=D(1,"activityCrossInfo",".CSMsg.activityCrossInfo",false,{},{},nil,{})
M13G=D(1,"TMSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_REQ",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_REQ",false,{},{},{},{})
F66D=F(2,"errCode",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP.errCode",1,0,2,false,nil,14,8)
M14G=D(1,"TMSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP",false,{},{},nil,{})
M15G=D(1,"TMSG_ZONEBATTLEDUEL_NOMATCH_REWARD_REQ",".CSMsg.TMSG_ZONEBATTLEDUEL_NOMATCH_REWARD_REQ",false,{},{},{},{})
F67D=F(2,"errCode",".CSMsg.TMSG_ZONEBATTLEDUEL_NOMATCH_REWARD_RSP.errCode",1,0,2,false,nil,14,8)
M16G=D(1,"TMSG_ZONEBATTLEDUEL_NOMATCH_REWARD_RSP",".CSMsg.TMSG_ZONEBATTLEDUEL_NOMATCH_REWARD_RSP",false,{},{},nil,{})
F68D=F(2,"nWorldID",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_VSINFO_REQ.nWorldID",1,0,1,false,0,5,1)
M17G=D(1,"TMSG_ZONEBATTLEDUEL_GET_VSINFO_REQ",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_VSINFO_REQ",false,{},{},nil,{})
F69D=F(2,"errCode",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_VSINFO_RSP.errCode",1,0,2,false,nil,14,8)
F70D=F(2,"vsItems",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_VSINFO_RSP.vsItems",2,1,1,false,nil,11,10)
F71D=F(2,"rankFirstInfos",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_VSINFO_RSP.rankFirstInfos",3,2,3,false,{},11,10)
F72D=F(2,"worlds",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_VSINFO_RSP.worlds",4,3,3,false,{},11,10)
F73D=F(2,"crossMoveCDTime",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_VSINFO_RSP.crossMoveCDTime",5,4,1,false,0,5,1)
F74D=F(2,"bSkipReward",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_VSINFO_RSP.bSkipReward",6,5,1,false,0,5,1)
F75D=F(2,"bSkipRoundWinReward",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_VSINFO_RSP.bSkipRoundWinReward",7,6,1,false,0,5,1)
M18G=D(1,"TMSG_ZONEBATTLEDUEL_GET_VSINFO_RSP",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_VSINFO_RSP",false,{},{},nil,{})
F76D=F(2,"worldID",".CSMsg.RankFirstInfo.worldID",1,0,2,false,0,5,1)
F77D=F(2,"playerBasic",".CSMsg.RankFirstInfo.playerBasic",2,1,1,false,nil,11,10)
F78D=F(2,"PlayerScore",".CSMsg.RankFirstInfo.PlayerScore",3,2,1,false,0,5,1)
F79D=F(2,"allianceBasic",".CSMsg.RankFirstInfo.allianceBasic",4,3,1,false,nil,11,10)
F80D=F(2,"AllianceScore",".CSMsg.RankFirstInfo.AllianceScore",5,4,1,false,0,5,1)
F81D=F(2,"nLikesNum",".CSMsg.RankFirstInfo.nLikesNum",6,5,1,false,0,5,1)
M19G=D(1,"RankFirstInfo",".CSMsg.RankFirstInfo",false,{},{},nil,{})
F82D=F(2,"leagueid",".CSMsg.AllianceBasicInfo.leagueid",1,0,1,false,0,5,1)
F83D=F(2,"leagueName",".CSMsg.AllianceBasicInfo.leagueName",2,1,1,false,"",9,9)
F84D=F(2,"leagueShortName",".CSMsg.AllianceBasicInfo.leagueShortName",3,2,1,false,"",9,9)
F85D=F(2,"leagueFlag",".CSMsg.AllianceBasicInfo.leagueFlag",4,3,1,false,0,5,1)
M21G=D(1,"AllianceBasicInfo",".CSMsg.AllianceBasicInfo",false,{},{},nil,{})
F86D=F(2,"sandboxid",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_REQ.sandboxid",1,0,2,false,0,13,3)
F87D=F(2,"sid",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_REQ.sid",2,1,2,false,0,4,4)
M22G=D(1,"TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_REQ",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_REQ",false,{},{},nil,{})
F88D=F(2,"RoleName",".CSMsg.TZoneBattleDuelTeamInfo.RoleName",1,0,2,false,"",9,9)
F89D=F(2,"FrameID",".CSMsg.TZoneBattleDuelTeamInfo.FrameID",2,1,2,false,0,5,1)
F90D=F(2,"FaceStr",".CSMsg.TZoneBattleDuelTeamInfo.FaceStr",3,2,2,false,"",9,9)
F91D=F(2,"simpleTroopStr",".CSMsg.TZoneBattleDuelTeamInfo.simpleTroopStr",4,3,2,false,"",9,9)
M23G=D(1,"TZoneBattleDuelTeamInfo",".CSMsg.TZoneBattleDuelTeamInfo",false,{},{},nil,{})
F92D=F(2,"errCode",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP.errCode",1,0,2,false,nil,14,8)
F93D=F(2,"sandboxid",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP.sandboxid",2,1,1,false,0,13,3)
F94D=F(2,"sid",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP.sid",3,2,1,false,0,4,4)
F95D=F(2,"List",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP.List",4,3,3,false,{},11,10)
M24G=D(1,"TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP",false,{},{},nil,{})
F96D=F(2,"sandboxid",".CSMsg.TMSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF.sandboxid",1,0,2,false,0,13,3)
F97D=F(2,"sid",".CSMsg.TMSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF.sid",2,1,2,false,0,4,4)
F98D=F(2,"totalSoldier",".CSMsg.TMSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF.totalSoldier",3,2,2,false,0,13,3)
F99D=F(2,"congressSid",".CSMsg.TMSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF.congressSid",4,3,2,false,0,4,4)
M25G=D(1,"TMSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF",".CSMsg.TMSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF",false,{},{},nil,{})
F100D=F(2,"ZoneBattleType",".CSMsg.TMSG_ZONE_BATTLE_DUEL_CHANGE_NTF.ZoneBattleType",1,0,2,false,nil,14,8)
F101D=F(2,"seasonTimeBegin",".CSMsg.TMSG_ZONE_BATTLE_DUEL_CHANGE_NTF.seasonTimeBegin",2,1,1,false,0,5,1)
F102D=F(2,"seasonTimeEnd",".CSMsg.TMSG_ZONE_BATTLE_DUEL_CHANGE_NTF.seasonTimeEnd",3,2,1,false,0,5,1)
F103D=F(2,"timePoints",".CSMsg.TMSG_ZONE_BATTLE_DUEL_CHANGE_NTF.timePoints",4,3,1,false,nil,11,10)
F104D=F(2,"vsItems",".CSMsg.TMSG_ZONE_BATTLE_DUEL_CHANGE_NTF.vsItems",5,4,1,false,nil,11,10)
F105D=F(2,"groups",".CSMsg.TMSG_ZONE_BATTLE_DUEL_CHANGE_NTF.groups",6,5,1,false,nil,11,10)
M26G=D(1,"TMSG_ZONE_BATTLE_DUEL_CHANGE_NTF",".CSMsg.TMSG_ZONE_BATTLE_DUEL_CHANGE_NTF",false,{},{},nil,{})
F106D=F(2,"sandboxid",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_REQ.sandboxid",1,0,2,false,0,13,3)
M28G=D(1,"TMSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_REQ",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_REQ",false,{},{},nil,{})
F107D=F(2,"errCode",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP.errCode",1,0,2,false,nil,14,8)
F108D=F(2,"sandboxSid",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP.sandboxSid",2,1,1,false,0,5,1)
F109D=F(2,"x",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP.x",3,2,1,false,0,13,3)
F110D=F(2,"y",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP.y",4,3,1,false,0,13,3)
M29G=D(1,"TMSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP",false,{},{},nil,{})
F111D=F(2,"arrWorldIDs",".CSMsg.TMSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_REQ.arrWorldIDs",1,0,3,false,{},13,3)
M30G=D(1,"TMSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_REQ",".CSMsg.TMSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_REQ",false,{},{},nil,{})
F112D=F(2,"nErrorCode",".CSMsg.TMSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP.nErrorCode",1,0,2,false,nil,14,8)
F113D=F(2,"arrPresidentInfo",".CSMsg.TMSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP.arrPresidentInfo",2,1,3,false,{},11,10)
M31G=D(1,"TMSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP",".CSMsg.TMSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP",false,{},{},nil,{})
F114D=F(2,"nWorldID",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_REQ.nWorldID",1,0,2,false,0,13,3)
M32G=D(1,"TMSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_REQ",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_REQ",false,{},{},nil,{})
F115D=F(2,"nErrorCode",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP.nErrorCode",1,0,2,false,nil,14,8)
F116D=F(2,"nDefScore",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP.nDefScore",2,1,1,false,0,5,1)
F117D=F(2,"nAttScore",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP.nAttScore",3,2,1,false,0,5,1)
M33G=D(1,"TMSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP",".CSMsg.TMSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M}
E2M.values = {V11M,V12M,V13M}
E3M.values = {V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M}
E4M.values = {V24M,V25M,V26M,V27M}
E5M.values = {V28M,V29M,V30M}
E6M.values = {V31M,V32M,V33M,V34M,V35M,V36M,V37M,V38M,V39M,V40M}
M1G.fields={F1D, F2D, F3D, F4D, F5D, F6D, F7D, F8D, F9D, F10D, F11D, F12D, F13D, F14D, F15D}
M2G.fields={F16D, F17D, F18D, F19D}
F35D.message_type=M1G
M3G.fields={F20D, F21D, F22D, F23D, F24D, F25D, F26D, F27D, F28D, F29D, F30D, F31D, F32D, F33D, F34D, F35D}
F37D.message_type=M3G
M4G.fields={F36D, F37D}
F39D.message_type=M4G
F40D.message_type=M2G
M5G.fields={F38D, F39D, F40D}
M6G.fields={F41D}
F46D.message_type=M6G
F50D.message_type=M5G
M7G.fields={F42D, F43D, F44D, F45D, F46D, F47D, F48D, F49D, F50D}
F51D.message_type=M7G
M8G.fields={F51D}
M9G.fields={F52D}
F53D.enum_type=error_code_pb.E1M
F56D.message_type=M6G
F57D.message_type=M5G
F60D.message_type=M12G
M10G.fields={F53D, F54D, F55D, F56D, F57D, F58D, F59D, F60D}
M12G.fields={F61D, F62D, F63D, F64D, F65D}
F66D.enum_type=error_code_pb.E1M
M14G.fields={F66D}
F67D.enum_type=error_code_pb.E1M
M16G.fields={F67D}
M17G.fields={F68D}
F69D.enum_type=error_code_pb.E1M
F70D.message_type=M3G
F71D.message_type=M19G
F72D.message_type=M2G
M18G.fields={F69D, F70D, F71D, F72D, F73D, F74D, F75D}
F77D.message_type=common_new_pb.M1G
F79D.message_type=M21G
M19G.fields={F76D, F77D, F78D, F79D, F80D, F81D}
M21G.fields={F82D, F83D, F84D, F85D}
M22G.fields={F86D, F87D}
M23G.fields={F88D, F89D, F90D, F91D}
F92D.enum_type=error_code_pb.E1M
F95D.message_type=M23G
M24G.fields={F92D, F93D, F94D, F95D}
M25G.fields={F96D, F97D, F98D, F99D}
F100D.enum_type=M27G
F103D.message_type=M6G
F104D.message_type=M3G
F105D.message_type=M5G
M26G.fields={F100D, F101D, F102D, F103D, F104D, F105D}
M28G.fields={F106D}
F107D.enum_type=error_code_pb.E1M
M29G.fields={F107D, F108D, F109D, F110D}
M30G.fields={F111D}
F112D.enum_type=error_code_pb.E1M
F113D.message_type=common_new_pb.M1G
M31G.fields={F112D, F113D}
M32G.fields={F114D}
F115D.enum_type=error_code_pb.E1M
M33G.fields={F115D, F116D, F117D}

AllianceBasicInfo =M(M21G)
RankFirstInfo =M(M19G)
TDBZoneBattleDuel =M(M7G)
TDBZoneBattleDuelTotal =M(M8G)
TMSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF =M(M25G)
TMSG_ZONEBATTLEDUEL_GETINFO_REQ =M(M9G)
TMSG_ZONEBATTLEDUEL_GETINFO_RSP =M(M10G)
TMSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_REQ =M(M28G)
TMSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP =M(M29G)
TMSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_REQ =M(M32G)
TMSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP =M(M33G)
TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_REQ =M(M22G)
TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP =M(M24G)
TMSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_REQ =M(M13G)
TMSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP =M(M14G)
TMSG_ZONEBATTLEDUEL_GET_VSINFO_REQ =M(M17G)
TMSG_ZONEBATTLEDUEL_GET_VSINFO_RSP =M(M18G)
TMSG_ZONEBATTLEDUEL_NOMATCH_REWARD_REQ =M(M15G)
TMSG_ZONEBATTLEDUEL_NOMATCH_REWARD_RSP =M(M16G)
TMSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_REQ =M(M30G)
TMSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP =M(M31G)
TMSG_ZONE_BATTLE_DUEL_CHANGE_NTF =M(M26G)
TZoneBattleDuelGroup =M(M5G)
TZoneBattleDuelRound =M(M4G)
TZoneBattleDuelTeamInfo =M(M23G)
TZoneBattleDuelTime =M(M6G)
TZoneBattleDuelVSItem =M(M3G)
TZoneBattleDuelVSLog =M(M1G)
TZoneBattleDuelWorldItem =M(M2G)
ZoneBattleDuel_Type_AtyEnd = 9
ZoneBattleDuel_Type_AtyStart = 1
ZoneBattleDuel_Type_BattleEnd = 2
ZoneBattleDuel_Type_CongressBattleEnd = 6
ZoneBattleDuel_Type_CongressBattleSettlement = 5
ZoneBattleDuel_Type_CongressBattleStart = 4
ZoneBattleDuel_Type_CrossEnd = 7
ZoneBattleDuel_Type_CrossStart = 3
ZoneBattleDuel_Type_CrossTotalEnd = 8
ZoneBattleDuel_Type_UnKnow = 0
activityCrossInfo =M(M12G)
eMatchRule_PowerRule = 2
eMatchRule_RandomRule = 1
eMatchRule_UnKnow = 0
eZoneDuel_EightMatch = 8
eZoneDuel_FourMatch = 4
eZoneDuel_TwoMatch = 2
eZoneDuel_UnKnow = 0
enZoneBattleDuelDesertStrom_Alliance_Win = 4
enZoneBattleDuelDesertStrom_Mvp = 9
enZoneBattleDuelScoreType_AllianceDuel = 1
enZoneBattleDuelScoreType_AllianceDuel_Mvp = 2
enZoneBattleDuelScoreType_ArenaFirst = 3
enZoneBattleDuelScoreType_ArmyRace = 8
enZoneBattleDuelScoreType_LootTruck = 7
enZoneBattleDuelScoreType_Max = 10
enZoneBattleDuelScoreType_WorldBoss = 5
enZoneBattleDuelScoreType_WorldHarmFirst = 6
enZoneBattleDuelTimeType_BattleScoreBegin = 8
enZoneBattleDuelTimeType_BattleScoreEnd = 9
enZoneBattleDuelTimeType_CongressAtkBegin = 5
enZoneBattleDuelTimeType_CongressAtkEnd = 6
enZoneBattleDuelTimeType_CrossMoveBegin = 3
enZoneBattleDuelTimeType_CrossMoveEnd = 4
enZoneBattleDuelTimeType_Max = 10
enZoneBattleDuelTimeType_RoundTimeEnd = 7
enZoneBattleDuelTimeType_ScoreTimeBegin = 1
enZoneBattleDuelTimeType_ScoreTimeEnd = 2
enZoneBattleScoreAlliance = 2
enZoneBattleScoreBattle = 3
enZoneBattleScorePersonal = 1

