local require = require
local util = require "util"
local os = os
local math = math
local OldResVersionManager = CS.War.Base.OldResVersionManager
local SplitServerResVerMgr = CS.War.Base.SplitServerResVerMgr
local split_server_res_ver_mgr = require"split_server_res_ver_mgr"
local split_define = require "split_server_res_define"
local Warning = split_define.logger.Warning

module("split_server_res_predownload_checker")
local cache = {}

function StartCheck()
    if cache.isInit then
        return
    end
    
    cache.isInit = true
    RemovePreDownloadCheckTimer()
    if split_server_res_ver_mgr.IsCanUseSplitServerRes() then
        RequestUpdateJson()
        cache.lastCheckTime = 0
        cache.preDownloadCheckTimer = util.IntervalCall(split_define.preDownloadCheckInterval, function()
            RequestUpdateJson()
        end) 
    end
end

function RemovePreDownloadCheckTimer()
    if cache.preDownloadCheckTimer then
        util.RemoveDelayCall(cache.preDownloadCheckTimer)
        cache.preDownloadCheckTimer = nil
    end
end

function CheckRequestTimeInterval()
    local timePassed = os.clock() - cache.lastCheckTime
    if timePassed < split_define.preDownloadCheckTimeLimit then
        return false
    end

    cache.lastCheckTime = os.clock()
    return true
end

--检测登录
function CheckIsLogin()
    local ui_login_main_mgr = require "ui_login_main_mgr"
    local enter = ui_login_main_mgr.GetOnceLogined()
    if not enter then
        return false
    end

    return true
end

function CheckCanRequest()
    if not split_server_res_ver_mgr.IsCanUseSplitServerRes() then
        return false
    end
    
    if not CheckIsLogin() then 
        return false
    end

    if not CheckRequestTimeInterval() then
        return false
    end
    
    return true
end

function RequestUpdateJson()
    Warning(1,"split_server_res_predownload_checker.RequestUpdateJson()")
    if not CheckCanRequest() then
        Warning(1,"can not request UpdateJson")
        return
    end
    
    local updateJsonUrl = split_server_res_ver_mgr.GetUpdateJsonUrl()
    if not updateJsonUrl then
        Warning(1,"updateJsonUrl is null")
        return
    end
    
    local http_inst = require "http_inst"
    http_inst.Req_Timeout(updateJsonUrl, split_define.reqUpdateJsonTimeout, function(data, _, _)
        if nil == data then
            Warning(1,"RequestUpdateJson fail, data is null")
            return
        end

        local dkjson = require "dkjson"
        local jsonData = dkjson.decode(data)
        OnRequestUpdateJsonSuccess(jsonData)
    end)
end

function OnRequestUpdateJsonSuccess(jsonData)
    if jsonData == nil then
        return
    end

    local filesUrl = jsonData["files_url"]
    if filesUrl == nil then
        return
    end

    CheckIsNeedPreDownload(filesUrl)
end

function CheckIsNeedPreDownload(filesUrl)
    local setting_server_data = require"setting_server_data"
    local curServerId = setting_server_data.GetLoginWorldID()
    local index = SplitServerResVerMgr.GetIndexInSplitServerInfo(curServerId)
    local serverMinResVer = SplitServerResVerMgr.GetServerMinResVer(index)
    local serverMaxResVer = SplitServerResVerMgr.GetServerMaxResVer(index)
    local remoteResVersion = OldResVersionManager.GetResVerByRemoteFileUrl(filesUrl)
    split_server_res_ver_mgr.UpdateRemoteFilesVer(remoteResVersion)
    
    local preDownloadResVer = remoteResVersion
    preDownloadResVer = math.max(preDownloadResVer, serverMinResVer)
    preDownloadResVer = math.min(preDownloadResVer, serverMaxResVer)

    local  streamingLocalFilesVer = split_server_res_ver_mgr.GetStreamingLocalFilesVer()
    if preDownloadResVer == streamingLocalFilesVer then
        Warning(2,"not need predownload, equal streamingLocalFilesVer", preDownloadResVer, curUseResVersion)
        return
    end
    
    local curUseResVersion = split_server_res_ver_mgr.GetCurUseFilesVer()
    if preDownloadResVer <= curUseResVersion then  --预下载比当前使用版本低
        Warning(2,"not need predownload", preDownloadResVer, curUseResVersion)
        return
    end

    local isExist = split_server_res_ver_mgr.IsExistInRemoteVirtual(preDownloadResVer)
    if isExist then
        Warning(2,"not need predownload, is exist in hashRemoteVirVerList", preDownloadResVer, curUseResVersion)
        return
    end
    
    Warning(1,"add predownload to task list", preDownloadResVer, curUseResVersion)
    split_server_res_ver_mgr.PreDownloadCurServerRes(preDownloadResVer)
    
    --测试代码11
    --local split_server_res_download_mgr = require"split_server_res_download_mgr"
    --local severId = curServerId + 1
    --local verTaskType = split_define.VerTaskType.PreDownload
    --local downloadVersion
    --for i = curUseResVersion + 1, preDownloadResVer do
    --    downloadVersion = i
    --    if i == preDownloadResVer then
    --        severId = curServerId
    --        verTaskType = split_define.VerTaskType.PreDownload
    --    elseif i%2 == 0 then
    --        severId = severId + 1
    --        verTaskType = split_define.VerTaskType.SwitchServerResDownload
    --    else
    --        verTaskType = split_define.VerTaskType.SwitchServerResDownload
    --    end
    --    split_server_res_download_mgr.AddVersionTaskToList(downloadVersion, severId, verTaskType)
    --end
    --
    --downloadVersion = curUseResVersion
    --severId = curServerId
    --verTaskType = split_define.VerTaskType.BgDownload
    --split_server_res_download_mgr.AddVersionTaskToList(downloadVersion, severId, verTaskType)
end
