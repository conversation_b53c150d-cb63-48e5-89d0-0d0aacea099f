local print = print
local require = require
local pairs = pairs     
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local limit_gift_data =require "limit_gift_data"
local limit_gift_define =require "limit_gift_define"
local event =require "event"
local dump =dump

--region Controller Life
module("ui_limit_gift_main_controller")
local controller = nil
local UIController = newClass("ui_limit_gift_main_controller", controller_base)

local curIndex = 1

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    limit_gift_data.ClearTimes()
    if data and data.curIndex then 
        self.selectIndex = data.curIndex
    else
        self.selectIndex = limit_gift_data.GetPriority()
    end
end

function UIController:OnShow()
    self.__base.OnShow(self)
    --如果已经没有了，则关闭页面
    local canShow= self:CheckHasDataToShow()
    if canShow then 
        ui_window_mgr:UnloadModule(self.view_name)
        return 
    end
    self:ShowSelectPanel()
    self:InitTopInfo() 
    self:BuildContentListData() 
    self:BuildBottomListData()
end

function UIController:CheckHasDataToShow()
    if not self.selectIndex then 
        return true
    end
    limit_gift_data.SetMainServerData()
    local data = limit_gift_data.GetServerData()
    local log = require "log"
    log.Warning("CheckHasDataToShow",#data)
    if #data == 0 then 
        log.Error("limitGift 当前可显示的礼包数据长度异常",#data)
        return true
    end
    if self.selectIndex and self.selectIndex > #data then 
        log.Error("limitGift 当前索引值大于数据最大长度",self.selectIndex,#data)
        return true
    end
    return false
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    curIndex=1
    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

--根据当前子模块索引 显示对应的面板
function UIController:ShowSelectPanel(index)
    if self.selectIndex then 
        curIndex = self.selectIndex
    end
    if index then 
        curIndex=index
    end
    limit_gift_data.SetChooseIndex(curIndex)
    local data = limit_gift_data.GetIsChooseInfo()
    -- print("ShowSelectPanel",curIndex)
    -- dump(data)
    if data then 
        self:TriggerUIEvent("SelectPanel",data.PageType)
    else
        --尝试获取第一个礼包 如果还存在礼包的情况下
        curIndex = 1
        limit_gift_data.SetChooseIndex(curIndex)
        data = limit_gift_data.GetIsChooseInfo()
        if data then 
            self:TriggerUIEvent("SelectPanel",data.PageType)
        else
            -- 不存在礼包时 自动卸载页面
            self:OnBtnCloseBtnClickedProxy()
        end
    end
end
--初始化中心显示的面板
function UIController:InitTopInfo() 
    --展示选中的数据
    local data = limit_gift_data.GetIsChooseInfo()
    local alldata = limit_gift_data.GetrenderDataLength()
    local outData={}
    if data then 
        outData.topbg=data.topBanner
        outData.contentbg=data.ContentBg
        outData.goodsId=data.goodsID
        outData.limitNum=data.CanBuyNum
        outData.endTime =data.ContDown
        outData.activeName =data.titleName
        outData.supervalue =data.superValue
        outData.tip = data.tip
        outData.canbuy =data.canbuy
        outData.alldata =alldata
        -- print("InitTopInfo")
        self:TriggerUIEvent("SetInfo",outData,data.PageType)
    else
        self:OnBtnCloseBtnClickedProxy()
    end
    
end

--设置奖励列表数据
function UIController:BuildContentListData() 
    local outData={}
    local data = limit_gift_data.GetIsChooseInfo()
    if data then 
        for i ,v in pairs(data.RewardList) do 
            outData[i]={
                rewardIcon=v.iconID,
                rewardNum =v.num,
                rewardName=v.name,
                rewardId=v.Id,
                rewardBg =data.rewardItemBg,
                survivorId =v.survivorId,
                quality=v.quality,
                type=v.type
            }
        end
    end
    -- print("BuildContentListData")
    -- dump(outData)
    self:TriggerUIEvent("UpdateRewardScrollList",outData)
end

function UIController:BuildBottomListData(isSelect) 
    local data={}
    local showData= limit_gift_data.BuildRenderData()
    local count=0
    local slectIndex = limit_gift_data.GetChooseIndex()
    if showData then
        for i,v in pairs(showData) do 
            local temp={}
            temp.titleName =v.titleName
            temp.selctIndex =slectIndex
            temp.bg =v.BottomBanner
            temp.OnClickTag=ChangeIndex
            count=count+1
            table.insert(data,temp)
        end
    end
    
    -- print("BuildBottomListData")
    -- dump(data)
    self:TriggerUIEvent("ShowList",data,count,isSelect)
end

function ChangeIndex(index) 
    -- print("ChangeIndex",index)
    curIndex=index
    limit_gift_data.SetChooseIndex(curIndex)
    event.Trigger(limit_gift_define.UPDATEPAGE,curIndex)
end


--刷新底部Toggle信息 和 选中面板信息
function UIController:UpdatePage() 
    local count = limit_gift_data.GetrenderDataLength()
    if count > 0 then 
        if curIndex <= count then 
            limit_gift_data.SetChooseIndex(curIndex)
        else
            limit_gift_data.SetChooseIndex(count)
        end
    end
    local data = limit_gift_data.GetIsChooseInfo()
    if data then 
        self:TriggerUIEvent("SelectPanel",data.PageType)
        self:InitTopInfo()
        self:BuildContentListData()
        self:BuildBottomListData()
    else
        ui_window_mgr:UnloadModule("ui_limit_gift_main")
    end
end

-- function UIController:RefreshInfo() 
--     local selcetIndex =limit_gift_data.GetChooseIndex()
--     self:TriggerUIEvent("RefreshSelctState",selcetIndex)
-- end

function UIController:AutoSubscribeEvents() 
    local UpdataEvent = function (eventName,index)
        self:ShowSelectPanel(index)
        self:InitTopInfo()
        self:BuildContentListData()
        self:BuildBottomListData(true)
        --self:RefreshInfo()
    end
    self:RegisterEvent(limit_gift_define.UPDATEPAGE,UpdataEvent)

    local RefreshEvent = function ()
        self:UpdatePage() 
    end
    self:RegisterEvent(limit_gift_define.RefreshPage,RefreshEvent)

    local ChangePageStatic = function (eventName,index) 
        -- print("ChangePageStatic",index)
        ChangeIndex(index) 
    end
    self:RegisterEvent(limit_gift_define.STATICUPDATEPAGE,ChangePageStatic)
end

function UIController:AutoUnsubscribeEvents() 

end
--endregion

--region Controller Logic
function  UIController:OnBtnCloseBtnClickedProxy()
    if self and self.view_name then 
        ui_window_mgr:UnloadModule(self.view_name)
    end
end

function UIController:OnBtnBuygiftClickedProxy() 
    local chooseData = limit_gift_data.GetIsChooseInfo()
    if chooseData then 
        local curRechargeID = chooseData.goodsID
        if curRechargeID then 
            local net_activity_module =require "net_activity_module"
            net_activity_module.Send_New_Recharge_REQ(curRechargeID)
        end
    end
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
