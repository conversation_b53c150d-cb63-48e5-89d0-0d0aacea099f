﻿--- Created by: 袁楠
--- DateTime: 2024/8/27
--- desc: 设置界面View
---
local print = print
local require = require
local table = table
local string = string
local tostring = tostring
local tonumber = tonumber
local util = require "util"
local lang = require "lang"
local lang_key = require "lang_res_key"
local ui_base = require "ui_base"
local class = require "class"
local event = require "event"
local player_mgr = require "player_mgr"
local ui_community_data = require "ui_community_data"
local game_scheme = require "game_scheme"
local game_config = require "game_config"
local message_box = require "message_box"
local windowmgr = require "ui_window_mgr"
local q1sdk = require "q1sdk"
local account_data = require "account_data"
local login_pb = require "login_pb"
local log = require "log"
local unforced_guide_mgr = require "unforced_guide_mgr"
local ReviewingUtil = require "ReviewingUtil"
local laymain_data = require "laymain_data"
local const = require "const"
local red_const = require "red_const"
local red_system = require "red_system"
local Slider = CS.UnityEngine.UI.Slider
local RectTransform = CS.UnityEngine.RectTransform
local Application = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local Utility = CS.War.Script.Utility
local TextMeshProUGUI = CS.TMPro.TextMeshProUGUI
local LeanTween         = CS.LeanTween
local ui_setting_attr_enum = require "ui_setting_attr_enum"
local EnSubModel = ui_setting_attr_enum.EnSubModel
local EnBaseAttrKey = ui_setting_attr_enum.EnBaseAttrKey
local EnVoiceAttrKey = ui_setting_attr_enum.EnVoiceAttrKey

module("ui_personal_setting")
local ui_path = "ui/prefabs/gw/gw_personalinfo/uisetting.prefab"

local window = nil
local SettingSystem = {}
local oldLangIndex = nil

local bFirstLogin = true

local bShowGuid = false

SettingSystem.widget_table = {
    soundSlider = { path = "title1/soundSlider", type = Slider }, --音效
    musicSlider = { path = "title1/musicSlider", type = Slider }, --音乐
    soundSliderRect = { path = "title1/soundSlider", type = RectTransform }, --音效
    musicSliderRect = { path = "title1/musicSlider", type = RectTransform }, --音乐	
    --dropLang = {path = "title0/langList", type = "Dropdown"},			--语言选择列表
    getPrizeBtn = { path = "title1/Scroll View/Viewport/Btns/getPrizeBtn", type = "Button" }, --兑换奖励
    getPrizeGreyBtn = { path = "title1/Scroll View/Viewport/Btns/getPrizeGreyBtn", type = "Button" }, --兑换奖励未开启
    text_unlockLevel = { path = "title1/Scroll View/Viewport/Btns/getPrizeGreyBtn/text_unlockLevel", type = "Text" }, --兑换奖励未开启提示
    langBtn = { path = "title1/Scroll View/Viewport/Btns/langBtn", type = "Button" }, --设置语言
    langBtnText = { path = "title1/Scroll View/Viewport/Btns/langBtn/text", type = "Text" }, --当前语言
    channelBtn = { path = "title1/Scroll View/Viewport/Btns/channelBtn", type = "Button" }, --设置语言频道
    communityBtn = { path = "title1/Scroll View/Viewport/Btns/communityBtn", type = "Button" }, --社区
    communityDot = { path = "title1/Scroll View/Viewport/Btns/communityBtn/qipao", type = "RectTransform" }, --社区气泡
    shareBtn = { path = "title1/Scroll View/Viewport/Btns/shareBtn", type = "Button" }, --邀请有礼
    manualBtn = { path = "title1/Scroll View/Viewport/Btns/manualBtn", type = "Button" }, --人工客服
    aihelpBtn = { path = "title1/Scroll View/Viewport/Btns/aihelpBtn", type = "Button" }, --aihelp客服
    aihelpBtnReddot = { path = "title1/Scroll View/Viewport/Btns/aihelpBtn/reddot", type = "RectTransform" }, --aihelp客服
    aiServiceBtn = { path = "title1/Scroll View/Viewport/Btns/aiServiceBtn", type = "Button" }, --ai客服
    bindPhoneBtn = { path = "title1/Scroll View/Viewport/Btns/bindPhoneBtn", type = "Button" }, --手机绑定
    bindPhoneReddot = { path = "title1/Scroll View/Viewport/Btns/bindPhoneBtn/reddot", type = "RectTransform" }, --手机绑定红点
    deleteAccountBtn = { path = "title1/Scroll View/Viewport/Btns/deleteAccountBtn", type = "Button" }, --删除账号
    
    chatBlockListBtn = { path = "title1/Scroll View/Viewport/Btns/chatBlockListBtn", type = "Button" }, --聊天黑名单列表

    announcementBtn = { path = "title1/Scroll View/Viewport/Btns/announcementBtn", type = "Button", event_name = "OnClickAnnouncementBtnEvent" }, --公告
    announcementBtnReddot = { path = "title1/Scroll View/Viewport/Btns/announcementBtn/reddot", type = "RectTransform" }, --公告按钮红点
    pushMsgBtn = { path = "title1/Scroll View/Viewport/Btns/pushMsgBtn", type = "Button", event_name = "OnClickPushMsgBtnEvent" }, --消息推送

    switchBtn = { path = "title3/btn/switchBtn", type = "Button" }, --切换账号
    bindingBtn = { path = "title3/btn/bindingBtn", type = "Button" }, --绑定账号
    bindingBtnTips = { path = "title3/btn/bindingBtn/tips", type = "RectTransform" }, --绑定账号
    changeServerBtn = { path = "title3/btn/changeServerBtn", type = "Button" }, --切换服务器
    switchBtn1 = { path = "title3/btn1/switchBtn", type = "Button" }, --切换账号
    changeServerBtn1 = { path = "title3/btn1/changeServerBtn", type = "Button" }, --切换服务器
    switchBtnText = { path = "title3/btn/switchBtn/SwtichAccount", type = "Text" }, --越南地区切换账号按钮显示多语言

    btnGroup = { path = "title3/btn", type = RectTransform },
    btnGroup1 = { path = "title3/btn1", type = RectTransform },
    --fps
    fpsButton30 = { path = "title1/btnGroup/btn_30", type = "Button", event_name = "OnBtnFPS30Proxy" },
    fps30SelectObj = { path = "title1/btnGroup/btn_30/selectObj", type = "RectTransform" },
    fps30NoSelectObj = { path = "title1/btnGroup/btn_30/noSelectObj", type = "RectTransform" },
    fpsButton60 = { path = "title1/btnGroup/btn_60", type = "Button", event_name = "OnBtnFPS60Proxy" },
    fps60SelectObj = { path = "title1/btnGroup/btn_60/selectObj", type = "RectTransform" },
    fps60NoSelectObj = { path = "title1/btnGroup/btn_60/noSelectObj", type = "RectTransform" },

    selText60SelectObj = { path = "title1/btnGroup/btn_60/selectObj/selText", type = "Text" },
    selText60NoSelectObj = { path = "title1/btnGroup/btn_60/noSelectObj/selText", type = "Text" },
    selText30SelectObj = { path = "title1/btnGroup/btn_30/selectObj/selText", type = "Text" },
    selText30NoSelectObj = { path = "title1/btnGroup/btn_30/noSelectObj/selText", type = "Text" },
    
    versionText = { path = "title1/versionText", type = "TextMeshProUGUI" }, --版本号显示
    versionBtn = { path = "title1/versionText/Button", type = "Button", event_name = "OnClickVersionBtnEvent" }, --
}

function SettingSystem:FixMultiLang()
    if lang.USE_LANG == lang.AR then
        self.selText60SelectObj.fontSize = 15
        self.selText60NoSelectObj.fontSize = 15
        self.selText30SelectObj.fontSize = 15
        self.selText30NoSelectObj.fontSize = 15
        self.selText60SelectObj.lineSpacing = 0.8
        self.selText60NoSelectObj.lineSpacing = 0.8
        self.selText30SelectObj.lineSpacing = 0.8
        self.selText30NoSelectObj.lineSpacing = 0.8
    end
end

function SettingSystem:ctor(selfType)
    self.__base:ctor(selfType)
end

function SettingSystem:UpdateBaseInfo()
    local showBindingBtn = true
    -- 绑定提示小红点
    local showBingdingTips = false

    local const = require "const"
    local version_mgr = require "version_mgr"
    if version_mgr.CheckSvnTrunkVersion(30871) or version_mgr.CheckSvnBranchVersion(31261) then
        local bindTypeList = q1sdk.GetRoleBindType()
        local channel_tag = game_config.CHANNEL_TAG
        print("google", bindTypeList["GooglePlay"], "facebook", bindTypeList["Facebook"])
        self.bindingBtnTips.gameObject:SetActive(false)
        if channel_tag == const.package_name_set.com_q1_hero then
            --老包没绑定谷歌就显示绑定
            showBindingBtn = (not bindTypeList["GooglePlay"])
            showBingdingTips = showBindingBtn
            log.Warning("uisetting 老包没绑定谷歌就显示绑定")
        elseif channel_tag == const.package_name_set.com_q1_hero_huawei then
            showBindingBtn = (not bindTypeList["HuaWei"] or not bindTypeList["Facebook"])
            showBingdingTips = false
        elseif const.IsVietnamChannel() or const.IsVietnamIosChannel() then
            -- 爱玩联运包不需要绑定功能		贪玩越南不需要绑定账号界面
            showBindingBtn = false
            showBingdingTips = false
        elseif const.CanBindGoogle() or const.CanBindFacebook() or const.CanBindTwitter() or const.CanBindApple() or const.CanBindHuaWei() or const.CanBindEmail() then
            
            local needShow= (const.CanBindGoogle() and not bindTypeList["GooglePlay"]) or
            (const.CanBindFacebook() and not bindTypeList["Facebook"]) or
            (const.CanBindTwitter() and not bindTypeList["Twitter"]) or
            (const.CanBindApple() and not bindTypeList["AppleID"]) or
            (const.CanBindHuaWei() and not bindTypeList["HuaWei"])--华为包
            or (const.CanBindEmail() and not bindTypeList["EMail"]) -- 邮箱登录
            
            local needShowtips = not (
                (const.CanBindGoogle() and bindTypeList["GooglePlay"]) or
                (const.CanBindFacebook() and bindTypeList["Facebook"]) or
                (const.CanBindTwitter() and bindTypeList["Twitter"]) or
                (const.CanBindApple() and bindTypeList["AppleID"]) or
                (const.CanBindHuaWei() and bindTypeList["HuaWei"]) -- 华为包
                or (const.CanBindEmail() and bindTypeList["EMail"]) -- 邮箱登录
            )
            --新包有一个没绑就显示
            showBindingBtn = needShow
            showBingdingTips = needShowtips --只要有一个绑定了就不显示红点
        else
            --游客显示
            showBindingBtn = q1sdk.Visitor()
            showBingdingTips = showBindingBtn
        end
    else
        --游客显示
        showBindingBtn = q1sdk.Visitor()
        showBingdingTips = showBindingBtn
    end
    showBindingBtn = ((not game_config.Q1SDK_DOMESTIC) or Utility.IsInEditor()) and showBindingBtn
    self.btnGroup.gameObject:SetActive(showBindingBtn)
    self.btnGroup1.gameObject:SetActive(not showBindingBtn)
    self.bindingBtnTips.gameObject:SetActive(showBingdingTips)

    local bShowAccountUrl = not game_config.Q1SDK_DOMESTIC and Application.platform ~= RuntimePlatform.IPhonePlayer

    --国服隐藏语言切换
    --海外非审核服或者编辑器模式才显示入口
    self.langBtn.gameObject:SetActive((not game_config.Q1SDK_DOMESTIC) and not ReviewingUtil.IsReviewing() and not const.IsVietnamIosChannel() and not const.IsVietnamChannel() or Utility.IsInEditor())

    --需要隐藏切换按钮
    if const.IsNoSwitchLang() then
        self.langBtn.gameObject:SetActive(false)
    end

    -- todo 暂时隐藏频道切换按钮
    --self.channelBtn.gameObject:SetActive(not ReviewingUtil.IsReviewing())

    local util = require "util"
    local const = require "const"
    local channel_tag = util.GetChannelTag()
    local isQ1Sdk = channel_tag == const.package_name_set.com_wmzz2_q1 or channel_tag == const.package_name_set.com_cnsj_youyi
            or channel_tag == const.package_name_set.com_xgame_q1 or channel_tag == const.package_name_set.com_cnsj_q1
            or channel_tag == const.package_name_set.com_xgame_hnsz
    self.bindPhoneBtn.gameObject:SetActive(game_config.Q1SDK_DOMESTIC and isQ1Sdk) -- 绑定手机
    self.manualBtn.gameObject:SetActive(game_config.Q1SDK_DOMESTIC and isQ1Sdk) -- 人工客服
    local bShowDeleteAccountBtn = ReviewingUtil.IsReviewing() and Application.platform == RuntimePlatform.IPhonePlayer
    self.deleteAccountBtn.gameObject:SetActive(bShowDeleteAccountBtn)        -- 删除账号

    if const.IsVietnamChannel() then
        self.switchBtnText.text = lang.Get(860)
    elseif const.IsVietnamIosChannel() then
        self.switchBtnText.text = lang.Get(861)
    end
    self.aiServiceBtn.gameObject:SetActive(game_config.ENABLE_AIHELP and not ReviewingUtil.IsReviewing())
    
    -- todo 暂时隐藏社区按钮
    --self.announcementBtn.gameObject:SetActive(not const.IsVietnamIosChannel() and not const.IsVietnamChannel())
    
    local bShowHuaWeiAccountUrl = false
    if bShowAccountUrl then
        local channel_tag = util.GetChannelTag()
        if channel_tag == const.package_name_set.com_q1_hero_huawei then
            bShowAccountUrl = false
            bShowHuaWeiAccountUrl = true
        end
    end

    local push_message_mgr = require "push_message_mgr"
    self.pushMsgBtn.gameObject:SetActive(push_message_mgr.CheckIsUnlock())
    self.versionText.text = "<i><u>"..string.formatL(1696, util.GetClientVersion()).."</u></i>"
    -- self.versionText.color = { r = 0.3, g = 0.4, b = 0.46, a = 1 }
    -- self.googleBtn.gameObject:SetActive(bShowAccountUrl)
    -- self.huaweiBtn.gameObject:SetActive(bShowHuaWeiAccountUrl)
end

function SettingSystem:UpdateUI()
    local ui_setting_data = require "ui_setting_data"
    local value = ui_setting_data.GetAttrData(EnSubModel.En_Model_Voice, EnVoiceAttrKey.En_AttrKey_VoiceEffect)
    self.soundSlider.value = tonumber(value)

    local value2 = ui_setting_data.GetAttrData(EnSubModel.En_Model_Voice, EnVoiceAttrKey.En_AttrKey_BGM)
    self.musicSlider.value = tonumber(value2)

    --填充语言列表	-- 这里除了中文, 就显示英文
    local listData = lang.GetLangListData()
    local selLang = ui_setting_data.GetAttrData(EnSubModel.En_Model_Base, EnBaseAttrKey.En_AttrKey_Lang)
    local selectedIndex = tonumber(selLang)
    if selectedIndex ~= 1 then
        --selectedIndex = 3
    end
    oldLangIndex = selectedIndex
    self.langBtnText.text = lang.Get(listData[selectedIndex].key)
    --基础信息
    self:UpdateBaseInfo()
    --更新红点
    self:UpdateAIHelpMsgRed()
end

function SettingSystem:UpdateAIHelpMsgRed()
    local aihelp = require "aihelp"
    local game_config = require "game_config"
    if game_config.ENABLE_AIHELP then
        self.aihelpBtnReddot.gameObject:SetActive(aihelp.GetUnReadMsgShow())
    end
end

function SettingSystem:SubscribeEvents()
    self.UpdateRedAIHelpTips = function(eventname, isShowRed)
        if not self or not self:IsValid() then
            return
        end
        local game_config = require "game_config"
        if game_config.ENABLE_AIHELP then
            self.aihelpBtnReddot.gameObject:SetActive(isShowRed)
        end
    end
    event.Register(event.AI_HELP_UNREAD_MSG_RED, self.UpdateRedAIHelpTips)

    self.OnSoundSliderHandler = function()
        --音效改变
        local ui_setting_data = require "ui_setting_data"
        local value = tostring(self.soundSlider.value)
        ui_setting_data.SetAttrData(EnSubModel.En_Model_Voice, EnVoiceAttrKey.En_AttrKey_VoiceEffect, value)
    end
    self.soundSlider.onValueChanged:AddListener(self.OnSoundSliderHandler)

    self.OnMusicSliderHandler = function()
        --音乐改变
        local ui_setting_data = require "ui_setting_data"
        local value = tostring(self.musicSlider.value)
        ui_setting_data.SetAttrData(EnSubModel.En_Model_Voice, EnVoiceAttrKey.En_AttrKey_BGM, value)
    end
    self.musicSlider.onValueChanged:AddListener(self.OnMusicSliderHandler)

    self.clickLangBtnEvent = function()
        if windowmgr:IsModuleShown("ui_pointing_target") and not unforced_guide_mgr.IsGuiding() then
            windowmgr:UnloadModuleImmediate("ui_pointing_target")
        end
        local ui_setting_lang = require "ui_setting_lang"
        ui_setting_lang.ShowWithParam(function(selectIdx)
            local listData = lang.GetLangListData()
            self.langBtnText.text = lang.Get(listData[selectIdx].key)

            local ui_personal_helper = require "ui_personal_helper"
            ui_personal_helper.Refresh()
        end)
    end
    self.langBtn.onClick:AddListener(self.clickLangBtnEvent)
    
    self.getPrizeBtnEvent = function()
        local giftPrize = require "ui_setting_gift_code"
        giftPrize.ShowDirectly()
    end
    self.getPrizeBtn.onClick:AddListener(self.getPrizeBtnEvent)

    self.getPrizeGreyBtnEvent = function()
        local cfg = game_scheme:InitBattleProp_0(835)
        local needLevel = cfg and cfg.szParam.data[0] or 151
        local passLevel = laymain_data.GetPassLevel()
        if passLevel < needLevel then
            local cfg = game_scheme:HookLevel_0(needLevel)
            local levelName = cfg.Name
            local flow_text = require "flow_text"
            flow_text.Add(string.format(lang.Get(9390), levelName))
        end
    end
    self.getPrizeGreyBtn.onClick:AddListener(self.getPrizeGreyBtnEvent)

    --设置语言频道
    self.clickChannelBtnEvent = function()
        local windowMgr = require "ui_window_mgr"
        windowMgr:ShowModule("ui_setting_lang_channel")
    end
    self.channelBtn.onClick:AddListener(self.clickChannelBtnEvent)

    --切换服务器
    self.clickChangeServerBtnEvent = function()
        if windowmgr:IsModuleShown("ui_pointing_target") and not unforced_guide_mgr.IsGuiding() then
            windowmgr:UnloadModuleImmediate("ui_pointing_target")
        end
        windowmgr:ShowModule("ui_setting_server_new")
    end
    self.changeServerBtn.onClick:AddListener(self.clickChangeServerBtnEvent)
    self.changeServerBtn1.onClick:AddListener(self.clickChangeServerBtnEvent)

    self.OnSwitchBtnHandler = function()
        --切换账号
        local ui_login_main_mgr = require "ui_login_main_mgr"
        local build_diff_setting = require "build_diff_setting"
        if build_diff_setting.GetIsChannelSwitchAccount() then
            --贪玩韩国 贪玩越南
            log.Warning("use GetIsChannelSwitchAccount:")
            local ui_login_main = require "ui_login_main"
            ui_login_main.Channel_SwitchAccount()
            return
        end
        if not ui_login_main_mgr.IsAccountWithPlatform() then
            --不通过账号列表切换时，直接返回登录
            local content = lang.Get(lang_key.KEY_SETTING_TIPS1)
            message_box.Open(
                    content, message_box.STYLE_YESNO,
                    function(data, nRet)
                        if message_box.RESULT_YES == nRet then
                            --返回登录
                            if game_config.Q1SDK_DOMESTIC and (not util.ShouldUseCustomSDKUI()) and (not Application.isEditor) then
                                self.switchingAccount = true

                                local ui_login_main = require "ui_login_main"
                                ui_login_main.Q1SDK_SwitchAccount()
                            else
                                self:GotoUILoginMain()
                            end
                        end
                    end,
                    0,
                    lang.KEY_OK,
                    lang.KEY_CANCEL
            )
        else
            function gotoUILoginMain(bSuccess, bGotoBindAccount)
            local email_subscribe_mgr = require "email_subscribe_mgr"                
                email_subscribe_mgr.Log("gotoUILoginMain! bSuccess ",bSuccess," bGotoBindAccount", bGotoBindAccount)

                if bGotoBindAccount then
                    self:OnBindingBtnHandler()
                else
                    self.switchingAccount = true
                    if not bSuccess then
                        q1sdk.Logout()
                        self:GotoUILoginMain()
                    end
                end
            end
            local message_box_switch_account = require "ui_setting_message_box_account"
            message_box_switch_account.SwitchAccount(gotoUILoginMain)
        end
    end
    self.switchBtn.onClick:AddListener(self.OnSwitchBtnHandler)
    self.switchBtn1.onClick:AddListener(self.OnSwitchBtnHandler)

    self.OnBindingBtnHandler = function()
        --绑定账号(国内)
        --local ui_setting_bind = require "ui_setting_bind"
        --ui_setting_bind.Show()
        if windowmgr:IsModuleShown("ui_pointing_target") and not unforced_guide_mgr.IsGuiding() then
            windowmgr:UnloadModuleImmediate("ui_pointing_target")
        end

        local callback = function(success, errorCode, msg, jsonStr)
            local email_subscribe_mgr = require "email_subscribe_mgr"
            email_subscribe_mgr.Log("BindHandle callback!",success, errorCode, msg, jsonStr)
            local _, newUserID = BindHandle(success, errorCode, msg, jsonStr)
            q1sdk.SetRoleBindType(jsonStr)
            -- 绑定成功后 q1sdk.Visitor()可能是true

            if not self:IsValid() then
                return
            end
            --游戏绑定成功会走这，插入流程上处理绑定成功关闭验证界面
            if success and windowmgr:IsModuleShown("ui_email_code") then
                windowmgr:UnloadModuleImmediate("ui_email_code")
                windowmgr:UnloadModule("ui_email_switch")
                windowmgr:UnloadModule("ui_email_bind")
                windowmgr:UnloadModule("ui_email_change_bind")
                util.DelayCallOnce(0.5,function (  )
                    if not self:IsValid() then
                        return
                    end
                    self.OnBindingBtnHandler()
                end)
            end
            if success then
                local version_mgr = require "version_mgr"
                if version_mgr.CheckSvnTrunkVersion(30871) or version_mgr.CheckSvnBranchVersion(31261) then
                    local bindTypeList = q1sdk.GetRoleBindType()
                    local channel_tag = util.GetChannelTag()

                    if channel_tag == const.package_name_set.com_q1_hero then
                        self.bindingBtn.gameObject:SetActive(not bindTypeList["GooglePlay"]) --老包没绑定谷歌就显示绑定
                    elseif channel_tag == const.package_name_set.com_q1_hero_huawei then
                        -- 华包显示
                        self.bindingBtn.gameObject:SetActive(not bindTypeList["HuaWei"] or not bindTypeList["Facebook"])
                    elseif const.CanBindGoogle() or const.CanBindFacebook() or const.CanBindTwitter() or const.CanBindApple() or const.CanBindHuaWei() or const.CanBindEmail() then
                        local needShow = (const.CanBindGoogle() and not bindTypeList["GooglePlay"]) or
                                (const.CanBindFacebook() and not bindTypeList["Facebook"]) or
                                (const.CanBindTwitter() and not bindTypeList["Twitter"]) or
                                (const.CanBindApple() and not bindTypeList["AppleID"]) or
                                (const.CanBindHuaWei() and not bindTypeList["HuaWei"])  or--华为包                              
                                (const.CanBindEmail() and not bindTypeList["EMail"])
                        self.bindingBtn.gameObject:SetActive(needShow) --新包有一个没绑就显示
                    else
                        self.bindingBtn.gameObject:SetActive(q1sdk.Visitor()) --游客显示
                    end
                else
                    self.bindingBtn.gameObject:SetActive(q1sdk.Visitor()) --游客显示
                end
            end

            if --[[q1sdk.Visitor()]]not success then
                local lastUserID = PlayerPrefs.GetString('last_login_userID', '')
                -- 如果是游客登录，直接显示为空没有问题，但如果是以第三方账号登录（e.g. 谷歌），去绑定第三方账号（e.g. FB），直接显示为空则不对
                -- 所以前面判断 q1sdk.Visitor() 有其正确性。这里先改为显示上一次 userID

                local bindType = q1sdk.GetRoleBindType()
                local binded = false
                if util.get_len(bindType) > 0 then
                    binded = true
                end
            end
        end

        -- 自定义UI
        if game_config.Q1SDK_DOMESTIC then
            local ui_login_main = require "ui_login_main"
            ui_login_main.Q1SDK_LoginBind(callback)
        else
            --直接打开sdk的绑定面板 userRegisterBind
            local message_box_bind_account = require "ui_setting_message_box_account"
            message_box_bind_account.BindAccount(callback)
        end
    end
    self.bindingBtn.onClick:AddListener(self.OnBindingBtnHandler)

    --网页相关start-------
    -- local url_mgr = require "url_mgr"
    -- self.OnFacebookBtnHandler = function()					--facebook按钮
    -- 	q1sdk.ApplicationOpenURL(--[["http://www.q1.com/"]]url_mgr.FACEBOOK_URL)
    -- end
    -- self.facebookBtn.onClick:AddListener(self.OnFacebookBtnHandler)

    -- self.OnGoogleBtnHandler = function()
    -- 	q1sdk.ApplicationOpenURL(--[["http://www.q1.com/"]]url_mgr.GOOGLE_URL)
    -- 	--[[
    -- 		绑定测试 
    -- 		local login_main = require "ui_login_main"
    -- 		account_data.SendBindMessage(login_pb.enLoginPartnerID_BingChuan, tostring(login_main.GetAccountName()), tostring(login_main.GetStrToken()),tostring(login_main.GetAccountName()) )
    -- 	]]
    -- end
    -- self.googleBtn.onClick:AddListener(self.OnGoogleBtnHandler)

    -- self.OnHuaWeiBtnHandler = function ()
    -- 	q1sdk.ApplicationOpenURL(url_mgr.HUAWEI_URL)
    -- end
    -- self.huaweiBtn.onClick:AddListener(self.OnHuaWeiBtnHandler)

    --网页相关end-------

    self.OnLoginCallBack = function(eventName, isCallLogin, token, userID, loginType)
        --账号绑定（国外）
        if isCallLogin == false then
            if self.switchingAccount then
                self.switchingAccount = false
                --切换账号直接返回登录界面
                return
            end

            local flow_text = require "flow_text"
            flow_text.Clear()
            flow_text.Add(lang.Get(lang_key.KEY_BIND_SUCESS))

            local login_main = require "ui_login_main"
            --刷新

            local version_mgr = require "version_mgr"
            if version_mgr.CheckSvnTrunkVersion(30871) or version_mgr.CheckSvnBranchVersion(31261) then
                local bindTypeList = q1sdk.GetRoleBindType()
                local channel_tag = util.GetChannelTag()
                local const = require "const"
                if channel_tag == const.package_name_set.com_q1_hero then
                    self.bindingBtn.gameObject:SetActive(not bindTypeList["GooglePlay"]) --老包没绑定谷歌就显示绑定

                elseif channel_tag == const.package_name_set.com_q1_hero_huawei then
                    -- 华为包显示
                    self.bindingBtn.gameObject:SetActive(not bindTypeList["HuaWei"] or not bindTypeList["Facebook"])
                elseif const.CanBindGoogle() or const.CanBindFacebook() or const.CanBindTwitter() or const.CanBindApple() or const.CanBindHuaWei() or const.CanBindEmail() then
                    local needShow = (const.CanBindGoogle() and not bindTypeList["GooglePlay"]) or
                            (const.CanBindFacebook() and not bindTypeList["Facebook"]) or
                            (const.CanBindTwitter() and not bindTypeList["Twitter"]) or
                            (const.CanBindApple() and not bindTypeList["AppleID"]) or
                            (const.CanBindHuaWei() and not bindTypeList["HuaWei"]) or --华为包
                            (const.CanBindEmail() and not bindTypeList["EMail"])

                    self.bindingBtn.gameObject:SetActive(needShow) --新包有一个没绑就显示
                else
                    self.bindingBtn.gameObject:SetActive(q1sdk.Visitor()) --游客显示
                end
            else
                self.bindingBtn.gameObject:SetActive(q1sdk.Visitor()) --游客显示
            end

            --重设登录数据和发送绑定消息
            account_data.SendBindMessage(loginType, tostring(userID), tostring(token), tostring(login_main.GetAccountName()))
            account_data.SetLoginType(loginType)
            login_main.SetAccountName(userID)
            --login_main.SetStrToken(token)
            local net_login_module = require "net_login_module"
            net_login_module.SetPartnerID(loginType)
            net_login_module.ResetLoginSession(token)
        end
    end
    event.Register(event.ON_LOGIN_CALLBACK, self.OnLoginCallBack)
    

    self.OnModifyNameHandler = function()
        if windowmgr:IsModuleShown("ui_pointing_target") and not unforced_guide_mgr.IsGuiding() then
            windowmgr:UnloadModuleImmediate("ui_pointing_target")
        end
        windowmgr:ShowModule("ui_actor_modify_name")
    end

    self.communityBtnEvent = function()
        windowmgr:ShowModule("ui_setting_community_entrance")
    end
    self.communityBtn.onClick:AddListener(self.communityBtnEvent)

    self.OnClickAnnouncementBtnEvent = function()
        --公告按钮点击事件
        windowmgr:ShowModule("ui_announcement_popup")

        local notice_data = require "notice_data"
        notice_data.SetLocalClickTime(notice_data.ENUM_BtnType.AnnouncementBtn)
        notice_data.GameEventReport_Click()
    end

    --消息推送点击事件
    self.OnClickPushMsgBtnEvent = function()
        windowmgr:ShowModule("ui_push_message_setting")
    end

    --查看版本的弹窗
    self.OnClickVersionBtnEvent = function()
        if ReviewingUtil.IsReviewing() then
            return
        end
        windowmgr:ShowModule("ui_version_info")
    end

    self.shareBtnEvent = function()
        local ui_open_test_activity = require "ui_open_test_activity"
        ui_open_test_activity.SetInputParam(2)
        windowmgr:ShowModule("ui_open_test_activity")
        windowmgr:UnloadModule("ui_personalInfo")
    end
    self.shareBtn.onClick:AddListener(self.shareBtnEvent)

    self.bindPhoneBtnEvent = function()
        windowmgr:ShowModule("ui_bind_phone")
    end
    self.bindPhoneBtn.onClick:AddListener(self.bindPhoneBtnEvent)

    self.chatBlockListBtnEvent = function()
        windowmgr:ShowModule("ui_personal_chat_block_list")
    end
    self.chatBlockListBtn.onClick:AddListener(self.chatBlockListBtnEvent)


    self.manualBtnEvent = function()
        local ui_personalInfo = require "ui_personalInfo"
        local ui_personal_helper = require "ui_personal_helper"
        ui_personal_helper.GetCurIndex(3)
        ui_personal_helper.SetCurPage(3)
        ui_personalInfo.GetCurIndex(3)
    end

    self.manualBtn.onClick:AddListener(self.manualBtnEvent)
    self.aihelpBtnEvent = function()
        if const.IsVietnamIosChannel() or const.IsVietnamChannel() then
            --越南渠道拉起自己的客服工具
            q1sdk.ChannelCustomer()
        else
            local aihelp = require "aihelp"
            aihelp.AIHelpShowConversation();
        end
    end
    self.aihelpBtn.onClick:AddListener(self.aihelpBtnEvent)

    self.aiServiceBtnEvent = function()
        local ai_customer_service_data = require "ai_customer_service_data"
        ai_customer_service_data.OpenUrl()
    end
    self.aiServiceBtn.onClick:AddListener(self.aiServiceBtnEvent)

    --删除账号
    self.deleteAccountBtnEvent = function()
        local ui_personalInfo = require "ui_personalInfo"
        local ui_personal_helper = require "ui_personal_helper"
        ui_personal_helper.GetCurIndex(3)
        ui_personal_helper.SetCurPage(3)
        ui_personalInfo.GetCurIndex(3)
        ui_personal_helper.SetCurPageSelectId(1692)    --注销账号的id为1692
    end
    self.deleteAccountBtn.onClick:AddListener(self.deleteAccountBtnEvent)

    event.Register(event.COMMUNITY_FB_REWARD, ShowReward)

    self.OnBtnFPS30Proxy = function()
        local fps = PlayerPrefs.GetInt("User_Set_FPS", 0)
        if fps == 30 then
            return
        end
        self:SetFPS(30)
    end
    self.OnBtnFPS60Proxy = function()
        local fps = PlayerPrefs.GetInt("User_Set_FPS", 0)
        if fps == 60 then
            return
        end
        local MsgBox = require "message_box"
        local okCall = function(data, nRet)
            if MsgBox.RESULT_YES == nRet then
                self:SetFPS(60)
            end
        end
        local content = lang.Get(402503)--"是否确认选择\n（确认后请按下确认按钮获得奖励）"
        MsgBox.Open(content, MsgBox.STYLE_YESNO, okCall, 0, lang.KEY_OK, lang.KEY_CANCEL, lang.Get(7507))
    end
end
function SettingSystem:RefreshFPSBtn(fps)
    local bActive = fps and fps == 60
    self.fps30SelectObj.gameObject:SetActive(bActive == false)
    self.fps30NoSelectObj.gameObject:SetActive(bActive)
    self.fps60SelectObj.gameObject:SetActive(bActive)
    self.fps60NoSelectObj.gameObject:SetActive(bActive == false)
end

function SettingSystem:SetFPS(fps)
    self:RefreshFPSBtn(fps)
    const.TargetFrameRate = fps
    Application.targetFrameRate = const.TargetFrameRate
    PlayerPrefs.SetInt("User_Set_FPS", fps)
end
function ShowReward(eventName, resultData, title)
    if window then
        local list = {}
        table.insert(list, resultData)
        local ui_reward_result = require "ui_reward_result"
        ui_reward_result.SetInputParam(list, nil, title)
        windowmgr:ShowModule("ui_reward_result")
    end
end

function SettingSystem:GotoUILoginMain()
    event.Trigger(event.RETURN_LOGIN)
    windowmgr:UnloadModule("ui_personalInfo")
    local login_module = require "net_login_module"
    login_module.ReturnLogin()
end

function SettingSystem:UnsubscribeEvents()

    self.soundSlider.onValueChanged:RemoveListener(self.OnSoundSliderHandler)
    self.musicSlider.onValueChanged:RemoveListener(self.OnMusicSliderHandler)
    --self.dropLang.onValueChanged:RemoveListener(self.OnDropLangHandler)
    self.getPrizeBtn.onClick:RemoveListener(self.getPrizeBtnEvent)
    self.getPrizeGreyBtn.onClick:RemoveListener(self.getPrizeGreyBtnEvent)
    self.langBtn.onClick:RemoveListener(self.clickLangBtnEvent)
    self.channelBtn.onClick:RemoveListener(self.clickChannelBtnEvent)
    self.shareBtn.onClick:RemoveListener(self.shareBtnEvent)
    self.manualBtn.onClick:RemoveListener(self.manualBtnEvent)
    self.aihelpBtn.onClick:RemoveListener(self.aihelpBtnEvent)
    self.aiServiceBtn.onClick:RemoveListener(self.aiServiceBtnEvent)
    self.changeServerBtn.onClick:RemoveListener(self.clickChangeServerBtnEvent)
    self.changeServerBtn1.onClick:RemoveListener(self.clickChangeServerBtnEvent)
    self.switchBtn.onClick:RemoveListener(self.OnSwitchBtnHandler)
    self.switchBtn1.onClick:RemoveListener(self.OnSwitchBtnHandler)
    self.bindingBtn.onClick:RemoveListener(self.OnBindingBtnHandler)
    -- self.facebookBtn.onClick:RemoveListener(self.OnFacebookBtnHandler)
    -- self.googleBtn.onClick:RemoveListener(self.OnGoogleBtnHandler)
    -- self.huaweiBtn.onClick:RemoveListener(self.OnHuaWeiBtnHandler)
    event.Unregister(event.ON_LOGIN_CALLBACK, self.OnLoginCallBack)
    self.bindPhoneBtn.onClick:RemoveListener(self.bindPhoneBtnEvent)
    self.chatBlockListBtn.onClick:RemoveListener(self.bindPhoneBtnEvent)
    self.deleteAccountBtn.onClick:RemoveListener(self.deleteAccountBtnEvent)

    local playerProp = player_mgr.GetPlayerProp()
    if playerProp then
        playerProp:RemoveListener("roleName", self.playerRoleNameChange)
    end
    event.Unregister(event.ACTOR_FACE_UPDATE, self.ActorFaceUpdateEvent)
    event.Unregister(event.ACTOR_FRAME_UPDATE, self.ActorFaceUpdateEvent)
    self.communityBtn.onClick:RemoveListener(self.communityBtnEvent)
    event.Unregister(event.AI_HELP_UNREAD_MSG_RED, self.UpdateRedAIHelpTips)
    event.Unregister(event.COMMUNITY_FB_REWARD, ShowReward)
end

function SettingSystem:initBtnTips()
    local actor_face_data = require "actor_face_data"
    local button_tips_trigger = require "button_tips_trigger"
    if bFirstLogin and not actor_face_data.HasModifiedName() then
        bFirstLogin = false

    end
end

function SettingSystem:OnLogin()
end

function SettingSystem:Init()
    self.switchingAccount = false
    print("SettingSystem:Init")
    -- --请求公告数据
    local notice_data = require "notice_data"
    notice_data.RequestAnnouncementUrl()
    self:initBtnTips()
    self:SubscribeEvents()
    self:UpdateUI()
    self:UpdateReddot()

    if bShowGuid and self.bindingBtn.gameObject.activeSelf and not unforced_guide_mgr.IsGuiding() then
        local ui_pointing_target = require "ui_pointing_target"
        ui_pointing_target.ShowWithParam(self.bindingBtn.gameObject, true, true, false, 0, 0, 0, -9, 0.8)
    end
    if ReviewingUtil.IsReviewing() then
        self.getPrizeBtn.gameObject:SetActive(false)
        self.getPrizeGreyBtn.gameObject:SetActive(false)
    else
        local passLevel = laymain_data.GetPassLevel()
        local cfg = game_scheme:InitBattleProp_0(835)
        local needLevel = cfg and cfg.szParam.data[0] or 151
        if passLevel >= needLevel then
            self.getPrizeBtn.gameObject:SetActive(true)
            self.getPrizeGreyBtn.gameObject:SetActive(false)
        else
            self.getPrizeBtn.gameObject:SetActive(false)
            self.getPrizeGreyBtn.gameObject:SetActive(true)
            local cfg = game_scheme:HookLevel_0(needLevel)
            local levelName = cfg.Name
            self.text_unlockLevel.text = string.format("(%s)", levelName)
        end
    end
    
    -- todo 暂时隐藏社区按钮
    --self.communityBtn.gameObject:SetActive(((not game_config.Q1SDK_DOMESTIC) or Utility.IsInEditor()) and (not ReviewingUtil.IsReviewing()))
    
    -- todo 暂时隐藏邀请按钮
    --local open_test_mgr = require "open_test_mgr"
    --self.shareBtn.gameObject:SetActive(Utility.IsInEditor() and (not ReviewingUtil.IsReviewing() and open_test_mgr.IsOpen()))
end

function SettingSystem:OnShow()
    local fps = Application.targetFrameRate
    self:RefreshFPSBtn(fps)
    self:BindUIRed(self.aiServiceBtn.transform, red_const.Enum.AICustomerService, nil, {pos = {x = -117,y = -3},redPath = red_const.Type.GM})
end

function SettingSystem:UpdateReddot()
    local game_config = require "game_config"
    local util = require "util"
    local const = require "const"
    local channel_tag = util.GetChannelTag()
    local isQ1Sdk = channel_tag == const.package_name_set.com_wmzz2_q1 or channel_tag == const.package_name_set.com_cnsj_youyi or channel_tag == const.package_name_set.com_xgame_q1 or channel_tag == const.package_name_set.com_cnsj_q1 or channel_tag == const.package_name_set.com_xgame_hnsz
    if game_config.Q1SDK_DOMESTIC and isQ1Sdk then
        local laymain_data = require "laymain_data"
        local passLv = laymain_data.GetPassLevel()
        local isGot = PlayerPrefs.GetInt(player_mgr.GetPlayerRoleID() .. "hadBindPhone", 0)
        self.bindPhoneReddot.gameObject:SetActive((isGot == 0) and (passLv and passLv >= 6))
    end
    local isShowQipao = ui_community_data.GetFacebookGuideRewardState()
    self.communityDot.gameObject:SetActive(isShowQipao)

    --公告红点
    local notice_data = require "notice_data"
    local isShowAnnouncementRed = notice_data.CheckAnnouncementRedDot()
    self.announcementBtnReddot.gameObject:SetActive(isShowAnnouncementRed)
end

function UpdateReddotEvent()
    if window and window:IsValid() then
        window:UpdateReddot()
    end
end
event.Register(event.BINDING_PHONE_SUCCESSFULLY, UpdateReddotEvent)
event.Register(event.COMMUNITY_FB_REDDOT, UpdateReddotEvent)
event.Register(event.UPDATE_ANNOUNCEMENT_DATA, UpdateReddotEvent)

function SettingSystem:Close()
    if self.UIRoot and self:IsValid() then
        self:UnsubscribeEvents()
    end
    if windowmgr:IsModuleShown("ui_pointing_target") and not unforced_guide_mgr.IsGuiding() then
        windowmgr:UnloadModuleImmediate("ui_pointing_target")
    end

    local new_scene_mgr = require "new_scene_mgr"
    new_scene_mgr.InitMenuBgSize()

    if self.asset then
        self.asset:Dispose()
        self.asset = nil
    end
    self.__base:Close()
    window = nil
end

local CUIView = class(ui_base, nil, SettingSystem)

function Show(parentTransform)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window:LoadUIResource(ui_path, nil, parentTransform, nil)
        window.switchingAccount = false
    end
    window:Show()
    return window
end

function Hide()
    if windowmgr:IsModuleShown("ui_pointing_target") and not unforced_guide_mgr.IsGuiding() then
        windowmgr:UnloadModuleImmediate("ui_pointing_target")
    end
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    bShowGuid = false
    if window ~= nil then
        window.switchingAccount = false
        window:Close()
        window = nil
    end
end

local SessionLoginHelp = {
    sessionLoginWays = {
        [login_pb.enLoginPartnerID_Google] = 1,
        [login_pb.enLoginPartnerID_Facebook] = 1,
        [login_pb.enLoginPartnerID_GameCenter] = 1,
        [login_pb.enLoginPartnerID_Apple] = 1,
        [login_pb.enLoginPartnerID_Email] = 1,
        [login_pb.enLoginPartnerID_HuaWei] = 1,
        [login_pb.enLoginPartnerID_Visitor] = 1,
    }
}
function SessionLoginHelp.CanSessionLogin(loginType)
    if SessionLoginHelp.sessionLoginWays[loginType] then
        return true
    else
        return false
    end
end

local loginFuncName = {
    [login_pb.enLoginPartnerID_Visitor] = "guestLogin",
    [login_pb.enLoginPartnerID_Google] = "googleLogin",
    [login_pb.enLoginPartnerID_Facebook] = "facebookLogin",
    [login_pb.enLoginPartnerID_GameCenter] = "gamecenterLogin",
    [login_pb.enLoginPartnerID_Apple] = "appleLogin",
}

-- 绑定回调
function BindHandle(success, errorCode, msg, jsonStr)
    log.Warning("OnBindingBtnHandler>>>>>>>", tostring(success), errorCode, msg, jsonStr)
    local newUserID = ''
    if success then
        q1sdk.SetRoleBindType(jsonStr)
        local net_login_module = require "net_login_module"

        local json = require "dkjson"
        local jsonData = json.decode(jsonStr) or {}
        newUserID = jsonData["user"] -- 这里的 user 变成了游客 userID

        if not q1sdk.Visitor() then
            -- 如果是非游客第三方账号登录，这里不显示游客的 user, 而显示最近一次登录时使用的 user
            local lastUserID = PlayerPrefs.GetString('last_login_userID', '')
            if not string.empty(lastUserID) then
                log.Warning("绑定接口返回 userid:", newUserID, ",lastUserID:", lastUserID)
                newUserID = lastUserID
            end
        end

        local after = function(userID)
            local login_main = require "ui_login_main"
            --做兼容，iOS绑定返回的参数中不保证user值有效
            -- e.g. 2021/12/30 Android 后台返回的值 user 可能为 0 ， {"user":0,"partner_id":3}
            if (newUserID == nil or newUserID == "" or newUserID == 0) and userID ~= nil then
                log.Warning("use login userID:" .. userID)
                newUserID = userID
            end

            -- 重设登录数据和发送绑定消息
            local loginType = jsonData["partner_id"] or login_pb.enLoginPartnerID_BingChuan

            -- TODO 如果强杀进程没上报上去。本地保存下次上报上去???
            --intervalHandle = util.IntervalCall(4, function()
            account_data.SendBindMessage(
                    loginType,
                    tostring(newUserID),
                    tostring(--[[login_main.GetStrToken()]]net_login_module.GetLoginSession()),
                    tostring(login_main.GetAccountName())
            )
            --end, 0)

            --不因绑定去修改登录类型，仍使用游客登录
            --account_data.SetLoginType(loginType)
            login_main.SetAccountName(newUserID)
            net_login_module.SetPartnerID(loginType)
        end

        local relogin = function(lastLoginType)
            q1sdk.Logout()

            local loginFnc = loginFuncName[lastLoginType]
            if loginFnc == nil then
                log.Error("不支持的登录类型：", lastLoginType)
                -- 不清楚为什么之前会将按上次登录类型选择这里的登录类型统一改成 guestLogin, 这里将 guestLogin 作为备选项
                loginFnc = "guestLogin"
            end
            q1sdk.Login(
                    function(errCode, errMsg, token, userID, openInfo)
                        local json = require "dkjson"
                        local jsonData = json.decode(openInfo) or {}
                        -- print("根据usertypelist 判断是否是游客   openInfo = ",openInfo)
                        -- print("根据usertypelist 判断是否是游客   usertypelist = ",jsonData["usertypelist"])
                        if jsonData["usertypelist"] == "" or jsonData["usertypelist"] == "0" then
                            -- print("usertypelist   游客")
                            q1sdk.SetVisitorState(true)
                        else
                            -- print("usertypelist  不是 游客")
                            q1sdk.SetVisitorState(false)
                        end

                        net_login_module.ResetLoginSession(token)
                        q1sdk.SetRoleBindType(openInfo)

                        after(userID)
                        if window and window:IsValid() and window.UpdataBaseInfo then
                            window:UpdataBaseInfo()
                        end
                        event.Trigger(event.BIND_ACCOUNT_SUCCESS_NTF)
                    end,
                    loginFnc
            )
        end

        local lastLoginType = PlayerPrefs.GetInt('last_login_type', login_pb.enLoginPartnerID_Visitor)
        if not game_config.Q1SDK_DOMESTIC then
            local version_mgr = require "version_mgr"
            if not version_mgr.CheckSvnTrunkVersion(30871) and not version_mgr.CheckSvnBranchVersion(31261) then
                relogin(lastLoginType)
            else
                --主干30871分支31261版本开始才有双平台绑定，需要从openInfo里面拿到绑定平台列表
                local lastUserID = PlayerPrefs.GetString('last_login_userID', '')
                log.LoginWarning("上次登录用户id:", lastUserID, "上次登录类型：", lastLoginType)
                -- 联运的包不走session缓存，需要登录
                local build_diff_setting = require "build_diff_setting"
                if SessionLoginHelp.CanSessionLogin(lastLoginType) and lastUserID ~= '' and build_diff_setting.NeedSessionLogin() then
                    q1sdk.SessionValid(function(bSuccess, id, msg)
                        log.LoginWarning("SessionValid:", bSuccess, id, msg)
                        if bSuccess then
                            net_login_module.ResetLoginSession(msg)

                            local token = PlayerPrefs.GetString('last_login_token', '')
                            if not string.empty(msg) then
                                token = msg
                            end
                            PlayerPrefs.SetString('last_login_token', token)

                            local openInfo = q1sdk.GetCurrentUser()
                            q1sdk.SetRoleBindType(openInfo)

                            if openInfo then
                                local json = require "dkjson"
                                local jsonData = json.decode(openInfo) or {}
                                bindTypeList = jsonData["usertypelist"]
                                if bindTypeList and bindTypeList ~= "" then
                                    local util = require "util"
                                    local temp = util.SplitString(bindTypeList, ",")
                                    property = { channel_type = table.concat(temp, "#") }
                                    event.Trigger(event.GAME_EVENT_REPORT, "Account_Bound_channel", property)

                                    PlayerPrefs.SetString('last_login_openInfo', openInfo)
                                end
                            end

                            -- 避免与函数作用域的 newUserID 混淆
                            local _newUserID = lastUserID
                            if _newUserID == "" then
                                _newUserID = nil
                            end
                            after(_newUserID)
                            if window and window:IsValid() and window.UpdataBaseInfo then
                                window:UpdataBaseInfo()
                            end
                            event.Trigger(event.BIND_ACCOUNT_SUCCESS_NTF)
                        else
                            relogin(lastLoginType)
                        end
                    end)
                else
                    relogin(lastLoginType)
                end
            end
        else
            after(nil)
        end
    else
        -- https://q1doc.yuque.com/docs/share/53f49b6a-64f6-4075-8916-8b9908da5231?#_Toc20434
        --[[
        code 返回代码说明
        0 参数不正确
        1 绑定成功
        2 签名不正确
        3 您的设备时间不准确，请先校准
        4 获取开放用户信息失败，请重新登录
        5 校验开放用户信息失败，请重新登录
        6 不支持绑定已有账号
        7 移动账号不存在，无法绑定
        8 移动账号已经绑定过
        -1 绑定失败
        ]]
        -- 海外版给个提示
        if not game_config.Q1SDK_DOMESTIC and errorCode == 1--[[这是取消，解释为原账号已绑定]] then
            message_box.Open(lang.Get(4075), message_box.STYLE_YES, function()
            end, 0, lang.KEY_OK)
        end

        if Application.platform == RuntimePlatform.IPhonePlayer and errorCode ~= 1 and msg ~= nil and #msg > 0 then
            --绑定失败提示信息，errCode == 1为正确返回，否则显示回调 response.info信息
            if errorCode == 6 or errorCode == 8 then
                message_box.Open(lang.Get(4075), message_box.STYLE_YES, function()
                end, 0, lang.KEY_OK)
            else
                local flow_text = require "flow_text"
                flow_text.Add(msg)
            end
        end
    end

    return success, newUserID
end

-- 判断当前服务器是否处于指定的区间
function CheckMyServerIsInTargetGroup(min, max)
    local server_data = require "setting_server_data"
    local worldid = server_data.GetLoginWorldID()
end

function OnBindSuccess()
    --[[if intervalHandle then
        util.RemoveDelayCall(intervalHandle)
        intervalHandle = nil
    end]]
end

function ShowGuid()
    bShowGuid = true
end

function OnSceneDestroy()
    Close()
    bFirstLogin = true
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)