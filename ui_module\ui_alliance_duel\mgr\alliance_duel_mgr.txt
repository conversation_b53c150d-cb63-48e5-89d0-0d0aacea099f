--- Created by mr
--- DateTime: 2024/12/9
--- Des: 联盟对决管理

local require = require
local ipairs = ipairs
local os = os
local GWG = GWG
local festival_activity_cfg = require "festival_activity_cfg"
local festival_activity_mgr = require "festival_activity_mgr"
local gw_event_activity_define = require "gw_event_activity_define"
local sandbox_pb = require "sandbox_pb"
local event_alliance_define = require "event_alliance_define"
local gw_sand_data = require "gw_sand_data"
local function_open_mgr = require "function_open_mgr"
local net_login_module = require "net_login_module"
local gw_task_const = require "gw_task_const"
local event_task_define = require "event_task_define"
local red_const = require "red_const"
local red_system = require "red_system"
local game_scheme = require "game_scheme"
local net_allianceDuel_module = require "net_allianceDuel_module"
local event = require "event"
local ui_window_mgr = require "ui_window_mgr"
local alliance_duel_data = require "alliance_duel_data"
local event_allianceDuel_define = require "event_allianceDuel_define"
local main_slg_data = require "main_slg_data"
local gw_const = require "gw_const"
local PlayerPrefs = CS.UnityEngine.PlayerPrefs

module("alliance_duel_mgr")
local curJumpTim = nil
---@field 联盟对决开启后要执行的方法
local DuelOpenFun = nil
local openTimeKey = "DuelOpenTime"
---@field 点击说明的时间戳记录
local clickExplainKey = "clickExplainKey"

--今天是否点击过说明
local isClickExplain = false

function Init()
    --用户数据重置
    event.Register(event.USER_DATA_RESET, Dispose)

    event.Register(event.LOGIN_UI_POPUP_END, PopTips)

    event.Register(event_task_define.REFRESH_TASK, TaskPointUpdate)
    --event.Register(event_allianceDuel_define.TMSG_ALLIANCEDUEL_DEFEAT_NTF,ShowDefeatPopup)

    --数据初始化
    --alliance_duel_data.Init()

    --注册红点
    red_system.RegisterRedFunc(red_const.Enum.AllianceDuel, GetAllianceDuelRed)
    red_system.RegisterRedFunc(red_const.Enum.DuelExplain, GetDuelExplainRed)

    event.Register(event.FUNCTION_OPEN_INIT_FINISH, InitFinishFun)
    event.Register(event.SERVER_CROSS_DAY, ResetClickExplain)

    event.Register(event_alliance_define.EXIT_ALLIANCE, DuelDataClear)

    event.Register(event_allianceDuel_define.TMSG_ALLIANCEDUEL_BATTLEINFO_NTF,SetBattleInfo)
    event.Register(event_allianceDuel_define.TMSG_ALLIANCEDUEL_BATTLEINFO_RSP,SetBattleInfo)
    event.Register(event_allianceDuel_define.TMSG_ALLIANCEDUEL_BATTLE_RECORD_RSP,SetBattleRecord)
    event.Register(event_allianceDuel_define.TMSG_ALLIANCEDUEL_BATTLE_RANK_SCORE_RSP,SetMyRankScore)
end


--region 服务器消息

---@public 登录后的推送
function MSG_ALLIANCE_DUEL_DEFEAT_NTF(_, msg)
    alliance_duel_data.UpdateAllianceDuelInfo(msg)
end

---@public 主动请求同盟对决信息回复
function MSG_ALLIANCE_DUEL_INFO_RSP(_, msg)
    alliance_duel_data.UpdateAllianceDuelInfo(msg)
end

---@public 请求每日主题的详情回复
function MSG_ALLIANCE_THEME_DUEL_RSP(_, msg)
    alliance_duel_data.UpdateThemeDuelResult(msg)
end

--endregion

---@see 客户端主动获取对决联赛信息
function GetBattleInfo()
    net_allianceDuel_module.MSG_ALLIANCEDUEL_BATTLEINFO_REQ()
end

function GetBattleUpDown()
    net_allianceDuel_module.MSG_ALLIANCEDUEL_BATTLE_UP_DOWN_REQ()
end

function GetMyRankScore()
    net_allianceDuel_module.MSG_ALLIANCEDUEL_BATTLE_RANK_SCORE_REQ()
end

function SetMyRankScore(_,msg)
    alliance_duel_data.SetMyRankScore(msg)
end

---@see 客户端设置对决联赛信息
function SetBattleInfo(_,msg)
    alliance_duel_data.SetBattleInfo(msg)
end

---@see 客户端主动获取对决联赛记录
function GetBattleRecord()
    net_allianceDuel_module.MSG_ALLIANCEDUEL_BATTLE_RECORD_REQ()
end

---@see 客户端设合资对决联赛记录
function SetBattleRecord(_,msg)
    alliance_duel_data.SetBattleRecord(msg)
end

---@public 获取联盟对决红点
function GetAllianceDuelRed()
    local red = 0
    red = alliance_duel_data.OnGetThemeRedDot()
    return red
end

--region 攻略添加红点逻辑

---@public functionOpen 初始化完成
function InitFinishFun()

    local player_mgr = require "player_mgr"
    openTimeKey = "DuelOpenTime_" .. player_mgr.GetPlayerRoleID()
    clickExplainKey = "clickExplainKey_" .. player_mgr.GetPlayerRoleID()

    DuelOpenFun = function()
        --写入
        PlayerPrefs.SetInt(openTimeKey, os.server_time())
    end
    function_open_mgr.RegisterFunctionOpen("DuelExplain", function_open_mgr.OpenIdEnum.AllianceDuel, DuelOpenFun)
end

---@public 判断是否开启超过14天
---@return boolean,boolean 活动是否开启,开启是否超过14天
function JudgeDuelOpen14Days()

    local openTime = PlayerPrefs.GetInt(openTimeKey, 0)
    if openTime == 0 then
        --本地没有数据
        local isOpen = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.AllianceDuel)
        if isOpen then
            --开启了 --将当前时间写入
            openTime = os.server_time()
            PlayerPrefs.SetInt(openTimeKey, openTime)
            return true, false
        else
            return false, false
        end
    else
        --活动一定是开启的

        --间隔时间
        local intervalTime = os.server_time() - openTime
        local is14Day = intervalTime > 1209600 --间隔大于14天

        return true, is14Day
    end
end

---@public 同盟对决红点
function GetDuelExplainRed()

    local red = 0
    local isOpen, is14Day = JudgeDuelOpen14Days()

    if not isOpen then
        return 0
    end

    if is14Day then
        --超过了14天
        --今天零点时间
        local zeroTime = net_login_module.GetServerNextZeroTime() - 86400
        --上次点击时间
        local clickTime = PlayerPrefs.GetInt(clickExplainKey, 0)

        if clickTime > zeroTime then
            return 0
        else
            return 1
        end
    else
        --未超过14天
        if isClickExplain then
            return 0
        else
            return 1
        end
    end
    return red
end

---@public 点击了说明
function SetClickExplain()
    isClickExplain = true
    PlayerPrefs.SetInt(clickExplainKey, os.server_time())

    red_system.TriggerRed(red_const.Enum.DuelExplain)
end

---@public 重置点击说明变量
function ResetClickExplain()
    isClickExplain = false
    red_system.TriggerRed(red_const.Enum.DuelExplain)
    red_system.TriggerRed(red_const.Enum.AllianceDuel)
end

--endregion

---@public 根据联盟id判断是否是自己联盟
---@param  allianceId number  联盟id
---@return boolean true是自己联盟 false是地方联盟
function JudgeSelfAllianceById(allianceId)
    local selfInfo = alliance_duel_data.GetSelfAllianceInfo()
    if selfInfo then
        return selfInfo.allianceId == allianceId
    end
    return false
end

---@public 跳转到对应科技
---@param scienceId number 科技id
function JumpToScience(scienceId)
    --防止多次点击
    if not curJumpTim then
        curJumpTim = os.server_time()
    elseif os.server_time() - curJumpTim < 1 then
        return
    end
    curJumpTim = os.server_time()

    if main_slg_data.GetCurSceneType() == gw_const.ESceneType.Home then
        local technology_data = require "technology_data"
        technology_data.JumpToTargetTechnology(scienceId)
    elseif main_slg_data.GetCurSceneType() == gw_const.ESceneType.Sand then
        local gw_common_util = require "gw_common_util"
        gw_common_util.SwitchToHome(function()
            local technology_data = require "technology_data"
            technology_data.JumpToTargetTechnology(scienceId)
        end)
    end
    ui_window_mgr:UnloadModule("ui_alliance_duel_main")
end

function SwitchToSandAndMoveCity()
    local jumpEvent = function(_, msg)
        local pos = { x = msg.x, y = msg.y }
        local gw_common_util = require "gw_common_util"
        gw_common_util.SwitchToSand(function()
            -- 返回迁城默认打开迁城界面
            local gw_admin = require "gw_admin"
            gw_admin.SandRelocationEntityUtil.SetRelocationEntitySid(0, { x = pos.x, y = 0, z = pos.y }, function(state, gridPos)
                if state then
                    --local msgData = {
                    --    sandboxSid = msg.sandboxSid,
                    --    x = gridPos.x,
                    --    y = gridPos.y,
                    --}
                    --net_allianceDuel_module.MSG_ALLIANCEDUEL_BATTLE_CITY_MOVE_REQ(msgData)
                else
                    gw_common_util.SwitchToSand()
                end
            end, sandbox_pb.enSandboxCrossMoveType_AllianceDuel_Battle)
        end, pos, msg.sandboxSid,true)
    end
    if main_slg_data.GetCurSceneType() == gw_const.ESceneType.Sand then
        ui_window_mgr:UnloadModule("ui_alliance_duel_main")
    end
    event.RegisterOnce(event_allianceDuel_define.TMSG_ALLIANCEDUEL_MOVE_COORDINATE_RSP, jumpEvent)
end

function BackSandAndMoveCity()
    local jumpEvent = function(_, msg)
        local pos = { x = msg.x, y = msg.y }
        local gw_common_util = require "gw_common_util"
        gw_common_util.SwitchToSand(function()
            -- 返回迁城默认打开迁城界面
            local gw_admin = require "gw_admin"
            gw_admin.SandRelocationEntityUtil.SetRelocationEntitySid(0, { x = pos.x, y = 0, z = pos.y }, function(state, gridPos)
                if state then
                    --local msgData = {
                    --    sandboxSid = msg.sandboxSid,
                    --    x = gridPos.x,
                    --    y = gridPos.y,
                    --}
                    --net_allianceDuel_module.MSG_ALLIANCEDUEL_BATTLE_CITY_BACK_REQ(msgData)
                else
                    gw_common_util.SwitchToSand()
                end
            end, sandbox_pb.enSandboxCrossMoveType_AllianceDuel_Battle)
        end, pos, msg.sandboxSid, true)
    end
    if main_slg_data.GetCurSceneType() == gw_const.ESceneType.Sand then
        ui_window_mgr:UnloadModule("ui_alliance_duel_main")
    end
    event.RegisterOnce(event_allianceDuel_define.TMSG_ALLIANCEDUEL_BACK_COORDINATE_RSP, jumpEvent)
end

---@public 获取对决比分
---@return number selfScore 自己的比分
---@return number enemyScore 对方的比分
function GetSelfAllianceDuelInfo()
    local selfScore = 0
    local enemyScore = 0
    local resultArr = alliance_duel_data.GetThemeDuelResult()
    local enemyData = alliance_duel_data.GetEnemyAllianceInfo()
    for _, v in ipairs(resultArr) do
        --v.themeId 找到比分(读表)
        if v.isCompete then
            local tempCfg = game_scheme:AllianceDuelTheme_0(v.themeId)
            if winAllianceId ~= 0 then
                local isSelfWin = JudgeSelfAllianceById(v.winAllianceId)
                if isSelfWin then
                    selfScore = selfScore + tempCfg.PointValue
                elseif enemyData and (not enemyData.dismiss or enemyData.dismiss ~= 1) then
                    enemyScore = enemyScore + tempCfg.PointValue
                end
            end
        end
    end

    return selfScore, enemyScore
end

---@public 是否显示对决登陆弹窗
function IsShowDuelLoginPopup()
    --当玩家未加入联盟 或者 本联盟未开启联盟对决时，返回false
    local alliance_mgr = require "alliance_mgr"
    local duelInfo = alliance_duel_data.GetAllianceDuelInfo()
    local themeId = alliance_duel_data.GetThemeDuelID()
    if themeId == 0 then --没有主题
        return false
    end
    
    if duelInfo then
        if alliance_mgr.GetUserAllianceId() ~= 0 and duelInfo.attend then
            return true
        end
    end

    return false
end

---@public 弹出对决登陆弹窗
function PopTips()

    local activity_preview_mgr = require "activity_preview_mgr"
    local activity_preview_cfg = require "activity_preview_cfg"
    local isPreview = activity_preview_mgr.IsJudgePreview(activity_preview_cfg.ActivityPreviewID.AllianceDuel)
    if not isPreview then
        local main_slg_tips_mgr = require "main_slg_tips_mgr"
        main_slg_tips_mgr.ShowTip(main_slg_tips_mgr.EnumTipType.DuelTip)
    end
end

function TaskPointUpdate(eventName, taskData,moduleId, moduleList)
    if moduleList[gw_task_const.TaskModuleType.AllianceDuel] then
        red_system.TriggerRed(red_const.Enum.AllianceDuel)
        local activityID = festival_activity_mgr.GetActivityIdByCodeType(festival_activity_cfg.ActivityCodeType.AllianceDuel)
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,activityID)
    end
end

---@public 对决数据清除
function DuelDataClear()
    alliance_duel_data.ExitAllianceClearData()
end

function Dispose()
    function_open_mgr.UnregisterFunctionOpen("DuelExplain", function_open_mgr.OpenIdEnum.AllianceDuel, DuelOpenFun)
    allianceOpenFun = nil
    isClickExplain = false

    alliance_duel_data.Dispose()

end
