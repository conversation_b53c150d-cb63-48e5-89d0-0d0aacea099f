-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
module('moduleOpenPro_pb')


V1M=V(4,"emModuleID_Recast",0,1)
V2M=V(4,"emModuleID_EquipAdvance",1,2)
V3M=V(4,"emModuleID_Technology2",2,3)
V4M=V(4,"emModuleID_TechnologyProp2",3,4)
V5M=V(4,"emModuleID_MazeHell",4,5)
V6M=V(4,"emModuleID_PuzzleGame",5,6)
V7M=V(4,"emModuleID_IdleHard",6,7)
V8M=V(4,"emModuleID_BattleshipTech",7,8)
V9M=V(4,"emModuleID_SpaceExplore",8,9)
V10M=V(4,"emModuleID_FuWen",9,10)
V11M=V(4,"emModuleID_FuWenModule",10,11)
V12M=V(4,"emModuleID_ZhuanShuModule",11,12)
V13M=V(4,"emModuleID_CampSecret",12,13)
V14M=V(4,"emModuleID_ChinaRed",13,14)
V15M=V(4,"emModuleID_Token",14,15)
V16M=V(4,"emModuleID_Decorate",15,16)
V17M=V(4,"emModuleID_BuiltInCommunities",16,17)
V18M=V(4,"emModuleID_DecorPassport",17,18)
V19M=V(4,"emModuleID_TreasureRare",18,19)
V20M=V(4,"emModuleID_ArtifactArousal",19,21)
V21M=V(4,"emModuleID_StarWeaponDiamond",20,22)
V22M=V(4,"emModuleID_GalaxyTemple",21,24)
V23M=V(4,"emModuleID_GemBatchComposit",22,29)
V24M=V(4,"emModuleID_NaltionalFlag",23,101)
V25M=V(4,"emModuleID_Slave",24,102)
V26M=V(4,"emModuleID_SlaveRecommendation",25,103)
V27M=V(4,"emModuleID_XManAllianceTrain",26,2001)
V28M=V(4,"emModuleID_ZoneBattleDuel",27,2002)
V29M=V(4,"emModuleID_XYX_Soldiers",28,2003)
V30M=V(4,"emModuleID_XYX_HelpDog",29,2004)
V31M=V(4,"emModuleID_XYX_Bomberman",30,2005)
V32M=V(4,"emModuleID_AllianceHelper",31,2006)
V33M=V(4,"emModuleID_DesertStorm",32,2007)
V34M=V(4,"emModuleID_MonsterComing",33,2008)
V35M=V(4,"emModuleID_MonsterComingNew",34,2009)
V36M=V(4,"emModuleID_OfflineCache",35,2010)
V37M=V(4,"emModuleID_ZombieApocalypse",36,2011)
V38M=V(4,"emModuleID_MiniGameWeekendActivity",37,2012)
V39M=V(4,"emModuleID_MiniGameFirstRechargeHeroActivity",38,2013)
V40M=V(4,"emModuleID_MonthCardSchloss",39,2014)
V41M=V(4,"emModuleID_DroneAdvance",40,2015)
V42M=V(4,"emModuleID_AllianceR4R5Todo",41,2016)
V43M=V(4,"emModuleID_SchlossEffect",42,2017)
V44M=V(4,"emModuleID_LeagueBattleDuel",43,2018)
V45M=V(4,"emModuleID_ForceRemoveRoleCity",44,2019)
V46M=V(4,"emModuleID_ForceMoveRoleCity",45,2020)
V47M=V(4,"emModuleID_StrayDog",46,2021)
V48M=V(4,"emModuleID_LordPatrol",47,2022)
V49M=V(4,"emModuleID_NationalFlag",48,2023)
E1M=E(3,"EModuleOpenID",".CSMsg.EModuleOpenID")
F1D=F(2,"id",".CSMsg.TModuleOpenInfo.id",1,0,2,false,0,5,1)
F2D=F(2,"isOpen",".CSMsg.TModuleOpenInfo.isOpen",2,1,2,false,false,8,7)
F3D=F(2,"beginTime",".CSMsg.TModuleOpenInfo.beginTime",3,2,1,false,0,13,3)
F4D=F(2,"endTime",".CSMsg.TModuleOpenInfo.endTime",4,3,1,false,0,13,3)
M1G=D(1,"TModuleOpenInfo",".CSMsg.TModuleOpenInfo",false,{},{},nil,{})
F5D=F(2,"arrInfo",".CSMsg.TMSG_MODULEOPEN_UPDATE_NTF.arrInfo",1,0,3,false,{},11,10)
M2G=D(1,"TMSG_MODULEOPEN_UPDATE_NTF",".CSMsg.TMSG_MODULEOPEN_UPDATE_NTF",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M,V24M,V25M,V26M,V27M,V28M,V29M,V30M,V31M,V32M,V33M,V34M,V35M,V36M,V37M,V38M,V39M,V40M,V41M,V42M,V43M,V44M,V45M,V46M,V47M,V48M,V49M}
M1G.fields={F1D, F2D, F3D, F4D}
F5D.message_type=M1G
M2G.fields={F5D}

TMSG_MODULEOPEN_UPDATE_NTF =M(M2G)
TModuleOpenInfo =M(M1G)
emModuleID_AllianceHelper = 2006
emModuleID_AllianceR4R5Todo = 2016
emModuleID_ArtifactArousal = 21
emModuleID_BattleshipTech = 8
emModuleID_BuiltInCommunities = 17
emModuleID_CampSecret = 13
emModuleID_ChinaRed = 14
emModuleID_DecorPassport = 18
emModuleID_Decorate = 16
emModuleID_DesertStorm = 2007
emModuleID_DroneAdvance = 2015
emModuleID_EquipAdvance = 2
emModuleID_ForceMoveRoleCity = 2020
emModuleID_ForceRemoveRoleCity = 2019
emModuleID_FuWen = 10
emModuleID_FuWenModule = 11
emModuleID_GalaxyTemple = 24
emModuleID_GemBatchComposit = 29
emModuleID_IdleHard = 7
emModuleID_LeagueBattleDuel = 2018
emModuleID_LordPatrol = 2022
emModuleID_MazeHell = 5
emModuleID_MiniGameFirstRechargeHeroActivity = 2013
emModuleID_MiniGameWeekendActivity = 2012
emModuleID_MonsterComing = 2008
emModuleID_MonsterComingNew = 2009
emModuleID_MonthCardSchloss = 2014
emModuleID_NaltionalFlag = 101
emModuleID_NationalFlag = 2023
emModuleID_OfflineCache = 2010
emModuleID_PuzzleGame = 6
emModuleID_Recast = 1
emModuleID_SchlossEffect = 2017
emModuleID_Slave = 102
emModuleID_SlaveRecommendation = 103
emModuleID_SpaceExplore = 9
emModuleID_StarWeaponDiamond = 22
emModuleID_StrayDog = 2021
emModuleID_Technology2 = 3
emModuleID_TechnologyProp2 = 4
emModuleID_Token = 15
emModuleID_TreasureRare = 19
emModuleID_XManAllianceTrain = 2001
emModuleID_XYX_Bomberman = 2005
emModuleID_XYX_HelpDog = 2004
emModuleID_XYX_Soldiers = 2003
emModuleID_ZhuanShuModule = 12
emModuleID_ZombieApocalypse = 2011
emModuleID_ZoneBattleDuel = 2002

