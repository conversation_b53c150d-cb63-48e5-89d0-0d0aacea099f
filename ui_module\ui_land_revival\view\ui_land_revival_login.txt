local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local gw_ui_effect_utility = require "gw_ui_effect_utility"
local goods_item_new = require "goods_item_new"
local item_data = require "item_data"
local iui_item_detail = require "iui_item_detail"
local time_util = require "time_util"
local os = require "os"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_land_revival_login_binding"
local Vector3 = CS.UnityEngine.Vector3

--region View Life
module("ui_land_revival_login")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)
    self.VData = {}
    self:InitLoginEffect()
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic
---@public function 设置活动结束时间
function UIView:SetActivityTimer(endTime)
    if endTime then
        self.endTimer = self.endTimer or util.IntervalCall(1, function()
            local tempTime = endTime - os.server_time()
            if not util.IsObjNull(self.txt_time) then
                self.txt_time.text = string.format(lang.Get(1008005), time_util.FormatTime5(tempTime))
            end
            if tempTime <= 0 then
                if not util.IsObjNull(self.txt_time) then
                    self.txt_time.text = lang.Get(102401) --"活动已结束"
                end
                self.endTimer = nil
                return true
            end
        end)
    end
end

---@public function 显示奖励
function UIView:InitBoxRewardShow(data)
    if not data then
        return
    end
    self.goodsItemArr = self.goodsItemArr or {}
    for i, v in ipairs(data) do
        if i > 2 then
            return
        end
        local parent = i == 1 and self.rtf_smallReward or self.rtf_bigReward
        self.goodsItemArr[i] = self.goodsItemArr[i] or goods_item_new.CGoodsItem():Init(parent.transform, nil,i == 1 and  0.63 or 0.91)
        self.goodsItemArr[i]:SetGoods(nil, v.id, v.num, function()
            iui_item_detail.Show(v.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, v.num, nil, nil)
        end)
        self.goodsItemArr[i]:SetCountEnable(true)
    end
end

function UIView:SetActivityCommonUIData(data)
    if not data then
        return
    end

    self.txt_tips1.text = lang.Get(tonumber(data.tipLang1))
    self.txt_tips2.text = lang.Get(tonumber(data.tipLang2))
    
    --self.goodsItem1 =  goods_item_new.CGoodsItem():Init(self.rtf_bigReward.transform, nil,0.91)
    --self.goodsItem1:SetGoods(nil, data.itemID1, nil, function()
    --    iui_item_detail.Show(data.itemID1, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil)
    --end)
    --
    --self.goodsItem2 =  goods_item_new.CGoodsItem():Init(self.rtf_smallReward.transform, nil,0.63)
    --self.goodsItem1:SetGoods(nil, data.itemID2, nil, function()
    --    iui_item_detail.Show(data.itemID2, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil)
    --end)
end

function UIView:InitLoginEffect()
    self.purpleLightPath = "art/effects/effects/ui_territoryrevival_poppup/prefabs/ui_territoryrevival_poppup.prefab"
    self.purpleLight = self.purpleLight or gw_ui_effect_utility.GetEffectRendererTexture(self.purpleLightPath, function(item, renderTexture)
        self.rImg_bigBubble.texture = renderTexture
        self.rImg_bigBubble.transform.localScale = Vector3(1, 1, 1)

    end,600,600)
    self.purpleLight:SetEffectPosition(Vector3(0, 0, 0))
    self.purpleLight:SetEffectScale(1)
    table.insert(self.VData,self.purpleLight)
    
    self.yellowLightPath = "art/effects/effects/ui_territoryrevival_poppup_1/prefabs/ui_territoryrevival_poppup_1.prefab"
    self.yellowLight = self.yellowLight or gw_ui_effect_utility.GetEffectRendererTexture(self.yellowLightPath, function(item, renderTexture)
        self.rImg_smallBubble.texture = renderTexture
        self.rImg_smallBubble.texture = renderTexture
    end,600,600)
    self.yellowLight:SetEffectPosition(Vector3(0, 0, 0))
    self.yellowLight:SetEffectScale(5)
    table.insert(self.VData,self.yellowLight)
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
		window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, true, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil, true, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
