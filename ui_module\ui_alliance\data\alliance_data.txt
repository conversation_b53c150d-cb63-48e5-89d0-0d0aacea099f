--- alliance_data.txt  --------------------------------------
--- author: 韩淑俊
--- Date:   2024/6/20 14:43
--- ver:    1.0
--- desc:   联盟数据管理
-------------------------------------------------------------
local require = require
local ipairs = ipairs
local string = string
local pairs = pairs
local table = table
local os = os

local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local time_util = require "time_util"
local util = require "util"
local sand_ui_event_define = require "sand_ui_event_define"
local alliance_gift_data = require "alliance_gift_data"
local event = require "event"
local event_alliance_define = require "event_alliance_define"
local game_scheme = require "game_scheme"
local alliance_pb = require "alliance_pb"
local player_mgr = require "player_mgr"
local alliance_user_data = require "alliance_user_data"
local net_login_module = require "net_login_module"

local log = require "log"
local flow_text = require "flow_text"
local lang = require "lang"

module("alliance_data")

--TODO 联盟数据
local leaveAllianceTime = nil-- 上次离开联盟的时间
local allianceBaseData = {} -- 推荐联盟列表数据
local allianceSearchBaseData = {} -- 推荐搜索联盟列表数据
local recommendedAllianceId = 0 -- 快速加入联盟id -- 没有就写0 有就填写 推荐联盟排序好的数据
local userAllianceData = {} --玩家联盟数据
local userAllianceId = 0;
-- TODO排行榜
local allianceRank = {}
local allianceUserRank = {}
-- TODO系统配置
local peopleMaxLimit = 0-- 联盟最大人数限制
-- TODO联盟详情缓存信息
local allianceLookBaseInfoData = {}

-- 联盟城池的数据
local allianceDeclareCount = 0 -- 已宣战城池次数
local allianceDeclareTime = 0 -- 上一次宣战的时间
local allianceDeclareNCRegionList = {} -- 已宣战城池,{ sid , regionID}
local allianceOccupyNCRegionList = {} -- 已占领城池,{ sid , regionID}

local allianceOccupyNCList = {} -- 已占领城池详细数据
local allianceCanOccupyNCList = {} -- 可占领城池详细数据

local allianceFirstJoinTime = 0     -- 玩家第一次加入联盟时间

local allianceInvitedArr = {}

local alliance_Init = false
--联盟邀请相关数据
local allianceInvitationInfo = {}
local allianceShareDelayCall -- 联盟分享CD刷新函数
--module Init方法 指定调用不能修改名称
function Init()
    if alliance_Init then
        return
    end
    InitConfig()
    alliance_Init = true
end

---官职多语言定义
AllianceOfficeDataMap = {
    [alliance_pb.emAlliancePosition_zhanshen] = {
        langID = 600209,
    },
    [alliance_pb.emAlliancePosition_zhaomuguan] = {
        langID = 600210,
    },
    [alliance_pb.emAlliancePosition_nvshen] = {
        langID = 600211,
    },
    [alliance_pb.emAlliancePosition_waijiaoguan] = {
        langID = 600212,
    },
}

---赋值联盟基础数据
local function UpdateAllianceBase(info, curInfo)
    local data = { }
    if curInfo then
        data = curInfo
    end
    if info:HasField("allianceId") then
        data.allianceId = info.allianceId
    end
    if info:HasField("allianceName") then
        data.allianceName = info.allianceName
    end
    if info:HasField("announcement") then
        data.announcement = info.announcement
    end
    if info:HasField("flag") then
        data.flag = info.flag
    end
    if info:HasField("count") then
        data.count = info.count
    end
    if info:HasField("apply") then
        data.apply = info.apply
    end
    if info:HasField("shortName") then
        data.shortName = info.shortName
    end
    if info:HasField("language") then
        data.language = info.language
    end
    if info:HasField("applySet") then
        data.applySet = info.applySet
    end
    if info:HasField("lvLimit") then
        data.lvLimit = info.lvLimit
    end
    if info:HasField("ceLimit") then
        data.ceLimit = info.ceLimit
    end
    if info:HasField("clearSet") then
        data.clearSet = info.clearSet
    end
    if info:HasField("newCreate") then
        data.newCreate = info.newCreate
    end
    if info:HasField("impeachCount") then
        data.impeachCount = info.impeachCount
    end
    if info:HasField("power") then
        data.power = info.power
        data.oldPower = data.power
    end
    if info:HasField("iAnnNextSetTime") then
        data.iAnnNextSetTime = info.iAnnNextSetTime
    end
    if info.arrFlag then
        data.arrFlag = info.arrFlag
    end
    if info:HasField("r5Info") then
        data.r5Info = info.r5Info
    end
    if info:HasField("giftLv") then
        data.giftLv = info.giftLv
    end
    if info:HasField("giftExp") then
        data.giftExp = info.giftExp
    end
    if info:HasField("emailCount") then
        data.emailCount = info.emailCount
    end
    if info:HasField("emailLastTime") then
        data.emailLastTime = info.emailLastTime
    end
    if info:HasField("autoResearch") then
        data.autoResearch = info.autoResearch
    end
    if info:HasField("achievementCloseTime") then
        data.achievementCloseTime = info.achievementCloseTime
    end
    if info:HasField("createTime") then
        data.createTime = info.createTime
    end
    if info:HasField("lastMassTime") then
        data.lastMassTime = info.lastMassTime
    end
    if info:HasField("newPower") then
        data.power = info.newPower
    end
    if info:HasField("bRobotFlag") then
        data.bRobotFlag = info.bRobotFlag >= 1
    end
    data.peopleMaxNum = peopleMaxLimit
    return data
end

--初始化数据
function InitConfig()
    local maxConfig = game_scheme:InitBattleProp_0(8022)
    if maxConfig and maxConfig.szParam.data[0] then
        peopleMaxLimit = maxConfig.szParam.data[0]
    end
end

--获取联盟人数上限
function GetPeopleMaxLimit()
    return peopleMaxLimit
end

--是否解锁了 联盟系统 --true 解锁 false 未解锁
function GetAllianceUnlock()
    --读取配置表数据
    return true
end

-- 联盟列表排序
local function SortAllianceData(a, b)
    -- 满足条件的玩家排在前面
    local aCheck = CheckAlliancePlayerSort(a)
    local bCheck = CheckAlliancePlayerSort(b)
    if aCheck and not bCheck then
        return true
    end
    if not aCheck and bCheck then
        return false
    end
    -- 如果两者满足条件或都不满足条件，按 applySet 排序
    return a.applySet < b.applySet
end

--刷新联盟数据
function UpdateAllianceData(msg)
    if msg.st1 or msg.st2 or msg.st5 or msg.st6 then
        --清空推荐列表
        allianceBaseData = {}
    end
    if msg.arrAlliances and #msg.arrAlliances > 0 then
        --清空推荐列表
        allianceBaseData = {}
        for i, alliBase in ipairs(msg.arrAlliances) do
            --赋值数据
            local data = UpdateAllianceBase(alliBase)
            table.insert(allianceBaseData, data)
        end
    end
    -- 排序函数
    --table.sort(allianceBaseData, SortAllianceData)

    --region 插入第1,2,5,6位联盟

    local length = util.get_len(allianceBaseData)
    if msg.st1 then
        local st1 = UpdateAllianceBase(msg.st1)
        if st1.allianceId ~= nil then
            table.insert(allianceBaseData, 1, st1)
            length = length + 1
        end
    end
    if msg.st2 and length >= 1 then
        local st2 = UpdateAllianceBase(msg.st2)
        if st2.allianceId ~= nil then
            table.insert(allianceBaseData, 2, st2)
            length = length + 1
        end
    end
    if msg.st5 and length >= 4 then
        local st5 = UpdateAllianceBase(msg.st5)
        if st5.allianceId ~= nil then
            table.insert(allianceBaseData, 5, st5)
            length = length + 1
        end
    end
    if msg.st6 and length >= 5 then
        local st6 = UpdateAllianceBase(msg.st6)
        if st6.allianceId ~= nil then
            table.insert(allianceBaseData, 6, st6)
            length = length + 1
        end
    end

    --endregion


    --存储快速加入 联盟id
    if (#allianceBaseData >= 1) then
        recommendedAllianceId = allianceBaseData[1].roleId
    end
    leaveAllianceTime = msg.leaveAllianceTime
    --刷新 推荐联盟数据
    event.Trigger(event_alliance_define.UPDATE_ALLIANCE_RECOMMENDED, allianceBaseData)
end

--获取联盟退出CD
function GetLeaveTime()
    return leaveAllianceTime
end

--搜索联盟 数据刷新
function SelectUpdateAllianceData(msg)
    --搜索列表清空
    if msg.stBase and #msg.stBase > 0 then
        allianceSearchBaseData = { }
        for i, alliBase in ipairs(msg.stBase) do
            --赋值数据
            local data = UpdateAllianceBase(alliBase)
            table.insert(allianceSearchBaseData, data)
        end
        --刷新 搜索推荐联盟数据 --有可能是空
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_SELECT_RECOMMENDED, allianceSearchBaseData)
    else
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_SELECT_RECOMMENDED, nil)
    end
end

--联盟查看基本信息响应
function UpdateLookAllianceBaseInfo(msg)
    if msg.stBase then
        allianceLookBaseInfoData = UpdateAllianceBase(msg.stBase)
        --刷新面板数据
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_BASE_INFO, allianceLookBaseInfoData)
    end
end

function GetAllianceRecommendList()
    return allianceBaseData
end

--获取查看联盟信息
function GetAllianceBaseInfoData()
    return allianceLookBaseInfoData
end


--清楚查看联盟信息
function ClearAllianceBaseInfoData()
    allianceLookBaseInfoData = {}
end

--刷新申请联盟响应
function RefreshApplyRsp(msg)
    if msg.allianceId then
        local curdata = nil
        if allianceLookBaseInfoData and msg.allianceId == allianceLookBaseInfoData.allianceId then
            --对比查看联盟数据 是否是当前查看的联盟
            allianceLookBaseInfoData.apply = true
            curdata = allianceLookBaseInfoData
        end
        if not curdata then
            --查询推荐列表
            for i, v in pairs(allianceBaseData) do
                if v.allianceId == msg.allianceId then
                    v.apply = true
                    curdata = v
                    break
                end
            end
        end
        if not curdata then
            --查询排行列表
            for i, v in pairs(allianceRank) do
                if v.allianceId == msg.allianceId then
                    v.apply = true
                    curdata = v
                    break
                end
            end
        end
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_APPLY_RSP, allianceBaseData, curdata)
    end
    if msg.leaveAllianceTime then
        RefreshLeaveAllianceTime(msg.leaveAllianceTime)
    end
end

function RefreshLeaveAllianceTime(leaveTime)
    leaveAllianceTime = leaveTime
    event.Trigger(event_alliance_define.UPDATE_ALLIANCE_EXIT_TIME)
end
--创建联盟响应
function CreateAllianceRsp(msg)
    if msg.leaveAllianceTime then
        RefreshLeaveAllianceTime(msg.leaveAllianceTime)
    end
    if not msg.errorCode then
        return
    end
    if msg.errorCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorCode))
    else
        --创建联盟成功
        flow_text.Add(lang.Get(600389))
        local data_personalInfo = require "data_personalInfo"
        local power = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RolePower)

        local UTCOffset = time_util.GetTimeArea()
        local account_pb = require "account_pb"
        local timeZoneId = account_pb.enTimeZone_meizhou
        if UTCOffset then
            if UTCOffset >= 7 and UTCOffset <= 12 then
                timeZoneId = account_pb.enTimeZone_yatai
            elseif UTCOffset >= -2 and UTCOffset <= 6 then
                timeZoneId = account_pb.enTimeZone_ouzhou
            elseif UTCOffset >= -11 and UTCOffset <= -3  then
                timeZoneId = account_pb.enTimeZone_meizhou
            end
        end
        
        local reportMsg = {
            LeagueID = userAllianceData and userAllianceData.allianceId or 0, --联盟ID
            Role_totalPower = power and power or 0, --捐献者的总战力
            role_TimeZone = timeZoneId,
            LeagueRobot = 0,--是否是机器人联盟
        }
        event.EventReport("league_creator", reportMsg)
    end
end

--返回联盟快速加入id
function GetQuickAddID()
    return recommendedAllianceId
end

--region ----- 旗帜 -----
--返回当前联盟的旗帜数据
function GetCurAllianceFlagList()
    local length = game_scheme:LeagueIcon_nums()
    local __curIconID --当前选中的
    local __icons = {}
    local __curHeadID = userAllianceData.flag
    local arrFlag = userAllianceData.arrFlag

    for i = 0, length - 1 do
        local cfg = game_scheme:LeagueIcon(i)
        if cfg then
            local data = {}
            data.headID = cfg.headID
            data.iconID = cfg.iconID
            data.limitedLv = cfg.limitedLv
            data.LangID = cfg.LangID
            data.condition = cfg.condition
            if __curHeadID == data.headID then
                __curIconID = data.iconID
                data.isSelect = true
            else
                data.isSelect = false
            end
            if cfg.condition and cfg.condition.count > 0 then
                data.isLock = arrFlag[cfg.headID] == nil
            else
                data.isLock = false
            end
            data.cfg = cfg
            table.insert(__icons, data)
        end
    end
    table.sort(__icons, function(a, b)
        if a.isLock ~= b.isLock then
            if not a.isLock then
                return true
            end
            return false
        end
        return a.cfg.Priority > b.cfg.Priority
    end)
    return __icons
end

--创建联盟时候调用 获取全部旗帜数据
function GetAllFlagList()
    local __icons = {}
    local length = game_scheme:LeagueIcon_nums()
    for i = 0, length - 1 do
        local cfg = game_scheme:LeagueIcon(i)
        if cfg then
            local data = {}
            data.headID = cfg.headID
            data.iconID = cfg.iconID
            data.limitedLv = cfg.limitedLv
            data.LangID = cfg.LangID
            data.condition = cfg.condition
            data.isSelect = false
            data.isLock = cfg.condition and cfg.condition.count > 0
            data.cfg = cfg
            table.insert(__icons, data)
        end
    end
    return __icons
end
function GetDefaultFlag()
    local cfg = game_scheme:LeagueIcon(0)
    if cfg then
        return cfg
    end
    return nil
end

---@public 根据旗帜id获取旗帜数据
function GetFlagIdData(flagId)
    local cfg = game_scheme:LeagueIcon_0(flagId)
    --添加一个默认值
    if cfg == nil then
        cfg = game_scheme:LeagueIcon_0(413)
    end

    local arrFlag = userAllianceData.arrFlag
    if cfg then
        local data = {}
        data.headID = cfg.headID
        data.iconID = cfg.iconID
        data.limitedLv = cfg.limitedLv
        data.LangID = cfg.LangID
        data.condition = cfg.condition
        if cfg.condition and cfg.condition.count > 0 then
            data.isLock = arrFlag[cfg.headID] == nil
        else
            data.isLock = false
        end
        return data
    end
end
--endregion

--region 联盟基础数据 名称检测
--刷新随机联盟名称
function UpdateRandomNameData(msg)
    if msg:HasField("randomName") then
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_RANDOM_NAME, msg.randomName)
    end
end

--检测网络层名称
function RefreshNetAllianceName(msg)
    if msg.checkType and msg.errorCode then
        if (msg.checkType == 1) then
            local code = msg.errorCode
            event.Trigger(event_alliance_define.CHECK_ALLIANCE_NAME, code)
        else
            if msg.checkType == 2 then
                local code = msg.errorCode
                event.Trigger(event_alliance_define.CHECK_ALLIANCE_SHORT_NAME, code)
            end
        end
    end
end

--返回联盟名称限制
function GetAllianceNameLimit()
    return 4, 16;
end

--返回联盟简称限制
function GetAllianceShortNameLimit()
    return 3;
end

--获取联盟创建消耗id 和 数量
function GetAllianceCreateData()
    -- 联盟创建消耗 id num
    local createConsume = {}
    local createCfg = game_scheme:InitBattleProp_0(8030)
    if createCfg and createCfg.szParam.data then
        local create = createCfg.szParam.data
        createConsume.id = create[0]
        createConsume.num = create[1]
    end
    return createConsume.id, createConsume.num
end

--获取修改联盟名称 id 和数量
function GetModifyAllianceName()
    local modifyAllianceName = {} -- 修改联盟名称消耗 id num
    local modifyNameCfg = game_scheme:InitBattleProp_0(8032)
    if modifyNameCfg and modifyNameCfg.szParam.data then
        local modifyName = modifyNameCfg.szParam.data
        modifyAllianceName.id = modifyName[0]
        modifyAllianceName.num = modifyName[1]
    end
    return modifyAllianceName.id, modifyAllianceName.num
end

--获取修改联盟简称 id 和数量
function GetModifyAllianceShortName()
    local modifyAllianceShortName = {} -- 修改联盟简称消耗 id num
    local modifyShortNameCfg = game_scheme:InitBattleProp_0(8033)
    if modifyShortNameCfg and modifyShortNameCfg.szParam.data then
        local modifyShortName = modifyShortNameCfg.szParam.data
        modifyAllianceShortName.id = modifyShortName[0]
        modifyAllianceShortName.num = modifyShortName[1]
    end
    return modifyAllianceShortName.id, modifyAllianceShortName.num
end

--获取公告最大多少字符
function GetNotifyMax()
    local notifyMax = 0     -- 公告最大字数限制
    local notifyMaxCfg = game_scheme:InitBattleProp_0(8024)
    if notifyMaxCfg and notifyMaxCfg.szParam.data[0] then
        notifyMax = notifyMaxCfg.szParam.data[0]
    end
    return notifyMax
end

--获取标记最大多少字符
function GetMarkMax()
    local markMax = 0
    local markMaxCfg = game_scheme:InitBattleProp_0(84)
    if markMaxCfg and markMaxCfg.szParam.data[0] then
        markMax = markMaxCfg.szParam.data[0]
    end
    return markMax
end

--返回可选择语言列表
function GetLangList(currentLangId)
    local langTable = {}
    local langCfg = game_scheme:InitBattleProp_0(8025)
    if langCfg and langCfg.szParam.data then
        local len = #langCfg.szParam.data;
        local index = 1;
        for i = 0, len do
            langTable[index] = {}
            local id = langCfg.szParam.data[i];
            langTable[index][1] = id;
            langTable[index][2] = false;
            --是否是当前选中的语言
            if currentLangId and currentLangId == id then
                langTable[index][2] = true;
            end
            index = index + 1
        end
        if not currentLangId then
            langTable[1][2] = true
        end
    end
    return langTable
end

--获取默认语言
function GetDefaultLang()
    local langCfg = game_scheme:InitBattleProp_0(8025)
    if langCfg and langCfg.szParam.data then
        local len = #langCfg.szParam.data;
        if len > 0 then
            return langCfg.szParam.data[0];
        end
    end
end

--获取游戏设置语言
function GetGameSettingLang()
    local alliance_mgr = require "alliance_mgr"
    local langId = alliance_mgr.GetAllianceLangID(lang.USE_LANG)
    return langId
end

--获取联盟退出CD
function GetAllianceCdTime()
    local exitAllianceCDTime = 0 -- 退出联盟Cd时间
    local exitallianceCdCfg = game_scheme:InitBattleProp_0(8031)
    if exitallianceCdCfg and exitallianceCdCfg.szParam.data[0] then
        exitAllianceCDTime = exitallianceCdCfg.szParam.data[0]
    end
    return exitAllianceCDTime
end
--是否可以加入联盟  cd 战力 等级
function GetIsApplyAlliance()
    if not leaveAllianceTime then
        return true
    end
    --当前时间 减去上次的事件 = 剩余的时间
    local timer = net_login_module.GetServerTime() - leaveAllianceTime
    local tempDis = GetAllianceCdTime() - timer
    if tempDis <= 0 then
        return true
    else
        return false, tempDis
    end
end

--检测 玩家是否符合当前联盟的要求
function CheckAlliancePlayerSort(allianceData)
    if not allianceData then
        return false;
    end
    local playerLevel = player_mgr.GetPlayerLV()
    local playerPower = player_mgr.GetPlayerPower()
    --判断等级
    if playerLevel < allianceData.lvLimit then
        return false
    end
    --判断战力
    if playerPower < allianceData.ceLimit then
        return false
    end
    return true
end
function CheckAlliancePlayer(allianceData)
    if not allianceData then
        return false;
    end
    local newData = {}
    local playerLevel = player_mgr.GetPlayerLV()
    local playerPower = player_mgr.GetPlayerPower()
    --判断等级
    if allianceData.lvLimit then
        if playerLevel < allianceData.lvLimit then
            newData.isLevelLimit = false
        else
            newData.isLevelLimit = true
        end
    end
    if allianceData.ceLimit then
        --判断战力
        if playerPower < allianceData.ceLimit then
            newData.IsPowerLimit = false
        else
            newData.IsPowerLimit = true
        end
    end

    return newData
end

--检测联盟邮件和公告内容
function CheckNotifyOrMailData(msg)
    if msg.checkType then
        if msg.checkType == alliance_pb.EAllianceCheckType_Mail then
            event.Trigger(event_alliance_define.CHECK_ALLIANCE_MAIL_DATA, msg.checkContent)
        elseif msg.checkType == alliance_pb.EAllianceCheckType_Announcement then
            event.Trigger(event_alliance_define.CHECK_ALLIANCE_NOTICE_DATA, msg.checkContent)
        elseif msg.checkType == alliance_pb.EAllianceCheckType_MailTitle then
            event.Trigger(event_alliance_define.CHECK_ALLIANCE_MAIL_TITLE_DATA, msg.checkContent)
        end
    end
end

--返回用户联盟数据
function GetUserAllianceData()
    return userAllianceData
end

--设置联盟科技自动研究数据
function SetAllianceBaseInfoAutoStudy(opt)
    if userAllianceData then
        userAllianceData.autoResearch = opt
    end
end

--返回用户联盟id
function GetUserAllianceCreateTime()
    if userAllianceData and userAllianceData.createTime then
        return userAllianceData.createTime
    end
    return 0
end

--返回用户联盟id
function GetUserAllianceId()
    if userAllianceData and userAllianceData.allianceId then
        return userAllianceData.allianceId
    end
    return 0
end

--返回用户联盟名
function GetUserAllianceName()
    if userAllianceData and userAllianceData.allianceName then
        return userAllianceData.allianceName
    end
    return ""
end

--返回联盟简称
function GetUserAllianceShortName()
    if userAllianceData and userAllianceData.shortName then
        return userAllianceData.shortName
    end
    return ""
end

--返回联盟语言
function GetUserAllianceLanguage()
    if userAllianceData and userAllianceData.language then
        return userAllianceData.language
    end
    return 0
end

--返回联盟旗帜
function GetUserAllianceFlag()
    if userAllianceData and userAllianceData.flag then
        return userAllianceData.flag
    end
    return 0
end


--获取联盟邮件是否解锁
function GetUserAllianceMailLock()
    local cfg = game_scheme:InitBattleProp_0(8001)
    if cfg and userAllianceData and userAllianceData.giftLv then
        if userAllianceData.giftLv >= cfg.szParam.data[0] then
            return true
        end
    end
    return false
end

---@public 设置上次盟主邀请集结时间
function SetAllianceLastMassTime(lastMassTime)
    if userAllianceData then
        userAllianceData.lastMassTime = lastMassTime
    end
end

--endregion

--刷新联盟通知
function RefreshAllianceNtf(msg)
    --刷新联盟基础信息
    RefreshUserAllianceData(msg);
end
--刷新用户联盟基本信息
function RefreshUserAllianceData(msg)
    --刷新联盟基本信息
    if msg.stBase then
        --变动联盟城池数据
        ModifyAllianceNCData(msg)
        
        -- 判断 加入类型--打开联盟主界面--刷新面板数据
        if msg.optType then
            if msg.optType == alliance_pb.emllianceOptType_Leave then
                -- 离开联盟
                alliance_user_data.AllianceKickedOut()

            elseif msg.optType == alliance_pb.emllianceOptType_Create then
                --flow_text.Add(#"配置多语言 创建联盟成功")
                CreateAlliance(msg)
                flow_text.Add(lang.Get(600389))
                event.Trigger(event_alliance_define.UPDATE_JOIN_ALLIANCE_BUBBLE)
            elseif msg.optType == alliance_pb.emllianceOptType_Join then
                --flow_text.Add(#"配置多语言 加入联盟成功")
                JoinAlliance(msg)
                flow_text.Add(lang.Get(600390))
                event.Trigger(event_alliance_define.UPDATE_JOIN_ALLIANCE_BUBBLE)


            elseif msg.optType == alliance_pb.emllianceOptType_Login then
                -- 登录
                alliance_user_data.RoleListDataNtf(msg)
                alliance_user_data.UpdateApplyDataNTF(msg)
                userAllianceData = UpdateAllianceBase(msg.stBase)
                RefreshGiftLevelData(userAllianceData.giftLv, userAllianceData.giftExp)
                --赋值玩家id
                userAllianceId = userAllianceData.allianceId
                event.Trigger(event_alliance_define.ALLIANCE_TO_MODULE, "HelpSelfREQ")

                local alliance_mgr_extend = require "alliance_mgr_extend"
                alliance_mgr_extend.CreateAllianceAchievementEndTimer()
                event.Trigger(event_alliance_define.UPDATE_JOIN_ALLIANCE_BUBBLE)
            elseif msg.optType == alliance_pb.emllianceOptType_Expel then
                alliance_user_data.AllianceKickedOut()
            elseif msg.optType == alliance_pb.emllianceOptType_autoResearch then
                --userAllianceData = UpdateAllianceBase(msg.stBase)
                --发送科技请求
                local net_alliance_module = require "net_alliance_module"
                net_alliance_module.MSG_ALLIANCE_TECHNOLOGY_REQ()
            elseif msg.optType == alliance_pb.emllianceOptType_AutoChange then
                --自动切换联盟
                AutoChangeAlliance(msg)
                event.Trigger(event_alliance_define.UPDATE_JOIN_ALLIANCE_BUBBLE)
                event.Trigger(event_alliance_define.ALLIANCE_AUTO_CHANGED)
            elseif msg.optType == alliance_pb.emllianceOptType_Invitation then
                --联盟邀请函
                SetAllianceInvitationInfo(msg)
            elseif msg.optType == alliance_pb.emllianceOptType_Share then
                --联盟分享 
                SetAllianceShareTime(msg)
            else
                log.Error("#### 联盟optType 类型错误 ####")
            end
        else
            --变动联盟基础数据
            ModifyAllianceBaseInfo(msg)

            alliance_user_data.AllianceExpel(msg);
            --可能通过申请列表进入 -- 刷新申请列表
            alliance_user_data.ModifyApplyDataNTF(msg)
            --有人拒绝申请加入联盟 -》从申请列表删除的玩家
            alliance_user_data.RefuseApply(msg)
            alliance_user_data.AllianceModifyArrMember(msg)

            --联盟徽章基本信息
            if msg.stBase.arrMedal then
                local alliance_medal_data = require("alliance_medal_data")
                alliance_medal_data.SetArrMedalList(msg.allianceId, msg.stBase.arrMedal)

            end
            local red_system = require "red_system"
            local red_const = require "red_const"
            red_system.TriggerRed(red_const.Enum.AllianceInviteBtnRed)
        end
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_INFO_CHANGE)

    end
    
    AllianceInvitedRoleArr(msg)
    ReportAllianceIdAndShortName()
  
end


--@public 上报联盟id和简称
function ReportAllianceIdAndShortName()
    local json = require "dkjson"
    local q1sdk = require "q1sdk"
    
    local json_str = json.encode({
        alliance_id = GetUserAllianceId(),
        alliance_shortName = GetUserAllianceShortName()
    })
    q1sdk.UserSet(json_str)
end


function JoinAlliance(msg)
    alliance_user_data.RoleListDataNtf(msg)
    userAllianceData = UpdateAllianceBase(msg.stBase)
    RefreshGiftLevelData(userAllianceData.giftLv, userAllianceData.giftExp)
    --赋值玩家id
    userAllianceId = userAllianceData.allianceId


    local alliance_ui_util = require "alliance_ui_util"
    local data_personalInfo = require "data_personalInfo"
    local power = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RolePower)

    local alliance_mgr = require "alliance_mgr"
    local allianceLang = GetUserAllianceLanguage()
    local isLang = lang.USE_LANG == alliance_mgr.GetAllianceLanguage(allianceLang)

    local UTCOffset = time_util.GetTimeArea()
    local account_pb = require "account_pb"
    local timeZoneId = account_pb.enTimeZone_meizhou
    if UTCOffset then
        if UTCOffset >= 7 and UTCOffset <= 12 then
            timeZoneId = account_pb.enTimeZone_yatai
        elseif UTCOffset >= -2 and UTCOffset <= 6 then
            timeZoneId = account_pb.enTimeZone_ouzhou
        elseif UTCOffset >= -11 and UTCOffset <= -3  then
            timeZoneId = account_pb.enTimeZone_meizhou
        end
    end
    local reportMsg = {
        LeagueID = msg.allianceId, --联盟ID
        Join_Type = 2, --加入方式（1申请/ 2快速加入/ 3直接加入
        Role_totalPower = power and power or 0, --捐献者的总战力
        LanguageConsistency = isLang, --联盟语言是否和玩家语言一致
        Role_totalLanguage = lang.USE_LANG, --玩家语言
        LeagueLanguage = alliance_mgr.GetAllianceLanguage(allianceLang) ,--联盟语言
        role_TimeZone = timeZoneId
    }
    alliance_ui_util.EventReport("league_Join", reportMsg, true)
    
    event.Trigger(event_alliance_define.ALLIANCE_TO_MODULE, "JoinAllianceSuccess", msg)
end

function AutoChangeAlliance(msg)
    alliance_user_data.RoleListDataNtf(msg)
    userAllianceData = UpdateAllianceBase(msg.stBase)
    RefreshGiftLevelData(userAllianceData.giftLv, userAllianceData.giftExp)
    --赋值玩家id
    userAllianceId = userAllianceData.allianceId
end

function CreateAlliance(msg)
    alliance_user_data.RoleListDataNtf(msg)
    userAllianceData = UpdateAllianceBase(msg.stBase)
    RefreshGiftLevelData(userAllianceData.giftLv, userAllianceData.giftExp)
    --赋值玩家id
    userAllianceId = userAllianceData.allianceId
    event.Trigger(event_alliance_define.ALLIANCE_TO_MODULE, "CreateAllianceSuccess", msg)
end

--修改联盟基础信息
function ModifyAllianceBaseInfo(msg)
    if msg.stBase then
        userAllianceData = UpdateAllianceBase(msg.stBase, userAllianceData)
        RefreshGiftLevelData()
        --刷新面板数据
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_MODIFY_BASE_INFO, userAllianceData)
    end
end

function RefreshGiftLevelData()
    alliance_gift_data.RefreshLevelData(userAllianceData.giftLv, userAllianceData.giftExp)
end

-- 获取服务器时间
function GetServerTime()
    return net_login_module.GetServerTime()
end
--region 联盟管理
-- 修改联盟名、简称
function SetModifyNameData(msg)
    if msg.modifyType and msg.modifyName then
        local data = {}
        data.modifyType = msg.modifyType
        data.modifyName = msg.modifyName
        if msg.modifyType == alliance_pb.emAllianceNameType_Name then
            userAllianceData.allianceName = msg.modifyName
        elseif msg.modifyType == alliance_pb.emAllianceNameType_ShortName then
            userAllianceData.shortName = msg.modifyName
        end
        event.Trigger(event_alliance_define.SET_ALLIANCE_NAME_SHORT_DATA, data)
    end
end
function SetModifyLanguageData(msg)
    if msg.language then
        userAllianceData.language = msg.language
        event.Trigger(event_alliance_define.SET_ALLIANCE_LANG_DATA, msg.language)
    end
end
function SetModifyFlagData(msg)
    if msg.flag then
        userAllianceData.flag = msg.flag
        event.Trigger(event_alliance_define.SET_ALLIANCE_FLAG_DATA, msg.flag)
    end
end
function SetModifyNotice(content)
    userAllianceData.announcement = content
end
function SetModifyJoinConditions(msg)
    local alliance_ui_util = require "alliance_ui_util"
    if msg.applySet then
        userAllianceData.applySet = msg.applySet
    end
    if msg.power then
        if userAllianceData.ceLimit ~= msg.power then
            local reportMsg = {
                Old_PowerCondition = userAllianceData.ceLimit,
                New_PowerCondition = msg.power,
            }
            alliance_ui_util.EventReport("league_PowerCondition", reportMsg, false)
        end
        userAllianceData.ceLimit = msg.power
    end
    if msg.headOffice then
        if userAllianceData.lvLimit ~= msg.headOffice then
            local reportMsg = {
                Old_BaseCondition = userAllianceData.lvLimit,
                New_BaseCondition = msg.headOffice,
            }
            alliance_ui_util.EventReport("league_ChangeLevelRequirements", reportMsg, false)
        end
        userAllianceData.lvLimit = msg.headOffice
    end
    if msg.clearSet then
        if userAllianceData.clearSet ~= msg.clearSet then
            local reportMsg = {
                New_CleanSetting = msg.clearSet == 1 and 0 or 1, -- 变动后设置（0—关闭  1—打开）
            }
            alliance_ui_util.EventReport("league_ChangeAutomaticCleaningSettings", reportMsg, false)
        end
        userAllianceData.clearSet = msg.clearSet
    end
    flow_text.Add(lang.Get(600394))
end
--退出联盟
function ExitAlliance()
    --先清楚数据
    ClearAllianceData()
    event.Trigger(event_alliance_define.EXIT_ALLIANCE)
    event.Trigger(event_alliance_define.ALLIANCE_STATE_UPDATE)
    ReportAllianceIdAndShortName()
end
--endregion

--region 联盟排行榜数据
local function RefreshAllianceBaseInfo(info)
    local data = { }
    if info:HasField("allianceId") then
        data.allianceId = info.allianceId
    end
    if info:HasField("rankId") then
        data.rankId = info.rankId
    end
    if info:HasField("power") then
        data.power = info.power
    end
    if info:HasField("allianceName") then
        data.allianceName = info.allianceName
    end
    if info:HasField("shortName") then
        data.shortName = info.shortName
    end
    if info:HasField("flag") then
        data.flag = info.flag
    end
    if info:HasField("killNum") then
        data.killNum = info.killNum
    end
    if info:HasField("newPower") then
        data.power = info.newPower
    end
    data.isUser = false
    if userAllianceId ~= 0 then
        data.isUser = data.allianceId == userAllianceId
    end
    return data
end
local function RefreshAllianceRankData(data,worldId)
    local rankData = {}
    for i, v in ipairs(data) do
        local baseData = RefreshAllianceBaseInfo(v)
        baseData.worldId = worldId
        table.insert(rankData, baseData)
    end
    return rankData
end
--刷新联盟战力排行
function UpdateAllianceRankData(msg)
    if msg.arrRoleBase then
        allianceUserRank = {}
        allianceRank = RefreshAllianceRankData(msg.arrRoleBase,msg.worldId)
        -- 排序函数，根据 power 进行降序排序
        local activity_pb = require "activity_pb"
        table.sort(allianceRank, function(a, b)
            return a.rankId < b.rankId
        end)
        for i, v in ipairs(allianceRank) do
            if v.isUser then
                allianceUserRank = allianceRank[i]
            end
        end
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_RANK_DATA, allianceRank)
    end
end
--获取联盟排行榜
function GetAllianceRankData()
    return allianceRank
end
--获取当前用户联盟战力排行榜数据
function GetAllianceUserRankData()
    return allianceUserRank
end

--region 存储这个联盟邀请过哪些玩家

function AllianceInvitedRoleArr(msg)
    allianceInvitedArr = {}
    if msg.roleInfos and #msg.roleInfos > 0 then
        for _, v in ipairs(msg.roleInfos) do
            if v.dbid and v.dbid > 0 then
                allianceInvitedArr[v.dbid] = v
            end
        end
    end
    event.Trigger(event_alliance_define.MSG_ALLIANCE_INVITE_SUCCESS)
end

----@public IsJudgeInvitedRole 判断是否是邀请过的玩家
function IsJudgeInvitedRole(roleId)
    if allianceInvitedArr[roleId] then
        return true, allianceInvitedArr[roleId]
    else
        return false
    end
end

--endregion


------------------------------------------ 中立城池 ------------------------------------------
--设置中立城池数据
function ModifyAllianceNCData(msg)
    if not string.IsNullOrEmpty(msg.NCInfo) then
        local ncInfo = alliance_pb.TSandboxNCInfo()
        ncInfo:ParseFromString(msg.NCInfo)

        local dump_msg = require "dump_msg"
        dump_msg.DumpMsg(200069, ncInfo, "<color=green>NCInfo</color>", msg)

        if ncInfo:HasField("declarewarNum") then
            allianceDeclareCount = ncInfo.declarewarNum
        end
        if ncInfo:HasField("declarewarTime") then
            allianceDeclareTime = ncInfo.declarewarTime
        end

        allianceDeclareNCRegionList = {}
        allianceOccupyNCRegionList = {}
        if ncInfo.infoList and #ncInfo.infoList > 0 then
            for k, v in ipairs(ncInfo.infoList) do
                if v.nType == 1 then
                    table.insert(allianceDeclareNCRegionList, v)
                else
                    table.insert(allianceOccupyNCRegionList, v)
                end
            end
        end
        --刷新面板数据
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_MODIFY_CITY_INFO)
    end
end

--获得今日已宣战次数
function GetAllianceDeclareCount()
    local time_util = require "time_util"
    if time_util.GetIsCurDayTime(allianceDeclareTime, os.server_zone()) then
        return allianceDeclareCount
    else
        return 0
    end
end

--获取当前联盟已宣战的城池 regionId 列表
function GetAllianceDeclareNCRegionList()
    return allianceDeclareNCRegionList
end

--获取当前联盟已占领的城池 regionId 列表
function GetAllianceOccupyNCRegionList()
    return allianceOccupyNCRegionList
end

--获取当前联盟已占领的城池详情列表
function GetAllianceOccupyNCList()
    return allianceOccupyNCList
end

--获取当前联盟可占领的城池详情列表
function GetAllianceCanOccupyNCList()
    return allianceCanOccupyNCList
end

--设置联盟中立城池详情数据
function SetAllianceOccupyNCData(_, msg)
    local allianceId = GetUserAllianceId()
    if allianceId == 0 then
        return
    end

    allianceOccupyNCList = {}
    allianceCanOccupyNCList = {}
    for i, v in ipairs(msg.tInfoList) do
        if v.nAllianceID and v.nAllianceID == allianceId then
            table.insert(allianceOccupyNCList, v)
        else
            table.insert(allianceCanOccupyNCList, v)
        end
    end
end

--设置玩家首次加入联盟时间
function SetAllianceFirstJoinTime(time)
    allianceFirstJoinTime = time
end

--获取玩家首次加入联盟时间
function GetAllianceFirstJoinTime()
    return allianceFirstJoinTime or 0
end

------------------------------------联盟邀请函-----------------------------------------

-- 联盟邀请函设置
function SetAllianceInvitationInfo(msg)
    if msg:HasField("allianceId") then
        allianceInvitationInfo.InvitationAllianceID = msg.allianceId
    end
    if msg.stBase then
        -- allianceInvitationInfo.allianceInfo = msg.stBase
        local data = UpdateAllianceBase(msg.stBase)
        allianceInvitationInfo.allianceInfo = data
    end
    if msg:HasField("invitation_reward_times") then
        allianceInvitationInfo.rewardTimes = msg.invitation_reward_times
    end
    event.Trigger(event_alliance_define.REFRESH_ALLIANCE_INVITATE_INFO)
    event.Trigger(event_alliance_define.REFRESH_ALLIANCE_INVITATE_BUBBLE)
end
-- 联盟分享CD设置
function SetAllianceShareTime(msg)
    if msg.optType == alliance_pb.emllianceOptType_Share then
        --联盟分享
        if msg:HasField("share_edTime") then
            allianceInvitationInfo.share_edTime = msg.share_edTime
        end
        event.Trigger(event_alliance_define.REFRESH_SHARE_CD)
        local red_system = require "red_system"
        local red_const = require "red_const"
        red_system.TriggerRed(red_const.Enum.AllianceInviteBtnRed)
        if allianceShareDelayCall then
            util.RemoveDelayCall(allianceShareDelayCall)
        end
        if allianceInvitationInfo.share_edTime > os.server_time() then
            allianceShareDelayCall = util.DelayOneCall(allianceInvitationInfo.share_edTime + 1 - os.server_time(), function ()
                red_system.TriggerRed(red_const.Enum.AllianceInviteBtnRed)
            end)
        end
    end
end

-- 联盟邀请函ID
function GetInvitationAllianceID()
    return allianceInvitationInfo.InvitationAllianceID or 0
end

-- 联盟邀请函邀请的联盟详情
function GetInvitationAllianceInfo()
    -- return userAllianceData
    return allianceInvitationInfo.allianceInfo
end

-- 联盟邀请函奖励次数
function GetInvitationRewardTimes()
    return allianceInvitationInfo.rewardTimes or 0
end

-- 联盟分享CD
function GetAllianceShareTime()
    return allianceInvitationInfo.share_edTime or 0
end

local OPEN_INVITATION_FLAG = "%s_OPEN_INVITATION_FLAG" -- 今日是否打开窗口的时间戳标志
-- 推荐联盟气泡勾选时间
function SetInvitationTipTime(_time)
    if allianceInvitationInfo.InvitationAllianceID == 0 then
        -- log.Error("活动未开启")
        return
    end
    local player_mgr = require "player_mgr"
    local roleID = player_mgr.GetPlayerRoleID()
    local InvitationTime = string.format(OPEN_INVITATION_FLAG, roleID)
    
    PlayerPrefs.SetInt(InvitationTime, _time or os.server_time())
    event.Trigger(event_alliance_define.REFRESH_ALLIANCE_INVITATE_BUBBLE)
end
function GetInvitationTipTime()
    if allianceInvitationInfo.InvitationAllianceID == 0 then
        -- log.Error("活动未开启")
        return os.server_time()
    end
    local player_mgr = require "player_mgr"
    local roleID = player_mgr.GetPlayerRoleID()
    local InvitationTime = string.format(OPEN_INVITATION_FLAG, roleID)
    
    local tipTime = PlayerPrefs.GetInt(InvitationTime, 0)
    return tipTime
end

-- 联盟邀请函第几次关闭邀请函
local CLOSE_INVITATION_FLAG = "%s_CLOSE_INVITATION_FLAG" -- 今日是否打开窗口的时间戳标志
function SetInvitationCloseTimes()
    if allianceInvitationInfo.InvitationAllianceID == 0 then
        -- log.Error("活动未开启")
        return
    end
    local player_mgr = require "player_mgr"
    local roleID = player_mgr.GetPlayerRoleID()
    local InvitationTime = string.format(CLOSE_INVITATION_FLAG, roleID)
    
    local times = PlayerPrefs.GetInt(InvitationTime, 0)
    PlayerPrefs.SetInt(InvitationTime, times + 1)
    event.Trigger(event_alliance_define.REFRESH_ALLIANCE_INVITATE_BUBBLE)
end
function GetInvitationCloseTimes()
    if allianceInvitationInfo.InvitationAllianceID == 0 then
        -- log.Error("活动未开启")
        return 0
    end
    local player_mgr = require "player_mgr"
    local roleID = player_mgr.GetPlayerRoleID()
    local InvitationTime = string.format(CLOSE_INVITATION_FLAG, roleID)
    
    local times = PlayerPrefs.GetInt(InvitationTime, 0)
    return times
end

-- 联盟邀请函限制奖励次数上限
local limitInvitationReward
function GetInvitationRewardLimit()
    if not limitInvitationReward then
        local cfg = game_scheme:InitBattleProp_0(8390)
        if cfg and cfg.szParam.data[0] then
            limitInvitationReward = cfg.szParam.data[0]
        end
    end
    return limitInvitationReward or 3
end
-- 联盟邀请函开启配置
local invitationOpenCfg
function GetInvitationOpen()
    if not invitationOpenCfg then
        local cfg = game_scheme:InitBattleProp_0(8389)
        if cfg and cfg.szParam.data[0] then
            invitationOpenCfg = {}
            invitationOpenCfg.startTime = cfg.szParam.data[0]
            invitationOpenCfg.endTime = cfg.szParam.data[1]
        end
    end
    return invitationOpenCfg or {}
end

-- 联盟邀请函奖励配置
local invitationRewardCfg
function GetInvitationReward()
    if not invitationRewardCfg then
        local cfg = game_scheme:InitBattleProp_0(8391)
        if cfg and cfg.szParam.data[0] then
            invitationRewardCfg = cfg.szParam.data[0]
        end
    end
    return invitationRewardCfg or 910
end

-- 联盟邀请函冷却配置
local invitationCDCfg
function GetInvitationCDCfg()
    if not invitationCDCfg then
        local cfg = game_scheme:InitBattleProp_0(8396)
        if cfg then
            invitationCDCfg = cfg.szParam.data
        end
    end
    -- return {[0] = 0, 1, 3, 5, 30}
    return invitationCDCfg or {[0] = 0, 1, 3, 5, 30}
end

function ClearAllianceData()
    leaveAllianceTime = net_login_module.GetServerTime()
    allianceBaseData = {}
    recommendedAllianceId = 0
    userAllianceData = {}
    userAllianceId = 0
    allianceRank = {}
    allianceUserRank = {}
    allianceLookBaseInfoData = {}

    -- 中立城池
    allianceDeclareCount = 0
    allianceDeclareNCRegionList = {}
    allianceOccupyNCRegionList = {}
    allianceOccupyNCList = {}
    allianceCanOccupyNCList = {}
    allianceInvitedArr = {}
	allianceInvitationInfo = {}
    limitInvitationReward = nil
end

--TODO清除数据
function ClearData()
    ClearAllianceData()
    leaveAllianceTime = nil
    allianceFirstJoinTime = 0
end

event.Register(event.USER_DATA_RESET, ClearData)
event.Register(event_alliance_define.EXIT_ALLIANCE, ClearAllianceData)
event.Register(sand_ui_event_define.GET_CITY_SIEGE_DATA, SetAllianceOccupyNCData)

Init()