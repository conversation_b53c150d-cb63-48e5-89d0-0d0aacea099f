local require = require
local os = os
local string = string
local table = table
local Vector3 = CS.UnityEngine.Vector3

local event = require "event"
local gw_sand_lod_const = require "gw_sand_lod_const"
local gw_sand_effect_const = require "gw_sand_effect_const"
local gw_sand_effect_param = require "gw_sand_effect_param"
local gw_const = require "gw_const"
local util = require "util"

local ESEntityType = gw_const.ESEntityType
local resType = gw_const.ESViewLevelResType

local CompVFX = {}

function CompVFX:ctor()

end

function CompVFX:InitComp(data)
    self.isLoaded = true
    self.compEffectList = {}
    self.compEffectTimers = {}
    self:OnUpdateData()
    self:RegisterListener()
end

function CompVFX:OnUpdateData()
    local serData = self.serData
    local props = serData.props
    local type = serData.type

    if type == ESEntityType.Base then
        local gw_common_util = require "gw_common_util"
        if gw_common_util.CheckInSand_Storm() then
            local maxHp, curHp = self.serData:GetBaseDefend()
            props.baseBurn = curHp < maxHp and 1 or 0
        else
            local burnTimeStamp = props.baseWallEndTime - os.server_time()
            if burnTimeStamp > 0 then
                props.baseBurn = 1
                self:CreateVFXTimer(burnTimeStamp)
            else
                props.baseBurn = 0
            end
        end
        props.baseSafetyState = props.baseSafety >= os.server_time() and 1 or 0
    elseif type == ESEntityType.NeutralCity then
        local finalKey = self.serData:GetFinalKey2()
        -- 保护罩
        local protectCD = serData:GetNCProtectCD()
        self:CreateVFXTimer(protectCD)
        if finalKey == gw_const.ESEntityResViewType.BigGun then
            props.baseSafety_bigGun = protectCD > 0 and 1 or 0
        else
            -- 中立城池着火
            local cfg = serData.cfg
            if cfg and cfg.congress ~= 1 then
                if cfg.Fire > 0 then
                    local value = serData:GeNCDefenseValue()
                    if value >= 0 and value ~= cfg.cityHp then
                        local hpRate = value / cfg.cityHp * 100
                        props.baseBurn = hpRate < cfg.Fire and 1 or 0
                    else
                        props.baseBurn = 0
                    end
                end
            else
                props.baseSafety = protectCD > 0 and 1 or 0
            end
        end
    elseif type == ESEntityType.Storm then
        props.baseSafetyState = 0
        local cfg = serData.cfg
        --有初始位置的，才需要保护罩
        if cfg and cfg.Coordinate.count > 0 then
            local gw_storm_mgr = require "gw_storm_mgr"
            local isOpen = gw_storm_mgr.CheckBuiildOpen(serData)
            if not isOpen then
                --还没到开启时间，开启保护罩
                props.baseSafetyState = 1
            end
        end
    end

    local tab = gw_sand_effect_const.MountEntity[type]
    if not tab then
        return
    end
    
    for k, v in pairs(tab) do
        if serData.props[v.props] == v.propsValue then
            if not self.compEffectList[k] then
                self:AddEffectByKey(k, gw_sand_effect_param.CreateEffectBaseParam(Vector3(self.serData.pos.x, 0, self.serData.pos.y)))
            end
        else
            if self.compEffectList[k] then
                self:RemoveEffectByKey(k)
            end
        end
    end
end

function CompVFX:AddEffectByKey(key, effectBaseParam, callback)
    local gw_sand_effect_mgr = require "gw_sand_effect_mgr"
    self.compEffectList[key] = gw_sand_effect_mgr.CreateEffects(key, effectBaseParam, nil, nil, callback)
end

function CompVFX:RemoveEffectByKey(keyName)
    local gw_sand_effect_mgr = require "gw_sand_effect_mgr"
    gw_sand_effect_mgr.RemoveEffect(self.compEffectList[keyName])
    self.compEffectList[keyName] = nil
end

function CompVFX:ResetPos()
    -- 刷新挂载特效的位置
    local gw_sand_effect_mgr = require "gw_sand_effect_mgr"
    for k, v in pairs(self.compEffectList) do
        gw_sand_effect_mgr.CallEffectFunc(v, "SetBaseParamLocalPosition", self.serData.pos.x, 0, self.serData.pos.y)
    end
end

function CompVFX:RegisterListener()
    if self.serData then
        self.OnUpdateDataEvent = function()
            self:OnUpdateData()
        end
        local finalKey = self.serData:GetFinalKey2()
        local hudMap = gw_sand_lod_const.LOD[finalKey]
        if hudMap and hudMap[resType.vfx] then
            local vfxHud = hudMap[resType.vfx]
            self.strProps = vfxHud.strProps
            self.props = vfxHud.props

            if self.strProps then
                self.serData.strProps:AddListener(self.strProps, self.OnUpdateDataEvent, self)
            end
            if self.props then
                self.serData.props:AddListener(self.props, self.OnUpdateDataEvent, self)
            end
        end
        if self.serData.type == gw_const.ESEntityType.Storm then
            --如果是沙漠风暴，建筑解锁，更新一下保护罩
            local event_DesertStrom_define = require("event_DesertStrom_define")
            self.OnUpdateDataEventStorm = function(_, buildId)
                if self.serData.cfg and self.serData.cfg.Building == buildId then
                    self:OnUpdateData()
                end
            end
            event.Register(event_DesertStrom_define.EVENT_STORM_BUILD_OPEN_EFFECT, self.OnUpdateDataEventStorm)
        end
    end
end

function CompVFX:UnRegisterListener()
    if self.serData and self.OnUpdateDataEvent then
        if self.strProps then
            self.serData.strProps:RemoveListener(self.strProps, self.OnUpdateDataEvent)
        end
        if self.props then
            self.serData.props:RemoveListener(self.props, self.OnUpdateDataEvent)
        end
        if self.serData.type == gw_const.ESEntityType.Storm and self.OnUpdateDataEventStorm then
            --如果是沙漠风暴，建筑解锁，更新一下保护罩
            local event_DesertStrom_define = require("event_DesertStrom_define")
            event.Unregister(event_DesertStrom_define.EVENT_STORM_BUILD_OPEN_EFFECT, self.OnUpdateDataEventStorm)
        end
    end
end

-- 开启计时器
function CompVFX:CreateVFXTimer(timeStamp)
    if self.compEffectTimers then
        local gw_sand_timer_mgr = require "gw_sand_timer_mgr"
        local timerId = gw_sand_timer_mgr.AddSandOnceTimer(timeStamp,
                function()
                    if self.isLoaded then
                        self:OnUpdateData()
                    end
                end
        )
        table.insert(self.compEffectTimers, timerId)
    end
end

function CompVFX:DisposeVFXTimer()
    if self.compEffectTimers then
        for k, v in pairs(self.compEffectTimers) do
            local gw_sand_timer_mgr = require "gw_sand_timer_mgr"
            gw_sand_timer_mgr.RemoveSandTimer(v)
        end
    end
end

function CompVFX:Dispose(unloadShowOnly)
    self.isLoaded = false

    if self.compEffectList then
        for k, v in pairs(self.compEffectList) do
            if v then
                GWG.GWSandEffectMgr.RemoveEffect(v)
            end
        end
        self.compEffectList = {}
    end
    self:UnRegisterListener()
    self:DisposeVFXTimer()
end

function CompVFX.new(data)
    local move = setmetatable({}, { __index = CompVFX })
    move:ctor()
    if data then
        move:InitComp(data)
    end
    return move
end

return CompVFX
