local ipairs = ipairs
local pairs = pairs
local table = table
local print = print
local os = os
local math = math
local require = require
local type =type
local gw_event_activity_define = require "gw_event_activity_define"
local log = require "log"
local string = string

local event = require "event"
local util =require "util"
local activity_cfg_util =require "activity_cfg_util"
local limit_gift_define =require "limit_gift_define"
local game_scheme = require "game_scheme"
local gw_recharge_mgr =require "gw_recharge_mgr"
local GWConst =require "gw_const"
local GWMgr =require "gw_mgr"
local ui_window_mgr =require "ui_window_mgr"
local net_route =require "net_route"
local xManMsg_pb =require"xManMsg_pb"
local rechargeGift_pb =require "rechargeGift_pb"
local gw_ed =require "gw_ed"
local gw_popups_trigger_mgr =require "gw_popups_trigger_mgr"
local festival_activity_cfg =require "festival_activity_cfg"
local festival_activity_mgr = require "festival_activity_mgr"

local PlayerPrefs = CS.UnityEngine.PlayerPrefs

--region 内部方法

local curMainAtyID,curSpecialAtyID=nil,nil
local limitData={}
local limitRechargeIDList={}
local CurpopUplist={} --当前页面需要弹出的礼包 （存储SpecialGIft的礼包ID）
local RechargeGiftData={}
local changeList={} --结束时间发生变化的数据
local chooseIndex =0
local rechargeTime = nil
-- local PopUpNum = 0
local TimerMgr = nil
local curShowIndex = 1
local poptimes = 0
local isLoginPop = false

module("limit_gift_data")
local logSwitch = false
local function LogError(...)
    if logSwitch then
        local log = require "log"
        log.Error(moduleName,...)
    end
end
local function Log(...)
    if logSwitch then
        local log = require "log"
        log.LogFormat(moduleName,...)
    end
end


-------------------------------对外接口 start----------------------------------------------
--设置活动id
function SetMainActivityId(id)
    curMainAtyID = id
end

--获取活动id
function GetMainActivityId()
    return curMainAtyID
end

--设置特殊活动id
function SetSpecialActivityId(id)
    curSpecialAtyID = id
end

--获取特殊活动id
function GeSpecialActivityId()
    return curSpecialAtyID
end

-- function CkeckEntranceOpen()
--     local isOpen = GetIsOpen() 
--     if isOpen then 
        
--     end
-- end

--判断入口开启
function GetIsOpen() 
    CheckPopUpScene()
    --print("GetIsOpen",#CurpopUplist)
    if CurpopUplist and #CurpopUplist > 0 then 
        return true
    end
    return false
end
--判断特殊页面入口开关
function GetSpecialGiftIsOpen()
    local cfg = activity_cfg_util.GetFestivalActivityCfg(limit_gift_define.SpecialGiftActID)
    if cfg then 
        local linkGiftConID=gw_recharge_mgr.GetLinkGiftCurId(cfg.ctnID3.data[0])
        --print("linkGiftConID",linkGiftConID)
        local ActCfg = game_scheme:ActivityContent_0(linkGiftConID)
        if ActCfg then 
            if ActCfg.PackageNext== -1 then 
                --print("GetSpecialGiftIsOpen :: false")
                return false
            end
        end
    end
    --print("GetSpecialGiftIsOpen :: true")
    return true
end

--设置入口信息
function GetShowInfo() 
    if CurpopUplist and #CurpopUplist > 0 then 
        local GroupTime={}
        local IconList={}
        for i,v in ipairs(CurpopUplist) do 
            for index,value in ipairs(limitData.ServerData[limit_gift_define.CfgDataIndex.Main]) do 
                if value.giftID == v then 
                    local ServerData = value
                    if ServerData then 
                        local UIDetailID =ServerData.cfgdata.UIDetailID
                        local DetailData=game_scheme:SpecialGiftUIDetail_0(UIDetailID)
                        if DetailData then 
                            table.insert(IconList,DetailData.EntryIcon)
                            table.insert(GroupTime,ServerData.time)
                        end
                    end
                    break
                end
            end
        end
        local MinTime=SetMinTime(GroupTime)
        local giftList={
            MinTime = MinTime,
            IconList = IconList,
        }
        -- print("GetShowInfo",#giftList)
        -- dump(giftList)
        return giftList
    end
    -- print("GetShowInfo")
    return nil --显示表不存在信息的应该设置为空，防止进入函数
end

--获取一组时间中最短的时间
function SetMinTime(timeGroup) 
    if timeGroup and #timeGroup > 0 then 
        table.sort( timeGroup, function(a,b) return a < b end )
    end
    return timeGroup[1] or 0
end

function GetChangeIconCD() 
    local changeTime = game_scheme:InitBattleProp_0(8241).szParam.data[0]
    return changeTime
end

--获取配置数据
--参数 index 礼包ID
--     type  获取限时礼包数据 或者 墨菲礼物数据
function GetCfgData(index,type) 
    if limitData.ServerData and limitData.ServerData[type][index] then 
        return limitData.ServerData[type][index].cfgdata
    end
    return nil
end

function GetServerData() 
    return limitData.ServerData[limit_gift_define.CfgDataIndex.Main]
end

--获取当前可弹出礼包ID（GiftID）列表
function GetCurpopUplist() 
    return CurpopUplist or {}
end

--选中礼包索引
function SetChooseIndex(index)
    chooseIndex=index
end
--获取当前礼包索引
function GetChooseIndex()
    return chooseIndex
end
--外部使用 根据礼包ID 获取链式礼包中当前正在使用的礼包
function GetMainDataByActivityConID(contentID) 
    if limitData.ServerData[limit_gift_define.CfgDataIndex.Main] then 
        for index,value in ipairs(limitData.ServerData[limit_gift_define.CfgDataIndex.Main]) do 
            if value.cfgdata and value.cfgdata.rewardID and value.cfgdata.rewardID.count == 1 and contentID == value.cfgdata.rewardID.data[0] then
                local ActContData= game_scheme:ActivityContent_0(contentID)
                if RechargeGiftData[value.giftID] and RechargeGiftData[value.giftID][ActContData.rechargeID.data[0]].curBuyCount and RechargeGiftData[value.giftID][ActContData.rechargeID.data[0]].curBuyCount == 0 then 
                    return contentID
                else
                    return nil
                end
            end
            if value.cfgdata and value.cfgdata.rewardID and value.cfgdata.rewardID.data and value.cfgdata.rewardID.count > 1 and contentID == value.cfgdata.rewardID.data[0] then 
                print("GetMainDataByActivityConID #1",contentID, value.cfgdata.rewardID.data[0])
                local curcontentID = GetCurLinkGiftIndex(value.giftID,value.cfgdata.rewardID.data,value.cfgdata.rewardID.count)
                local ActContData= game_scheme:ActivityContent_0(curcontentID)
                if RechargeGiftData[value.giftID] and RechargeGiftData[value.giftID][ActContData.rechargeID.data[0]].curBuyCount and RechargeGiftData[value.giftID][ActContData.rechargeID.data[0]].curBuyCount == 0 then 
                    return curcontentID
                else
                    return nil
                end
            end
        end
        return nil
    end
    return nil
end

--判断是否需要启用入口动画 单个礼包不启用 
function GetHasAnimate() 
    local canShowAni = #CurpopUplist
    return canShowAni > 1
end
function ClearTimes() 
    poptimes = 0
end

--入口展示索引设置
function SetCurShowIndex(eventName,index) 
    curShowIndex = index
end

--入口展示索引获取
function GetCurShowIndex() 
    return curShowIndex
end
--判断失败页面是否显示英雄碎片礼包条目 优化需求3
function HasGiftShowInDefeat()
    local serverData = limitData.ServerData[limit_gift_define.CfgDataIndex.Main]
    if serverData and #serverData > 0 then 
        for i,v in ipairs(serverData) do 
            --判断是否存在该礼包 能存在该列表中的元素是可购买的，不可购买的已经被筛掉了
            if v.giftID == limit_gift_define.ShowGiftID[limit_gift_define.LimitGiftShowType.SaiwierFragments] then 
                return true
            end
        end
    end
    return false
end
--点击英雄碎片礼包条目后跳转方法 优化需求3
function OpenUI(showType)
    --判断传过来的枚举是否为英雄碎片礼包
    if showType and showType[1] == limit_gift_define.DefeatShowType[limit_gift_define.LimitGiftShowType.SaiwierFragments] then 
        local index = 1
        local serverData = limitData.ServerData[limit_gift_define.CfgDataIndex.Main]
        if serverData and #serverData > 0 then 
            for i,v in ipairs(serverData) do 
                --判断是否存在该礼包 能存在该列表中的元素是可购买的，不可购买的已经被筛掉了
                if v.giftID == limit_gift_define.ShowGiftID[limit_gift_define.LimitGiftShowType.SaiwierFragments] then 
                    index = i
                    break
                end
            end
        end
        local closecallBack = function()
            local battle_data = require "battle_data"
            battle_data.ShowMainPanel = false
        end
        ui_window_mgr:ShowModule("ui_limit_gift_main",nil,closecallBack,{curIndex = index})
    end
end

--获取有优先级的礼包索引，如果当前服务器数据中无优先级礼包则不进行设置 (登录时弹窗使用) 
--优化需求2
function GetPriority()
    --判断是否为登录弹窗时触发，如果不是则按照原本的设置
    if not isLoginPop then 
        log.Warning("当前为登录弹窗 返回索引1")
        return 1
    end
    isLoginPop = false
    -- 获取索引时，再刷一遍礼包数据数据，防止有些礼包的结束时间跟下发时的对不上
    SetMainServerData() 
    log.Warning("GetPriority 礼包数据长度 ： ",#limitData.ServerData[limit_gift_define.CfgDataIndex.Main])
    if #limitData.ServerData[limit_gift_define.CfgDataIndex.Main] == 0 then 
        return 
    end
    local ServerData = limitData.ServerData[limit_gift_define.CfgDataIndex.Main]
    log.Warning("ServerData Length limitGift",#ServerData)
    local index = 1
    if ServerData and #ServerData > 1 then 
        local priorityIndex = ServerData[1].cfgdata.LoginpPriority
        for i=2,#ServerData do 
            local Data = ServerData[i]
            if Data.cfgdata and Data.cfgdata.LoginpPriority and Data.cfgdata.LoginpPriority > priorityIndex then 
                priorityIndex = Data.cfgdata.LoginpPriority
                index = i
            end
        end
        -- log.Warning("GetPriority",index,priorityIndex)
    end
    log.Warning("GetPriority 当前索引",index)
    return index
end

--当达到条件时，触发弹出限时礼包规则（优化需求1）
function CheckPopRuleForLimit(eventID,levelResult)
    local ServerData = limitData.ServerData[limit_gift_define.CfgDataIndex.Main]
    local player_mgr = require "player_mgr"
    local roleID = player_mgr.GetPlayerRoleID()
    if ServerData and #ServerData > 0 then 
        for i,v in ipairs(ServerData) do 
            if v.cfgdata and v.cfgdata.RepetConditionType and v.cfgdata.RepetConditionType.data then  
                local condition = v.cfgdata.RepetConditionType.data
                local data = v.cfgdata.RepetConditionValue.data

                for index = 0,#data do 
                    if eventID and data[index] and eventID == data[index] then 
                        if condition[index] and condition[index] == limit_gift_define.RepeatShow.UnlockBlock and not levelResult then 
                            -- log.Warning("达到解锁地块条件 CheckPopRuleForLimit",i)
                            insertData(roleID,v,i)
                            break
                        elseif condition[index] and condition[index] == levelResult then 
                            -- log.Warning("达到胜利或者失败条件 CheckPopRuleForLimit",levelResult)
                            insertData(roleID,v,i)
                            break
                        end
                    end
                end
            end
        end
    end
end

function insertData(roleID,Data,index)
    local key = string.format("CheckPopRuleForLimit#%s#%s",roleID,Data.giftID)
    local cache=PlayerPrefs.GetInt(key)
    local time = os.server_time()
    if cache == 0 then 
        -- log.Warning("insertData 首次弹出")
        PlayerPrefs.SetInt(key,time)
        PopUpWin(index)
    else
        local offset = time - cache
        if Data.cfgdata.RepetCD and offset >= Data.cfgdata.RepetCD then 
            -- log.Warning("insertData CD已结束 可弹出")
            PlayerPrefs.SetInt(key,time)
            PopUpWin(index)
        else
            -- log.Warning("insertData 不可弹出")
        end
    end
end
 --监听主线战斗结算回调
function OnCheckBattleResult()
    local battle_data = require "battle_data"
    local laymain_data = require "laymain_data"
    local result = battle_data.IsVictory() and limit_gift_define.RepeatShow.CrossSucess or limit_gift_define.RepeatShow.CrossDefeat
    local curlevel = laymain_data.GetPassLevel()
    -- log.Warning("OnCheckBattleResult",curlevel)
    CheckPopRuleForLimit(curlevel,result)
end
--监听主线小游戏战斗结算回调
function OnCheckMiniBattleResult(eventName,result)
    local laymain_data = require "laymain_data"
    local curlevel = laymain_data.GetPassLevel()
    local realResult = result and limit_gift_define.RepeatShow.CrossSucess or limit_gift_define.RepeatShow.CrossDefeat
    -- log.Warning("OnCheckMiniBattleResult",curlevel)
    if realResult == limit_gift_define.RepeatShow.CrossDefeat then 
        curlevel = curlevel + 1
    end
    CheckPopRuleForLimit(curlevel,realResult)
end
--监听玩家移动到下一个地块的回调
function OnGotoNextBlock()
    local laymain_data = require "laymain_data"
    local curlevel = laymain_data.GetPassLevel()
    -- log.Warning("OnGotoNextBlock",curlevel)
    CheckPopRuleForLimit(curlevel)
end
-------------------------------对外接口 end----------------------------------------------
--初始化
function Init() 
    InitData()
    InitEvent() 
end

--初始化数据
function InitData() 
    limitData={
        ServerData={},
        CurData={}
    }
    for i=1,2 do 
        limitRechargeIDList[i]={}
    end
end

--初始化事件
function InitEvent()  
    -- event.Register(limit_gift_define.CHECKNEXTPAGE,OpenNextPopUp) --点击关闭时回调
    --event.Register(event.CHECK_DATA_FINISH,SetLoginData) --topic数据完成登录设置回调
    --event.Register(event.FIRST_ENTER_GAME,firstEnter) --第一次加载场景完成回调
    --event.Register(limit_gift_define.CloserIcon,CloseEvent) --倒计时结束回调
    --充值完成时回调
    event.Register(limit_gift_define.RechargeSucess,RechargeComplete) 
    --特殊限时礼包
    event.Register(event.GW_REFRESH_RECHARGE_GOODS,SetSpecialServerData)
    --更新时下发的TopicData
    event.Register(event.UPDATE_SPECIAL_GIFT_DATA,UpdateTopicData)
    --当前页面定时器回调
    event.Register(limit_gift_define.TimerEndEvent,RefreshData)
    --主城被打飞时触发更新
    gw_ed.mgr:Register(gw_ed.GW_LIMIT_GIFT_UPDATE,OnDefailEvent)
    --展示Icon更新索引
    event.Register(event.UPDATE_SHOW_INDEX,SetCurShowIndex)
    --到达某个地块时触发回调
    event.Register(gw_ed.GW_HOME_EVENT_PASS_LEVEL,OnGotoNextBlock)
    --监听主线战斗结算回调
    event.Register(event.BATTLE_CLOSE_SEND,OnCheckBattleResult)
    --监听主线小游戏战斗结算回调
    event.Register(event.MINIGAME_CLOSE,OnCheckMiniBattleResult)
end
--设置登录时下发的TopicData
-- function SetLoginData() 
--     UpdateMainSpecialGiftData()
-- end

--主城被打飞时触发
function OnDefailEvent() 
    RefreshData()
    -- print("基地打飞触发入口刷新")
	-- print("强更补丁测试222")
    event.Trigger(gw_event_activity_define.GW_LIMIT_GIFT_ICON_UPDATE) --入口刷新
end

--更新特惠礼包Topic触发事件
function UpdateTopicData() 
    SetMainServerData()
    CheckPopUpScene()
    ShowPanel()
    -- print("UpdateSpecialGiftDataByGiftID", #CurpopUplist)
    -- dump(CurpopUplist)
end

--登录后进入场景回调
-- function firstEnter() 
--     UpdateMainSpecialGiftData()
--     RefreshData()
--     print("firstEnter",#CurpopUplist)
--     event.Trigger(gw_event_activity_define.GW_LIMIT_GIFT_ICON_UPDATE) --入口刷新
-- end

--关闭面板 重新设置Icon
-- function CloseEvent()
--     if ui_window_mgr:IsModuleShown("ui_limit_gift_main") then 
--         ui_window_mgr:UnloadModule("ui_limit_gift_main")
--     end
--     --CheckPopUpScene()
--     event.Trigger(gw_event_activity_define.GW_LIMIT_GIFT_ICON_UPDATE) --入口刷新
-- end

--刷新页面回调
function RefreshData() 
    SetMainServerData()
    CheckPopUpScene()
    event.Trigger(limit_gift_define.RefreshPage)
    event.Trigger(gw_event_activity_define.GW_LIMIT_GIFT_ICON_UPDATE) --入口刷新
end

--显示限时礼包弹窗（服务器下发时触发显示函数）
function ShowPanel()
    local isOpen = festival_activity_mgr.GetIsOpenByActivityID(curMainAtyID)
    if CurpopUplist and #CurpopUplist > 0 and isOpen and not isLoginPop then 
        if not ui_window_mgr:IsModuleShown("ui_limit_gift_main") then --当前没有显示弹窗的时候显示，如果当前是购买礼包也会有下发数据，但是不会关闭掉页面
            poptimes = poptimes + 1
            local index = poptimes
            PopUpWin(index)
        else
            event.Trigger(limit_gift_define.RefreshPage) --刷新页面信息
        end
    else
        if  ui_window_mgr:IsModuleShown("ui_limit_gift_main") then 
            ui_window_mgr:UnloadModule("ui_limit_gift_main")
        end
    end
    event.Trigger(gw_event_activity_define.GW_LIMIT_GIFT_ICON_UPDATE) --入口刷新
end

--客户端主动弹出弹窗逻辑（根据弹窗规则进行弹出）
function PopUpWin(curIndex)
    local priority = 0
    local gw_popup_config = require "gw_popup_config"
    local popupIndex = gw_popup_config.LoginPopupEnum.SpecialGift
    local cfg = game_scheme:PopupControl_0(popupIndex)
    if cfg then
        priority = cfg.Priority
    end
    gw_popups_trigger_mgr.AddTriggerFunc(function()
        local gw_popups_data = require "gw_popups_data"
        gw_popups_data.AddMessage("ui_limit_gift_main",function()
            ui_window_mgr:ShowModule("ui_limit_gift_main", nil, function()
                gw_popups_data.ShowNextMessage(0)
            end,{curIndex=curIndex})
        end,gw_popups_data.PopupsType.Popups,0.2,priority)    
    end)
end


--刷新面板事件
-- function RefreshPanel()
--     PopUpNum = PopUpNum + 1 
-- end


--打开下一个弹窗 UI层调用
-- function OpenNextPopUp()
--    if NextPopUp and #NextPopUp > 1 then 
--         if NextIndex <= #NextPopUp then 
--             chooseIndex=NextIndex
--             ui_window_mgr:ShowModule("ui_limit_gift_main")
--             NextIndex=NextIndex+1
--         else
--             NextIndex=1
--             chooseIndex=1
--             NextPopUp={}
--             ui_window_mgr:UnloadModule("ui_limit_gift_main")
--         end
--     else
--         if #NextPopUp == 0 or #NextPopUp == 1 then 
--             ui_window_mgr:UnloadModule("ui_limit_gift_main")
--         end
--     end
--     ui_window_mgr:UnloadModule("ui_limit_gift_main")
-- end

--检测当前列表是否存在相同的giftID
function CheckHasSameKey(list,giftID)
    if not list or #list == 0 then 
        return false
    end
    for i,v in ipairs(list) do 
        if v.giftID == giftID then 
            return true
        end
    end
    return false
end

--构建服务器登录下发数据
function SetMainServerData() 
    --获取服务器下发礼包数据
    if not limitData then
        limitData = {}
    end
    if not limitData.ServerData then
        limitData.ServerData= {}
    end
    -- if not limitData.CfgData then 
    --     limitData.CfgData={}
    -- end
    local Data,giftList=GetSpecialGiftData()
    local server_time = os.server_time()
    limitData.ServerData[limit_gift_define.CfgDataIndex.Main]={}
    if giftList and #giftList>0 then 
        for i,v in ipairs(giftList) do 
            --过期礼包不进行设置
            if v.time - server_time > 0 then 
                local temp={}
                temp.giftID=v.giftID
                temp.time=v.time
                temp.cfgdata=v.data
                --数据下发时触发设置
                -- limitData.CfgData[temp.giftID]=temp.cfgdata
                --判断链式礼包还是普通礼包
                if temp.cfgdata.rewardID then 
                    temp.contentID=temp.cfgdata.rewardID.data[0]
                end
    
                if temp.cfgdata.rewardID and temp.cfgdata.rewardID.count > 1 then 
                    local curContentID,index=GetCurLinkGiftIndex(v.giftID,temp.cfgdata.rewardID.data,temp.cfgdata.rewardID.count)
                    -- if curContentID then 
                    --     local contentCfg = game_scheme:ActivityContent_0(curContentID)
                    --     local rechargeData=gw_recharge_mgr.GetRechargeServerData(contentCfg.rechargeID.data[0])
                    --     if rechargeData then 
                    --         if rechargeData then 
                    --             if not RechargeGiftData[v.giftId] then 
                    --                 RechargeGiftData[v.giftId]={}
                    --             end
                    --             RechargeGiftData[v.giftId][curContentID.rechargeID.data[0]]=rechargeData
                    --         end
                    --     end
                    -- end
                    temp.contentID = curContentID
                    -- temp.LinkIndex = index
                end
                --limitData.ServerData[limit_gift_define.CfgDataIndex.Main][temp.giftId] = temp
                local CheckSameData = CheckHasSameKey(limitData.ServerData[limit_gift_define.CfgDataIndex.Main],temp.giftID)
                if not CheckSameData then
                    gw_recharge_mgr.FilterFunctionOpenContentByContentID(temp.contentID,limitData.ServerData[limit_gift_define.CfgDataIndex.Main],temp)
                end
                 --table.insert( limitData.ServerData[limit_gift_define.CfgDataIndex.Main],temp )
            end
        end
    end
    -- print("SetMainServerData")
    -- dump(limitData.ServerData[limit_gift_define.CfgDataIndex.Main])
    -- log.Warning("SetMainServerData",#limitData.ServerData[limit_gift_define.CfgDataIndex.Main])
    event.Trigger(gw_event_activity_define.GW_LIMIT_GIFT_ICON_UPDATE) --入口刷新
end

--设置特殊限时礼包数据 （服务器数据）
function SetSpecialServerData()
     --获取服务器下发礼包数据
    if not limitData.ServerData then
        limitData.ServerData= {}
    end
    local cfg = festival_activity_cfg._GetActivityCfgByActivityID(limit_gift_define.SpecialGiftActID)
    if cfg then 
        local linkGiftConID=gw_recharge_mgr.GetLinkGiftCurId(cfg.ctnID3.data[0])
        if linkGiftConID then 
            local contentData =game_scheme:ActivityContent_0(linkGiftConID)
            local GiftData = gw_recharge_mgr.GetRechargeData(linkGiftConID) 
            local Data={}
            local bigRewardData,rewardlist = ParsingRewardData(GiftData.rewardList)
            --配置数据
            Data.name=GiftData.name
            Data.goodsId=GiftData.rechargeId  --充值id
            Data.activityIcon=GiftData.activityIcon  --活动背景
            Data.onSale = GiftData.multiples --超值礼包百分比
            Data.rewardList=rewardlist  --其他奖励
            Data.bigRewardData = bigRewardData  -- 大奖
            Data.curActConId=linkGiftConID
            Data.allReward = GiftData.rewardList            -- print("SetSpecialServerData")
            -- dump(cfg.ctnID3.data)
            if Data.curActConId then 
                local rechargeCfg = gw_recharge_mgr.GetRechargeData(Data.curActConId)
                Data.rechargeID = rechargeCfg.rechargeId
            end
            --服务器数据
            Data.buyTime=gw_recharge_mgr.GetRechargeBuyCount(contentData.rechargeID.data[0])--购买次数
            Data.endTime=gw_recharge_mgr.GetRechargeBuyEndTime(contentData.rechargeID.data[0])--结束时间
            Data.Hasbuy = linkGiftConID == cfg.ctnID3.data[4] and Data.buyTime > 0
            limitData.ServerData[limit_gift_define.CfgDataIndex.Special]=Data
        end
    end
end
--获取特殊限时礼包数据
function GetSpecialData()
    return limitData.ServerData[limit_gift_define.CfgDataIndex.Special]
end
--特殊页面 设置大奖
function ParsingRewardData(data) 
    local bigRewardData={}
    local rewardList={}
    if data then 
        for i,v in pairs(data) do 
            local itemCfg=game_scheme:Item_0(v.id)
            if itemCfg and itemCfg.type == 38 then --如果奖励中存在英雄碎片
                bigRewardData.id = v.id
                bigRewardData.num =v.num
            else
                local temp={}
                temp.id=v.id
                temp.num=v.num
                table.insert(rewardList,temp)
            end
        end
    end
    -- print("ParsingRewardData")
    -- dump(bigRewardData)
    -- dump(rewardList)
    return bigRewardData,rewardList
end

--检查当前场景下可以弹出的弹窗
function CheckPopUpScene() 
    local curStage = limit_gift_define.SceneType.None
    if GWMgr.curScene == GWConst.ESceneType.Home then 
        curStage =limit_gift_define.SceneType.Home
    elseif GWMgr.curScene == GWConst.ESceneType.Sand then 
        curStage =limit_gift_define.SceneType.Sand
    end
    
    CurpopUplist={}
    --print("CheckPopUpScene",#CurpopUplist)
    if not limitData.ServerData or not limitData.ServerData[limit_gift_define.CfgDataIndex.Main] or 
        curStage == limit_gift_define.SceneType.None then 
        return 
    end 
    --print("CheckPopUpScene 2",#CurpopUplist)
    for i,v in ipairs(limitData.ServerData[limit_gift_define.CfgDataIndex.Main]) do 
        local cfgData = v.cfgdata
        --处于可弹出的场景
       -- print("CheckPopUpScene  GWMgr.curScene  GWConst.ESceneType.Home",GWMgr.curScene ,GWConst.ESceneType.Home,GWConst.ESceneType.Sand)
        --print("CheckPopUpScene  curStage  cfgData.PopupValue",curStage ,cfgData.PopupValue.data[0],cfgData.PopupValue.data[1])
        if curStage == cfgData.PopupValue.data[0] or curStage == cfgData.PopupValue.data[1] then 
            --print("CheckPopUpScene",PopUpNumGroup[v.giftId])
            -- if not PopUpNumGroup[v.giftId] then 
            --     PopUpNumGroup[v.giftId] = 1
            --     table.insert(CurpopUplist,v.giftId)
            -- else
            --     if changeList and #changeList > 0 then 
            --         --print("has change")
            --         for index,value in pairs(changeList) do 
            --             if value == v.giftId then 
            --                 table.insert(CurpopUplist,v.giftId)
            --                 break
            --             end
            --         end
            --     end
            -- end
            table.insert(CurpopUplist,v.giftID)
        end
    end
    --print("CheckPopUpScene 3",#CurpopUplist)
    -- changeList={}
end

--弹出条件判断 登录弹窗逻辑 登录完进入主城场景后调用
function PopupLoginGift()
    SetMainServerData()
    CheckPopUpScene()
    -- print("PopupLoginGift",#CurpopUplist)
    if CurpopUplist and #CurpopUplist > 0 then  --登录后就把当前显示的弹窗设为已弹出过 并且拿取第一个显示的数据
        --event.Register(event.GW_SCENE_CHANGE_SUCCESS,SwitchSceneHandle) --反复切换场景时回调
        isLoginPop = true
        return true
    end
    return false
end

--构建显示数据 供Controller层调用
function BuildRenderData()
    local ShowData = {}
    -- print("limitData.ServerData[limit_gift_define.CfgDataIndex.Main]")
    -- dump(limitData.ServerData[limit_gift_define.CfgDataIndex.Main])
    for i,v in ipairs(limitData.ServerData[limit_gift_define.CfgDataIndex.Main]) do 
        local ContentID = v.contentID
        local ActContData= game_scheme:ActivityContent_0(ContentID)
        if ActContData then 
            local RechargeData=gw_recharge_mgr.GetRechargeData(ContentID)
            -- print("BuildRenderData :: RechargeData")
            -- dump(RechargeData)
             --读取UIdetail
            local UIDetailData=game_scheme:SpecialGiftUIDetail_0(v.cfgdata.UIDetailID)
            local topBanner = UIDetailData.Banner
            local ContentBg =UIDetailData.background
            local BottomBanner =UIDetailData.toggleIcon
            local CanBuyNum = 0
            local curHasBuy = RechargeGiftData[v.giftID][ActContData.rechargeID.data[0]]
            local Canbuy =false
            if curHasBuy and ActContData.LimitNumber and ActContData.LimitNumber > 0 then 
                CanBuyNum = ActContData.LimitNumber-curHasBuy.curBuyCount
                Canbuy =true
            end
            
            local RewardList = GetRewardList(RechargeData.rewardList)
            local PageType =UIDetailData.subtemplatetype
            local rewardItemBg =UIDetailData.rewardItemBg
            local supervalue =RechargeData.multiples.."%"
            local tip =UIDetailData.bannerText
            local temp={
                titleName=RechargeData.name,
                superValue= supervalue,
                ContDown = v.time,
                topBanner=topBanner,
                ContentBg=ContentBg,
                BottomBanner=BottomBanner,
                CanBuyNum=CanBuyNum,
                RewardList=RewardList,
                goodsID=RechargeData.rechargeId,
                PageType=PageType,
                rewardItemBg = rewardItemBg,
                tip=tip,
                canbuy=Canbuy,
            }
            table.insert(ShowData,temp)
        end
    end
    -- print("BuildRenderData")
    -- dump(ShowData)
    return ShowData
end

--获取当前选中的数据
function GetIsChooseInfo() 
    local data=BuildRenderData()
    -- if NextPopUp and #NextPopUp > 0 then 
    --     if  NextPopUp[NextIndex] then 
    --         return data[NextPopUp[NextIndex]]
    --     else
    --         return nil
    --     end
    -- end

    -- if CurpopUplist and #CurpopUplist > 0 then 
    --     if CurpopUplist[chooseIndex] then 
    --         return data[CurpopUplist[chooseIndex]]
    --     else
    --         return nil
    --     end
    -- end
    -- print("GetIsChooseInfo",chooseIndex)
    -- dump(data)
    return data[chooseIndex]
end

--获取渲染数据的长度
function GetrenderDataLength() 
    local data=BuildRenderData()
    if data then 
        return #data
    end
    return 0
end

--链式礼包选择合适的ActvityCOntentID
function GetCurLinkGiftIndex(giftID,data,count)
    for i=0,count - 1 do
        --print("GetCurLinkGiftIndex ActivityContent", data[i])
        local activityContentCfg = game_scheme:ActivityContent_0(data[i])
        if activityContentCfg then 
            local ServerGiftData=RechargeGiftData[giftID][activityContentCfg.rechargeID.data[0]]
            if ServerGiftData then 
                local buytime=ServerGiftData.curBuyCount 
                --print("GetCurLinkGiftIndex",data[i],buytime)
                if activityContentCfg.LimitNumber - buytime > 0 then 
                    return data[i],i
                end
            else
                --print("GetCurLinkGiftIndex not ServerGiftData",data[i])
                return data[i],i
            end
        end
    end
    return nil
end

--特殊页面链式礼包获取
function GetCurLinkSpecialGiftIndex(firstcontID) 
    local contID =gw_recharge_mgr.GetLinkGiftCurId(firstcontID)
    return contID
end

--设置所有giftID的礼包数据
function SetAllRechargeData(specialData)
    -- 验证输入
    if not specialData or type(specialData) ~= "table" then
        return
    end
    local rechargeGiftData = {}  -- 封装 RechargeGiftData

    for i,v in pairs(specialData) do 
        if not rechargeGiftData[v.giftID] then 
            rechargeGiftData[v.giftID]={}
        end
        local cfgdata=game_scheme:SpecialGift_0(v.giftID)
        if cfgdata and cfgdata.rewardID then 
            for index,value in pairs(cfgdata.rewardID.data) do 
                local ActCfg = game_scheme:ActivityContent_0(value)
                if ActCfg and ActCfg.rechargeID then 
                    local rechargeID  = ActCfg.rechargeID.data[0]
                    local cacheData = rechargeGiftData[v.giftID][rechargeID]
                    if not cacheData then 
                        local RechargeData = gw_recharge_mgr.GetRechargeServerData(ActCfg.rechargeID.data[0])
                        if RechargeData then 
                            rechargeGiftData[v.giftID][ActCfg.rechargeID.data[0]]=RechargeData
                        else
                            local data ={}
                            data.curBuyCount=0
                            rechargeGiftData[v.giftID][ActCfg.rechargeID.data[0]]=data
                        end
                    end 
                end
            end
        end
    end

    RechargeGiftData = rechargeGiftData
    -- print("SetAllBuyTimeFromSpecialGift")
    -- dump(buytimeGroup)
end

--获取奖励数据 （ iconid，id ，num）
function GetRewardList(rewardList)
    local HandleData={}
    local item_data =require "item_data"
    for i,v in pairs(rewardList) do 
        if v.nType == item_data.Reward_Type_Enum.Survivor then 
            local survivorCfg =game_scheme:BuildSurvivor_0(v.id)
            local ui_util =require "ui_util"
            if survivorCfg then 
                local temp={}
                temp.iconID = survivorCfg.faceID
                temp.name =survivorCfg.NameID
                temp.num = 1
                temp.survivorId=v.id
                temp.quality =survivorCfg.stage
                temp.type =  ui_util.SummonRewardType.Survivor
                table.insert(HandleData,temp)
            end
        else
            local itemCfg=game_scheme:Item_0(v.id)
            local temp={}
            temp.iconID=itemCfg.icon
            temp.num=v.num
            temp.name = itemCfg.nameKey
            temp.Id =v.id
            table.insert(HandleData,temp)
        end
    end
    return HandleData
end

--场景切换对弹窗进行检测 主动点击场景切换时调用
-- function SwitchSceneHandle(eventName,scene)
--     --从缓存中拿出需要弹窗的礼包
--     --当队列中只有一个弹窗
--     if scene == GWConst.ESceneType.Truck then 
--         return
--     end
--     print("SwitchSceneHandle",scene)
--     --切换场景时打开弹窗显示
--     if not ui_window_mgr:IsModuleShown("ui_limit_gift_main") then 
--         CheckPopUpScene()
--         if CurpopUplist and #CurpopUplist > 0 then --存在可弹出页面的情况下 选中第一个数据
--             -- NextPopUp={}
--             -- for i,v in pairs(CurpopUplist) do 
--             --     table.insert(NextPopUp,v)
--             -- end
--             ui_window_mgr:ShowModule("ui_limit_gift_main")
--         end
--     end
-- end


-------------------------------旧代码迁移------------------------------------
local _specialGiftDataMap = {}


--- 更新限时礼包弹出数据 
---@param giftID number
---@param data table
---@param State number
function UpdateMainSpecialGiftData()
    local task_data =require "task_data"
    local taskData = task_data.GetSpecialGiftList()
    local taskTimeData = task_data.GetSpcialGiftTimerList()
    log.Warning("UpdateMainSpecialGiftData",#taskData)
    for i,v in ipairs(taskData) do 
        -- _specialGiftDataMap[v.giftID]=v
        _specialGiftDataMap[i]=v
        for index,value in ipairs(taskTimeData) do 
            if value.id == v.id then 
                _specialGiftDataMap[i].time = value.time
                break
            end
        end
    end

    event.Trigger(limit_gift_define.UPDATE_TOPIC_DATA)
    -- print("UpdateSpecialGiftData")
    -- dump(_specialGiftDataMap)
end

local teamGroup = nil

function CheckTeamData(specialGiftData)
    teamGroup = {}
    local giftList = {}
    -- print("CheckTeamData  11")
    -- dump(RechargeGiftData)
    for k,v in pairs(specialGiftData) do
        if v and v.giftID and v.giftID > 0 then
            local specialGiftCfg = game_scheme:SpecialGift_0(v.giftID)
            if specialGiftCfg~=nil then
                -- print("v.singlePurchaseNum < specialGiftCfg.LimitedBuy.data[0]",v.giftID,v.singlePurchaseNum,specialGiftCfg.LimitedBuy.data[0])
                if v.singlePurchaseNum < specialGiftCfg.LimitedBuy.data[0] then
                    -- 单次弹出小于购买次数
                    local contentID = 0
                    if specialGiftCfg.rewardID.count > 1 then 
                        contentID=GetCurLinkGiftIndex(v.giftID,specialGiftCfg.rewardID.data,specialGiftCfg.rewardID.count)
                    else
                        contentID=specialGiftCfg.rewardID.data[0]
                    end
                    local actCfg = game_scheme:ActivityContent_0(contentID)
                    if actCfg then
                        local data =RechargeGiftData[v.giftID]
                        if data and data[actCfg.rechargeID.data[0]] and (data[actCfg.rechargeID.data[0]].curBuyCount == 0 or actCfg.LimitNumber == 0 or data[actCfg.rechargeID.data[0]].curBuyCount < actCfg.LimitNumber)  then
                            local indexKey = util.get_len(giftList) + 1
                            local curTime =os.server_time()
                            --print("v.giftID specialGiftCfg.LimitedTime  v.time  curTime",v.giftID,specialGiftCfg.LimitedTime,v.time,curTime)
                            if specialGiftCfg.LimitedTime==0 or (v.time~=nil and type(v.time) == "number" and v.time>curTime) then
                                if not teamGroup[specialGiftCfg.TeamID] then
                                    teamGroup[specialGiftCfg.TeamID] = {} 
                                end
                                teamGroup[specialGiftCfg.TeamID][indexKey] = {giftID = v.giftID,time = v.time or 0,data = specialGiftCfg}
                            end
                        end
                    end
                end
            end
        end
    end
    -- print("CheckTeamData")
    -- dump(teamGroup)
end

--获得当前开启的限时礼包
function GetSpecialGiftData()
    UpdateMainSpecialGiftData()
    local specialGiftData = _specialGiftDataMap
    -- print("specialGiftData")
    -- dump(specialGiftData)
    local giftList = {}
    SetAllRechargeData(specialGiftData)
    CheckTeamData(specialGiftData)
    for k,v in ipairs(specialGiftData) do
        if v and v.giftID and v.giftID > 0 then
            local specialGiftCfg = game_scheme:SpecialGift_0(v.giftID)
            if specialGiftCfg~=nil then
                if v.singlePurchaseNum < specialGiftCfg.LimitedBuy.data[0] then
                    local curActContID =specialGiftCfg.rewardID.data[0]
                    
                    --如果是链式礼包
                    if specialGiftCfg.rewardID.count > 1 then 
                        curActContID=GetCurLinkGiftIndex(v.giftID,specialGiftCfg.rewardID.data,specialGiftCfg.rewardID.count) --获取不存在礼包数据的ContentID
                    end
                    local actCfg = game_scheme:ActivityContent_0(curActContID)
                    if actCfg then
                        --根据设置的buyTime判断礼包购买次数
                        local data =RechargeGiftData[v.giftID]
                        if data and data[actCfg.rechargeID.data[0]] and (data[actCfg.rechargeID.data[0]].curBuyCount == 0 or actCfg.LimitNumber == 0 or data[actCfg.rechargeID.data[0]].curBuyCount < actCfg.LimitNumber)  then 
                            local indexKey = util.get_len(giftList) + 1
                            local curTime =os.server_time()
                            if specialGiftCfg.LimitedTime==0 or (v.time~=nil and v.time>curTime) then
                                if teamGroup and teamGroup[specialGiftCfg.TeamID] then
                                    local count = util.get_len(teamGroup[specialGiftCfg.TeamID])
                                    if count <= 1 then
                                        giftList[indexKey] = {giftID = v.giftID,time = v.time or 0,data = specialGiftCfg}
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end
    end
    ReverTable(giftList)
    local tab = {}
    for i,v in ipairs(giftList) do
        ------  --print("获得当前开启的特惠礼包================i",i,"giftID",v.giftID,"time",v.time,"v.TeamID:",v.data.TeamID)
        -- dump(v.data.ConditionType.data)
        table.insert( tab, v.data )
    end
    -- print("GetSpecialGiftData")
    -- dump(giftList)
    return tab,giftList
end
----------------------------------旧代码迁移---------------------------------------
--翻转列表 
function ReverTable(table)
    local n =#table
    for i=1,math.floor(n/2) do 
        table[i],table[n-i+1]=table[n-i+1],table[i]
    end
end
--获取充值ID
function GetRechargeID(contentID)
    local rechargeCfg=gw_recharge_mgr.GetRechargeData(contentID)
    if rechargeCfg then 
        return rechargeCfg.rechargeId
    end
end

--充值完成回调事件，设置一个定时器，延时处理显示
function RechargeComplete() 
    if rechargeTime then
        util.RemoveDelayCall(rechargeTime)
        rechargeTime = nil
    end

    rechargeTime=util.DelayCallOnce(1,function()
        --local IsModuleShown = ui_window_mgr:IsModuleShown("ui_limit_gift_main")
        UpdateData()
        event.Trigger(limit_gift_define.RefreshPage) --刷新页面信息
        -- if CurpopUplist and #CurpopUplist > 0 then --当前显示数据
        --     if IsModuleShown then
        --         local CheckInsert = function(target, source)
        --             local existing = {}
        --             -- 初始化已存在元素的集合
        --             for _, value in ipairs(target) do
        --                 existing[value] = true
        --             end
        --             for _, value in ipairs(source) do
        --                 if not existing[value] then
        --                     table.insert(target, value)
        --                     existing[value] = true -- 记录新插入的元素
        --                 end
        --             end
        --         end
        --         --主要用于当前存在显示页面，
        --         if tempGroup and #tempGroup > 0 then --存储上一次显示的数据，加入到检查后的显示列表中
        --             CheckInsert(CurpopUplist, tempGroup) --检查重复元素
        --         end
                
        --     end
        -- else
        --     --print("MSG_RECHARGE_Limit_GIFT_DATA_NTF",ui_window_mgr:IsModuleShown("ui_limit_gift_main"))
            
        -- end
        event.Trigger(gw_event_activity_define.GW_LIMIT_GIFT_ICON_UPDATE) --入口刷新
        --rechargeState = false
        util.RemoveDelayCall(rechargeTime)
        rechargeTime=nil
    end)
end

--礼包数据刷新
function UpdateData() 
    for i,v in pairs(RechargeGiftData) do 
        for index,value in pairs(v) do 
            local RechargeData = gw_recharge_mgr.GetRechargeServerData(index)
            if RechargeData then 
                value =RechargeData
            end
        end
    end
    SetMainServerData() 
    CheckPopUpScene()
end
---@public 获取充值数据的index
---@param contentId number 充值ID
---@return number 
function GetContentServerDataIndex(contentId)
    local serverDataList = limitData.ServerData[limit_gift_define.CfgDataIndex.Main]
    if not serverDataList then
        return nil
    end
    local index = nil
    for i, v in ipairs(serverDataList) do
        if v.giftID == contentId then
            index = i
            break
        end
    end
    return index
end

--限时礼包充值回复响应
function MSG_RECHARGE_Limit_GIFT_DATA_NTF(msg) 
    local recharegeId = msg.rechargeId
    local giftType = msg.giftType
    --print("MSG_RECHARGE_Limit_GIFT_DATA_NTF",recharegeId,giftType,#CurpopUplist)
    --rechargeState=true
    event.Trigger(limit_gift_define.RechargeSucess) 
end

local MessageTable = {
    {xManMsg_pb.MSG_RECHARGE_GIFT_BUY_NTF, MSG_RECHARGE_Limit_GIFT_DATA_NTF, rechargeGift_pb.TMSG_RECHARGE_GIFT_BUY_NTF},
}
net_route.RegisterMsgHandlers(MessageTable)

--换服清理数据回调
function Clear()
    curMainAtyID,curSpecialAtyID=nil,nil
    InitData()
    CurpopUplist={} --当前页面需要弹出的礼包 （存储SpecialGIft的礼包ID）
    RechargeGiftData={}
    -- changeList={} --结束时间发生变化的数据
    chooseIndex =0
    rechargeTime = nil
    -- PopUpNum = 0
    TimerMgr = nil
    curShowIndex = 0
    poptimes = 0
    isLoginPop = false
    _specialGiftDataMap = {}
end

event.Register(event.USER_DATA_RESET, Clear)