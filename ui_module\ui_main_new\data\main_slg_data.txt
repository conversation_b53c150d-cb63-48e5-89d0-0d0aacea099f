local require = require
local ReviewingUtil = require "ReviewingUtil"
local lang = require "lang"
local sand_ui_event_define = require "sand_ui_event_define"
local event = require "event"
local main_slg_const = require("main_slg_const")
local main_slg_common_define = require("main_slg_common_define")
local festival_activity_cfg = require("festival_activity_cfg")
local gw_const = require("gw_const")
local red_const = require("red_const")
local table_util = require("table_util")
local type = type
local xpcall = xpcall
local log = log
local ipairs = ipairs
local table = table
local util = require "util"

local M = {}

util.RegisterConsole("main_show_state", 0, function(st)
    if st == 1 then
        dump(M.groupData)
    end
end)

function M.Init()
    --M.curSceneType = gw_const.ESceneType.None
    M.buttonConfig = {}
    M.showState = main_slg_const.MainShowState.None
    --进入试炼界面后如果有跳转记录跳转，结束时需要打开试炼界面
    M.experimentRecord = 0
end
--region 设置场景

function M.GetCurSceneType()
    if GWG.GWMgr.openSceneFsm then
        return GWG.GWMgr.curScene
    end
    return M.curSceneType
end

function M.SetCurSceneType(sceneType)
    if not GWG.GWMgr.openSceneFsm then
        M.curSceneType = sceneType
    end
end

function M.GetIsSandScene()
    local gw_common_util = require "gw_common_util"
    return gw_common_util.CheckInSand_All()
end

--endregion

--region 设置主界面显示状态


function M.GetCloseState()
    return M.showState
end

function M.SetMainState(state)
    if M.showState ~= state then
        local oldState = M.showState
        M.showState = state
        event.Trigger(sand_ui_event_define.GW_MAIN_SHOW_STATE_CHANGE, oldState, state)
    end
end

--endregion


--region 试炼界面数据

---@description 获取当前的在哪个试炼
function M.GetCurExperimentRecord()
    return M.experimentRecord
end

---@description 设置当前的在哪个试炼,用于结束试炼返回试炼主界面
function M.SetCurExperimentRecord(record)
    M.experimentRecord = record
end

---@description 是否在试炼中
function M.IsInExperiment()
    return M.experimentRecord > 0
end

--endregion

--region 按钮数据


--[[
    {
        buttonType = MainButtonType.build,
        groupType = MainButtonGroupType.LeftTop,
        icon = "zjm_icon_tshd",
        text = "活动",
        clickFunc = function()
            --打开活动
        end,
        isHide = false,
        redFunc = function()
            return false
        end,    
        unlockFunc = function()
            return true
        end,
            
    }
]]
---@description key是groupType,value是列表,存放按钮数据,通过buttonType来做索引
M.groupData = {}

---@description 根据按钮类型获取按钮数据
function M.GetButtonDataByType(buttonType)
    local configData = main_slg_common_define.buttonConfig[buttonType]
    if configData and configData.groupType then
        return M.GetButtonDataByGroupTypeAndType(configData.groupType, buttonType)
    else
        for _, v in pairs(M.groupData) do
            for _, v2 in ipairs(v) do
                if v2.buttonType == buttonType then
                    return v2
                end
            end
        end
    end
end


---@description 根据按钮组类型获取按钮数据,没有则新建插入groupData
---@param groupType number 按钮组类型
---@param buttonType number 按钮类型
---@param notAutoCreate boolean 是否自动创建  默认会自动创建
---@return table 按钮数据
---@return boolean 是否第一次创建
function M.GetButtonDataByGroupTypeAndType(groupType, buttonType,notAutoCreate)
    M.groupData[groupType] = M.groupData[groupType] or {}
    local data = M.groupData[groupType]
    for _, v in ipairs(data) do
        if v.buttonType == buttonType then
            return v
        end
    end
    if notAutoCreate then
        return
    end
    --没有就创建一个
    local newData = {}
    newData.buttonType = buttonType
    table.insert(data, newData)
    return newData, true
end

--移除一个不符合要求的按钮
function M.RemoveButtonDataByGroupTypeAndType(groupType, buttonType)
    if M.groupData[groupType] then
        for i,v in ipairs(M.groupData[groupType]) do
            if v.buttonType == buttonType then
                table.remove(M.groupData[groupType],i)
                return
            end
        end
    end
end

---@public 设置对应类型groupButtonType的数据
function M.SetGroupButtonTypeData(type, data)
    if main_slg_common_define.groupButtonType[type] then
        main_slg_common_define.groupButtonType[type] = data
    end
end

---@public 创建一个活动相关的入口按钮数据
function M.CreateActivityEntranceButtonTypeData(buttonType)
    if type(buttonType) ~= 'number' then
        return
    end
    local offset = main_slg_common_define.ActivityQuickEntranceOffset
    local isEntrance = buttonType < offset
    local icon
    local cfg
    if isEntrance then
        cfg = festival_activity_cfg.GetActivityEntranceCfg(buttonType)
        if not cfg then
            log.Error("没有找到活动入口配置", buttonType)
            return
        end

    else
        cfg = festival_activity_cfg.GetActivityEntranceCfg(buttonType - offset)
        if not cfg then
            log.Error("没有找到活动快捷入口配置", buttonType)
            return
        end
    end
    icon = cfg.buttonRect
    local reallyButtonType = isEntrance and buttonType or buttonType - offset
    local targetConfigData
    if main_slg_common_define.customActivityButtonConfig[reallyButtonType] then
        targetConfigData = main_slg_common_define.customActivityButtonConfig[reallyButtonType]
        targetConfigData.__refreshFunction = function(para1,param2)
            event.Trigger(sand_ui_event_define.GW_MAIN_REFRESH_BUTTON, targetConfigData.buttonType, para1, param2)
        end
        if targetConfigData.refreshMsg then
            M.RegisterEventByButtonData(targetConfigData, targetConfigData.__refreshFunction)
        end
    else
        if isEntrance then
            targetConfigData = main_slg_common_define.customActivityButtonConfig["ActivityCommon"]
        else
            targetConfigData = main_slg_common_define.customActivityButtonConfig["ActivityCommonQuickEntrance"]
        end
    end
    --增加判断按钮是否显示
    if targetConfigData and targetConfigData.conditionShowFunc then
        local check, result = xpcall(targetConfigData.conditionShowFunc,
                function(err)
                    log.Error("主界面按钮的显示添加判断出现错误",  err)
                end)
        if check and (not ReviewingUtil.IsReviewing() or main_slg_const.ReviewingMainShowType[buttonType])  then
            if not result then
                return
            end
        else
            return
        end
    end
    
    local configData = {}
    configData.entranceID = cfg.AtyEntrance
    configData.isEntrance = isEntrance  --表示是否是普通活动入口 活动入口/快捷入口
    configData.buttonType = buttonType
    configData.icon = targetConfigData.icon or icon
    configData.ShowEffect = cfg.ShowEffect or targetConfigData.ShowEffect
    if targetConfigData.ShowEffectList then
        configData.ShowEffectList = targetConfigData.ShowEffectList
    end

    if targetConfigData.checkDataFunc then
        configData.checkDataFunc = targetConfigData.checkDataFunc
    end

    if targetConfigData.ShowEffectCheckFunc then
        configData.ShowEffectCheckFunc = targetConfigData.ShowEffectCheckFunc
    end

    if targetConfigData.getTextTimerFunc then
        configData.getTextTimerFunc = targetConfigData.getTextTimerFunc
    end
    configData.getTextFunc = targetConfigData.getTextFunc or function()
        --获取文本
        return lang.Get(cfg.buttonName)
    end
    if buttonType == festival_activity_cfg.ActivityEntranceType.SevenDayLogin then
        --七日登录
        configData.timeStr = lang.Get(cfg.buttonName).."\n%s"
    end
    local atyBubbleData = {}
    local ui_festival_activity_mgr = require("festival_activity_mgr")
    local atyList = ui_festival_activity_mgr.GetAllActivityByEntrance(cfg.AtyEntrance,false)
    for i,v in ipairs(atyList) do
        if v.bubble and v.bubble ~= 0 and main_slg_common_define.activityBubbleCheckFunc[v.bubble] then
            local value =
            {
                checkFunc = main_slg_common_define.activityBubbleCheckFunc[v.bubble].checkFunc,
                value = v,
            }
            table.insert(atyBubbleData,value)
        end
    end
    table.sort(atyBubbleData,function(a, b)
        local game_scheme = require "game_scheme"
        local bubbleA = game_scheme:Bubble_0(a.value.bubble)
        local bubbleB = game_scheme:Bubble_0(b.value.bubble)
        return bubbleA.Priority < bubbleB.Priority
    end)
    local main_slg_tips_mgr = require "main_slg_tips_mgr"
    for i,v in ipairs(atyBubbleData) do
        main_slg_tips_mgr.AddActivityLoginTips(v)
    end
    
    configData.redData = {}
    if targetConfigData.redData then
        configData.redData = table_util.Copy(configData.redData, targetConfigData.redData)
    end
    configData.atyBubbleData = atyBubbleData
	-- 优先用配置里的红点事件
    configData.redData.redType = targetConfigData.redData and (targetConfigData.redData.redType) or (red_const.Enum.ActivityEntrance..(reallyButtonType or 0)) 
    if targetConfigData.redData and targetConfigData.redData.redPathType then
        configData.redData.redPathType = targetConfigData.redData.redPathType or red_const.Type.Default
    else
        configData.redData.redPathType = isEntrance and red_const.Type.Num or red_const.Type.Default --红点类型
    end
    
    configData.redData.funcParam = { reallyButtonType, not isEntrance }--设置入口
    configData.redData.isActivityEntrance = true

    configData.clickFunc = targetConfigData.clickFunc or function(cData)
        
        if cData.isEntrance then
            ui_festival_activity_mgr.OpenActivityUIByEntranceID(cData.buttonType)
        else
            ui_festival_activity_mgr.OpenActivityUIByQuickEntranceID(cData.buttonType - main_slg_common_define.ActivityQuickEntranceOffset)
        end
    end    
    configData.conditionShowFunc = targetConfigData.conditionShowFunc
    
    if targetConfigData.countdownTimeCfg then
        configData.countdownTimeCfg = targetConfigData.countdownTimeCfg
        configData.countdownTimeCfg.buttonType = targetConfigData.countdownTimeCfg.buttonType or reallyButtonType
        configData.countdownTimeCfg.timeStamp = targetConfigData.countdownTimeCfg.timeStamp
    end
    if targetConfigData.showLockState then
        configData.lockState=targetConfigData.showLockState
    end
    if targetConfigData.curIconState then
        configData.curIconStateCfg=targetConfigData.curIconState
        configData.curIconStateCfg.State=targetConfigData.curIconState.State
        configData.curIconStateCfg.StateName=targetConfigData.curIconState.StateName
        configData.curIconStateCfg.RewardNum=targetConfigData.curIconState.RewardNum
    end

    if targetConfigData.showIcon then 
        configData.loopIcon = targetConfigData.showIcon
    end
    -- if targetConfigData.changIconCD then 
    --     configData.changIconCD = targetConfigData.changIconCD
    -- end
    --累充活动的图标设置
    if targetConfigData.curEnterIcon then
        configData.icon = targetConfigData.curEnterIcon() or configData.icon
    end
    --渐隐动画
    -- if targetConfigData.hasAnimate then 
    --     configData.hasAnimate = targetConfigData.hasAnimate
    -- end 
    return configData
end

---根据按钮配置数据重置按钮数据
function M.ResetButtonData(buttonData)

    if not buttonData then
        return
    end
    local configData = buttonData.configData
    --设置文本
    if configData.getTextFunc then
        buttonData.text = configData.getTextFunc()
    end

    --每秒设置文本的配置
    if configData.getTextTimerFunc then
        buttonData.textTimeFunc = configData.getTextTimerFunc
    end
    
    --设置所有属性
    for k, v in pairs(configData) do
        buttonData[k] = v
    end

    --设置是否显示
    local sceneShowType = main_slg_common_define.sceneOnlyData[buttonData.buttonType]
    local showFunc = function()
        if configData.conditionShowFunc then
            local isShow = false
            local check, result = xpcall(configData.conditionShowFunc,
                    function(err)
                        log.Error("主界面按钮的显示添加判断出现错误",  err)
                    end)
            if check and (not ReviewingUtil.IsReviewing() or main_slg_const.ReviewingMainShowType[buttonData.buttonType]) then
                isShow =  result
            else
                isShow = false
            end
            buttonData.isHide = not isShow
        else
            buttonData.isHide = false
        end
    end
    if M.GetCurSceneType() ~= gw_const.ESceneType.Truck then
        if sceneShowType then
            if not sceneShowType[main_slg_const.MainSceneType.CitySceneOnly] and M.GetCurSceneType() == gw_const.ESceneType.Home then
                buttonData.isHide = true
            elseif not sceneShowType[main_slg_const.MainSceneType.SandSceneOnly] and M.GetCurSceneType() == gw_const.ESceneType.Sand then--gw_common_util.CheckInSand_All() then
                buttonData.isHide = true
            elseif not sceneShowType[main_slg_const.MainSceneType.DesertStormSceneOnly] and M.GetCurSceneType() == gw_const.ESceneType.Storm then
                buttonData.isHide = true
            else
                showFunc()
            end
        else
            if M.GetCurSceneType() ~= gw_const.ESceneType.Storm then
                showFunc()
            else
                buttonData.isHide = true
            end
        end
    else
        buttonData.isHide = true
    end
    
    if configData.bubbleData then
        M.SetBubbleData(buttonData, configData.bubbleData)
    end

end

---description 返回刷新后的按钮组列表数据
function M.GetButtonDataListByGroupType(buttonGroupType)
    local dataList = main_slg_common_define.groupButtonType[buttonGroupType] or {}
    if buttonGroupType == main_slg_const.MainButtonGroupType.RightTop or 
    buttonGroupType == main_slg_const.MainButtonGroupType.RightTop2  or 
    buttonGroupType == main_slg_const.MainButtonGroupType.RightTop3 then
       --先判断一下有没有变动。有变动重新生成
        local group = M.groupData[buttonGroupType]
        local  isSame =  group and  (#dataList == #group)
        if  isSame then
            for i, v in ipairs(dataList) do
                if group[i] and  v ~= group[i].buttonType then
                    isSame = false
                    break
                end
            end
        end
        if not isSame then
            M.groupData[buttonGroupType] = {}
        end        
    end
    for _, buttonType in ipairs(dataList) do
        local configData = main_slg_common_define.buttonConfig[buttonType]
        if not configData and (
            buttonGroupType == main_slg_const.MainButtonGroupType.RightTop or 
            buttonGroupType == main_slg_const.MainButtonGroupType.RightTop2 or 
            buttonGroupType == main_slg_const.MainButtonGroupType.RightTop3 or 
            buttonGroupType == main_slg_const.MainButtonGroupType.LeftBottom2
        ) then
            --如果是右上的活动入口，new一个动态的进来
            configData = M.CreateActivityEntranceButtonTypeData(buttonType)
            M.buttonConfig[buttonType] = configData
        end
        if configData then
            local buttonData, isFirst = M.GetButtonDataByGroupTypeAndType(buttonGroupType, buttonType)
            configData.groupType = buttonGroupType
            buttonData.groupType = buttonGroupType
            buttonData.configData = configData
            M.ResetButtonData(buttonData)
            if isFirst then
                M.InitButtonData(buttonData)
            end
        else
            M.RemoveButtonDataByGroupTypeAndType(buttonGroupType,buttonType)
        end
    end

    return M.groupData[buttonGroupType]
end

function M.GetButtonConfigData(buttonType)
    return main_slg_common_define.buttonConfig[buttonType] or M.buttonConfig[buttonType]
end

function M.RegisterEventByButtonData(buttonData, __refreshFunction)
    if not buttonData then
        return
    end
    if type(buttonData.refreshMsg) == "string" then
        event.Register(buttonData.refreshMsg, __refreshFunction)
    elseif type(buttonData.refreshMsg) == "table" then
        for _, v in ipairs(buttonData.refreshMsg) do
            event.Register(v, __refreshFunction)
        end
    end
end

---@description 第一次创建初始化，监听刷新事件
function M.InitButtonData(buttonData)
    if buttonData then
        if not buttonData.refreshMsg and not buttonData.bubbleData then
            return
        end
        buttonData.__refreshFunction = function(para1,param2)
            event.Trigger(sand_ui_event_define.GW_MAIN_REFRESH_BUTTON, buttonData.buttonType, para1, param2)
        end
        if buttonData.refreshMsg then
            M.RegisterEventByButtonData(buttonData, buttonData.__refreshFunction)
        end
        if buttonData.bubbleData then
            for _, buttonType in ipairs(buttonData.bubbleData) do
                local bubbleButtonData = main_slg_common_define.buttonConfig[buttonType]
                if bubbleButtonData then
                    M.RegisterEventByButtonData(bubbleButtonData, buttonData.__refreshFunction)
                end
            end
        end
    end

end

---@description 根据气泡配置数据，遍历设置需要显示的按钮气泡
---@param buttonData table 按钮数据
---@param bubbleData table 气泡数据，内容为按钮类型列表
function M.SetBubbleData(buttonData, bubbleData)
    buttonData.bubbleClickFunc = nil
    buttonData.bubbleImg = nil
    for _, buttonType in ipairs(bubbleData) do
        local bubbleButtonData = main_slg_common_define.buttonConfig[buttonType]
        if bubbleButtonData then

            --设置气泡的点击事件，气泡icon
            if bubbleButtonData.conditionShowFunc then
                if bubbleButtonData.conditionShowFunc() then
                    if not ReviewingUtil.IsReviewing() or main_slg_const.ReviewingMainShowType[buttonType] then
                        buttonData.bubbleImg = bubbleButtonData.icon
                        buttonData.bubbleClickFunc = bubbleButtonData.clickFunc
                        buttonData.loadBubbleIconFunc = bubbleButtonData.loadIconFunc
                        buttonData.isAnimation = bubbleButtonData.isAnimation
                        break
                    end
                end
            end
        end
    end
end

function M.Reset()
    M.Init()
end

---获取主界面左上角数据
function M.GetMainLeftTopData()
    return M.GetButtonDataListByGroupType(main_slg_const.MainButtonGroupType.LeftTop)
end
---@description 获取主界面左下角数据
function M.GetMainLeftBottomData()
    return M.GetButtonDataListByGroupType(main_slg_const.MainButtonGroupType.LeftBottom)
end
----@description 获取主界面左下角数据2
function M.GetMainLeftBottom2Data()
    return M.GetButtonDataListByGroupType(main_slg_const.MainButtonGroupType.LeftBottom2)
end
----@description 获取主界面左下角数据4
function M.GetMainLeftBottom4Data()
    return M.GetButtonDataListByGroupType(main_slg_const.MainButtonGroupType.LeftBottom4)
end
----@description 获取主界面右上角数据
function M.GetMainRightTopData()
    return M.GetButtonDataListByGroupType(main_slg_const.MainButtonGroupType.RightTop)
end
----@description 获取主界面右下角数据
function M.GetMainRightBottomData()
    return M.GetButtonDataListByGroupType(main_slg_const.MainButtonGroupType.RightBottom)
end

function M.GetMainBottomData()
    return M.GetButtonDataListByGroupType(main_slg_const.MainButtonGroupType.bottom)
end
--endregion

--region 聊天气泡数据

---@field ShowChatBubbleData table 当前显示的气泡数据
M.ShowChatBubbleData = {}

---@public InitChatBubbleData 初始化聊天气泡数据
function M.InitChatBubbleData()
    local chatData = main_slg_common_define.chatBulletConfig
    for i, v in pairs(chatData) do
        v.__refreshFunction = function()
            event.Trigger(sand_ui_event_define.GW_MAIN_CHAT_BUBBLE_REFRESH, v)
        end
        event.Register(v.refreshMsg, v.__refreshFunction)
        M.JudgeChatBubbleIsShow(v)
    end
end

---@function GetChatBubbleData 获取聊天气泡数据 (界面初始化时要主动获取一次数据，初始化时没有触发更新事件)
---@return table 返回聊天显示数据
function M.GetChatBubbleData()
    return M.ShowChatBubbleData
end

---@public JudgeChatBubbleIsShow 判断聊天气泡是否展示
function M.JudgeChatBubbleIsShow(chatBubbleData)
    if chatBubbleData.conditionShowFunc then
        --将要显示的气泡加入队列
        local isShow = chatBubbleData.conditionShowFunc()
        if isShow then
            M.ShowChatBubbleData[chatBubbleData.bubbleType] = chatBubbleData
        else
            M.ShowChatBubbleData[chatBubbleData.bubbleType] = nil
        end

        event.Trigger(sand_ui_event_define.GW_MAIN_CHAT_BUBBLE_SHOW)
    end
end

--endregion


M.Init()

return M