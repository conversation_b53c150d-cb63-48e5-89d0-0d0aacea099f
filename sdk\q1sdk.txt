local math = math
local os = os
local pairs = pairs
local require = require
local print = print
local type = type
local tostring = tostring
local string = string
local tonumber = tonumber
local game_config = require "game_config"
local channel_config = require "channel_config"
local event = require "event"
local web_view = require "web_view"
local log = require "login_log"
local dkjson = require "dkjson"
local firebase = require "firebase"
local adjust = require "adjust"
local facebook = require 'facebook'
local util = require "util"
local game_scheme = require "game_scheme"
local version_mgr = require "version_mgr"
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local GameObject = CS.UnityEngine.GameObject
local Resources = CS.UnityEngine.Resources
local Utility = CS.War.Script.Utility
local Application = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local Q1_LoginErrorCode = CS.Q1.LoginErrorCode
local NetworkReachability = CS.UnityEngine.NetworkReachability
local Time = CS.UnityEngine.Time
local LogDebug = CS.War.Base.LogDebug
local const = require "const" 
local build_diff_setting = require "build_diff_setting"

local q1sdk = {}
local hasReportOffline = false
local savedInfo = {}
local userInfoSet = {}
local sdkLoginTimeOutTimer = nil
local sdkLoginTimeOutInterval = 20 -- 秒
local s_sdkLoginUUID = 0
local sdkLoginInfo = 
{
    sdkLoginUUID = s_sdkLoginUUID,
    triggerTimes = 0,
}
local hasU8Init = false
local hasGooglePointDataInit = false

local nothing = function() return false end
local userBindType = {}

local textAsset = Resources.Load("distrib_channels")
local allChannels, pos, err = dkjson.decode(textAsset.text, 1, nil)

local localAIhelpUserInfo = {}

local AddCachedUserInfo = function (serverId, roleID, roleName, roleLevel, userID)
    if userInfoSet[roleID] ~= nil then
        return
    end
    userInfoSet[roleID] = {serverId=serverId, roleID=roleID, roleName=roleName, roleLevel=roleLevel, userID=userID}
end

local GetUserInfo = function(roleID)
    if roleID and userInfoSet and userInfoSet[roleID] then
        return userInfoSet[roleID]
    end
end

local CheckValidUserInfo = function (serverId, roleID, roleName, roleLevel, userID)
    -- 数数科技出现 serverId 与 玩家 id 不对应的问题，增加检测，方便定位问题
    local userInfo = userInfoSet[roleID]
    if userInfo and userInfo.serverId ~= nil and serverId ~= userInfo.serverId then
        log.Error("EventReport 切换的玩家服务器数据与缓存不一致")
        log.Warning("EventReport serverId:", serverId, ", roleID:", roleID, ", roleName:", roleName, ", roleLevel:", roleLevel, ", userID:", userID)
        log.Warning("EventReport cached serverId:", userInfo.serverId, ", roleID:", userInfo.roleID, ", roleName:", userInfo.roleName, 
            ", roleLevel:", userInfo.roleLevel, ", userID:", userInfo.userID)
    end
end

-- 获取当前 SDK 登录唯一标识
local GetSDKLoginInfo = function ()
    return sdkLoginInfo
end

-- 生成一次新 SDK 登录，用于区分不同的 sdk 登录调用
local PeekSDKLoginInfo = function ()
    s_sdkLoginUUID = s_sdkLoginUUID + 1
    sdkLoginInfo =
    {
        sdkLoginUUID = s_sdkLoginUUID,
        triggerTimes = 0,
    }
    return sdkLoginInfo
end

if not game_config.ENABLE_Q1SDK then
    q1sdk.Login = function(callback)
        --print("GooglePlay not enable")
    end
    q1sdk.LoginTimeOut = function() end
    q1sdk.Logout = function()
        --print("GooglePlay not enable")
    end

    q1sdk.LogoutLogic = function ()
    end

    q1sdk.clearCache = function ()
    	return false
    end
    q1sdk.SetLogcat = function(on) 
    end
    q1sdk.IsEnable = function()
        return false
    end
    
    q1sdk.Pay = function(serverID, userId, payNum, orderItem, orderNO, currencyType, orderSign, noLoginPay, level, callback, rechageID, payUrl)
        print('not supported!!!')
    end

    q1sdk.IsSupportAppleSignin = function ()
        return false
    end

    q1sdk.GetUUID = function()
        return ''
    end

    q1sdk.GetUserInfo = function()
        return ''
    end
	
	q1sdk.GetImeiMD5 = function()
        return ''
    end
	
	q1sdk.GetRadid = function()
        return ''
    end
	
	q1sdk.GetRsid = function()
        return ''
    end

    q1sdk.UserEvent = function(serverId, roleId, roleName, roleLevel, user, action, msg)
        -- do nothing
    end

    q1sdk.ReportGameLogin = function (roleId, roleName, roleLevel, serverId)
    end

    q1sdk.QueryGoogleProductInfoById = function (id,callback)
    end

    q1sdk.ReportOldRoleOnlineTime = function ()
    end

    q1sdk.CheckDevicePermission = function(permissionStr)
        return false
    end
    q1sdk.UserBind = function(platform, callBack)
        
    end
    q1sdk.Visitor = function()
        return false
    end
    -- q1sdk.CurrentUser = function()
    --     return nil
    -- end

    q1sdk.OneKeyLogin = nothing
    q1sdk.StartEvent = nothing
    q1sdk.ExitApplication = nothing
    q1sdk.SessionValid = nothing
    q1sdk.LoginWithBind = nothing
    q1sdk.PseudonymSDK = nothing
	q1sdk.TrackCreateRole = nothing
    q1sdk.OpenUrlLoading = nothing
    q1sdk.OpenLink = nothing
    q1sdk.ApplicationOpenURL = function(url)
        if CS.UnityEngine.Application.isEditor then   --for pc debug easy
            CS.UnityEngine.Application.OpenURL(url)
        end
    end
	-- 设置区id
	q1sdk.SetRegion = nothing
    -- 设置 SDK 语言
    q1sdk.SetLanguage = nothing
	-- 角色登录
	q1sdk.StartTrack = nothing
	-- 角色登出
	q1sdk.EndTrack = nothing
	q1sdk.EndTrackWithoutEvent = nothing
	-- 充值成功
	q1sdk.TrackUpdateTotalRevenue = nothing
	-- 改名成功
	q1sdk.TrackUpdateName = nothing
	-- 角色升级
    q1sdk.TrackLevelUp = nothing
	-- 角色升级
	q1sdk.TrackVipLevelUp = nothing
	q1sdk.TrackEvent = nothing
	q1sdk.UserSetOnce = nothing
	q1sdk.UserSetOnceSingle = nothing
    q1sdk.SetContext = nothing
    q1sdk.SwitchWorld = nothing
	q1sdk.UserSet = nothing
	q1sdk.UserSetSingle = nothing
	q1sdk.UserAdd = nothing
    q1sdk.UserAddSingle = nothing
    q1sdk.SetSysMemSizeProperties = nothing
    q1sdk.ShareFacebook = function()
        print("q1 sdk is not supported")
        return true
    end
    q1sdk.GetCurrentUser = function()
        print("q1 sdk is not supported")
    end
    q1sdk.GotoAppSetting = nothing

    q1sdk.SwitchAccount = nothing

    q1sdk.trackUserLoginSessionError = nothing

    q1sdk.trackUserLogin = nothing

    q1sdk.trackSelectServer = nothing

    q1sdk.reportPurchase = nothing

    q1sdk.SetSuperProperties = nothing

    q1sdk.RequestCaptchaForBinding = nothing

    q1sdk.ConfirmBindingCaptcha = nothing

    q1sdk.QueryBindingPhone = nothing

    q1sdk.GetAppProperty = function (key, defaultValue, filedName, propertyType)
        return defaultValue
    end

    q1sdk.TrackPayBegin = nothing
    q1sdk.U8SubmitExtraData = nothing
    q1sdk.U8ExitGame = nothing
    q1sdk.getPackageName = nothing
    q1sdk.InitU8Callback = nothing
    q1sdk.getExtParams = nothing

    -- 海外版新增接口
    q1sdk.GetLocalRegionID = function ()
        return game_config.REGION_ID
    end

    q1sdk.SaveLocalRegionID = nothing
    q1sdk.SetIsChannel = nothing
    q1sdk.ChannelSwitchAccount = nothing
    q1sdk.ChannelBindAccount = nothing
    q1sdk.ChannelCustomer = nothing
    q1sdk.OpenUrl = function(url)
        if CS.UnityEngine.Application.isEditor then   --for pc debug easy
            CS.UnityEngine.q1sdk.ApplicationOpenURL(url)
        end
    end
    q1sdk.OpenWebUrl = function (url) 
        q1sdk.OpenUrl(url)
    end
    q1sdk.OpenH5Url = nothing
    q1sdk.AIHelpInit = function (appKey, domain, appId)
        print("q1 sdk is not supported")
    end
    -- 未读的消息 通知显示红点
    q1sdk.AIHelpUnReadMsgListen = function (userId)
        print("q1 sdk is not supported")
    end
    -- 跟进类型显示对话 0-默认系统 1-机器人和可以手动联系客服 2-直接响应人工客服
    q1sdk.AIHelpShowConversation = function(type, storyNodeUserSay, welcomeHelp)
        print("q1 sdk is not supported")
    end   
    q1sdk.AIHelpSetUserId = nothing

    q1sdk.AIHelpLogin = nothing
    q1sdk.AIHelpInfoUpdate = nothing
    q1sdk.AIHelpSetLanguage = nothing
    q1sdk.AIHelpHelpEnableDebug = nothing
    q1sdk.AIHelpSetUploadLogPath = nothing
    q1sdk.ChannelReportRoleInfo = nothing
    q1sdk.ChannelSDKReport = nothing
    q1sdk.ChannelFirebaseAndOtherReport = nothing
    
    q1sdk.QuerySkuDetailsAsync = nothing
    q1sdk.SetProductIds = nothing
    q1sdk.GetCachedProductsWithProductIds = nothing
    
    q1sdk.LogoutAccountAction = nothing   

    q1sdk.GetProductIds = nothing
    q1sdk.RequestGooglePoints = nothing
    q1sdk.RegisterOnConfigurationChanged = nothing
    q1sdk.RegisterOnGooglePointData = nothing
    q1sdk.IsV3 = nothing
    q1sdk.OpenProlicy = nothing
    q1sdk.OpenCMP = nothing
    q1sdk.SaveImageToGallery = nothing
    q1sdk.HasNewSaveImageFunction = nothing
    q1sdk.UploadImage = nothing
    q1sdk.GetImageReviewStatus = nothing

    q1sdk.GetPushNotifiState = nothing
    q1sdk.OpenNotificationSetting = nothing
    q1sdk.CMPFirebaseReport = nothing
else
    local Q1SDK = CS.Q1.Q1SDK

    q1sdk.QuerySkuDetailsAsync = function(goodsIdList)
        -- 安卓查询sku接口
        if Q1SDK.Instance.QuerySkuDetailsAsync then
            Q1SDK.Instance:QuerySkuDetailsAsync(goodsIdList)
        end
    end

    q1sdk.SetProductIds = function(goodsIdList,callback)
        print("zzd____goodsIdList",goodsIdList)
        if Q1SDK.Instance.SetProductIds then
            print("zzd____Q1SDK.Instance:SetProductIds()  exist ")
            Q1SDK.Instance:SetProductIds(goodsIdList,callback)
        else
            print("zzd____Q1SDK.Instance:SetProductIds()  not exist")
        end
    end

    q1sdk.GetCachedProductsWithProductIds = function(productsIdList)
        local isAndroid = Application.platform == RuntimePlatform.Android
        if isAndroid then
            if Q1SDK.Instance and Q1SDK.Instance.GetCachedProductsWithProductIds then
                print("zzd____Q1SDK.Instance:GetCachedProductsWithProductIds()  exist ")
                Q1SDK.Instance:GetCachedProductsWithProductIds(productsIdList)
            else
                print("zzd____Q1SDK.Instance:GetCachedProductsWithProductIds()  not exist")
            end
        end
    end

    --判断是否是V3版本
    q1sdk.IsV3 = function()
        if Q1SDK.Instance.IsV3 then
            return Q1SDK.Instance:IsV3()
        end
        return false
    end

    -- 渠道sdk新增authCallback, u8Callback实名认证和U8的相关回调
    q1sdk.Login = function(callback, way)
        event.RecodeTrigger("login_start", 
        {
            login_way = way
        })
        log.Warning('login:' .. way..",internetReachability:"..tostring(Application.internetReachability))
        if Application.internetReachability == NetworkReachability.NotReachable then
            if callback ~= nil then
                callback(Q1_LoginErrorCode.Cancel, "Internet not reachable", "", "", "")
            end
            return
        end

        --新包有谷歌积分监听回调函数
        q1sdk.RegisterOnGooglePointData(function()
            local google_point_data = require "google_point_data"
            google_point_data.ShowGooglePoint()
        end)

        q1sdk.ChannelInitCallback()
        q1sdk.StopLoginTimerOutTimer()

        local isThirdLogin = false
        if build_diff_setting.GetIsThirdLogin() and Q1SDK.Instance.ThirdLogin then
            isThirdLogin = true
        end
        log.Warning("isThirdLogin",isThirdLogin,Q1SDK.Instance.ThirdLogin,"way",way)

        if way == "guestLogin" and not isThirdLogin then
            -- 获取当次登录 UUID，闭包保存用于标识当次登录
            local currentLoginInfo = PeekSDKLoginInfo()
            local startLoginTime = Time.realtimeSinceStartup

            log.Warning("start login timer out timer:", sdkLoginTimeOutInterval)

            sdkLoginTimeOutTimer = util.DelayCallOnce(sdkLoginTimeOutInterval, function ( ... )
                q1sdk.LoginTimeOut(currentLoginInfo, startLoginTime, callback, ...)
            end)
            Q1SDK.Instance:Login(function( ... )
                q1sdk.LoginCallBack(currentLoginInfo, startLoginTime, callback, ...)
            end, way)
        elseif isThirdLogin then
            Q1SDK.Instance:ThirdLogin(callback)
        else
            if _G["_STOP_LOGIN"] then
                log.Warning("_STOP_LOGIN",true)
                local prepareFunc = function ()
                    q1sdk.LoginNew(callback, way)
                end
                    
                event.Trigger("CHECK_TRIGGER_LOGIN",prepareFunc)
            else
                q1sdk.LoginNew(callback, way)
            end
        end
    end

    q1sdk.LoginNew = function (callback, way)
        local net_login_module = require "net_login_module"
            q1sdk.UserEvent(0, 0, "null", 0, "null", "loginBegin", "")
            event.RecodeTrigger("login_sdk_start", 
            {
                login_way = way
            })
            log.LoginWarning("开始sdk登录")
            event.Trigger(event.RECORD_LOGIN_COST_TIME_POINT,"CostTime_开始sdk登录")
            if not game_config.ENABLE_Q1SDK_CHANNEL then
                Q1SDK.Instance:Login(callback, way)
            else
                q1sdk.InitU8Callback()
                Q1SDK.Instance:Login(callback, way, function (age)
                    net_login_module.SetUserAge(age)
                end, function (success, code, msg, tokenData)
                    local strData = dkjson.decode(tokenData)
                    local data = {}
                    data.userID = tostring(strData["userID"])
                    data.sdkUserID = strData["sdkUserID"]
                    data.username = strData["username"]
                    data.sdkUserNam = strData["sdkUserNam"]
                    data.timestamp = strData["timestamp"]
                    data.extension = strData["extension"]
                    data.session = strData["token"]
                    net_login_module.SetU8UserInfo(data)
                end)
            end
    end

    -- 国内版，非自定义界面，切换账号
    q1sdk.SwitchAccount = function (callback, way)
        if not game_config.Q1SDK_DOMESTIC or util.ShouldUseCustomSDKUI() then
            return
        end

        if not game_config.ENABLE_Q1SDK_CHANNEL then
            Q1SDK.Instance:Login(callback, way)
        else
            q1sdk.Logout()
            q1sdk.LoginNew(callback, "login")
        end
        
    end

    q1sdk.StopLoginTimerOutTimer = function()
        if sdkLoginTimeOutTimer  then
            log.Warning("stop login timer out timer")
            util.RemoveDelayCall(sdkLoginTimeOutTimer)
            sdkLoginTimeOutTimer = nil
        end
    end

    -- 初始化
    q1sdk.AIHelpInit = function (appKey, domain, appId)
        print("AIHelpInit lua ",appKey,domain,appId)
        if game_config.ENABLE_AIHELP == false or game_config.Q1SDK_DOMESTIC then
            return
        end
        if Q1SDK.Instance.AIHelpInit ~= nil then
            Q1SDK.Instance:AIHelpInit(appKey,domain,appId,function ()  
                log.Warning("AIHelp init complete")        
            end,function (unReadMsgCount)
                local game_config = require "game_config"
                if game_config.ENABLE_AIHELP then
                    local aihelp = require "aihelp"
                    aihelp.SetUnReadMsgCount(unReadMsgCount)   
                end
            end)
        end  
        
    end

    q1sdk.AIHelpSetUserId = function (userId)
        if game_config.ENABLE_AIHELP == false or game_config.Q1SDK_DOMESTIC then
            return
        end
        log.Warning("AIHelp AIHelpSetUserId:", userId)
        if Q1SDK.Instance.AIHelpUpdateUserInfo ~= nil then
            Q1SDK.Instance:AIHelpUpdateUserInfo(userId)
        end  
        q1sdk.AIHelpUnReadMsgListen(userId)
    end

    -- 未读的消息 通知显示红点
    q1sdk.AIHelpUnReadMsgListen = function (userId)
        if game_config.ENABLE_AIHELP == false or game_config.Q1SDK_DOMESTIC then
            return
        end
        if Q1SDK.Instance.AIHelpUnReadMsgListen ~= nil then
            Q1SDK.Instance:AIHelpUnReadMsgListen(userId)
        end  
    end

    -- 跟进类型显示对话 0-默认系统 1-机器人和可以手动联系客服 2-直接响应人工客服
    q1sdk.AIHelpShowConversation = function(type, storyNodeUserSay, welcomeHelp)
        local game_config = require "game_config"
        if game_config.ENABLE_AIHELP == false or game_config.Q1SDK_DOMESTIC then
            return
        end
        if Q1SDK.Instance.AIHelpShowConversation ~= nil then
            local alwaysShowHumanSupportButtonInBotPage = true
            if type == 0 then
                alwaysShowHumanSupportButtonInBotPage = false
            end
            Q1SDK.Instance:AIHelpShowConversation(type,storyNodeUserSay,welcomeHelp,alwaysShowHumanSupportButtonInBotPage)
        end  
    end

    q1sdk.AIHelpLogin = function(gameUserId,roleId,roleName,level,vipLevel,serverId,device)
        local game_config = require "game_config"
        if game_config.ENABLE_AIHELP == false or game_config.Q1SDK_DOMESTIC then
            return
        end
        local open_test_mgr = require "open_test_mgr"
        local ip = open_test_mgr.GetCountryName() or "getIpError"
    
        local player_mgr = require "player_mgr"
        local totleRecharge = PlayerPrefs.GetInt(player_mgr.GetPlayerRoleID() .. "totleRecharge", 0)
        local createRoleTime = os.date("%Y_%m_%d_%H_%M_%S", player_mgr.GetRoleCreateTime()) 
        local roleLv = tostring(level or 0)
        local setting_server_data = require "setting_server_data"
        local regionID = setting_server_data.GetRegionID()
        -- REGION_ID: (大区ID: 0-中国区 1-欧美区 2-亚太区
        -- "region_id": 2-EA   3 -SA
        local serverId_int = tonumber(serverId) 
        local serverStr = "CH"
        local intParseServerId = serverId_int/10000
        if intParseServerId > 0 then
            serverId_int = serverId_int%10000
        end
        if regionID == 2 then
            serverStr = "EA"
        elseif regionID == 3 then
            serverStr = "SA"
        end

        local bindTypeList = q1sdk.GetRoleBindType()
        local sdkOpenInfo = q1sdk.GetSdkOpenInfo()

        local properties = 
        {
            boundFB = "none",
            boundGoogle = "none",
            boundApple = "none",
        }

        if bindTypeList["Facebook"] then
            if sdkOpenInfo and sdkOpenInfo["Facebook"] then
                properties.boundFB = sdkOpenInfo["Facebook"].nickname or "true"
                properties.bingingTimeFB= os.date("%Y_%m_%d_%H_%M_%S",tonumber(sdkOpenInfo["Facebook"].bindtime))  or ""
            end
        end
        if bindTypeList["GooglePlay"] then
            if sdkOpenInfo and sdkOpenInfo["GooglePlay"] then
                properties.boundGoogle = sdkOpenInfo["GooglePlay"].nickname or "true"
                properties.bingingTimeGoogle = os.date("%Y_%m_%d_%H_%M_%S",tonumber(sdkOpenInfo["GooglePlay"].bindtime))  or ""
            end 
        end
        if bindTypeList["AppleID"] then
            if sdkOpenInfo and sdkOpenInfo["AppleID"] then
                properties.boundApple = sdkOpenInfo["AppleID"].nickname or "true"
                properties.bingingTimeApple = os.date("%Y_%m_%d_%H_%M_%S",tonumber(sdkOpenInfo["AppleID"].bindtime))  or ""
            end     
        end
        local newTag = "";
        local VIP_tag = "VIP"..tostring(vipLevel)
        local serverId_string = serverStr..tostring(serverId_int)
        -- local totalRechargeDes = "累充"..tostring(totleRecharge)
        newTag = VIP_tag..','..serverId_string

        -- 信息扩展参数
        local extParam = dkjson.encode(properties or {})
        local useInfoStr = roleName..";"..roleLv..";"..tostring(totleRecharge)..";"..tostring(createRoleTime)..";"..extParam

        localAIhelpUserInfo = {
            gameUserId = gameUserId,
            roleId=roleId,
            roleName=roleName,
            level=level,
            vipLevel=vipLevel,
            serverId=serverId,
            device=device
        }
        if Q1SDK.Instance.AIHelpLogin ~= nil then
            Q1SDK.Instance:AIHelpLogin(gameUserId,roleId,useInfoStr,vipLevel,serverId,ip,device,newTag)
        end   
    end


    q1sdk.LoginCallBack = function (loginInfo, startLoginTime, callback, ...)
        local currentLoginInfo = GetSDKLoginInfo()
        local currentLoginUUID = currentLoginInfo.sdkLoginUUID
        local loginUUID = loginInfo.sdkLoginUUID
        if loginUUID < currentLoginUUID or currentLoginInfo.triggerTimes > 0 then
            -- 返回的登录回调不是当前最近的一次登录，可能为登录时间超过 sdkLoginTimeOutInterval 后判定为超时，但此后超时返回了结果
            -- currentLoginInfo.triggerTimes > 1 防止超时后，再次发起登录前，sdk 返回回调。
            local endLoginTime = Time.realtimeSinceStartup

            loginInfo.triggerTimes = loginInfo.triggerTimes + 1

            log.LoginWarning("登录返回过期,当前id:", currentLoginUUID, ",过期id:", loginUUID, ",startTime:", startLoginTime, 
                ",endTime:", endLoginTime, ",costTime", endLoginTime - startLoginTime, ",triggerTimes:", loginInfo.triggerTimes)

            local loginCallBackMsg = dkjson.encode({...})
            log.LoginWarning("登录返回信息：", loginCallBackMsg)

            event.RecodeTrigger("login_out_of_date",
            {
                login_cur_uuid = currentLoginUUID,
                login_login_uuid = loginUUID,
                login_start_time = startLoginTime,
                login_end_time = endLoginTime,
                login_cost_time = costTime,
                login_trigger_times = loginInfo.triggerTimes,
                login_callback_msg = loginCallBackMsg
            })
            -- 过期的回调，说明之前已经处理过，可以直接返回
            return
        end

        log.Warning("登录耗时：", Time.realtimeSinceStartup - startLoginTime)
        loginInfo.triggerTimes = loginInfo.triggerTimes + 1
        q1sdk.StopLoginTimerOutTimer()
        callback(...)
    end

    q1sdk.LoginTimeOut = function(loginInfo, startLoginTime, callback, ...)
        local currentLoginInfo = GetSDKLoginInfo()
        local currentLoginUUID = currentLoginInfo.sdkLoginUUID
        local loginUUID = loginInfo.sdkLoginUUID
        -- 此处理论上 currentLoginUUID 一定等于 loginUUID
        local endLoginTime = Time.realtimeSinceStartup

        loginInfo.triggerTimes = loginInfo.triggerTimes + 1

        log.LoginWarning("登录返回超时,当前id:", currentLoginUUID, "超时id:", loginUUID, "startTime:", startLoginTime, 
                "endTime:", endLoginTime, "costTime", endLoginTime - startLoginTime, ",triggerTimes:", loginInfo.triggerTimes)

        event.RecodeTrigger("login_timeout",
        {
            login_cur_uuid = currentLoginUUID,
            login_login_uuid = loginUUID,
            login_start_time = startLoginTime,
            login_end_time = endLoginTime,
            login_cost_time = costTime,
            login_trigger_times = loginInfo.triggerTimes
        })
        q1sdk.StopLoginTimerOutTimer()
        callback(Q1_LoginErrorCode.Error, "sdk login timeout " .. sdkLoginTimeOutInterval .. " second,", "", "", "")
    end

    q1sdk.IsSupportAppleSignin = function ()
        if Application.platform ~= RuntimePlatform.IPhonePlayer then
            return false
        end

        if not Q1SDK.Instance.IsSupportAppleSignin then
            return false
        end

        local isSupportAppleSignin = Q1SDK.Instance:IsSupportAppleSignin()
        log.Warning("IsSupportAppleSignin:"..tostring(isSupportAppleSignin))
        return isSupportAppleSignin
    end

    q1sdk.Logout = function()
        log.Warning("q1sdk Logout")
        if Q1SDK.Instance.CallSDKonPauseonResume ~= nil then
            Q1SDK.Instance:CallSDKonPauseonResume('onPause')
        end

        Q1SDK.Instance:Logout()

        if Q1SDK.Instance.TrackRoleLogout ~= nil then
            Q1SDK.Instance:TrackRoleLogout(savedInfo.serverId, savedInfo.userID, savedInfo.roleID, savedInfo.roleName, savedInfo.roleLevel)
        end

        -- 在里面会onResume
        q1sdk.UserEvent(savedInfo.serverId, savedInfo.roleID, savedInfo.roleName, savedInfo.roleLevel, savedInfo.userID, 'switchRole', savedInfo.msg)
        --Q1SDK.Instance:UserEvent(savedInfo.serverId, savedInfo.roleID, savedInfo.roleName, savedInfo.roleLevel, savedInfo.userID, 'switchRole', savedInfo.msg)
	log.Warning("worldID:", savedInfo.serverId, ",roleID:", savedInfo.roleID, ",roleName:", savedInfo.roleName, ",roleLv:", savedInfo.roleLevel, ",userID:", savedInfo.userID, ",logout,msg:", savedInfo.msg)
        Q1SDK.Instance:UserEvent(savedInfo.serverId, savedInfo.roleID, savedInfo.roleName, savedInfo.roleLevel, savedInfo.userID, "logout", savedInfo.msg)
    end

    --不调用SDK Logout,不清除账号信息
    q1sdk.LogoutLogic = function ()
        log.Warning("q1sdk LogoutLogic")
        if Q1SDK.Instance.CallSDKonPauseonResume ~= nil then
            Q1SDK.Instance:CallSDKonPauseonResume('onPause')
        end

        -- 在里面会onResume
        q1sdk.UserEvent(savedInfo.serverId, savedInfo.roleID, savedInfo.roleName, savedInfo.roleLevel, savedInfo.userID, 'switchRole', savedInfo.msg)
        --Q1SDK.Instance:UserEvent(savedInfo.serverId, savedInfo.roleID, savedInfo.roleName, savedInfo.roleLevel, savedInfo.userID, 'switchRole', savedInfo.msg)
        Q1SDK.Instance:UserEvent(savedInfo.serverId, savedInfo.roleID, savedInfo.roleName, savedInfo.roleLevel, savedInfo.userID, "logout", savedInfo.msg)
    end

    q1sdk.SwitchWorld = function()
        if Q1SDK.Instance.CallSDKonPauseonResume ~= nil then
            Q1SDK.Instance:CallSDKonPauseonResume('onPause')
        end

        -- 在里面会onResume
        q1sdk.UserEvent(savedInfo.serverId, savedInfo.roleID, savedInfo.roleName, savedInfo.roleLevel, savedInfo.userID, 'switchRole', savedInfo.msg)
        --Q1SDK.Instance:UserEvent(savedInfo.serverId, savedInfo.roleID, savedInfo.roleName, savedInfo.roleLevel, savedInfo.userID, 'switchRole', savedInfo.msg)
        log.Warning("worldID:", savedInfo.serverId, ",roleID:", savedInfo.roleID, ",roleName:", savedInfo.roleName, ",roleLv:", savedInfo.roleLevel, ",userID:", savedInfo.userID, ",logout,msg:", savedInfo.msg)
        Q1SDK.Instance:UserEvent(savedInfo.serverId, savedInfo.roleID, savedInfo.roleName, savedInfo.roleLevel, savedInfo.userID, "logout", savedInfo.msg)
    end

    q1sdk.clearCache = function ()
    	if nil == Q1SDK.Instance.ClearPlatformAccountCache then
    		--旧版SDK不支持接口兼容
    		return false
    	end
    	log.Warning("q1sdk clearCache")
        if Application.platform == RuntimePlatform.IPhonePlayer then
            --2020/06/30 iOS SDK新版本
            --[[
                切换账号前需要调用(清除fb账号缓存)
                需要重新登录facebook或切换账号前需要调用，否则无法登陆新的facebook账号
            ]]
            Q1SDK.Instance:Facebooklogout()
        else
    	   Q1SDK.Instance:ClearPlatformAccountCache()
        end
    	return true
    end

    q1sdk.SetLogcat = function(on) 
        Q1SDK.Instance:SetDebug(on)
    end

    q1sdk.IsEnable = function()
        return Q1SDK.IsEnable()
    end

    q1sdk.Pay = function(serverID, userId, payNum, orderItem, orderNO, currencyType, orderSign, noLoginPay, level, callback, rechageID, payUrl)
        print("game_config.ENABLE_OTHER_WAY",game_config.ENABLE_OTHER_WAY)
        if game_config.ENABLE_OTHER_WAY then
            local url_mgr = require "url_mgr"
            local url = string.format( url_mgr.PAY_URL,tostring(payNum),tostring(userId),tostring(currencyType),tostring(serverID),tostring(orderItem),tostring(orderNO),tostring(orderSign),tostring(userId))
            -- local url = "http://mpay-sa.xxk.4g.q1.com:800/?Money="..payNum.."&GameID=2118".."&UserID="..userId.."&CurrencyType="..currencyType.."&ServerID="..serverID.."&OrderItem="..orderItem.."&OrderNO="..orderNO.."&OrderSign="..orderSign.."&PID=1".."&ActorID="..userId.."&backurl=http://xherocz.yr.dev.q1.com/welfare.html#page2"
            print("url",url)
            local web = web_view.CWebView()
            web:Create("PayWeb")
            web:SetSizeTarget(GameObject.Find("UIRoot/Canvas").transform,true)
            web:Load(url)
            --回调函数清理？？
            -- if web then
            --     web:Destroy()
            --     web = nil
            -- end
            return
        end
        if game_config.Q1SDK_DOMESTIC then
            -- 2021/2/24 重新接入国内版 sdk, 实现 PayOrder 接口
            local player_mgr = require "player_mgr"
            local roleId = player_mgr.GetPlayerRoleID()
            -- payNum： USD 除100，保留两位小数，RMB 除100，取整
            if not game_config.ENABLE_Q1SDK_CHANNEL then
                Q1SDK.Instance:PayOrder(serverID, userId, payNum, orderItem, orderNO, orderSign, noLoginPay, level, callback, roleId)
            else
                local rechargeInfo = game_scheme:Recharge_0(rechageID)
                local setting_server_data = require "setting_server_data"
                local productID = rechageID
                if rechargeInfo.strProductID and rechargeInfo.strProductID ~= "" then
                    productID = rechargeInfo.strProductID
                end
                local lang = require "lang"
                local goodsName = ""
                if rechargeInfo and rechargeInfo.iPrice then
                    goodsName = rechargeInfo.iPrice / 100 ..lang.Get(81602)
                end
                local properties = {
                    productId = productID,
                    productName = goodsName,
                    productDesc = rechargeInfo and rechargeInfo.strName or "",
                    roleId = player_mgr.GetPlayerRoleID() or "",
                    roleName = player_mgr.GetRoleName() or "",
                    roleLevel = player_mgr.GetPlayerLV() or "",
                    serverId = setting_server_data.GetLoginWorldID() or "",
                    serverName = setting_server_data.GetLoginWorldName() or "",
                    coinNum = player_mgr.GetPlayerAllDiamond() or "0",
                    price = payNum,
                    buyNum = 1,
                    vip = player_mgr.GetPlayerVipLevel() or "0",
                    extension = orderNO
                }
                Q1SDK.Instance:U8PayOrder(dkjson.encode(properties), callback)
            end
            

        else
            local isAndroid = Application.platform == RuntimePlatform.Android
        	if isAndroid then
                local player_mgr = require "player_mgr"
                local roleId = player_mgr.GetPlayerRoleID()
                local url_mgr = require "url_mgr"
                local url = url_mgr.PAY_NOTIFY_URL
                local notifyUrl = url
                log.Warning(" PAY_NOTIFY_URL :",notifyUrl)
                local extParam = ""
                local setting_server_data = require "setting_server_data"
                local money_type_mgr = require "money_type_mgr"
                local data = money_type_mgr.GetMoneyExchangeRate("en",payNum*100,currencyType,rechageID)/100
                local properties = 
                {
                    roleName = player_mgr.GetRoleName(),
                    roleLevel = player_mgr.GetPlayerLV() or "0",
                    serverName = setting_server_data.GetLoginWorldName() or "0",
                    notifyUrl = notifyUrl,
                    amount_usd = tostring(data)
                }
                extParam = dkjson.encode(properties)
                if Q1SDK.Instance.PayOrderWithExtParam then
                    log.Warning(" 支付测试 PayOrderWithExtParam :",extParam)
                    Q1SDK.Instance:PayOrderWithExtParam(serverID, userId, string.format('%.2f', payNum), orderItem, orderNO, orderSign, noLoginPay, level, 
                    callback, roleId, currencyType, extParam)
                else
                    if Q1SDK.Instance.PayOrderNew then 
                        log.Warning(" 支付测试 PayOrderNew :",extParam)
                        Q1SDK.Instance:PayOrderNew(serverID, userId, string.format('%.2f', payNum), orderItem, orderNO, orderSign, noLoginPay, level, 
                        callback, roleId, currencyType)
                    elseif Q1SDK.Instance.PayOrder then 
                        log.Warning(" 支付测试 PayOrder :",extParam)
                        Q1SDK.Instance:PayOrder(serverID, userId, string.format('%.2f', payNum), orderItem, orderNO, orderSign, noLoginPay, level, 
                            callback, roleId)
                    else
                        Q1SDK.Instance:PayStrMoney(serverID, userId, string.format('%.2f', payNum), orderItem, orderNO, orderSign, noLoginPay, level, callback)
                    end
                end
        	else
                if Q1SDK.Instance.PayWithPayload then
                    -- 2021/1/21 iOS SDK 升级到 1.0.9 版本,支付增加透传参数，参数个数发生变化，为兼容外网版本，增加 PayWithPayload 接口
                    -- 最后一个 developerPayload ，目前后台未使用，先传空
                    Q1SDK.Instance:PayWithPayload(tostring(serverID), userId, string.format('%.2f', payNum), orderItem, orderNO, currencyType, orderSign,
                     noLoginPay, level, callback, "")
                else
            	   Q1SDK.Instance:PayWithCurrency(tostring(serverID), userId, string.format('%.2f', payNum), orderItem, orderNO, currencyType, orderSign,
                     noLoginPay, level, callback)
                end
            end
        end
    end

    q1sdk.GetUUID = function()
		if not Q1SDK.IsEnable() then return "" end
        return Q1SDK.Instance:GetUUID()
    end
	
	q1sdk.GetImeiMD5 = function()
        if not Q1SDK.IsEnable() then return "" end
        return Q1SDK.Instance:GetImeiMD5()
    end
	
	q1sdk.GetRadid = function()
        if not Q1SDK.IsEnable() then return 0 end
        return Q1SDK.Instance:GetRadid()
    end
	
	q1sdk.GetRsid = function()
        if not Q1SDK.IsEnable() then return 0 end
        return Q1SDK.Instance:GetRsid()
    end

    q1sdk.SetRegion = function(regionID)
		if not Q1SDK.IsEnable() then 
			return 
		end
		local isAndroid = Application.platform == RuntimePlatform.Android
		if isAndroid then
        	-- debug模式就不再设置Region因为它们不兼容
			if game_config.ENABLE_Q1_DEBUG_MODE then
				return
			end
		else
            q1sdk.SaveLocalRegionID(regionID)
			if game_config.ENABLE_Q1_DEBUG_MODE then
				-- SDK_Region.region_debug = 4,内网平台
                log.Warning("SetRegion:4")
				Q1SDK.Instance:SetRegion(4)
				return
			end

            -- 海外iOS这里的环境只能是欧美区 1
            if not game_config.Q1SDK_DOMESTIC then
                log.Warning("SetRegion:1")
				Q1SDK.Instance:SetRegion(1)
                return
            end
		end
        log.Warning("SetRegion:"..regionID)
		Q1SDK.Instance:SetRegion(regionID)	
	end

    -- 设置 SDK 语言
    q1sdk.SetLanguage = function ()
        -- SDK setLanguage 接口实现有问题。设置简体中文会显示为繁体中文。SDK组决定屏蔽此接口，使其返回语言跟随系统走
        return

        -- if not Q1SDK.IsEnable() then 
        --     return 
        -- end

        -- if Q1SDK.Instance.SetLanguage then
        --     if Application.platform == RuntimePlatform.IPhonePlayer then
        --         -- 预计 svn 38399 版本之后，iOS 添加 SetLanguage 接口。2020/10/23
        --         -- typedef enum{
        --         --     en = 0,             //英文
        --         --     zh_hans = 1,        //中文简体
        --         --     zh_hant = 2,        //中文繁体
        --         -- } SDK_Language;
        --         local lang = require "lang"
        --         local languageID = 0 -- 默认使用英文
        --         local language = lang.GetUseLang()
        --         if language == lang.ZH then
        --             languageID = 1 -- 中文简体
        --         elseif language == lang.FA then
        --             languageID = 2 -- 中文繁体
        --         else
        --             languageID = 0 -- 英文
        --         end
        --         Q1SDK.Instance:SetLanguage(languageID)
        --     end
        -- end
    end
    
    q1sdk.ExitApplication = function()
		if not Q1SDK.IsEnable() then return end
		Q1SDK.Instance:ExitApplication()	
    end

	q1sdk.StartEvent = function(action, msg)
		if not Q1SDK.IsEnable() then return end
		Q1SDK.Instance:StartEvent(action, msg)	
    end
	
    -- int serverId, int roleId, string roleName, int roleLevel, string user, string action, string msg
    q1sdk.UserEvent = function(serverId, roleId, roleName, roleLevel, user, action, msg)
        if not Q1SDK.IsEnable() then return end
        if action =="login" or action =="create" then
            SetSuperProperties()
        end
        local game = require "game"
        local ui_login_main = require "ui_login_main"
		-- 这里统一处理userID roleID
		local userID = ui_login_main.GetBCUserID()
		local roleID = roleId
		if game.actors and #game.actors > 0 then
			userID = game.actors[1].BCUserID or ''
			if userID == nil or userID == "" then
                userID = game.actors[1].userID or ''
			end
			--userID = userID.. '.' .. (channel_config[game_config.CHANNEL_ID] or 'not_configed')
			roleID = game.actors[1].roleID

            log.Warning("UserEvent user:", user, ", changed to userID:", userID, ",action:", action)
		end

        -- 这里，如果是u8渠道包，需要user需要替换成他们的userName
        if game_config.ENABLE_Q1SDK_CHANNEL then
            local net_login_module = require "net_login_module"
            userID = net_login_module.GetU8UserName()
        end

        -- 新增保底措施，防止 userID rolename 为空,C# 层会拦截
        if userID == nil or userID == "" then
            userID = "null"
        end
        if roleName == nil or roleName == "" then
            roleName = "null"
        end

        -- 扩展字段增加合服后id
        local ui_login_main_mgr = require "ui_login_main_mgr"
        local lastLoginWorldId = ui_login_main_mgr.GetLoginServerID()
        if msg == nil or type(msg) ~= 'string' then msg = ""
        elseif msg ~= "" then
            if string.find(msg,';') == 1 then msg = string.sub(msg, 2) end
            if msg ~= "" then msg = msg .. ";" end
        end
        msg = msg .. "lastWorldId=" .. lastLoginWorldId
        log.Warning("msg:", msg)

		-- 这里offline事件有多个地方触发，在连上之前只上报一次，不重复上报
        if action == "userLogin" then
			hasReportOffline = false
		end
		if action == "offLine" then
			if hasReportOffline == false then
				Q1SDK.Instance:UserEvent(serverId, roleID, roleName, roleLevel, userID, action, msg)
				hasReportOffline = true
			end
        else
            if action == "login" then
                local util = require "util"
                local extra = {
                    serverId = serverId,
                    roleID = roleID,
                    roleName = roleName,
                    roleLevel = roleLevel,
                    userID = userID,
                }
			    util.ReportCreateEntityTime("Q1SDK上报登录日志", true, extra)
            end
            Q1SDK.Instance:UserEvent(serverId, roleID, roleName, roleLevel, userID, action, msg)
            if action == "login" then
                local util = require "util"
			    util.ReportCreateEntityTime("Q1SDK上报登录日志", false)
            end
            if action == "userLogin" then
                savedInfo = {serverId=serverId, roleID=roleID, roleName=roleName, roleLevel=roleLevel, userID=userID, action='switchRole', msg=msg}
            end
		end
    end

    -- 角色成功上线之后，调用 SDK reportGameLogin 方法。2020/6/15 海外新SDK才有此接口
    q1sdk.ReportGameLogin = function (roleId, roleName, roleLevel, serverId)
        --国内版本处理
        if game_config.Q1SDK_DOMESTIC then
            
        else
            --海外版本处理
            if Application.platform == RuntimePlatform.Android and Q1SDK.Instance and Q1SDK.Instance.ReportGameLogin then
                log.Warning("ReportGameLogin :", roleId, roleName, roleLevel)
                Q1SDK.Instance:ReportGameLogin(tostring(roleId), roleName, roleLevel)
            elseif Application.platform == RuntimePlatform.IPhonePlayer then
                -- 2020/06/30 增加 ReportGameLogin 此接口
                -- 2021/1/21 iOS SDK 升级到 1.0.9 版本，删除了 reportGameLogin 接口
                log.Warning("ReportGameLogin :", roleId, roleName, roleLevel)
                Q1SDK.Instance:ReportGameLogin(serverId, roleId, roleName, roleLevel)
            end
        end
    end

    -- 
    q1sdk.QueryGoogleProductInfoById = function (id, callback)
        -- print('<<<<<<<<<<<<<<<q1sdk.QueryGoogleProductInfoById<<<<<<<<<<<')
        if Application.platform == RuntimePlatform.Android then
            -- log.Warning("QueryGoogleProductInfoById :", id , callback)
            -- print('！！！q1sdk.QueryGoogleProductInfoById!!!', id, callback)
            local version_mgr = require "version_mgr"
            if version_mgr.CheckSvnTrunkVersion(42009) then
                Q1SDK.Instance:QueryGoogleProductInfoById(id ,callback)
            end
        end
    end
    -- 游戏切换角色（切换账号同理）过程：
    -- （1）游戏启动
    -- （2）登录角色A
    -- （3）A角色在线
    -- （4）A角色退出
    -- （5）重新选择角色
    -- （6）选择B角色
    -- （7）B角色在线
    -- 之后如果切换C角色则是重复 4、5、6步骤。
    -- 切换角色，在A角色退出之后即第（4）步，调用 reportOldRoleOnlineTime 先上报A角色的在线时长
    -- 2020/6/15 海外新SDK才有此接口
    q1sdk.ReportOldRoleOnlineTime = function ()
        --国内版本处理
        if game_config.Q1SDK_DOMESTIC then
            
        else
            if Q1SDK.Instance and Q1SDK.Instance.ReportOldRoleOnlineTime then
                log.Warning("ReportOldRoleOnlineTime")
                Q1SDK.Instance:ReportOldRoleOnlineTime()
                -- 2021/1/21 iOS SDK 升级到 1.0.9 版本，删除 ReportOldRoleOnlineTime 接口。兼容外网版本，此处不删除在 C# 层修改此接口实现
            end
        end
    end

    q1sdk.CheckDevicePermission = function(permissionStr)
        return Q1SDK.Instance:CheckDevicePermission(permissionStr)
    end
	
	q1sdk.GetIdAuth = function()
		return Q1SDK.Instance:GetIdAuth()
	end
	
	q1sdk.IdAuth = function(closeable, callback)
		Q1SDK.Instance:IdAuth(closeable, callback)
    end
    
    q1sdk.UserBind = function(platform, callBack)
        if not Q1SDK.IsEnable() then return end

        local login_pb = require "login_pb"
        if platform == login_pb.enLoginPartnerID_HuaWei then
            -- C#中会对 platform - 1，保持外网兼容
            platform = 4
        end
        if platform == login_pb.enLoginPartnerID_Amazon then
            platform = 5
        end
        -- 1.google 2.facebook 3.huawei。 4.amazon
        Q1SDK.Instance:UserBind(platform, callBack)
    end

    q1sdk.Visitor = function()
        if not Q1SDK.IsEnable() then return false end
        local isVisitor = true
        print("______________Visitor:"..tostring(Q1SDK.Instance:Visitor()))
        if localIsVisitor ~= nil and localIsVisitor == false then
            isVisitor = false
        else
            isVisitor = Q1SDK.Instance:Visitor()
        end
        print("Visitor:"..tostring(isVisitor))
        return isVisitor
    end

    q1sdk.SetVisitorState = function(bVisitor)
        localIsVisitor = bVisitor
    end

    q1sdk.OneKeyLogin = function(account, pwd, callback)
        log.Warning("OneKeyLogin")
        Q1SDK.Instance:OneKeyLogin(account, pwd, callback)
    end

    q1sdk.RegisterByPhone = function(phone, captcha, pwd, isBind, callback)
        Q1SDK.Instance:RegisterByPhone(phone, captcha, pwd, isBind, callback)
    end

    q1sdk.CheckUsernameExists = function(account, callback)
        Q1SDK.Instance:CheckUsernameExists(account, callback)
    end

    q1sdk.RegisterByUsername = function(account, pwd, isBind, callback)
        Q1SDK.Instance:RegisterByUsername(account, pwd, isBind, callback)
    end

    q1sdk.RetrievePwd = function(account, captcha, pwd, callback)
        Q1SDK.Instance:RetrievePwd(account, captcha, pwd, callback)
    end

    q1sdk.BindPhoneConfirm = function(phone, captcha, callback)
        Q1SDK.Instance:BindPhoneConfirm(phone, captcha, callback)
    end

    q1sdk.CheckRegisterCaptcha = function(phone, captcha, isBind, callback) --<bool, string>
        Q1SDK.Instance:CheckRegisterCaptcha(phone, captcha, isBind, callback)
    end

    q1sdk.SessionValid = function(callback)
        event.RecodeTrigger("login_start",
        {
            login_way = "session_valid"
        })
        if nil == Q1SDK.Instance.getLatestSession then  --2020/6/2,接入新android sdk,不再使用 SessionValid 接口，改为 getLatestSession 异步实现
            callback(Q1SDK.Instance:SessionValid())
        else
            Q1SDK.Instance:getLatestSession(callback)
        end
    end

    -- q1sdk.CurrentUser = function ()
    --     if nil == Q1SDK.Instance.GetPluginClassInstance then
    --         return nil
    --     else
    --         local Q1SdkOpenApi = Q1SDK.Instance:GetPluginClassInstance("com.q1.sdk.Q1SdkOpenApi")
    --         if Q1SdkOpenApi then
    --             return Q1SdkOpenApi.CallStatic("currentUser")
    --         end
    --     end
    --     return nil
    -- end

    q1sdk.SessionLogin = function(callback)
        Q1SDK.Instance:SessionLogin(callback)
    end

    q1sdk.LoginWithBind = function(account, pwd, callback)
        Q1SDK.Instance:LoginWithBind(account, pwd, 1, callback)
    end

    q1sdk.PseudonymSDK = function()
        return allChannels[tostring(game_config.CHANNEL_ID)] ~= nil
    end
	
	--[[thinkData新版埋点事件]]
	-- 1.基础事件 创建角色
	q1sdk.TrackCreateRole = function(serverId, serverName, roleId, roleName, level, vipLevel, num, gameUserId, user)
        if not Q1SDK.IsEnable() then return end
        print("TrackCreateRole serverId:",serverId,",serverName:",serverName,",roleId:",roleId, ",roleName:",roleName,",level:",level,",vipLevel:",vipLevel,
            ",num:",num, ",gameUserId:",gameUserId, ",user:", user)
        SetSuperProperties()
		if Q1SDK.Instance.TrackCreateRoleWithUniqueId then
            local net_login_module = require "net_login_module"
            local uniqueId = const.GAMEID.."_"..net_login_module.GetLoginAreaID().."_"..roleId
            Q1SDK.Instance:TrackCreateRoleWithUniqueId(serverId, tostring(serverName), roleId, tostring(roleName), level, vipLevel, num, gameUserId, tostring(user), uniqueId)
        else
            Q1SDK.Instance:TrackCreateRole(serverId, tostring(serverName), roleId, tostring(roleName), level, vipLevel, num, gameUserId, tostring(user))
        end
		
		local properties = GetAdjustProperties()
		event.Trigger(event.GAME_EVENT_REPORT, "create_role2", properties)

        local properties = {
            dataType = 2,
            roleID = tostring(roleId) or "",
            roleName = tostring(roleName) or "",
            roleLevel = tostring(level) or "",
            serverID = tostring(serverId) or "",
            serverName = tostring(serverName) or "",
            moneyNum = "0",
            roleCreateTime = os.time(),
            roleLevelUpTime = os.time(),
            vip = "0"
        }
        if game_config.ENABLE_Q1SDK_CHANNEL then
            if Q1SDK.Instance.U8SubmitExtraData_new then
                local extraparam = {
                    guanqia = 0
                }
                Q1SDK.Instance:U8SubmitExtraData_new(dkjson.encode(properties),dkjson.encode(extraparam))
            else
            Q1SDK.Instance:U8SubmitExtraData(dkjson.encode(properties))
            end
            --q1sdk.U8SubmitExtraData(2)
        end

        q1sdk.AdjustAndFirebaseReport("create_role")

    end

	-- 角色登录,收到玩家实体数据后上报数据，serverID 会更新数数的 server_id 字段
	q1sdk.StartTrack = function(gameUserId, roleId, roleName, level, vipLevel, serverID)
        if not Q1SDK.IsEnable() then return end
        SetSuperProperties()

        -- 2022-06-17 和sdk确认海外并不需要此方法，如果调用此方法，没有action，会有问题 国内使用此方法作为数数的登录打点
        -- 2022-09-13 和平台确认海外没有调用数数的登录接口，新增StartTrackWithUniqueId
        log.Warning("StartTrack gameUserId:",gameUserId,",roleId:",roleId,",roleName:",roleName,",level:",level,",vipLevel:",vipLevel,",serverID:",serverID)
		    
        if Q1SDK.Instance.StartTrackWithUniqueId then
            local net_login_module = require "net_login_module"
            local uniqueId = const.GAMEID.."_"..net_login_module.GetLoginAreaID().."_"..roleId
            Q1SDK.Instance:StartTrackWithUniqueId(gameUserId, roleId, roleName, level, vipLevel, serverID, uniqueId)
        else
            if game_config.Q1SDK_DOMESTIC then
                Q1SDK.Instance:StartTrack(gameUserId, roleId, roleName, level, vipLevel, serverID)
            end
        end

        CheckValidUserInfo(serverID, roleId, roleName, level, gameUserId)
        AddCachedUserInfo(serverID, roleId, roleName, level, gameUserId)
		
		local properties = GetAdjustProperties()
		event.Trigger(event.GAME_EVENT_REPORT, "login2", properties)

		local adjust = require "adjust"
		local adjustAtt = adjust.GetAttribution()
		local json_str = dkjson.encode({
			latest_adjust_network = adjustAtt.network or "",
			latest_adjust_campaign = adjustAtt.campaign or "",
			latest_adjust_adgroup = adjustAtt.adgroup or "",
			latest_adjust_creative = adjustAtt.creative or "",
        })

		q1sdk.UserSet(json_str)

        q1sdk.U8SubmitExtraData(3)
        q1sdk.AIHelpSetUserId(tostring(roleId) or "testuser")
        q1sdk.AIHelpInfoUpdate(roleId,roleName,level,vipLevel,serverID)
    end

    q1sdk.AIHelpInfoUpdate = function(roleId,roleName,level,vipLevel,serverID)
        q1sdk.AIHelpLogin(tostring(roleId) or "testuser",roleId or 1,(roleName or "roleName"),level,vipLevel or 0,serverID or 0,q1sdk.GetImeiMD5())
    end

    q1sdk.AIHelpSetLanguage = function(language)
        local game_config = require "game_config"
        if game_config.ENABLE_AIHELP == false then
            return
        end
        if Q1SDK.Instance.AIHelpUpdateLanguage ~= nil then
            Q1SDK.Instance:AIHelpUpdateLanguage(language)
        end   
    end

    q1sdk.AIHelpHelpEnableDebug = function()
        local game_config = require "game_config"
        if game_config.ENABLE_AIHELP == false then
            return
        end
        if Q1SDK.Instance.AIHelpHelpEnableDebug ~= nil then
            print("zzd____AIHelpHelpEnableDebug ",game_config.ENABLE_Q1_DEBUG_MODE)
            if game_config.ENABLE_Q1_DEBUG_MODE == true then
                Q1SDK.Instance:AIHelpHelpEnableDebug()
            end
        end   
    end

    q1sdk.AIHelpSetUploadLogPath = function()
        local game_config = require "game_config"
        if game_config.ENABLE_AIHELP == false then
            return
        end
        if Q1SDK.Instance.AIHelpSetUploadLogPath ~= nil then
            local logDebug = LogDebug.Instance
            local logPath = logDebug:GetCurrentLogFilePath()
            print("zzd____AIHelpSetUploadLogPath logfilePath = ",logPath)
            Q1SDK.Instance:AIHelpSetUploadLogPath(logPath)
        end   
    end

    q1sdk.GetUserInfo = function()
        if not Q1SDK.IsEnable() then return "" end
        return GetUserInfo()
    end
	-- 角色登出
	q1sdk.EndTrack = function()
		if not Q1SDK.IsEnable() then return end
		Q1SDK.Instance:EndTrack()	
    end
    q1sdk.EndTrackWithoutEvent = function()
		if not Q1SDK.IsEnable() then return end
		Q1SDK.Instance:EndTrackWithoutEvent()	
    end
	-- 充值成功
	q1sdk.TrackUpdateTotalRevenue = function(num)
		if not Q1SDK.IsEnable() then return end
		Q1SDK.Instance:TrackUpdateTotalRevenue(num)	
    end
	-- 改名成功
	q1sdk.TrackUpdateName = function(newName)
		if not Q1SDK.IsEnable() then return end
		Q1SDK.Instance:TrackUpdateName(newName)	
    end
	-- 角色升级
	q1sdk.TrackLevelUp = function(serverId,roleId,roleName,roleLevel,user)
		if not Q1SDK.IsEnable() then return end
		if CheckReportTable("levelup", roleLevel) == false then
			return
        end
		
		local game = require "game"
        -- 这里统一处理userID
		local userID = user
		if game.actors and #game.actors > 0 then
			userID = game.actors[1].BCUserID or ''
			if userID == nil or userID == "" then
                userID = game.actors[1].userID or ''
			end
        end
        
        local net_login_module = require "net_login_module"
        if game_config.ENABLE_Q1SDK_CHANNEL then
            userID = net_login_module.GetU8UserName() or userID
        end
        --数数打点
        Q1SDK.Instance:TrackLevelUp(roleLevel)	
        -- 冰川后台
        Q1SDK.Instance:LevelUp(serverId,roleId,roleName,roleLevel,userID)	
        local player_mgr = require("player_mgr")
        local vipLevel = player_mgr.GetPlayerVipLevel()
        if localAIhelpUserInfo then
            q1sdk.AIHelpInfoUpdate(roleId,roleName,roleLevel,vipLevel,serverId)
        end
    end
	-- 角色升级
	q1sdk.TrackVipLevelUp = function(vipLevel)
		if not Q1SDK.IsEnable() then return end
		Q1SDK.Instance:TrackVipLevelUp(vipLevel)	
        if localAIhelpUserInfo then
            q1sdk.AIHelpInfoUpdate(localAIhelpUserInfo.roleId,localAIhelpUserInfo.roleName,localAIhelpUserInfo.level,vipLevel,localAIhelpUserInfo.serverId)
        end
    end
	-- 自定义事件
	q1sdk.TrackEvent = function(eventName, properties)
        if not Q1SDK.IsEnable() then return end
		Q1SDK.Instance:TrackEvent(eventName, properties)	
    end	
	-- 更新玩家属性 批量设置
	q1sdk.UserSetOnce = function(properties)
		if not Q1SDK.IsEnable() then return end
		Q1SDK.Instance:UserSetOnce(properties)	
	end	
	-- 更新玩家属性 单个设置
	q1sdk.UserSetOnceSingle = function(propertyName, propertyValue)
		if not Q1SDK.IsEnable() then return end
		Q1SDK.Instance:UserSetOnceSingle(propertyName, propertyValue)	
	end

	q1sdk.SetContext = function (key, value)
		if Q1SDK.Instance.SetContext ~= nil then
            Q1SDK.Instance:SetContext(key, value)
        end
	end
	-- 设置用户属性
	q1sdk.UserSet = function(properties)
		if not Q1SDK.IsEnable() then return end
		if Q1SDK.Instance.UserSet ~= nil then
            Q1SDK.Instance:UserSet(properties)
        end
	end
	q1sdk.UserSetSingle = function(propertyName, propertyValue)
		if not Q1SDK.IsEnable() then return end
		if Q1SDK.Instance.UserSetSingle ~= nil then
            Q1SDK.Instance:UserSetSingle(propertyName, propertyValue)
        end
	end
	-- 设置用户的可叠加属性
	q1sdk.UserAdd = function(properties)
		if not Q1SDK.IsEnable() then return end
		if Q1SDK.Instance.UserAdd ~= nil then
            Q1SDK.Instance:UserAdd(properties)
        end
	end
	q1sdk.UserAddSingle = function(propertyName, propertyValue)
		if not Q1SDK.IsEnable() then return end
		if Q1SDK.Instance.UserAddSingle ~= nil then
            Q1SDK.Instance:UserAddSingle(propertyName, propertyValue)
        end
    end

    q1sdk.SetSysMemSizeProperties = function()
        if not Q1SDK.IsEnable() then return end
        local util = require "util"
        local Q1SDK = CS.Q1.Q1SDK
        local sysMemSize = util.GetSystemMemorySize()
        local properties = {
            system_memory_size = sysMemSize,
            }
        local strProperties = dkjson.encode(properties)
        Q1SDK.Instance:SetSuperProperties(strProperties)
    end

    q1sdk.ShareFacebook = function(url)
        if not Q1SDK.IsEnable() then return end
        print("q1sdk.ShareFacebook")
        if Q1SDK.Instance.ShareFacebook ~= nil then
            local version_mgr = require "version_mgr"
            if Application.platform == RuntimePlatform.IPhonePlayer and not version_mgr.CheckSvnTrunkVersion(33621) then
                local log = require "log"
                log.Warning("0828 ios does not have share function")
                return true
            end
            print("Q1SDK.Instance.ShareFacebook ", url)
            Q1SDK.Instance:ShareFacebook(url)
        else
            return true
        end
    end

    q1sdk.GetCurrentUser = function()
        if not Q1SDK.IsEnable() then return end
        log.Warning("q1sdk.GetCurrentUser")
        if Q1SDK.Instance.GetCurrentUser ~= nil then
            log.Warning("q1sdk.GetCurrentUser ~= nil")
            return Q1SDK.Instance:GetCurrentUser()
        end
    end

    q1sdk.GotoAppSetting = function ( packageName )
        if not Q1SDK.IsEnable() then return end
        if packageName == nil then
            -- 2021/1/14 测试
            -- 若 Google Play 商店 "com.android.vending" 没有后台弹出权限，将出现 "Google 内部结算错误" "Google billing internal error"
            -- Google Play 服务 "com.google.android.gms" 权限关闭没有影响
            -- 将包名默认设置为 Google Play 商店 "com.android.vending"
            packageName = "com.android.vending"
        end

        Utility.GotoAppSetting(packageName)
        -- 屏蔽 Q1SDK 中 GotoAppSetting 实现，使用 Utility.GotoAppSetting() 替代
        -- if Q1SDK.Instance.GotoAppSetting then
        --     log.Warning("q1sdk.GotoAppSetting", packageName)
        --     Q1SDK.Instance:GotoAppSetting(packageName)
        -- end
    end

    --session校验失败
    q1sdk.trackUserLoginSessionError = function (userId, error)
        if not Q1SDK.IsEnable() then return end

        if game_config.Q1SDK_DOMESTIC then 
            Q1SDK.Instance:TrackUserLoginSessionError(userId, error)
        end
    end
    --session校验成功
    q1sdk.trackUserLogin = function (userId)
        if not Q1SDK.IsEnable() then return end

        if game_config.Q1SDK_DOMESTIC then 
            local net_login_module = require "net_login_module"
            -- 这里，如果是u8渠道包，需要user需要替换成他们的userName
            if game_config.ENABLE_Q1SDK_CHANNEL then
                userId = net_login_module.GetU8UserName() or userId
            end
            log.Warning("trackUserLogin user:", userId)
            Q1SDK.Instance:TrackUserLoginSession(userId)
        end
    end

    q1sdk.trackSelectServer = function (serverId, userID)
        if not Q1SDK.IsEnable() then return end

        if game_config.Q1SDK_DOMESTIC then 
            Q1SDK.Instance:TrackSelectServer(serverId, userID)
        end
    end

    q1sdk.reportPurchase = function (payNum)
        if game_config.Q1SDK_DOMESTIC then 
            Q1SDK.Instance:reportPurchase(payNum)
        end
    end

    
    q1sdk.SetSuperProperties = function (properties)
        if not Q1SDK.IsEnable() then return end
        if game_config.Q1SDK_DOMESTIC then
            local strProperties = dkjson.encode(properties)
            Q1SDK.Instance:SetSuperProperties(strProperties)
        end
    end

    q1sdk.RequestCaptchaForBinding = function (phone,callback)
        if game_config.ENABLE_Q1SDK_CHANNEL then
            return
        end

        local version_mgr = require "version_mgr"
        if version_mgr.CheckSvnTrunkVersion(75178) or version_mgr.CheckSvnBranchVersion(76123) then
            if game_config.Q1SDK_DOMESTIC then
                Q1SDK.Instance:RequestCaptchaForBinding(phone,callback)
            end
        end
    end
    
    q1sdk.ConfirmBindingCaptcha = function (phone,captcha,callback)
        if game_config.ENABLE_Q1SDK_CHANNEL then
            return
        end

        local version_mgr = require "version_mgr"
        if version_mgr.CheckSvnTrunkVersion(75178) or version_mgr.CheckSvnBranchVersion(76123) then
            if game_config.Q1SDK_DOMESTIC then 
                Q1SDK.Instance:ConfirmBindingCaptcha(phone,captcha,callback)
            end
        end
    end

    q1sdk.QueryBindingPhone = function (callback)
        if game_config.ENABLE_Q1SDK_CHANNEL then
            return
        end

        local version_mgr = require "version_mgr"
        if version_mgr.CheckSvnTrunkVersion(75178) or version_mgr.CheckSvnBranchVersion(76123) then
            if game_config.Q1SDK_DOMESTIC then 
                Q1SDK.Instance:QueryBindingPhone(callback)
            end
        end
    end

    -- 获取 app 安装包指定的配置参数
    -- 获取大区时： Android -> RegionCode, iOS -> RegionId
    q1sdk.GetAppProperty = function (key, defaultValue, isInt, filedName, propertyType)
        local util = require "util"
        return util.GetAppProperty(key, defaultValue, isInt, filedName, propertyType)
    end

    -- 冰川渠道包需要自己上报购买下单事件上报
    q1sdk.TrackPayBegin = function (serverId, userId, money, roleId, roleName, roleLevel, orderNo, orderItem, orderSign, currencyType)
        if not game_config.ENABLE_Q1SDK_CHANNEL then
            return
        end
         -- 这里的id换成u8的usename
        local net_login_module = require "net_login_module"
        if net_login_module.GetU8UserInfo().username then
            userId = net_login_module.GetU8UserInfo().username
        end
        print("serverId:",serverId,"userId:",userId,"money:", money,"roleId:",roleId, "roleName:",roleName,"orderNo:",orderNo,"orderItem:",orderItem,"orderSign:", orderSign,"currencyType:", currencyType)
        Q1SDK.Instance:TrackPayBegin(serverId, userId, money, roleId, roleName, roleLevel, orderNo, orderItem, orderSign, currencyType)
    end

    -- U8提交扩展数据(部分渠道要求在 选择服务器，创建角色，登录游戏，角色升级，退出游戏 等时刻，必须要上报游戏中玩家数据）
    --[[
        该方法将调用的时机分为几种类型：
        1：选择服务器
        2：创建角色
        3：进入游戏
        4：等级提升
        5：退出游戏
        6：提升关卡
    ]]
    q1sdk.U8SubmitExtraData = function (nType, newLevel)
        if not game_config.ENABLE_Q1SDK_CHANNEL then
            return
        end
        local player_mgr = require "player_mgr"
        local setting_server_data = require "setting_server_data"
        local _roleName = player_mgr.GetRoleName()
        if _roleName and _roleName == "" then
            _roleName = "0"
        end
        local _roleLevel = player_mgr.GetPlayerLV() or "0"
        if newLevel then
            _roleLevel = newLevel
        end
        local properties = {
            dataType = nType or 1,
            roleID = player_mgr.GetPlayerRoleID() or "0",
            roleName =  _roleName,
            roleLevel = _roleLevel,
            serverID = setting_server_data.GetLoginWorldID() or "0",
            serverName = setting_server_data.GetLoginWorldName() or "0",
            moneyNum = player_mgr.GetPlayerAllDiamond() or "0",
            roleCreateTime = player_mgr.GetRoleCreateTime() or "0",
            roleLevelUpTime = os.time(),
            vip = player_mgr.GetPlayerVipLevel() or "0"
        }
        local laymain_data 	= require "laymain_data"
        local cfg = laymain_data.GetCurrentLevelCfg()
        local guanqia = 0
        if cfg and cfg.mapID and cfg.checkpoint then
            guanqia = cfg.mapID * 1000 + cfg.checkpoint
        end
        local extraparam = {
            guanqia = guanqia or 0
        }
        --2022年1月19日 新增guanqia参数 如果存在新方法 使用新的sdk方法传递关卡数
        if Q1SDK.Instance.U8SubmitExtraData_new then
            Q1SDK.Instance:U8SubmitExtraData_new(dkjson.encode(properties),dkjson.encode(extraparam))
        else
            Q1SDK.Instance:U8SubmitExtraData(dkjson.encode(properties))
        end
    end

    q1sdk.U8ExitGame = function (callback)
        if not game_config.ENABLE_Q1SDK_CHANNEL then
            return
        end
        log.Warning("U8  ExitGame")
        Q1SDK.Instance:U8Exit(callback)
    end

    q1sdk.getPackageName = function ()
        
        if Application.platform == RuntimePlatform.IPhonePlayer then
            return ""
        end
        
        if Q1SDK.Instance.GetPackageName then
            return Q1SDK.Instance:GetPackageName()
        end
        return ""
    end

    q1sdk.getExtParams = function (callback)
        if game_config.ENABLE_Q1SDK_CHANNEL or not Q1SDK.Instance.GetExtParams then
            return false
        end

        -- 这里国内海外通用，都有这个接口
        --if game_config.Q1SDK_DOMESTIC then 
            if callback then
                Q1SDK.Instance:GetExtParams(callback)
            end
            return true
        --end
        --return false
    end
    
    --不推荐使用
    q1sdk.OpenUrl = function(OpenUrl)
        if not Q1SDK.IsEnable() then 
			return 
		end
        if Q1SDK.Instance.OpenUrlLoading then
            Q1SDK.Instance:OpenUrlLoading(OpenUrl,true,true)
            return
        end
        if not Q1SDK.Instance.OpenUrl then
            return
        end
        Q1SDK.Instance:OpenUrl(OpenUrl)	
    end

    --不推荐使用
    q1sdk.OpenLink = function(openLink)
        if not Q1SDK.IsEnable() then 
            return 
        end
        if not Q1SDK.Instance.OpenLink then
            return
        end

        Q1SDK.Instance:OpenLink(openLink) 
    end

    --不推荐使用
    -- 开启网页 (该接口不再调用，可调用下面OpenH5Url接口)
    q1sdk.OpenWebUrl = function (url)
        if not string.empty(url) then
            if Application.platform == RuntimePlatform.IPhonePlayer then
                if Q1SDK.Instance.OpenLink then
                    q1sdk.OpenLink(url)
                    return
                end
            end
            q1sdk.OpenUrl(url)
        end
    end 

    --不推荐使用
    q1sdk.OpenUrlLoading = function(OpenUrl)
        if not Q1SDK.IsEnable() then 
			return 
		end
        if Q1SDK.Instance.OpenUrlLoading then
            Q1SDK.Instance:OpenUrlLoading(OpenUrl,true,true)
            return
        end
        if not Q1SDK.Instance.OpenUrl then
            return
        end
        Q1SDK.Instance:OpenUrl(OpenUrl)	
    end

    --打开H5网页,新增H5的交互，可以上传图片等很多功能 （推荐使用）
    --isFullScreen:true or false  isFullScreen=true：全屏显示（隐藏导航栏及状态栏） isFullScreen=fasle:显示导航栏及状态栏
    q1sdk.OpenH5Url = function (url,isFullScreen)
        if not Q1SDK.IsEnable() then 
            return 
        end
        --安卓3.8.1版本支持H5跳转外部应用，之前所有版本的H5只支持交互功能不支持跳转外部应用
        local isIOS = Application.platform == RuntimePlatform.IPhonePlayer
        if isIOS or Q1SDK.Instance.isSupportH5Url and Q1SDK.Instance:isSupportH5Url() then
            if Q1SDK.Instance.OpenH5Url_IsFullScreen then
                Q1SDK.Instance:OpenH5Url_IsFullScreen(url,isFullScreen)
                return
            end
            if Q1SDK.Instance.OpenH5Url then
                Q1SDK.Instance:OpenH5Url(url)
                return
            end
        end
        q1sdk.OpenWebUrl(url)
    end

    --升级xcode16之后iOS18系统的包使用q1sdk.ApplicationOpenURL()会无法打开链接,使用iOS原生打开网页接口
    --Unity 2022.3.20+ LTS 之前版本、  Unity 2023.1.10+ 之前版本
    q1sdk.ApplicationOpenURL = function (url)
        if Application.platform == RuntimePlatform.IPhonePlayer then
            if Q1SDK.Instance.ApplicationOpenURL then
                Q1SDK.Instance:ApplicationOpenURL(url)
                return
            end
        end
        Application.OpenURL(url)
    end

	q1sdk.ChannelInitCallback = function ()
        --海外渠道
        if Q1SDK.Instance.ChannelInitCallback then
            Q1SDK.Instance:ChannelInitCallback(function ( ... )
                log.LoginWarning("SDK切换账号成功")
                local ui_login_main = require "ui_login_main"
                ui_login_main.SetIsSwitching(true)
                ui_login_main.ReturnLogin() 
                --越南IOS包需要游客登录
                if const.IsVietnamIosChannel() then
                    print("zzd______注册切换账号事件 先退出登录 后注册登录")
                    ui_login_main.Channel_GuestLogin()
                end
            end)
        end  
    end
    
    -- 渠道sdk埋点上报
    q1sdk.ChannelSDKReport = function(eventName, ext)
        if Q1SDK.Instance.ChannelSDKReport then
            local player_mgr = require "player_mgr"
            local net_prop_module = require "net_prop_module"
            local setting_server_data = require "setting_server_data"
            ext = ext or {}
            if type(ext) == "string" then
                ext = dkjson.decode(ext) or {}
            end
            ext.level = player_mgr.GetPlayerLV()--int类型
            ext.time = tonumber(os.server_time())*1000--将获取的秒数转化成long长整型
            ext.regTime = net_prop_module.GetLoginTime()--long类型
            ext.serverId = tostring(setting_server_data.GetLoginWorldID())--string类型
            ext.roleId = tostring(player_mgr.GetPlayerRoleID())--string类型
            ext.nickName = player_mgr.GetRoleName()--string类型
            ext = dkjson.encode(ext)
            Q1SDK.Instance:ChannelSDKReport(eventName, ext)
        end
    end

    -- 渠道三方埋点上报（Firebase、adjust、Appsflyer、Facebook等等）
    q1sdk.ChannelFirebaseAndOtherReport = function(eventName, properties)
        if game_config.CHANNEL_TYPE ~= const.CHANNEL_TYPE.BINGCHUAN then  --非冰川渠道
            -- 贪玩越南包
            -- firebase和Appsflyer af上报事件名需要添加af_
            if Q1SDK.Instance.ChannelFirebaseReport then
                Q1SDK.Instance:ChannelFirebaseReport(eventName, properties or "")
            end
            if Q1SDK.Instance.ChannelAppsflyerReport then
                if eventName ~= "purchase" and eventName ~= "complete_registration" then
                    Q1SDK.Instance:ChannelAppsflyerReport("af_"..eventName, properties or "")
                end
            end
        end
    end

    -- 直接评分弹窗
    q1sdk.RequestRateView = function ()
        if Q1SDK.Instance.requestRateView then
            Q1SDK.Instance:requestRateView()
        end
    end

    q1sdk.InitU8Callback = function ()
        if not game_config.ENABLE_Q1SDK_CHANNEL then
            return
        end
        if hasU8Init == true then
            return
        end
        hasU8Init = true
        local ui_login_main = require "ui_login_main"
        local net_login_module = require "net_login_module"
        Q1SDK.Instance:InitU8Callback(function (success, code, msg, tokenData)
            -- 切换账号 回到登录界面，直接重新进游戏 这里三方sdk没有调用切换账号的回调，先不处理，后面用到再测试
            -- local strData = dkjson.decode(tokenData)
            -- local data = {}
            -- data.userID = tostring(strData["userID"])
            -- data.sdkUserID = strData["sdkUserID"]
            -- data.username = strData["username"]
            -- data.sdkUserNam = strData["sdkUserNam"]
            -- data.timestamp = strData["timestamp"]
            -- data.extension = strData["extension"]
            -- data.session = strData["token"]
            -- net_login_module.SetU8UserInfo(data)
            -- ui_login_main.ReturnLogin()
            -- q1sdk.Logout()
        end, function ()
            -- 登出 回到登录界面，拉起sdk登录
            ui_login_main.ReturnLogin()
        end)
    end
    -- 海外版新增接口
    q1sdk.GetLocalRegionID = function ()
        if game_config.Q1SDK_DOMESTIC then
            return game_config.REGION_ID
        end
        -- 2022-07-13 海外指定PID进入指定默认服务器创角
        if PlayerPrefs.GetInt("localRegionID", -1) < 0 then
            local url_operation_mgr = require "url_operation_mgr"
            local defaultRegionCode = tonumber(url_operation_mgr.GetDefaultRegionCode()) 
            if defaultRegionCode == 1 or defaultRegionCode == 2 then
                log.Warning("GetLocalRegionID defaultRegionCode:", defaultRegionCode)
                return defaultRegionCode
            end
        end
        if Application.platform == RuntimePlatform.IPhonePlayer then
            local localRegion = PlayerPrefs.GetInt("localRegionID", -1)
            log.Warning("GetLocalRegionID:", localRegion)
            return localRegion
        else
            if Q1SDK.Instance.GetLocalRegionID then
                log.Warning("GetLocalRegionID:", Q1SDK.Instance:GetLocalRegionID())
                return Q1SDK.Instance:GetLocalRegionID()
            end
        end
        
        return game_config.REGION_ID
    end

    q1sdk.SaveLocalRegionID = function (regionID)
        PlayerPrefs.SetInt("localRegionID", regionID)
        if Q1SDK.Instance.SaveLocalRegionID then
            Q1SDK.Instance:SaveLocalRegionID(regionID)
        end
    end
    q1sdk.SetIsChannel = function ()
        if Q1SDK.Instance.SetIsChannel then
            Q1SDK.Instance:SetIsChannel(const.IsIndiaChannelV1())
        end
    end
    q1sdk.ChannelSwitchAccount = function(callback)
        if Q1SDK.Instance.ChannelSwitchAccount then
            Q1SDK.Instance:ChannelSwitchAccount(callback)
        end    
    end

    q1sdk.ChannelBindAccount = function()
        if Application.platform == RuntimePlatform.IPhonePlayer then--IOS包
            if Q1SDK.Instance.ChannelBindAccount then--绑定账号接口
                Q1SDK.Instance:ChannelBindAccount()
            end
        end
    end

    --渠道客服工具,目前越南包专用
    q1sdk.ChannelCustomer = function()
        print("q1 sdk ChannelCustomer")
        if Q1SDK.Instance.ChannelCustomer then
            print("Q1SDK.Instance:ChannelCustomer")
            Q1SDK.Instance:ChannelCustomer()
        end
    end

    --打开注销账号界面（通过sdk打开）
    q1sdk.LogoutAccountAction = function ()
        if Q1SDK.Instance.LogoutAccountAction then
            print("LogoutAccountAction function is exist ")
            Q1SDK.Instance:LogoutAccountAction()
            return true
        else
            print("Q1SDK.Instance not function LogoutAccountAction ")
        end
    end
    
    --获取谷歌商店积分奖励
    q1sdk.GetProductIds = function ()
        if not Q1SDK.IsEnable() then return end
        --log.Warning("获取谷歌商店积分奖励",Q1SDK.Instance.GetProductIds)
        if Q1SDK.Instance.GetProductIds then
            return Q1SDK.Instance:GetProductIds()
        end
    end

    --请求领取谷歌商店积分奖励
    q1sdk.RequestGooglePoints = function (actorId,serverId)
        if not Q1SDK.IsEnable() then return end
        --log.Warning("请求领取谷歌商店积分奖励",Q1SDK.Instance.RequestGooglePoints)
        if Q1SDK.Instance.RequestGooglePoints then
            Q1SDK.Instance:RequestGooglePoints(actorId,serverId)
        end
    end

    --监听屏幕变化
    q1sdk.RegisterOnConfigurationChanged = function ( callback )
        if not Q1SDK.IsEnable() then return end
        if Q1SDK.Instance and Q1SDK.Instance.RegisterOnConfigurationChanged then
            Q1SDK.Instance:RegisterOnConfigurationChanged(callback)
        end
    end

    --监听谷歌积分奖励
    q1sdk.RegisterOnGooglePointData = function ( callback )
        if hasGooglePointDataInit then
            return
        end
        if Q1SDK.Instance and not Q1SDK.IsEnable() then return end
        if Q1SDK.Instance.RegisterOnGooglePointData then
            Q1SDK.Instance:RegisterOnGooglePointData(callback)
            hasGooglePointDataInit = true
        end
    end

        --打开隐私协议
        q1sdk.OpenProlicy =function (callback)
            if not Q1SDK.IsEnable() then return false end
            if Q1SDK.Instance and Q1SDK.Instance.OpenProlicy then
                Q1SDK.Instance:OpenProlicy(callback)
                return true
            end
            return false
        end

    --保存图片到相册
    q1sdk.SaveImageToGallery = function(bytes,length)
        -- print("q1 sdk saveImageToGallery")
        local result = false
        if Q1SDK.Instance and Q1SDK.Instance.SaveImageToGallery then
            -- print("Q1SDK.Instance:saveImageToGallery")
            result = Q1SDK.Instance:SaveImageToGallery(bytes,length)
        end
        return result
    end
    -- 是否是有保存图片功能的新包
    q1sdk.HasNewSaveImageFunction = function()
        if Q1SDK.Instance and Q1SDK.Instance.SaveImageToGallery then
            return true
        else
            return false 
        end
    end

    --上传图片接口
    q1sdk.UploadImage = function(_imageId, callback)
        local url = nil
        local appid = const.GAMEID
        local pid = game_config.CHANNEL_ID
        local setting_server_data = require "setting_server_data"
        local serverId = setting_server_data.GetLoginWorldID()
        local player_mgr = require "player_mgr"
        local actorId = player_mgr.GetPlayerRoleID()
        local actorName = player_mgr.GetRoleName()
        local limitMemorySize = 50--测试
        local isCropper = true--默认开启裁剪功能
        if Q1SDK.Instance and Q1SDK.Instance.UploadImage then 
            Q1SDK.Instance:UploadImage(url, appid, pid, serverId, actorId, actorName, limitMemorySize, _imageId, isCropper, callback)
        end
    end

    --获取单张图片的审核状态
    q1sdk.GetImageReviewStatus = function(_imageId,callback)
        if Q1SDK.Instance and Q1SDK.Instance.GetImageReviewStatus then 
            Q1SDK.Instance:GetImageReviewStatus(_imageId,callback)
        end
    end

    --获取消息推送权限状态
    q1sdk.GetPushNotifiState = function(callback)
        --推送状态0未集成, 1同意, 2拒绝
        if Application.platform == RuntimePlatform.IPhonePlayer then
            if Q1SDK.Instance and Q1SDK.Instance.GetPushNotifiState then
                Q1SDK.Instance:GetPushNotifiState(callback)
                return true
            end
        else
            if Q1SDK.Instance and Q1SDK.Instance.GetPushNotifiState then
                local isOpen = Q1SDK.Instance:GetPushNotifiState()
                if isOpen then
                    callback("1", isOpen) 
                else
                    callback("0", isOpen) 
                end
                return true
            end
        end
        return false
    end

    --设置消息推送权限
    q1sdk.OpenNotificationSetting = function()
        if Application.platform == RuntimePlatform.IPhonePlayer then
            if Q1SDK.Instance and Q1SDK.Instance.OpenAppSetting then
                Q1SDK.Instance:OpenAppSetting()
                return true
            end
        else
            if Q1SDK.Instance and Q1SDK.Instance.OpenNotificationSetting then
                Q1SDK.Instance:OpenNotificationSetting()
                return true
            end
        end
        return false
    end



     --打开隐私协议
    q1sdk.OpenCMP = function (callback)
        if not Q1SDK.IsEnable() then return false end
        local const = require "const"
        log.Warning("CMP IsEuropeanUnionChannels", const.IsEuropeanUnionChannels())
        if not const.IsEuropeanUnionChannels() then return false end
        if Q1SDK.Instance and Q1SDK.Instance.OpenCMP then
            Q1SDK.Instance:OpenCMP(callback)
        end
    end

    -- CMP Firebase Report
    q1sdk.CMPFirebaseReport = function(jsonData)
        if Q1SDK.Instance and firebase then
            log.Warning("CMP CMPFirebaseReport jsonData", jsonData)
            local strData = dkjson.decode(jsonData)
            local adStorage = tostring(strData["ad_storage"] or "")
            local analyticsStorage = tostring(strData["analytics_storage"] or "")
            local adUserData = tostring(strData["ad_user_data"] or "")
            local adPersonalization = tostring(strData["ad_personalization"] or "")
            log.Warning("CMP CMPFirebaseReport： adStorage:"..adStorage.." analyticsStorage:"..analyticsStorage.." adUserData:"..adUserData.." adPersonalization:"..adPersonalization)
            firebase.SetConsent(adStorage, analyticsStorage, adUserData, adPersonalization)
        end
    end


end
q1sdk.SetLogcat(true)

local rechargeOrderidSet = {}
--AdjustAndFirebase 打点上报
--eventName事件名
--properties事件名组合参数
--rechargePay 支付额度，单位美元（非必要参数）
--rechargeOrderid 支付订单号（非必要参数）
--serverNtf 服务器通知的特殊打点（非必要参数）
q1sdk.AdjustAndFirebaseReport = function(eventName, properties, rechargePay, rechargeOrderid,serverNtf)   
    --adjust，firebase打点上报处理
    local reportCfg = GetGameEventReportCfg(eventName)
    --没有配置表示：该事件不需要在adjust，firebase上报
    if not reportCfg then
        --print("!!!!没有配置：该事件不需要在adjust，firebase上报", eventName)
        return
    end
    local eventFormat = reportCfg.eventFormat   --事件拼接模板
    local reportEventName = ""
    --没有模板直接使用事件名上报
    if string.empty(eventFormat) then
        reportEventName = eventName
        --log.Warning("adjust，firebase上报：", reportEventName)
    else
        --有模板，表示需要拼接参数后上报Adjust或Firebase
        reportEventName = string.format(eventFormat, properties.value)
        --log.Warning("adjust，firebase打点上报：模板：", eventFormat, "参数：", properties.value, "上报事件：", reportEventName)
    end
    --如果打的点在白名单中，且不是服务器单独打点，则退出  
    if  adjust.RechargeWhiteListConfig[reportEventName] == 1 and not serverNtf then
    	return
    end
    --如果参数列表中有美元支付额度，则取出给rechargePay赋值
    if not rechargePay and properties and properties.rechargePayUsd then 
        rechargePay = properties.rechargePayUsd
        if properties.rechargeOrderid then
            rechargeOrderid = properties.rechargeOrderid
        end
    end
    local needReport = true

    --首次上报判断
    if reportCfg.isFirst == 1 then

        local player_mgr = require "player_mgr"
        --获取事件是否已经有上报记录
        local hasReport = PlayerPrefs.GetInt(player_mgr.GetPlayerRoleID() .. reportEventName, 0) == 1
        if not hasReport then
            --没有上报记录，则本地写入记录
            PlayerPrefs.SetInt(player_mgr.GetPlayerRoleID() .. reportEventName, 1)
        end
        needReport = not hasReport
    end
    --needReport = true
    if needReport == false then
        -- log.Warning("<color=#FF00FF>事件上报,本地已经记录</color>eventName：", reportEventName)
        print("<color=#FF00FF>事件上报,本地已经记录</color>eventName：", reportEventName)
        return
    end

    --添加上报附加参数

    if reportCfg.reportAdjust == 1 then
        --获取配置token
        local tokenId = ""
        local tokenList = nil

        local const = require "const"
        local util = require "util"
        local channel_tag = util.GetChannelTag()
        local filedName
        if channel_tag then
            filedName = string.gsub(channel_tag, "%.", "_")
        end

        if Application.platform == RuntimePlatform.IPhonePlayer then
            if filedName ~= nil and reportCfg["iosToken_"..filedName] then
                tokenList = util.SplitString(reportCfg["iosToken_"..filedName], "#")
            else
                tokenList = util.SplitString(reportCfg.iosToken, "#")
            end
        else
            if filedName ~= nil and reportCfg["androidToken_"..filedName] then
                tokenList = util.SplitString(reportCfg["androidToken_"..filedName], "#")
            else
                tokenList = util.SplitString(reportCfg.androidToken, "#")
            end
        end
        print("adjust eventName:", eventName, ",token name:", filedName, ",tokenList:", tokenList)

        if not tokenList then 
            return 
        end

        --非拼接事件，直接使用0位token
        if string.empty(eventFormat) then 
            tokenId = tokenList[1]
            --log.Warning("非拼接事件，直接使用1位token", tokenId)
            --dump(tokenList)
        else
            --拼接事件，获取事件参数对应token
            local tokenIndex = GetTokenIndexBy(reportCfg.eventFormatParam, tostring(properties.value))
            if tokenIndex == -1 then
                --log.Warning("<color=#FF0000>token没有匹配到事件参数,退出上报</color>eventName：", reportEventName,"param:", properties.value)
            else
                tokenId = tokenList[tokenIndex]
            end
        end
        
        if string.empty(tokenId) then
            if Application.isEditor then
                log.Warning("error tokenId AdjustAndFirebaseReport eventName：", eventName)
            end
        else
            --log.Warning("<color=#FFFFFF>token事件上报</color>eventName：", reportEventName,"param:", properties and properties.value, "tokenid", tokenId)    
            -- util.DelayCallOnce(0.1, function()
                --价值参数上报
                local proper = nil
                if rechargePay then
                    proper = {num = rechargePay, type = "USD", orderid = rechargeOrderid}
                end
                if not string.empty(rechargeOrderid) then
                    if rechargeOrderidSet[rechargeOrderid] then
                        log.Error("充值订单号重复，adjust 内部去重会导致上报失败")
                    else
                        rechargeOrderidSet[rechargeOrderid] = true
                    end
                end
                --判断一下白名单
                local needChangeNum = false
                if adjust.RechargeWhiteListConfig[reportEventName] then
                    needChangeNum = true
                end
                if needChangeNum then
                    if not proper then
                        proper = {num = 1}
                    else
                        proper.num = 1
                    end
                end
                local url_operation_mgr = require "url_operation_mgr"
                local disableAdjustOtherRecharge = url_operation_mgr.GetConfig("disableAdjustOtherRecharge")
                log.Warning("disableAdjustOtherRecharge:", disableAdjustOtherRecharge, rechargePay, eventName)
                if not disableAdjustOtherRecharge or not rechargePay or eventName == "Event_RechargeSuccess" or needChangeNum then
                    -- (不开启禁用adjust其他充值 | 没有充值参数 | 事件为Event_RechargeSuccess)就发adjust事件
                    adjust.TrackEvent(tokenId, nil, proper)
                else
                    adjust.TrackEvent(tokenId)
                end
                log.Warning("<color=#FFFFFF>Adjus事件延时上报</color>eventName：", reportEventName,"rechargePay:", rechargePay,"param:", properties and properties.value, "tokenid", tokenId)
            -- end)
        end
    end

    if reportCfg.reportFirebase == 1 and not const.IsVietnamChannel() and not const.IsVietnamIosChannel() then
        --log.Warning("<color=#FF00FF>Firebase事件上报</color>eventName：", reportEventName,"param:", properties and properties.value)
        --添加价值参数
        local proper = nil
        local doubleValue = nil
        if rechargePay then
            doubleValue = rechargePay
            if type(rechargePay) ~= "number" then
                doubleValue = tonumber(rechargePay)
            end
            proper = string.format("{currency = 'USD',value = %s}", doubleValue)
        end
        if Application.platform == RuntimePlatform.IPhonePlayer then
            if firebase.RechargeSuccessTrackEvent and eventName == "Event_RechargeSuccess" and CS.com.firebase.sdk.Firebase.RechargeSuccessTrackEvent then
                firebase.RechargeSuccessTrackEvent(reportEventName,proper, doubleValue)
                local json = {
                    currency = 'USD',
                    value = doubleValue
                }
                local json_str = dkjson.encode(json)
                event.Trigger(event.GAME_EVENT_REPORT,"firebase_lua_valueType",json_str)
            else
                firebase.TrackEvent(reportEventName, proper)
            end
        else
            firebase.TrackEvent(reportEventName, proper)
        end
        log.Warning("<color=#FF00FF>Firebase事件延时上报</color>eventName：", reportEventName,"param:", properties and properties.value)
    end
    if reportCfg.reportFaceBook == 1 then
        --log.Warning("<color=#FF00FF>FaceBook事件上报</color>eventName：", reportEventName,"param:", properties and properties.value)
        -- util.DelayCallOnce(0.2, function()
            --带价值参数的上报
            if rechargePay then 
                facebook.TrackEventWithRechargeValue(reportEventName, 'USD', rechargePay)
            else
                facebook.TrackEvent(reportEventName)
            end
            log.Warning("<color=#FF00FF>FaceBook事件延时上报</color>eventName：", reportEventName,"rechargePay:", rechargePay,"param:", properties and properties.value)
        -- end)
    end
    --是否关闭af上报，目前联运渠道不需要af上报，走自己的af上报ChannelFirebaseAndOtherReport
    if not build_diff_setting.DisableAfReport() then
        --贪玩越南通过ChannelFirebaseAndOtherReport接口上报af,重复上报
        ReportAppsFlyer(reportCfg,reportEventName,rechargePay)
    end
    -- 其他渠道 三方上报
    local proper = nil
    if rechargePay then
        proper = string.format("{currency = 'USD',value = %s}", rechargePay)
    end
    --2022.6.27 添加条件解决贪玩韩国包首次安装recharge_init上报两次的问题
    -- if reportEventName ~= "recharge_init" then
    q1sdk.ChannelFirebaseAndOtherReport(reportEventName, proper)
    -- else
    --     log.Warning("zzd________reportEventName == \"recharge_init\"")
    -- end
end

--上报AF
function ReportAppsFlyer(_reportCfg,_reportEventName,_rechargePay)
    if _reportCfg.reportAppsFlyer == 1 then
        -- log.Warning("<color=#FF00FF>AppsFlyer事件上报</color>eventName：", _reportEventName)
        local appsflyer = require 'appsflyer'
        local url_operation_mgr = require "url_operation_mgr"
        local disableAppsFlyerReport = url_operation_mgr.GetConfig("disableAppsFlyerReport")
        -- log.Warning("disableAppsFlyerReport:", disableAppsFlyerReport)
        if not disableAppsFlyerReport then--不限制AF上报
            --带价值参数的上报
            local reportEventNameLower = string.lower(_reportEventName);
            if _rechargePay then 
                appsflyer.TrackEventWithRechargeValue(reportEventNameLower, _rechargePay, 'USD')
            else
                appsflyer.TrackEvent(reportEventNameLower)
            end
            -- log.Warning("<color=#FF00FF>AppsFlyer事件延时上报</color>eventName：", reportEventNameLower,"rechargePay:", _rechargePay)
        end
    end
end

function GetTokenIndexBy(paramListStr, param)
    local paramList = util.SplitString(paramListStr, "#")
    for key, value in pairs(paramList) do
        if value == param then 
            return key
        end
    end
    return -1
end

--获取打点配置
function GetGameEventReportCfg(eventName)
    local game_scheme = require "game_scheme"
    --配表未初始化
    if not game_scheme.m_pDC then
        return nil
    end

    local preload_resources = require "preload_resources"
    if not preload_resources.checkMark or not preload_resources.checkMark[2] then
        --print("lua未初始化完成！！！！！！！！！！！！！")
        return nil
    end

    local cfg = game_scheme:GameEventReport_1(eventName)
    return cfg
end

--djustAndFirebase累计充值打点上报
q1sdk.RoportTotalRechargeEvent = function(num, isUsd, orderid)
    local usNum = 0
    print("充值上报打点:", num, "isUsd", isUsd)
    local recharge_data = require "recharge_data"
    local money_type_mgr = require "money_type_mgr"
    if isUsd then 
        local fromMoneyType= recharge_data.IsUseRechargeChannel() and "zh" or nil
        local otherMoneyTypeSwitch = money_type_mgr.GetMoneyExchangeRate("en",num,fromMoneyType,orderid)
        usNum = otherMoneyTypeSwitch/100
    else
        --修改使用RechargeExchange,参数价格用分
        local cfg = recharge_data.GetExchangeCfg(num)
        if not cfg then
            --其他币种转换美元
            --新支付方式货币类型是人民币
            local fromMoneyType= recharge_data.IsUseRechargeChannel() and "zh" or nil
            local otherMoneyTypeSwitch = money_type_mgr.GetMoneyExchangeRate("en",num,fromMoneyType,orderid)
            if not otherMoneyTypeSwitch then
                return
            end
            usNum = otherMoneyTypeSwitch/100
        else
            --rmb换算美元
            usNum = cfg.USD/100
        end
    end
        -- print("充值上报打点:22222222222", num, "isUsd", isUsd)

    local player_mgr = require "player_mgr"
    local oldTotleRecharge = PlayerPrefs.GetInt(player_mgr.GetPlayerRoleID() .. "totleRecharge", 0)
    local newTotleRecharge = oldTotleRecharge + usNum
    PlayerPrefs.SetInt(player_mgr.GetPlayerRoleID() .. "totleRecharge", newTotleRecharge)
    local reportRanges = {4.99, 9.99, 19.99, 29.99, 49.99, 99.99}
    local range = 0
    local targetRange = 0
    for i = 1, #reportRanges do
        range = reportRanges[i]
        --print("遍历充值范围：", range)
        if newTotleRecharge >= range and oldTotleRecharge < range then
            targetRange = range
            --print("充值上报:", "oldTotleRecharge", oldTotleRecharge, "newTotleRecharge", newTotleRecharge, "targetRange", targetRange)
            --此次充值超过某个累计范围,则上报打点
            if targetRange ~= 0 then
                q1sdk.AdjustAndFirebaseReport("recharge_money", {value=targetRange*100}, usNum, orderid)
            end
        end
    end

    local vipLevel = player_mgr.GetPlayerVipLevel()
    if localAIhelpUserInfo then
        q1sdk.AIHelpInfoUpdate(localAIhelpUserInfo.roleId,localAIhelpUserInfo.roleName,localAIhelpUserInfo.level,vipLevel,localAIhelpUserInfo.serverId)
    end
end

--djustAndFirebase购买礼包打点上报
q1sdk.RoportPurchaseGiftEvent = function(goodsId, typeID, rechargePay, orderid)
    --购买首充礼包打点上报
    local rechargeCfg = goodsId and game_scheme:Recharge_0(goodsId)
    if rechargeCfg and rechargeCfg.iGoodsIdfy == 1047 then
        q1sdk.AdjustAndFirebaseReport("Successful_payment_first_charge", nil, rechargePay, orderid)
    end

    local giftGoodsEventDic = {[1] = "Monthly_Card"}
    --成长礼包上报
    if rechargeCfg and rechargeCfg.iGoodsIdfy == 1056 then
        q1sdk.AdjustAndFirebaseReport("Purchase_Gift", {value = giftGoodsEventDic[1056]}, rechargePay, orderid)
    end
    --召唤礼包上报
    if rechargeCfg and rechargeCfg.iGoodsIdfy == 1057 then
        q1sdk.AdjustAndFirebaseReport("Purchase_Gift", {value = giftGoodsEventDic[18]}, rechargePay, orderid)
    end

    --其他礼包上报
    local eventName = giftGoodsEventDic[typeID]
    if eventName then 
        q1sdk.AdjustAndFirebaseReport("Purchase_Gift", {value = eventName}, rechargePay, orderid)
    end
end

local abTestId = nil
local abTestType = nil
--上报ab测试类型
q1sdk.SetABTestProperty = function(_abTestType)
    abTestType = _abTestType
end

q1sdk.SetABTestProperty = function(_abTestType)
    abTestType = _abTestType
end

--发送请求验证码
q1sdk.SendEmailCodeReq = function(email,email_type,callback)
    local Q1SDK = CS.Q1.Q1SDK
    ---最新版本的调用接口加上邮箱状态检测
    if Q1SDK.Instance and Q1SDK.Instance.EmailQueryBindAndSendCode then
        Q1SDK.Instance.EmailQueryBindAndSendCode(email,email_type,callback)
    else
        --老版本发送验证码的接口
        if Q1SDK.Instance and Q1SDK.Instance.EmailSendCode then 
            Q1SDK.Instance:EmailSendCode(email,email_type,callback)
        end
    end
end

--发送邮箱绑定(邮箱和验证码一起发送)
q1sdk.SendEmailBindReq = function(email,code,email_type,callback,accountChangeCallback)
    local Q1SDK = CS.Q1.Q1SDK
    if Q1SDK.Instance and Q1SDK.Instance.EmailSendBinding then 
        Q1SDK.Instance:EmailSendBinding(email,code,email_type,callback,accountChangeCallback)
    -- else
    --     callback(true,email,code,1);
    end
end

--发送邮箱登录(邮箱和验证码一起发送)
q1sdk.SendEmailLoginReq = function(email,code,callback,accountChangeCallback)
    local Q1SDK = CS.Q1.Q1SDK
    if Q1SDK.Instance and Q1SDK.Instance.EmailSendLogin then 
        Q1SDK.Instance:EmailSendLogin(email,code,callback,accountChangeCallback)
    -- else
    --     callback(true,email,code,0);
    end
end

--发送邮箱验证换绑(邮箱和验证码一起发送)
q1sdk.SendEmailCheckReq = function(email,code,callback)
    local Q1SDK = CS.Q1.Q1SDK
    if Q1SDK.Instance and Q1SDK.Instance.EmailSendCheck then 
        Q1SDK.Instance:EmailSendCheck(email,code,callback)
    -- else
    --     callback(true,email_type,2);
    end
end


--时间戳转换为天数
function TimestampToDays(timestamp)
    local days = 0
    if timestamp and timestamp > 0 then
        days = math.ceil(timestamp/(3600 * 24))
    end
    return days
end

--- 旧版本properties传json,因需要另外添加统一字段,properties改成传table
function GameEventReport(event, eventName, properties,skipLog)
    --if game_config.CHANNEL_ID == 1 then
    properties = properties or {}
    if type(properties) == "string" then
        -- if not properties then
        --     properties = "{}"
        -- end
        properties = dkjson.decode(properties) or {}
    end
    --AdjustAndFirebase 打点上报
    q1sdk.AdjustAndFirebaseReport(eventName, properties)

    --local gw_task_data = require "gw_task_data"
    --local gw_task_mgr = require "gw_task_mgr"
    local laymain_data = require "laymain_data"
    local hookLevel = laymain_data.GetPassLevel()
    --local finishCnt = gw_task_mgr.GetDayTaskFinishCount()
    properties.hooklevel_ID = hookLevel


    --判断是否已经登录 打点每日任务数量
    local player_mgr = require "player_mgr"
    local user_id = player_mgr.GetPlayerUserID()
    if user_id ~= 0 then
        local gw_task_mgr = require "gw_task_mgr"
        properties.daily_task_num = gw_task_mgr.GetDayTaskCompleteCount()
    end    
    
    local player_mgr = require "player_mgr"
    local net_vip_module  = require "net_vip_module"
    properties.total_pay = net_vip_module.GetNewRechargeNum()
    local util = require "util"
    properties.random_ver = util.GetUserRandomClassify()
	local ui_login_main_mgr = require "ui_login_main_mgr"
    local setting_server_data = require "setting_server_data"
    properties.server_id_new = ui_login_main_mgr.GetLoginServerID()
    properties.server_id = ui_login_main_mgr.GetLoginWorldID() or ""
    properties.server =  setting_server_data.GetLoginWorldName() or ""
    
    local files_version_mgr = require "files_version_mgr"
    properties.curExternalVersion = files_version_mgr.ApkUpdateConfigTryGetValue("curExternalVersion") or ""
    if not abTestId then
        --TODO 没有获取deepLink的abTestId 后续如果使用DeepLink 需要修改
        if Application.isEditor then
            abTestId = 0
        else
            abTestId = files_version_mgr.GetAbTestId()    
        end
    end
    properties.ab_test_id = abTestId
    if abTestType then
        properties.ab_test_type = abTestType
    end
    
    local playerProp = player_mgr.GetPlayerProp()
    local currServerTime = os.server_time()
    local roleCreateTime = player_mgr.GetRoleCreateTime()
    local _diffTime = currServerTime - roleCreateTime

    local openSerTime = player_mgr.GetRoleOpenSvrTime()
    local _diffTime2 = currServerTime - openSerTime
    
    properties.pid_name = util.GetChannelTag()--渠道
    properties.pid = game_config.CHANNEL_ID--渠道id
    properties.account_id = player_mgr.GetPlayerUserID()--账号id
    properties.level = playerProp and playerProp.lv--当前等级
    properties.role_days = TimestampToDays(_diffTime)--角色创建时间差
    properties.sever_days = TimestampToDays(_diffTime2)--服务器开服天数时间差
    --properties.sever_days = properties.days
    properties.role_name = playerProp and playerProp.roleName--角色名
    properties.role_ID =  player_mgr.GetPlayerRoleID()
    ---2025/5/28 位面2新增通用属性联盟id
    if const.isInitLuaOver then
        local alliance_data = require "alliance_data"
        properties.LeagueAID =  alliance_data.GetUserAllianceId()
    else
        properties.LeagueAID = 0
    end
    
    properties = dkjson.encode(properties)
    local track_event_mgr = require "track_event_mgr"
    if track_event_mgr.GetState() then
        if not skipLog then
            log.Warning("GameEventReport>>>>>>>>>>>>>>>>>>>::", eventName, properties)
        end
    end
    q1sdk.TrackEvent(eventName, properties)

end
event.Register(event.GAME_EVENT_REPORT, GameEventReport)


----------------------------------上报事件处理----------------------------------------
local reportTable = {}
function CheckReportTable(eventName, newValue)
	if not reportTable[eventName] then
		if eventName == "levelup" then
			local player_mgr = require "player_mgr"
			reportTable[eventName] = player_mgr.GetPlayerLV()
		end
	else
		if reportTable[eventName] < newValue then
			reportTable[eventName] = newValue
			return true
		end
	end
	return false
end

function SetSysMemSizeProperties()
    local util = require "util"
    local Q1SDK = CS.Q1.Q1SDK
    local sysMemSize = util.GetSystemMemorySize()
    local properties = {
        system_memory_size = sysMemSize,
        }
    local strProperties = dkjson.encode(properties)
    Q1SDK.Instance:SetSuperProperties(strProperties)
end

--设置上报公共参数
local supPropIsSet = false
local roleCreateTime = 0
function SetSuperProperties()

    local Q1SDK = CS.Q1.Q1SDK
    if not Q1SDK.IsEnable() then return end
    if supPropIsSet ==false and roleCreateTime ~= 0 then
        local createDate = os.date("*t",roleCreateTime) 
        local createSecPass = createDate.hour*3600 + createDate.min*60+createDate.sec
        local num = math.floor((os.server_time()+createSecPass - roleCreateTime)/86400)
        -- 1.0.27版本的C#才有SetSuperProperties接口
        -- Application.platform == RuntimePlatform.Android and 屏蔽此平台判断，iOS需要此字段
        local util = require "util"
        local sysMemSize = util.GetSystemMemorySize()
        local properties = 
        {
            days = num,
            system_memory_size = sysMemSize,
        }
        local strProperties = dkjson.encode(properties)
        Q1SDK.Instance:SetSuperProperties(strProperties)
       
        supPropIsSet = true
    end
end

function swtichRole(serverId, roleId, roleName, roleLevel, user --[[,"switchRole"]], msg)
    Utility.GetPluginInstance():CallStatic("onPause")
end
--[[
public static void swtichRole() {

    // 首先上报角色A 从上线到切换角色时的在线时长
    Q1PlatformSDK.onPause();

    // 这是游戏端的切换角色逻辑
    ...

    // 切换角色完成，调用userEvent，传入角色B的RoleId等
    // 请一定注意：这里的倒数第二个参数action 一定要传值为 switchRole
    // 请一定注意：这里的倒数第二个参数action 一定要传值为 switchRole
    // 请一定注意：这里的倒数第二个参数action 一定要传值为 switchRole
    Q1PlatformSDK.userEvent(serverId,roleId,roleName,roleLevel,user,"switchRole",msg);

}]]
function SetRoleCreateTime(event, time )
    --roleCreateTime = time
    local player_mgr = require "player_mgr"
    roleCreateTime = player_mgr.GetRoleCreateTime()
end
event.Register(event.REC_ROLE_CREATE_TIME, SetRoleCreateTime)

function GetAdjustProperties()
	--local adjust = require "adjust"
    local files_version_mgr = require "files_version_mgr"
    local gameName = files_version_mgr.GetFinalTargetResKey()
    local isUisngStandAloneRes=files_version_mgr.IsUsingStandAloneTinyRes()
	local adjustAtt = adjust.GetAttribution()
	local json_str = dkjson.encode({
		adjust_network = adjustAtt.network or "",
		adjust_campaign = adjustAtt.campaign or "",
		adjust_adgroup = adjustAtt.adgroup or "",
        adjust_creative = adjustAtt.creative or "",
        minigamename = gameName,
        standalone = isUisngStandAloneRes,
	})
	return json_str
end

q1sdk.OnSceneDestroy = function()
    reportTable = {}
    supPropIsSet = false
    roleCreateTime = 0
end
event.Register(event.SCENE_DESTROY, q1sdk.OnSceneDestroy)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP, q1sdk.OnSceneDestroy)

local sdk_openInfo = {}
 -- usertype\":0,\"usertypelist\":\"2\",\"nickname\":\"\" binduserlist[{usertype:2,nickname:李青青,bingtime:1111}]
function q1sdk.GetSdkOpenInfo()
    return sdk_openInfo
end

function q1sdk.SetSdkOpenInfo(binduserlist)
    if binduserlist then
        local binduserlist = binduserlist
        for i=1, #binduserlist do
            local temp = binduserlist[i]
            if tostring(temp.usertype) == "2" then
                sdk_openInfo["GooglePlay"] = temp
            elseif tostring(temp.usertype)  == "1" then
                sdk_openInfo["Facebook"] = temp
            elseif tostring(temp.usertype)  == "7" then
                sdk_openInfo["AppleID"] = temp
            elseif tostring(temp.usertype)  == "8" then
                sdk_openInfo["HuaWei"] = temp
            elseif tostring(temp.usertype)  == "20" then
                sdk_openInfo["Twitter"] = temp
            elseif tostring(temp.usertype)  == "9999" then
                sdk_openInfo["EMail"] = temp
            end

        end
    end
end


function q1sdk.GetEmailInfo()
   if sdk_openInfo and sdk_openInfo["EMail"] then
        return sdk_openInfo["EMail"].nickname
   end
end

function q1sdk.SetRoleBindType(openInfo)
    if openInfo == nil then
        log.Warning("openInfo=nil")
        return
    end
    if openInfo == "" then
        log.Warning("openInfo=empty string")
        return
    end
    local json = require "dkjson"
    local jsonData = json.decode(openInfo) or {}
    q1sdk.SetSdkOpenInfo(jsonData["binduserlist"])

    bindTypeList = jsonData["usertypelist"]

    if bindTypeList == nil then
        log.Warning("bindTypeList=nil")
        return
    end
    userBindType = {}
    if bindTypeList == "" then
        log.Warning("bindTypeList=empty string")
        return
    end
    log.Warning("bindTypeList=", bindTypeList)

    local util = require "util"
    local temp = util.SplitString(bindTypeList, ",")
    for i=1, #temp do
        if temp[i] == "2" then
            log.Warning("已经绑定google 上报信息1")
            --local ChangePackgeConfig = require "ChangePackgeConfig"
            --ChangePackgeConfig.Request_ExchangeBindGoogle(1)--已经绑定google
            userBindType["GooglePlay"] = true
        elseif temp[i] == "1" then
            userBindType["Facebook"] = true
        elseif temp[i] == "7" then
            userBindType["AppleID"] = true
        elseif temp[i] == "8" then
            userBindType["HuaWei"] = true
        elseif temp[i] == "20" then
            userBindType["Twitter"] = true
        elseif temp[i] == "9999" then
            userBindType["EMail"] = true
        end
    end
    --手动绑定发奖有弹窗
    q1sdk.ReqBindReward()
end

---@see 是否存在绑定状态
function q1sdk.GetIsExitBindState() 
    local isBind = false
    if userBindType then
        for k, v in pairs(userBindType) do
            if v then
                isBind = true
                break
            end
        end
    end
    return isBind
end

function q1sdk.ReqBindReward() 
    local isBind = q1sdk.GetIsExitBindState()
    local email_subscribe_mgr = require "email_subscribe_mgr"
    email_subscribe_mgr.Log("ReqBindReward",isBind, userBindType["EMail"])
    if isBind then
        local net_activity_module = require "net_activity_module"
        local activity_mgr = require "activity_mgr"
        local isPopReward = net_activity_module.GetBindISPopReward()
        ---手动绑定通过登录回调触发
        local rewardStatus = activity_mgr.OnGetTopicBindRewardStatus()
        email_subscribe_mgr.Log("ReqBindReward A",isPopReward, rewardStatus)
        if isPopReward and not rewardStatus then
            log.Warning("Email-->绑定成功,需要发奖弹窗")
            net_activity_module.MSG_GETBINDEMAIL_REWARD_REQ()
        end
    end
end

function q1sdk.SetGoogleBindType(openInfo)
    if openInfo == nil then
        log.Warning("openInfo=nil")
        return
    end
    if openInfo == "" then
        log.Warning("openInfo=empty string")
        return
    end
    local json = require "dkjson"
    local jsonData = json.decode(openInfo) or {}
    print("googlePlay绑定成功并且解析")
    if jsonData["partner_id"] == 2 then
        log.Warning("已经绑定google 上报信息2")
        local ChangePackgeConfig = require "ChangePackgeConfig"
        ChangePackgeConfig.Request_ExchangeBindGoogle(2)--绑定google成功
        print("googlePlay绑定成功并且保存本地数据")
        userBindType["GooglePlay"] = true
        
        q1sdk.SetSdkOpenInfo(jsonData["binduserlist"]) 
        if localAIhelpUserInfo then
            local player_mgr = require("player_mgr")
            local vipLevel = player_mgr.GetPlayerVipLevel()
            q1sdk.AIHelpInfoUpdate(localAIhelpUserInfo.roleId,localAIhelpUserInfo.roleName,localAIhelpUserInfo.level,vipLevel,localAIhelpUserInfo.serverId)
        end
    end
end

function q1sdk.GetRoleBindType()
    local version_mgr = require "version_mgr"
    if version_mgr.CheckSvnTrunkVersion(30871) or version_mgr.CheckSvnBranchVersion(31261) then
        return userBindType
    else
        --30871版本以前还没有双平台绑定功能，按以前的逻辑走, 是游客就都没绑定(显示绑定按钮)，不是游客就都绑定(不显示绑定按钮)
        if q1sdk.Visitor() then
            log.Warning("visitor")
            return {}
        else
            log.Warning("allbind")
            return {["Facebook"] = true, ["GooglePlay"] = true, ["AppleID"] = true}
        end
    end
end


function q1sdk.RegisterOnUserActivity( callback )
    local Q1SDK = CS.Q1.Q1SDK
    if not Q1SDK.IsEnable() then return end

    if Q1SDK.Instance.RegisterOnUserActivity then
        Q1SDK.Instance:RegisterOnUserActivity(callback)
    end
end

return q1sdk