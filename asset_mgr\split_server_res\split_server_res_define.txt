local require = require
local define = {}

define.VerTaskState =
{
    None = 0,
    Waiting = 1,                    --等待  
    RequestFiles2 = 2,              --请求Files2
    RequestFiles2Finish = 3,        --请求Files2完成
    RequestFiles2Timeout = 4,       --请求Files2超时
    GetVersionTask = 5,             --获取版本下载任务
    Downloading = 6,                --后台下载
    SaveHashRemoteVirtual = 7,
    Finish = 8,                     --版本任务完成
    Fail = 9,                       --版本任务失败
}

define.TaskState =
{
    NONE = 0,         -- 无状态
    WAITING = 1,      -- 等待下载状态
    DOWNLOADING = 2,  -- 下载中 (下载-校验crc-改名)
    FINISH = 3,       -- 下载完成状态
    CANCEL = 4,       -- 取消下载
    ERROR = 5,        -- 下载失败状态 
}

define.VerTaskType =
{
    BgDownload = "background_download",       --当前服后台下载 (当前使用版本资源校验下载, 防止当前版本资源不全)
    PreDownload = "pre_download",             --当前服预下载 (当前区服可使用的最新版本下载)
    SwitchServerResDownload = "switch_server_res_download",  --切服资源下载 (目标区服没有可使用版本触发的下载)
}

---ui界面显示名字，todo lang配置
define.VerTaskTypeName =
{
    BgDownload = "BgDownload",         
    PreDownload = "PreDownload",            
    SwitchServerResDownload = "switch server download", 
}

define.FileDownloadWay = 
{
    BgDownloadMgr = "BgDownloadMgr",               --使用BgDownloadMgr实现，包含后台下载、后台写文件和后台校验CRC, 需要另外实现文件改名，支持断点续传，不更新文件下载进度
    HttpInst = "HttpInst",     --http_inst的UnityWebRequest+DownloadHandlerFile实现 包含后台下载和后台写文件，需要另外实现校验CRC和文件改名，不支持断点续传，更新文件下载进度略耗
    HttpWebRequest = "HttpWebRequest",             --使用HttpWebRequest的新实现，包含后台下载、后台写文件、后台校验CRC、后台改名，支持断点续传，不更新文件下载进度
    UnityWebRequest = "UnityWebRequest",           --使用UnityWebRequest的新实现，包含后台下载、后台写文件、后台校验CRC、主线程改名，支持断点续传，支持更新文件下载进度
}

define.DownloadTimeout = 
{
    bgDownloadMgrTimeout = 11,           --bgDownloadMgr下载超时时间
    unityWebReqTimeout = 6,              --unityWebReq下载超时时间
    newWayTimeout = 11,  
}

define.DownloadErrorCode =
{
    HttpError = -7,
    CheckSumFail = -11,       --crc校验失败
    SaveFileOccupied = -212,  --保存文件被占用
    RenameFail = -211,        --rename失败
    Timeout = -121,           --超时
    OtherError = -222,
}

define.requestFiles2Timeout = 21           --requestFiles2超时时间
define.requestFiles2MaxCount = 3           --requestFiles2超时次数
define.needWifi = false                    --是否需要Wifi
define.maxDownloadCount = 5                --同时处理的任务数量限制
define.reDownloadTimeOnChangeType = 1         --修改下载方式（多线程失败改主线程）后多少秒重试    
define.reDownloadTimeOnSaveFileOccupied = 6   --存在io冲突延时重试时间    
define.saveHashRemoteVirtualTag = "split_server_res_download"

define.isEnableDnsSwitch = false  --是否开启dns切换功能
define.nextDnsFailTimes = 5       --切换dns的失败次数
define.checkSumIsAsync = true
define.isEnableSaveTemp = true    --是否使用临时文件下载，checksum成功再Rename
define.fileRenameRetryTime = 2    --改名重试时间
define.maxRenamePreFrame = 5      --每帧最多数量
define.maxRenameRetryTime = 10    --改名最大重试次数
define.TaskListChangeEvent = "ResVersionTaskListChange"

define.predownloadStartCheckDelay = 5   --预下载检测启动延时时间
define.preDownloadCheckInterval = 20    --检测新版本资源定时器时间间隔
define.preDownloadCheckTimeLimit = 1    --前后两次检测新版本资源时间间隔
define.reqUpdateJsonTimeout = 10        --请求update.json的超时时间

define.GetResVerTaskClass = function(verTaskType)
    if verTaskType == define.VerTaskType.PreDownload then
        return require"split_server_res_predownload_task"

    elseif verTaskType == define.VerTaskType.BgDownload then
        return require"split_server_res_bgdownload_task"

    elseif verTaskType == define.VerTaskType.SwitchServerResDownload then
        return require"split_server_res_switch_server_task"

    else
        return require"split_server_res_base_task"
    end
end

define.logger = require("logger").new("sw_split_server_res_log")
------------------------ 上报打点相关---------------------
define.reportData =
{
    ["0%"] = 0,
    ["10%"] = 0,
    ["20%"] = 0,
    ["30%"] = 0,
    ["40%"] = 0,
    ["50%"] = 0,
    ["60%"] = 0,
    ["70%"] = 0,
    ["80%"] = 0,
    ["90%"] = 0,
    ["100%"] = 0,
}
return define