local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local log = require "log"
local red_const = require "red_const"
local flow_text = require "flow_text"
local ui_window_mgr = require "ui_window_mgr"
local gw_task_util = require "gw_task_util"
local item_day_btn = require "item_day_btn"
local time_util = require "time_util"
local os = require "os"
local e_handler_mgr = require "e_handler_mgr"
local gw_task_const = require "gw_task_const"
local gw_task_mgr = require "gw_task_mgr"
local taskpart_pb = require "taskpart_pb"
local land_revival_data = require "land_revival_data"
local game_scheme = require "game_scheme"
local item_data = require "item_data"
local goods_item_new = require "goods_item_new"
local reward_mgr = require "reward_mgr"
local iui_item_detail = require "iui_item_detail"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_land_revival_day_task_binding"

--region View Life
module("ui_land_revival_day_task")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self:InitScrollTable()

    self:BindUIRed(self.btn_BoxIcon.transform, red_const.Enum.LandRevivalBox)
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil
    
    if self.srt_taskContent then
        self.srt_taskContent:ItemsDispose()
    end

    if self.itemBoxBubble then
        self.itemBoxBubble:Dispose()
    end
    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

---@public function 设置活动结束时间
function UIView:SetActivityTimer(endTime)
    if endTime then
        self.endTimer = self.endTimer or util.IntervalCall(1, function()
            local tempTime = endTime - os.server_time()
            if not util.IsObjNull(self.txt_time) then
                self.txt_time.text = time_util.FormatTime5(tempTime)
            end
            if tempTime <= 0 then
                if not util.IsObjNull(self.txt_time) then
                    self.txt_time.text = lang.Get(102401) --"活动已结束"
                end
                self.endTimer = nil
                return true
            end
        end)
    end
end

---@public function 初始化天数toggle
function UIView:InitDayToggle(data)
    if not data then
        return
    end
    
    for i, v in ipairs(data.dayArr) do
        local itemGo = UIUtil.AddChild(self.rtf_dayBtnParent.gameObject, self.item_dayBtn)
        self:BindUIRed(itemGo.transform, red_const.Enum.LandRevivalDayToggle,{i},{pos ={ x = 50 ,y = 50 }})
        self.toggleList = self.toggleList or {}
        self.toggleList[i] = item_day_btn.NewItem(itemGo)
        
        local isLock = data.curDay < v
        local tempData = {
            isLock = isLock,--是否上锁
            isSelect = data.curDay == v, --是否选中
            day = v,
        }
        tempData.clickButtonFunc = function()
            e_handler_mgr.TriggerHandler(self.controller_name, "SelectDayToggle",v)
            self.toggleList[i]:RefreshSelectState(isLock,true)
            self:ChangeOtherDayBtn(v,data.curDay)
            
            if isLock then
                flow_text.Add(lang.Get(655014))
            end
        end
        self.toggleList[i]:UpdateData(tempData)
    end

    self:InitChildToggleList()
end

function UIView:ChangeOtherDayBtn(selectDay,curDay)
    if not self.toggleList then
        return
    end
    for i, v in ipairs(self.toggleList) do
        if i ~= selectDay then
            v:RefreshSelectState(curDay < i,false)
        end
    end
end

---@public function 仅用于跳转天数
function UIView:RefreshDayToggle(selectDay,curDay)
    if not self.toggleList then
        return
    end
    for i, v in ipairs(self.toggleList) do
        v:RefreshSelectState(curDay < i,i == selectDay)
    end
end

---@public function 初始化子页签
function UIView:InitChildToggleList()
    local item_up_hammer_toggle = require "item_up_hammer_toggle"
    self.UpHammerToggleArr = self.UpHammerToggleArr or {}
    self.UpHammerToggleArr[1] = item_up_hammer_toggle.NewItem(self.item_upHammerToggle1)
    self.UpHammerToggleArr[2] = item_up_hammer_toggle.NewItem(self.item_upHammerToggle2)
    self.UpHammerToggleArr[3] = item_up_hammer_toggle.NewItem(self.item_upHammerToggle3)
    --默认第一个页签
    self.UpHammerToggleArr[1]:SetToggleState(true)
    
end
---@public function 刷新toggle显示
function UIView:RefreshChildToggle(data,dayIndex,defaultIndex)
    for i, v in ipairs(self.UpHammerToggleArr) do
        v:UpdateData(data[i])
    end
    --默认第一个页签
    if defaultIndex then
        self.UpHammerToggleArr[defaultIndex]:SetToggleState(true)
    end

    self:BindUIRed(self.item_upHammerToggle1.transform, red_const.Enum.LandRevivalHammerToggle, {dayIndex , 1 })
    self:BindUIRed(self.item_upHammerToggle2.transform, red_const.Enum.LandRevivalHammerToggle,{dayIndex , 2 })
    self:BindUIRed(self.item_upHammerToggle3.transform, red_const.Enum.LandRevivalHammerToggle,{dayIndex , 3 })
end

function UIView:InitScrollTable()
    self.srt_taskContent.onItemRender = function(...)
        self:TaskListRender(...)
    end
    self.srt_taskContent.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            for _, v in pairs(scroll_rect_item.data.goodItemArr) do
                v:Dispose()
            end
            scroll_rect_item.data.goodItemArr = nil
        end
    end
end

function UIView:TaskListRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local listBg = scroll_rect_item:Get("listBg")
    local yellowTaskName = scroll_rect_item:Get("yellowTaskName")
    local blueTaskName = scroll_rect_item:Get("blueTaskName")
    local rewardParent = scroll_rect_item:Get("rewardParent") 
    local receiveBtn = scroll_rect_item:Get("receiveBtn")
    local receivedState = scroll_rect_item:Get("receivedState")
    local goBtn = scroll_rect_item:Get("goBtn")

    yellowTaskName.text = lang.Get(dataItem.taskLang)
    blueTaskName.text = lang.Get(dataItem.taskLang)

    listBg:Switch(dataItem.isHammerTask and 1 or 0)
    self:SetActive(yellowTaskName,dataItem.isHammerTask)
    self:SetActive(blueTaskName,not dataItem.isHammerTask)
    
    --刷新奖励列表
    if not scroll_rect_item.data.goodItemArr then
        scroll_rect_item.data.goodItemArr = {}
    end
    local rewardData = reward_mgr.GetRewardGoodsList(dataItem.rewardID)

    local maxIndex = 0

    for i, v in ipairs(rewardData) do
        local goodItem = scroll_rect_item.data.goodItemArr[i] or goods_item_new.CGoodsItem():Init(rewardParent.transform, nil, 0.59)
        goodItem:SetGoods(nil, v.id, v.num, function()
            iui_item_detail.Show(v.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, v.num, nil, nil)
        end)
        goodItem:SetCountEnable(true)
        scroll_rect_item.data.goodItemArr[i] = goodItem
        maxIndex = i
    end

    for i, v in ipairs(scroll_rect_item.data.goodItemArr) do
        if i > maxIndex then
            v:Dispose()
            scroll_rect_item.data.goodItemArr[i] = nil
        end
    end
    
    self:SetActive(receiveBtn, false)
    self:SetActive(receivedState, false)
    self:SetActive(goBtn, false)
    
    if not dataItem.isLockJump then
        if dataItem.status then
            self:SetActive(receivedState, true)
        else
            if dataItem.rate >= dataItem.targetRate then
                --已完成
                self:SetActive(receiveBtn, true)
            else
                self:SetActive(goBtn, true)
                self:AddOnClick(goBtn.onClick,function()
                    ui_window_mgr:UnloadModule("ui_festival_activity_center")
                    gw_task_util.TaskJumpFunc(dataItem.taskID)
                end)
            end
        end

        self:AddOnClick(receiveBtn.onClick,function()
            self:ReceiveBoxReward(dataItem.taskID)
        end)
    end
    
end

---@public function 刷新任务列表
function UIView:RefreshTaskList(data)
    if not data then
        return
    end
    
    local len = #data
    self.srt_taskContent:SetData(data, len)
    self.srt_taskContent:Refresh(0, -1)
end

---@public function 领取宝箱奖励
---@param taskID number 任务ID
function UIView:ReceiveBoxReward(taskID)
    local activityID = land_revival_data.GetActivityTaskID()

    local msg = taskpart_pb.TMSG_ACT_TASK_GETREWARD_REQ()
    msg.taskId:append(taskID)
    gw_task_mgr.TakeTaskReward(msg,activityID, gw_task_const.TaskModuleType.pioneer_target)
end

---@public function 设置vip任务进度
function UIView:SetVipTaskProgress(curProgress, totalProgressRate)
    if totalProgressRate > 0 then
        self.sld_leftSlider.value = curProgress/totalProgressRate
    end
    if curProgress >= totalProgressRate then
        self.txt_sldNum.text = lang.Get(36085)
    else
        self.txt_sldNum.text = string.format("%s/%s", curProgress, totalProgressRate)
    end
end

---@public function 初始化宝箱奖励展示
function UIView:InitBoxRewardShow(rewardData)
    if rewardData then
        local item_box_bubble = require "item_box_bubble"
        self.itemBoxBubble = self.itemBoxBubble or item_box_bubble.NewItem(self.item_boxBubble)
        self.itemBoxBubble:UpdateData(rewardData)
        self:SetActive(self.item_boxBubble, true)
    else
        self.ss_BoxIcon:Switch(1)
        self:SetActive(self.item_boxBubble, false)
        self.ator_BoxIcon.enabled = false
    end
end

function UIView:JumpDayIndex(curDay)
    local value = curDay/7
    self.sr_dayScrollList.horizontalNormalizedPosition = value --value
end

function UIView:PlayBoxAnim(isPlay)
    self.ator_BoxIcon.enabled = isPlay
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
		window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, true, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil, true, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
