local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local ui_util = require "ui_util"
local actor_face_data = require "actor_face_data"
local face_item_new = require "face_item_new"
local world_id_item = require "world_id_item"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_war_zone_race_binding"

--region View Life
module("ui_war_zone_race")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

---@public function 设置比拼弹窗数据
function UIView:ShowRacePopup(data)

    self.txt_left_ZoneID.text =string.format("#%s",ui_util.GetWorldIDToShowWorldID(data.leftWorld, nil, ui_util.WorldIDRangeType.Normal))
    self.txt_right_ZoneID.text = string.format("#%s",ui_util.GetWorldIDToShowWorldID(data.rightWorld, nil, ui_util.WorldIDRangeType.Normal))

    if data.leftData and data.leftData.roleID then
        self:SetActive(self.rtf_LeftMvpInfo,true)
        --self.rtf_leftHeadParent
        self.leftMvpHead = self.leftMvpHead or face_item_new.CFaceItem():Init(self.rtf_leftHeadParent, nil, 1.1)
        local face = actor_face_data.ProcessFaceIDAndFaceStr(data.leftData.faceID,data.leftData.faceStr)
        self.leftMvpHead:SetFaceInfo(face,function()
            local mgr_personalInfo = require "mgr_personalInfo"
            mgr_personalInfo.ShowRoleInfoView(data.leftData.roleID)
        end)
        self.leftMvpHead:SetFrameID(data.leftData.frameID,true)
        self.VData["leftHead"] = self.leftMvpHead
        self.txt_leftName.text = util.SplicingUnionShortName(data.leftData.playerShortName, data.leftData.playerName)
        self.txt_leftPower.text = string.format("%spt",data.leftData.playerScore or 0)
    end

    if data.rightData and data.rightData.roleID then
        self:SetActive(self.rtf_RightMvpInfo,true)
        self.rightMvpHead = self.rightMvpHead or face_item_new.CFaceItem():Init(self.rtf_rightHeadParent, nil, 1.1)
        local face2 = actor_face_data.ProcessFaceIDAndFaceStr(data.rightData.faceID,data.rightData.faceStr)
        self.rightMvpHead:SetFaceInfo(face2,function()
            local mgr_personalInfo = require "mgr_personalInfo"
            mgr_personalInfo.ShowRoleInfoView(data.rightData.roleID)
        end)
        self.rightMvpHead:SetFrameID(data.rightData.frameID,true)
        self.VData["rightHead"] = self.rightMvpHead
        self.txt_rightName.text = util.SplicingUnionShortName(data.rightData.playerShortName, data.rightData.playerName)
        self.txt_rightPower.text = string.format("%spt",data.rightData.playerScore or 0)
    end
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
		window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
