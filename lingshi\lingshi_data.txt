-- .txt ------------------------------------------
-- author:  梁骐显
-- date:    2020.10.21
-- ver:     1.0
-- desc:   	灵石数据管理
--[[
    TODO：
    1、命令分类
    2、命令搜索
    3、自定义收藏
]]
--------------------------------------------------------------
local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local tonumber = tonumber
local os = os

local PlayerPrefs = CS.UnityEngine.PlayerPrefs

local util = require "util"
local net = require "net"
local msg_pb = require "msg_pb"
local goldfinger_pb = require "goldfinger_pb"
local json = require "dkjson"
module("lingshi_data")
local enablePrintID = false
local enableLingshiRecharge = false
local enableRecallMessage = false
local enableSendMultiMsg = false
local enableSkipClientMsgLimit = false
local enableFlagExternScreen = false
local enableChangeLangTip = false
local CmdToClient = "CmdToClient"
--原CommandTable总目录换成CommandTableBase
local CommandTableBase = {
    [1] = {
        name = "旧版",
        command = {
            { text = "DelThisAccount", des = "删除这个账号(慎用!)" },
            { text = "clearall", des = "清除玩家所有数据" },
            { text = "leagueComp 0", des = "一键修改联盟争霸各种限制(leagueComp无参数重置副本)" },
            { text = "getheroteam 1", des = "一键添加阵容，1小R/2中R/3大R" },
            { text = "superstone", des = "一键添加各种资源" },
            { text = "getAllSigils", des = "一键添加所有符文" },
            { text = "modifyIdleStage 1", des = "慎用 会影响城建地格!!! 修改狩猎关卡" },
            { text = "ModifyFromWorldID", des = "修改fromWorldId" },
            { text = "modifyChinaRedStage 1", des = "修改舰队远征关卡" },
            { text = "ModifyIdleReward", des = "修改狩猎奖励" },
            { text = "gethero 1 1", des = "获取英雄 ID 数量" },
            { text = "delhero 1", des = "删除英雄 英雄ID" },
            { text = "getgoods 20001 1", des = "获取物品 ID 数量" },
            { text = "delgoods 20001", des = "删除物品 物品ID" },
            { text = "getcoin 1000", des = "获取金币" },
            { text = "getdiamond 1000", des = "获取钻石" },
            { text = "getequipment 25201 2 3", des = "获取装备ID 数量 强化等级" },
            { text = "getherostar 20 13", des = "获取英雄ID 星级" },
            { text = "addexp 1000", des = "增加玩家经验值" },
            { text = "modifylv 80", des = "修改玩家等级" },
            { text = "addvipexp 200", des = "增加VIP经验" },
            { text = "modifyviplv 1", des = "修改玩家VIP等级" },
            { text = "SetRoleCreateTime 86401", des = "修改角色创建时间 时间戳 重登生效" },
            { text = "addheroexp 1000", des = "增加玩家英魂" },
            { text = "showheropro 1", des = "查看英雄属性 herSid" },
            { text = "showheroproByID 1", des = "查看英雄属性 heroid" },
            { text = "showheropower", des = "打印英雄战力" },
            { text = "modifyTowerStage 1", des = "修改星际通缉关卡 关卡" },
            { text = "showtime", des = "在服务器上打印时间" },
            --{text = "settime 2018 12 25 12 12 12", des = "设置服务器逻辑时间年 月 日 时 分 秒"},  --灵石改时间会导致各种异常 暂不能用
            --{text = "addtime 12 12 12", des = "增加服务器逻辑时间 时 分 秒"},  
            --{ text = "resetLeagueBossCount", des = "重置联盟boss挑战次数" },
            --{ text = "setLeaugeOfficer", des = "设置为联盟官员" },
            { text = "preventwallow 0 0", des = "修改防沉迷时间(单位s) 上报间隔 每次上报时长" },
            { text = "getreward 0", des = "获取奖励 奖励id" },
            --{ text = "treasurerecover", des = "重置联盟探宝冷却时间 陷阱中不可用" },
            { text = "cleanStagePlot", des = "清空玩家过关剧情" },
            { text = "modifyMateLevel 1", des = "修改羁绊等级" },
            { text = "addMateExp 100", des = "增加羁绊经验" },
            { text = "completeTargetTask", des = "完成当前目标任务" },
            { text = "resetTargetTask", des = "重置目标任务" },
            --{ text = "createLeague", des = "创建新联盟" },
            --{ text = "joinLeague", des = "加入指定联盟" },
            --{ text = "setLeagueBoss 1", des = "设置当前联盟boss" },
            { text = "resetMaze", des = "重置异界迷宫" },
            { text = "passMaze", des = "通关异界迷宫" },
            { text = "passMazeMN", des = "通关迷宫M层N次" },
            { text = "showHistoryPower", des = "服务器打印最近几天战力" },
            { text = "pushHistoryPower", des = "增加最近几天战力" },
            { text = "passFactionStage 1 1", des = "阵营通缉 类型 关卡" },
            { text = "addRelic 3", des = "获取异界迷宫遗物" },
            { text = "AshMaxStage 1 1", des = "设置遗落之境最大关卡:map lv" },
            { text = "AshStage 1 1", des = "设置遗落之境关卡:map lv" },
            { text = "AshAddMedicine 1", des = "遗落之境增加药水:药品id" },
            { text = "AshAddStore 1 1", des = "遗落之境增加折扣商品:折扣类型 商品id" },
            { text = "AshRestart", des = "遗落之境重新开始活动" },
            { text = "getUStar 1000", des = "获取宇宙之星" },
            { text = "getGStar 1000", des = "获取银河之星" },
            { text = "getPassport", des = "获取通行证" },
            { text = "setAutoLogin -1", des = "-1:自动选择,0:不使用自动登录,1:自动登录" },
            { text = "UpgradeEquip", des = "未穿戴12档以上装备升9级" },
            { text = "UpgradeArtifact", des = "未穿戴神器每次升10级" },
            { text = "UpgradeExclusive 13", des = "13星英雄专属每次升10级" },
            { text = "PeakAddRelic 1", des = "获取时光之巅遗物：遗物id" },
            { text = "PeakSetLevel 1", des = "设置时光之巅关卡：levelid" },
            { text = "PeakGetSomeBox", des = "获取光之巅关卡的一些宝箱:levelid" },
            { text = "PeakDelIndependEvt", des = "移除独立事件以加快测试" },
            { text = "PeakClearFog", des = "移除迷雾" },
            { text = "PeakCreateDynEvent", des = "创建尚未创建的动态事件" },
            { text = "PeakMonsterSetHp1", des = "设置怪物血量1" },
            { text = "ResetEquipEctype", des = "重置装备副本个人数据" },
            { text = "ModifyFarm 1 30", des = "type,num(type1加能量, type2随机摇奖)" },
            --{ text = "SetLeagueLevel 1", des = "设置联盟等级 等级" },
            --{ text = "AddLeagueExp 1000", des = "增加联盟经验 数量" },
            --{ text = "AddLeagueActivity 1000", des = "增加联盟活跃度 数量" },
            { text = "SetSelfCEOR5", des = "设置为联盟盟主、R5" },
            { text = "SetSelfR4", des = "设置为联盟R4" },
            { text = "PrinTimeZoneId", des = "打印玩家和玩家所在联盟时区" },
            { text = "ModifyTimeZoneId 1", des = "修改玩家时区" },
            { text = "WeaponUnActive 3", des = "反激活战舰 id" },
            { text = "addArenaScore 100", des = "日常竞技场积分 score" },
            --{ text = "ResetLeagueActivityBossCount", des = "重置联盟枢纽boss挑战次数" },
            { text = "ResetMergeServer", des = "重置个人合服活动" },
            { text = "SetNextLogtouTime", des = "设置下一次上线时的离线时间 h" },
            { text = "ResetReturnAct", des = "重置回归好礼" },
            { text = "simulaterecharge 10011 1", des = "模拟充值 充值ID 数量" },
            { text = "Recharge 10011 1 2", des = "充值 充值ID 数量 状态1待充值2待发货" },
            { text = "RechargeDelivery 10011 1", des = "充值 充值ID 数量" },
            --{ text = "ModifyLeagueTechLv 0 20", des = "联盟科技等级 全部科技 加20级" },
            { text = "ModifyAirshipLv 1 60", des = "武器等级 武器ID 加60级" },
            { text = "privatemute 6 120", des = "禁言 类型 时间" },
            { text = "ResetVoidArenaAll", des = "重置全部擂台" },
            { text = "ResetVoidArenaSingle 1", des = "重置单个擂台 擂台id" },
            { text = "VoidArenaClearRoleCD", des = "清理擂台冷却时间" },
            { text = "VoidArenaSetRole 1", des = "设置为擂主 擂台id" },
            { text = "VoidArenaSetUpgradeLv 1 0", des = "设置擂台的升级次数 擂台id 升级次数" },
            { text = "SetHeroHandBook 0 0", des = "图鉴升级 英雄id 图鉴等级" },
            { text = "SetMonthlyTask 0 0", des = "月任务进度设置" },
            { text = "TavernTaskFinishAll", des = "一键完成酒馆任务" },
            { text = "SpaceExploreActivationAllPosition", des = "激活当前星系地图中所有地图" },
            { text = "BattleshipTechAllFullLevel 0 0", des = "一键满级所有科技" },
            { text = "BattleshipTechToXLv 1101 0 1", des = "指定科技到x级" },
            { text = "modifyKillingTower 1", des = "重置杀戮场关卡 等级" },
            { text = "modifyMultiKillingTower 1 1", des = "重置挑战秘境关卡 等级 阵营id" },
            { text = "addLeagueWarActiviy 3000", des = "增加盟战活跃度" },
            { text = "AddTreasureRare  77401 10", des = "添加秘宝" },
            { text = "HammerTreasureRare 1 78001", des = "秘宝立即锻造" },
            { text = "UpgradeWeapon 15 90 142", des = "升级原力武器等级" },
            { text = "AddAllStarDiamond", des = "原力武器一件添加宝石" },
        }
    },
    [2] = {
        name = "角色",
        command = {
            { text = "GetFullallotment", des = "获取满配号", CallCmd = function(str)
                local player_prefs = require "player_prefs"
                local gw_home_novice_const = require "gw_home_novice_const"
                player_prefs.SetCacheData(gw_home_novice_const.Novice_Jump_Base_Key, 1)
                player_prefs.DelaySave(1)
            end },
            { text = "CopyAccountNumber", des = "复制XX到当前账号 CopyAccountNumber 登录账号", CallCmd = function(str)
                local player_prefs = require "player_prefs"
                local gw_home_novice_const = require "gw_home_novice_const"
                player_prefs.SetCacheData(gw_home_novice_const.Novice_Jump_Base_Key, 1)
                player_prefs.DelaySave(1)
            end },
            { text = "ScientificResearch", des = "拉满所有科研" },
            { text = "DelThisAccount", des = "删除这个账号(慎用!)" },
            { text = "clearall", des = "清除玩家所有数据" },
            { text = "addexp 1000", des = "增加玩家经验值" },
            { text = "modifylv 80", des = "修改玩家等级" },
            { text = "addvipexp 200", des = "增加VIP经验" },
            { text = "modifyviplv 1", des = "修改玩家VIP等级" },
            { text = "SetRoleCreateTime 86401", des = "修改角色创建时间 时间戳 重登生效" },
            { text = "privatemute 6 120", des = "禁言 类型 时间" },
            { text = "AddTreasureRare  77401 10", des = "添加秘宝" },
            { text = "HammerTreasureRare 1 78001", des = "秘宝立即锻造" },
            { text = "showHistoryPower", des = "服务器打印最近几天战力" },
            { text = "pushHistoryPower", des = "增加最近几天战力" },
            { text = "SetNextLogtouTime", des = "设置下一次上线时的离线时间 h" },
            { text = "UpgradeWeapon 15 90 142", des = "升级原力武器等级" },
            { text = "AddAllStarDiamond", des = "原力武器一件添加宝石" },
            { text = "customface unlock", des = "解锁自定义头像" },
            { text = "customface cd", des = "清除冷却时间(需重启)" },
            { text = "customface verify 1", des = "头像认证通过" },
            { text = "customface verify 3", des = "头像认证失败" },
            { text = string.format("customface no check", CmdToClient), des = "自定义头像不检查前置条件", CallCmd = function(str)

                local custom_avatar_mgr = require "custom_avatar_mgr"
                custom_avatar_mgr.SetNoCheckCheat()
            end },
            { text = "CloseAllianceCheck", des = "关闭联盟平台检测" },
            { text = "PushMessage_SetQueueTime 600", des = "消息推送 设置消息队列遍历时间" },
            { text = "PushMessage_OpenButton 0 1", des = "消息推送 设置各个按钮状态" },
            { text = "PushMessage_SendMessage 1 ", des = "消息推送 立即发送消息" },
            { text = "PushMessage_AddTaskQueue 1 60", des = "消息推送 添加消息到队列" },
            { text = "PushMessage_ResetSendState", des = "消息推送 重置发送状态" },
            { text = "PushMessage_SetAreaId 1", des = "消息推送 设置玩家地区id 1是欧盟" },
			{ text = "CompleteTask 0 1", des = "完成指定任务 任务id 完成数量" },
        }
    },
    [3] = {
        name = "英雄",
        command = {
            { text = "getheroteam 1", des = "一键添加阵容，1小R/2中R/3大R" },
            { text = "gethero 1 1", des = "获取英雄 ID 数量" },
            { text = "gethero 0 0", des = "获取全部1级英雄" },
            { text = "GetHerosByLimit 101 131", des = "获取全部新版英雄" },
            { text = "delhero 1", des = "删除英雄 英雄ID" },
            { text = "getherostar 20 13", des = "获取英雄ID 星级" },
            { text = "showheropro 1", des = "查看英雄属性 herSid" },
            { text = "SetHeroHandBook 0 0", des = "图鉴升级 英雄id 图鉴等级" },
            { text = "showheroproByID 1", des = "查看英雄属性 heroid" },
            { text = "showheropower", des = "打印英雄战力" },
            { text = "HerotestResource 10000", des = "一键获得英雄测试资源 数量" },
            { text = "GetRarityHero 15 4", des = "根据稀有度获得所有英雄 星级 稀有度 等级" },
            { text = "AwakeHeroSkill", des = "一键激活觉醒技 英雄id" },
            { text = "UpgradeExclusiveEquipment", des = "一键专属装备满级 英雄id" },
            { text = "getmaxblankhero 10", des = "英雄满星,专属40级,圣物9白60级,觉醒技开启,圣痕等级90级" },
            { text = "gettrialhero 20 0", des = "获取试用英雄 英雄id 活动id(如果活动表没有配置试用英雄活动,则无法获取)" },
        }
    },
    [4] = {
        name = "物品资源",
        command = {
            { text = "superstone", des = "一键添加各种资源" },
            { text = "getAllSigils", des = "一键添加所有符文" },
            { text = "getgoods 20001 1", des = "获取物品 ID 数量" },
            { text = "delgoods 20001", des = "删除物品 物品ID" },
            { text = "getcoin 1000000", des = "获取金币" },
            { text = "getdiamond 1000000", des = "获取钻石" },
            { text = "addheroexp 1000000", des = "增加英雄经验值" },
            { text = "SetDroneCenterStarLv 1 1", des = "设置神兽结晶星级 sid, starlv" },
            { text = "getgoods 40009 1000000", des = "获取进阶石" },
            { text = "getUStar 1000", des = "（过气）获取宇宙之星" },
            { text = "getGStar 1000", des = "（过气）获取银河之星" },
            { text = "addPumnkinItem", des = "获取拼年画/南瓜资源" },
        }
    },
    [5] = {
        name = "装备神器专属",
        command = {
            { text = "getequipment 25201 2 3", des = "获取装备ID 数量 强化等级" },
            { text = "UpgradeEquip", des = "未穿戴12档以上装备升9级" },
            { text = "UpgradeArtifact", des = "未穿戴神器每次升10级" },
            { text = "UpgradeExclusive 13", des = "13星英雄专属每次升10级" },
            { text = "SigilLevelUp 6", des = "一键升级印记 星级" },
            { text = "UpgradeWeapon 15 90 142", des = "升级原力武器等级" },
            { text = "AddAllStarDiamond", des = "原力武器一件添加宝石" },
        }
    },
    [6] = {
        name = "战纪舰队远征",
        command = {
            { text = "modifyIdleStage 1", des = "慎用 会影响城建地格!!! 修改狩猎关卡" },
            { text = "ModifyIdleReward", des = "修改狩猎奖励" },
            { text = "modifyChinaRedStage 1", des = "修改舰队远征关卡" },
            {
                text = string.format("OpenFightLevel 2", CmdToClient), des = "进入主线战斗", CallCmd = function(str)
                local str = string.split(str, " ")
                local count = #str
                local level = tonumber(str[count])
                local new_hook_scene = require("new_hook_scene")
                new_hook_scene.OnFightClicked(level)
            end
            }
        }
    },
    [7] = {
        name = "星际通缉",
        command = {
            { text = "modifyTowerStage 1", des = "修改星际通缉关卡 关卡" },
            { text = "passFactionStage 1 1", des = "阵营通缉 类型 关卡" },
        }
    },
    [8] = {
        name = "联盟",
        command = {
            --{ text = "resetLeagueBossCount", des = "重置联盟黑暗首脑挑战次数" },
            --{ text = "treasurerecover", des = "重置联盟探宝冷却时间 陷阱中不可用" },
            --{ text = "SetLeagueLevel 1", des = "设置联盟等级 等级" },
            --{ text = "AddLeagueExp 1000", des = "增加联盟经验 数量" },
            --{ text = "AddLeagueActivity 1000", des = "增加联盟活跃度 数量" },
            { text = "SetSelfCEOR5", des = "设置为联盟盟主、R5" },
            { text = "SetSelfR4", des = "设置为联盟R4" },
            { text = "PrinTimeZoneId", des = "打印玩家和玩家所在联盟时区" },
            { text = "ModifyTimeZoneId 1", des = "修改玩家时区" },
            --{ text = "ResetLeagueActivityBossCount", des = "重置联盟枢纽boss挑战次数" },
            --{ text = "ModifyLeagueTechLv 0 20", des = "联盟科技等级 全部科技 加20级" },
            --{ text = "setLeaugeOfficer", des = "（过气）设置为联盟官员" },
            --{ text = "createLeague", des = "（过气）创建新联盟" },
            --{ text = "joinLeague", des = "（过气）加入指定联盟" },
            --{ text = "setLeagueBoss 1", des = "（过气）设置当前联盟boss" },
            --{ text = "dimensionwar recover", des = "次元之战恢复所有队伍体力、行动力" },
            { text = "ResetAllianceFirstJoinTime", des = "重置第一次加入联盟的时间" },
            { text = "AllianceAddTime 0", des = "加入联盟时间修改为`当前x秒前`" },
            { text = "INVITATION set id", des = "联盟邀请函-推荐联盟:联盟ID" },
            { text = "INVITATION reward 0", des = "联盟邀请函-设置已领奖励次数: 次数" },
            { text = "AllianceShare clearCD", des = "联盟分享-清除CD" },
        }
    },
    [9] = {
        name = "星际迷航",
        command = {
            { text = "resetMaze", des = "重置异界迷宫" },
            { text = "passMaze", des = "通关异界迷宫" },
            { text = "passMazeMN", des = "通关迷宫M层N次" },
            { text = "addRelic 3", des = "获取异界迷宫遗物" },
        }
    },
    [10] = {
        name = "遗落之境",
        command = {
            { text = "AshMaxStage 1 1", des = "设置遗落之境最大关卡:map lv" },
            { text = "AshStage 1 1", des = "设置遗落之境关卡:map lv" },
            { text = "AshAddMedicine 1", des = "（过气）遗落之境增加药水:药品id" },
            { text = "AshAddStore 1 1001", des = "遗落之境增加折扣商品:折扣类型 商品id" },
            { text = "AshRestart", des = "遗落之境重新开始活动" },
        }
    },
    [11] = {
        name = "深空探索",
        command = {
            { text = "PeakAddRelic 3", des = "获取时光之巅遗物：遗物id" },
            { text = "PeakSetLevel 1", des = "设置时光之巅关卡：levelid" },
            { text = "PeakGetSomeBox", des = "（很少用）获取光之巅关卡的一些宝箱:levelid" },
            { text = "PeakDelIndependEvt", des = "移除独立事件以加快测试" },
            { text = "PeakClearFog", des = "移除迷雾" },
            { text = "PeakCreateDynEvent", des = "创建尚未创建的动态事件" },
            { text = "PeakMonsterSetHp1", des = "设置怪物血量1" },
        }
    },
    [12] = {
        name = "时空穿越",
        command = {
            { text = "ResetEquipEctype", des = "重置装备副本个人体力数据（一天一次）" },
            { text = "Timetravel 1 20", des = "通关时空穿越指定关卡" },
        }
    },
    [13] = {
        name = "家园",
        command = {
            { text = "ModifyFarm 1 30", des = "type,num(type1加能量, type2随机摇奖)" },
            { text = "WeaponUnActive 3", des = "反激活战舰 id" },
            { text = "ModifyAirshipLv 1 60", des = "武器等级 武器ID 加60级" },
        }
    },
    [14] = {
        name = "竞技场",
        command = {
            { text = "addArenaScore 100", des = "增加日常竞技场积分 score" },
            { text = "arenaopen", des = "新手竞技场开放" },
            { text = "arenaclose", des = "新手竞技场关闭" },
            { text = "matchsvr 12 1 1 1819", des = "触发产生跨服组数据，最后四位是worldId" },
            { text = "warenaopen", des = "巅峰赛开放" },
            { text = "warenaclose", des = "巅峰赛关闭" },
            { text = "warenaclear", des = "巅峰赛清除" },
            { text = "matchsvr 10 1 1 1819", des = "触发产生跨服组数据,最后四位是worldId" },
            { text = "start3v3", des = "3v3开放" },
            { text = "arena3v3open", des = "3v3时间开始" },
            { text = "arena3v3close", des = "3v3时间关闭" },
            { text = "arena3v3clear", des = "3v3清除" },

        }
    },
    [15] = {
        name = "任务活动",
        command = {
            { text = "ResetMergeServer", des = "重置个人合服活动" },
            { text = "ResetReturnAct", des = "重置回归好礼" },
            { text = "getPassport", des = "获取通行证" },
            { text = "SetMonthlyTask 0 0", des = "月任务进度设置" },
            { text = "TavernTaskFinishAll", des = "一键完成酒馆任务" },
            { text = "GeneralSetStage 1 1", des = "将军的试炼设置关卡 困难度 等级" },
        }
    },
    [16] = {
        name = "时间",
        command = {
            { text = "ModifyOpenWorldTime 2024 12 26", des = "修改服务器开服时间" },
            { text = "showtime", des = "在服务器上打印时间" },
            { text = "settime 2025 05 20 10 30 35", des = "设置服务器逻辑时间年 月 日 时 分 秒" },
            { text = "addtime 12 12 12", des = "增加服务器逻辑时间 时 分 秒" },
            { text = "preventwallow 0 0", des = "修改防沉迷时间(单位s) 上报间隔 每次上报时长" },
        }
    },
    [17] = {
        name = "奖励充值",
        command = {
            { text = "getreward 0", des = "获取奖励 奖励id" },
            { text = "simulaterecharge 10011 1", des = "模拟充值 充值ID 数量" },
            { text = "Recharge 10011 1 2", des = "充值 充值ID 数量 状态1待充值2待发货" },
            { text = "RechargeDelivery 10011 1", des = "充值 充值ID 数量" },
        }
    },
    [18] = {
        name = "虚空擂台",
        command = {
            { text = "ResetVoidArenaAll", des = "重置全部擂台" },
            { text = "ResetVoidArenaSingle 1", des = "重置单个擂台 擂台id" },
            { text = "VoidArenaClearRoleCD", des = "清理擂台冷却时间" },
            { text = "VoidArenaSetRole 1", des = "设置为擂主 擂台id" },
            { text = "VoidArenaSetUpgradeLv 1 0", des = "设置擂台的升级次数 擂台id 升级次数" },
        }
    },
    [19] = {
        name = "星际探索",
        command = {
            { text = "SpaceExploreActivationAllPosition", des = "激活当前星系地图中所有地图" },
            { text = "BattleshipTechAllFullLevel 0 0", des = "一键满级所有科技" },
            { text = "BattleshipTechToXLv 1101 0 1", des = "指定科技到x级" },
        }
    },
    [20] = {
        name = "挑战秘境",
        command = {
            { text = "modifyKillingTower 1", des = "重置杀戮场关卡 等级" },
            { text = "modifyMultiKillingTower 1 1", des = "重置挑战秘境关卡 等级 阵营id" },
        }
    },
    [21] = {
        name = "小游戏",
        command = {
            { text = "SetMinGameLv 1", des = "设置当前小游戏关卡" },
            { text = "ClearMinGame", des = "清除小游戏数据" },
            --新添加的灵石命令暂存小游戏类别
            { text = "ModifyleagueTecBLv 0 10", des = "一键增加联盟研究所所有科技等级10" },
            { text = "Alltecbonus", des = "一键加满星际探索科技等级，联盟科技等级，联盟研究所科技等级，战舰等级（激活虫子战舰），图鉴激活等级，4种族塔到最高级" },
            { text = "upgradeheroavatar", des = "一键激活所有图鉴到最高等级" },
            { text = "Upgradestartec", des = "一键加星际探索科技等级" },
            { text = "heroawaken", des = "添加觉醒技，给背包中所有永恒阶级英雄觉醒觉醒技" },
            { text = "ZombieTreasure update", des = "丧尸的宝藏-全量更新" },
            { text = string.format("SoldierSSortie 2", CmdToClient), des = "士兵突击", CallCmd = function(str)
                local str = string.split(str, " ")
                local count = #str
                local level = tonumber(str[count])
                local minigame_mgr = require "minigame_mgr"
                minigame_mgr.OpenMiniGame(level)
            end },
            { text = string.format("kingshot 600009", CmdToClient), des = "kingshot", CallCmd = function(str)
                local str = string.split(str, " ")
                local count = #str
                local level = tonumber(str[count])
                local minigame_mgr = require "minigame_mgr"
                minigame_mgr.OpenMiniGame(level)
            end},
            { text = string.format("SoldierSSortieCampaign 1 6001_6002_6003 6001_6002_6003", CmdToClient), des = "士兵突击的首充活动入口", CallCmd = function(str)
                local str = string.split(str, " ")
                local count = #str
                if count >= 4 then
                    local level = tonumber(str[2])
                    local lvIDs = {}
                    local ctrlIDs = {}
                    local tmpStrs = string.split(str[3], "_")
                    for i,v in ipairs(tmpStrs) do
                        lvIDs[i] = tonumber(v)
                    end
                    tmpStrs = string.split(str[4], "_")
                    for i,v in ipairs(tmpStrs) do
                        ctrlIDs[i] = tonumber(v)
                    end
                    local minigame_mgr = require "minigame_mgr"
                    minigame_mgr.OpenMiniGame_Campaign(level,lvIDs,ctrlIDs)
                end
            end },
            { text = string.format("SoldierSSortieCampaign Skip_CheckPower", CmdToClient), des = "跳过战力校验", CallCmd = function(str)
                local tmpValue = PlayerPrefs.GetInt("SoldierSSortieCampaign_Skip_CheckPower", 0)
                if tmpValue == 0 then
                    tmpValue = 1
                else
                    tmpValue = 0
                end
                PlayerPrefs.SetInt("SoldierSSortieCampaign_Skip_CheckPower", tmpValue)
                PlayerPrefs.Save()
                local log = require "log"
                log.Error("跳过战力校验 =", tmpValue == 1)
            end },
            { text = string.format("okpullpursuitcn_Tiny 399 1", CmdToClient), des = "多小游戏框架测试: 游戏resKey 关卡索引", CallCmd = function(str)
                local str = string.split(str, " ")
                ---@type tiny_entry
                local tiny_entry = require "tiny_entry"
                tiny_entry.EnterGameByResKey(str[1], tonumber(str[2]), tonumber(str[3]))
            end },
        }
    },
    [22] = {
        name = "饰品",
        command = {
            { text = "modifyIdleStage 2465", des = "慎用 会影响城建地格!!! 修改狩猎关卡" },
            { text = "getgoods 41227 10000", des = "获取星之币10K" },
            { text = "getgoods 41228 10000", des = "获取棱镜碎片10K" },
            { text = "getgoods 41229 10000", des = "获取星之彩10K" },
            { text = "getgoods 70001 100", des = "获取70001 10" },
            { text = "getherostar 10 13", des = "获取英雄ID 星级" },
            { text = "DecorationsLevelUp 15 60", des = "15星英雄圣物升级" },
            { text = "GetDecorations 142 3", des = "获得圣物 英雄id  圣物稀有度" },
        }
    },
    [23] = {
        name = "其他",
        command = {
            { text = "global 0 -1 | tbl", des = "查看全局变量" },
            { text = "send_error 1", des = "服务器返回错误码: id" },
            { text = "setAutoLogin -1", des = "-1:自动选择,0:不使用自动登录,1:自动登录" },
            { text = "leagueComp 0", des = "（过气）一键修改联盟争霸各种限制(leagueComp无参数重置副本)" },
            { text = "cleanStagePlot", des = "（过气）清空玩家过关剧情" },
            { text = "modifyMateLevel 1", des = "（过气）修改羁绊等级" },
            { text = "addMateExp 100", des = "（过气）增加羁绊经验" },
            { text = "completeTargetTask", des = "（过气）完成当前目标任务" },
            { text = "resetTargetTask", des = "（过气）重置目标任务" },

            { text = "luaHotfix", des = "Lua 热更", CallCmd = function(str)
                if str == "" then return end
                local hotfix_helper = require "lpat_helper"
                hotfix_helper.debug_hotfix(str)
            end },

        }
    },
    [24] = {
        name = "互助",
        command = {
            { text = "AllianceHelp reset ", des = "重置个人数据(积分,医院次数等): " },
            { text = "AllianceHelp start 0 0 1 ", des = "个人随机发起求助 1 = 次数" },
            { text = "AllianceHelp healtimes 1", des = "个人今日已被治疗士兵总次数: 1= 次数 填写次数" },
            { text = "AllianceHelp setScore 100", des = "设置积分" },
            { text = "AllianceHelp resetLeave", des = "重置离开联盟时间" },
        }
    },
    [25] = {
        name = "科技",
        command = {
            { text = "ResetTechExp ", des = "重置科技经验" },
            { text = "FillRecommendTechExp ", des = "填满推荐科技经验" },
            { text = "AddDailyDonate 1", des = "增加每日捐献：1 =次数" },
            { text = "FlushCoinDonateCnt ", des = "刷新金币捐献次数" },
        }
    },
    [26] = {
        name = "礼物",
        command = {
            { text = "SetAllianceGiftLv 1", des = "设置联盟礼物等级" },
            { text = "SetAllianceGiftExp 100", des = "设置联盟礼物经验" },
        }
    },
    [27] = {
        name = "压测",
        command = {
            { text = "ClearApplyList", des = "清理联盟申请列表" },
            { text = "JoinAlliance", des = "批量加入联盟（压测需修改文件）" },
            { text = "ApplyJoinAlliance", des = "批量申请加入联盟（压测需修改文件）" },
            { text = "CreateSpecialLeagueFirst",  des = " 建盟  无需参数" },
            { text = "CreateSpecialLeagueSecond", des = " 建盟 名称 简称" },
            { text = "AddAllianceLog 1 aaa bbb 1 4", des = " 添加联盟日志 类型 参数" },
        }
    },
    [28] = {
        name = "沙盘",
        command = {
            { text = "InitCityTroop 1", des = "不进城建初始化队伍：1是队伍index" },
            { text = "Sandbox resource x y len", des = "沙盘创建正方形范围的资源点 把(x,y,len)修改为数字, len = 正方形长度" },
            { text = "Sandbox ResetNewBuff", des = "重置沙盘新手保护罩数据" },
            { text = "Sandbox DelRoleCity", des = "删除自己基地" },
            { text = "Sandbox RoleCityDefFaild 0", des = "打飞玩家基地" },
            { text = "Sandbox KillWonder 0", des = "杀死游荡怪,sid" },
            { text = "Sandbox WonderAtkSelf", des = "最近の空闲游荡怪攻击自己" },
            { text = "Sandbox WonderReTarget", des = "所有游荡怪重选目标" },
            { text = "Sandbox AtkAward 101 0 0", des = "杀怪发奖 怪物ID (0：非队长) (0：非集结)" },
            { text = "Sandbox CleanMarch", des = "清理玩家自己的行军线" },
            { text = "Sandbox MoveEntity sid x y 0", des = "移动实体,最后一个参数为是否覆盖" },
            { text = string.format("Sandbox %s JumpToGrid 1 1", CmdToClient), des = "跳转到xy的格子", CallCmd = function(str)
                local str = string.split(str, " ")
                local count = #str
                local GWCommonUtil = require "gw_common_util"
                GWCommonUtil.JumpToGrid({ x = tonumber(str[count - 1]), y = tonumber(str[count]) })
            end },
            { text = string.format("Sandbox Visual Change 10003555 500 500", CmdToClient), des = "视角跨服", CallCmd = function(str)
                local str = string.split(str, " ")
                local count = #str
                local data = {}
                data.pos = {}
                data.pos.x = tonumber(str[count - 1])
                data.pos.y = tonumber(str[count])
                data.enterSandboxSid = tonumber(str[count - 2])
                local gw_common_util = require "gw_common_util"
                gw_common_util.SwitchToSand(nil, data.pos, data.enterSandboxSid)
            end },
            { text = string.format("Sandbox City Move 10003555 500 500 1", CmdToClient), des = "基地跨服", CallCmd = function(str)
                local str = string.split(str, " ")
                local count = #str
                local data = {}
                data.pos = {}
                data.pos.x = tonumber(str[count - 2])
                data.pos.y = tonumber(str[count - 1])
                data.sandBoxSid = tonumber(str[count - 3])
                local gw_common_util = require "gw_common_util"
                gw_common_util.SwitchToSand(function()
                    -- 返回迁城默认打开迁城界面
                    local gw_admin = require "gw_admin"
                    gw_admin.SandRelocationEntityUtil.SetRelocationEntitySid(0, { x = data.pos.x, y = 0, z = data.pos.y }, function(state)
                        if not state then
                            gw_common_util.SwitchToSand()
                        end
                    end, 2)
                end, data.pos, data.sandBoxSid)
            end },
            { text = "Sandbox DeclareWar 宣言字符串 sid", des = "宣战城市: sid" },
            { text = "Sandbox NCOccupied 0", des = "占领某个城市: sid" },
            { text = "Sandbox NCGoldMode 1", des = "who is your daddy 1 ,传0变废物" },
            { text = "Sandbox GM_CreateBatchMarch 100  500  500", des = "创建沙盘行军：数量 ,X ,Y" },
            { text = "Sandbox GM_TestSandboxSearch 1001 1", des = "沙盘搜索测试: 搜索ID, 次数" },
            { text = "Sandbox RefreshMonster", des = "触发定时刷怪" },
            { text = "Sandbox DelAllMonster", des = "删除所有怪" },
            { text = "Sandbox SetMonsterSpacing 1", des = "修改刷怪间距配置" },
            { text = "Sandbox SetSingleNtfMax 64", des = "设置实体与行军推送单包最大数量" },
            { text = "Sandbox AllMoveToAlliance 0", des = "所有基地往自己联盟聚集点迁城: 基地数量(0为所有)" },
            { text = "Sandbox Create 1", des = "创建沙盘: 区服id" },
        }
    },
    [29] = {
        name = "城建",
        command = {
            { text = "AddWoundedSoldier 1 1", des = "增加受伤士兵：1 = 士兵等级；1 = 数量" },
            { text = "AddInnerCitySoldier 1 1", des = "增加城内士兵：1 = 士兵等级；1 = 数量" },
            { text = "AddOutsideCitySoldier 1 1", des = "增加城外士兵：1 = 士兵等级；1 = 数量" },
            { text = "CityDefeatBreak 1", des = "破城伤兵：1 = 存活队伍数量" },
            { text = "SetBuildingLevel 1000 30", des = "升级建筑：1000 = （大本）建筑ID；30 = 等级" },
            { text = "AddTechEffect 0 10501", des = "添加科技效果：0 = 类型；10501 = 科技效果ID" },
            { text = "RemoveTechEffect 0 10501", des = "移除科技效果：0 = 类型；10501 = 科技效果ID" },
            { text = "GetSurvivorById 11", des = "添加幸存者根据幸存者id：11 = 幸存者id；" },
            { text = "GetSurvivorByBuildingTypeId 1", des = "添加幸存者根据建筑类型id：1 = 类型；" },
            { text = "ClearAreaEvent 1", des = "完成区域所有事件id：1 = 区域id；" },
            { text = "ClearSingleEvent", des = "开始下一关" },
            { text = "JumpNewPlayerEvent", des = "跳过新手流程", CallCmd = function(str)
                local player_prefs = require "player_prefs"
                local gw_home_novice_const = require "gw_home_novice_const"
                player_prefs.SetCacheData(gw_home_novice_const.Novice_Jump_Base_Key, 1)
                player_prefs.DelaySave(1)
            end },
            { text = "JumpNewPlayerEvent", des = "还原新手流程设置", CallCmd = function(str)
                local player_prefs = require "player_prefs"
                local gw_home_novice_const = require "gw_home_novice_const"
                player_prefs.SetCacheData(gw_home_novice_const.Novice_Jump_Base_Key, 0)
                player_prefs.DelaySave(1)
            end },
            { text = "ClearSingleEvent", des = "开始下一关" },
            { text = "SetHeroHonorWallLv 1 1", des = "SetHeroHonorWallLv herosid honorlv" }
        }
    },
    [30] = {
        name = "雷达",
        command = {
            { text = "Sandbox RadarRefreshEndTimeEnd", des = "刷新任务倒计时归零" },
            { text = "Sandbox RadarMissionAllFinish", des = "一键完成所有任务" },
            { text = "Sandbox RadarLevelChange 1", des = "雷达等级修改 雷达等级" },
            { text = "Sandbox RadarMissionCanRecaive 0", des = "指定任务设置可领取 任务ID" },
            { text = "Sandbox RadarMissionGenerateOnTaskId 0", des = "根据任务Id生成指定任务 RadarMission表的taskID" },
            { text = "Sandbox RadarChangeTreasure 宝藏sid 1", des = "修改宝藏挖掘人数,最后个参数为人数增量(可为负数)" },
            { text = "Sandbox RadarBox 1 x y 0 0", des = "沙盘宝藏 配置ID x y 秒 (1:无归属玩家; 0:归属自己)" },
            { text = "Sandbox RadarBoxMulReward", des = "视角沙盘: 当前所有宝箱无限暴击" },
        }
    },
    [31] = {
        name = "新版英雄",
        command = {
            --region 新版英雄
            { text = "GetNewHeroByID 101 ", des = "获取新版英雄 101 ~131" },
            { text = "GetAllNewHero 101 131 ", des = "获取所有新版英雄 101 ~131" },
            { text = "ChangeNewHeroLv 101 5 ", des = "修改新版英雄等级 1~150级" },
            { text = "ChangeNewHeroStarLv 101 1 ", des = "修改新版英雄星级 0~25级" },
            { text = "ShowNewHeroProp 101 ", des = "打印新版英雄属性 " },
            { text = "DelNewHeroByID 101 ", des = "删除新版英雄 101~131" },
            { text = "ModifyPalInfo 101 = 1#100;2#200;3#300", des = "修改英雄属性 101 +/-/= 1#100;2#200;3#300" },
            { text = "SavePalInfo 101 ", des = "保存英雄属性 101~131" },
            --endregion
        }
    },
    [32] = {
        name = "活动",
        command = {
            { text = string.format("CheckActivityState 205", CmdToClient), des = "本地检测活动状态 ActivityID", CallCmd = function(str)
                local strArray = string.split(str, " ")
                local count = #strArray
                if count < 2 then
                    local log = require "log"
                    log.Error("参数错误，正确格式")
                    return
                end
                local activityID = tonumber(strArray[2])
                --发一个事件过去
                local event = require "event"
                --para1  事件子类型 CheckActivityState
                --para2  事件参数    ActivityID
                event.Trigger("GMEvent_LocalLingShi", "CheckActivityState", activityID)
            end },
			{ text = "https://q1doc.yuque.com/qc01ug/zm1dqk/nqdlv06yvlrf7z0d", des = "module文档" },
			{ text = "GMActivity PrintModule 0", des = "查看moudle:指定ID" },
			{ text = "GMActivity OpenModule 0", des = "module开启:指定ID" },
			{ text = "GMActivity CloseModule 0", des = "module关闭:指定ID" },
            --region 世界boss
            { text = "GenWorldBoss 1 ", des = "世界boss-GenWorldBoss bossId" },

            { text = "DamgeToWorldBoss 100 ", des = "世界boss-对boss造成伤害" },
            --endregion
            --region 先锋目标
            { text = "TopicNums 2005 ", des = "开启先锋目标活动" },
            --endregion
            --region 军备竞赛
            { text = "AddItem 7023 1", des = "军备竞赛添加积分" },

            { text = "DecreaseItem 1 1", des = "同盟对决：消耗物品item" },

            { text = "GMActivity Close 1", des = "关闭活动 1=活动ID" },
            { text = "GMActivity Open 1 3", des = "开启活动 1=活动ID 3=开启天数" },
            { text = "GMActivity Cancel", des = "活动还原" },
            { text = "GMPurchaseMonthCard 1", des = "月卡操作 1=购买  2=过期" },
            { text = "GoldenEggsZoneBattleDuelWin 1", des = "抛出沙漠风暴战区胜利事件 dbid" },
            --endregion
        }
    },

    [33] = {
        name = "同盟boss",
        command = {
            { text = "AllianceBoss AddDamage 2", des = "增加伤害：2=伤害量" },
            { text = "AllianceBoss Settle", des = "排行榜结算" },
            { text = "AllianceBoss LingShiMode 1", des = "灵石模式：1为开启；0为关闭" },
            { text = "AllianceBoss Reset", des = "重置活动数据" },
            { text = "AllianceBoss GetReady 1 -1 -1", des = "进入准备状态（灵石模式可立刻准备不用填后面的时间）：1=难度等级；2=小时（可选）；3=分钟（可选0/30）" },
            { text = "AllianceBoss Fighting", des = "进入挑战状态" },
            { text = "AllianceBoss SetDonate 0", des = "设置捐献数量：0=捐献数量" },
            { text = "AllianceBoss RobotRank 2 3", des = "上榜机器人：2=机器人数量[1,99]；3=伤害量(>0)" },
        }
    },
    [34] = {
        name = "邮件",
        command = {
            { text = "SendMail 11013 1", des = "发送邮件模板ID:100010" },
        }
    },
    [35] = {
        name = "联盟成就",
        command = {
            { text = "ResetLeagueAch  ", des = "重置所有联盟成就   不带参数" },
            { text = "FinishLeagueAch  ", des = "完成所有进行中的成就  不带参数" },
            { text = "ChangeLeagueAch 1 1 ", des = "修改联盟成就进度 id  进度" },
            { text = "ChangeLeagueAchUnlockTime  ", des = "修改联盟成就解锁时间" },
            { text = "ChangeLeagueAchOverTime  ", des = "修改联盟成就结束时间" },
            { text = "AddAllianceRecord 50 ", des = "添加联盟记录" },
            { text = "AddAllianceIntelligence 2 ", des = "添加联盟单人情报" },
        }
    },
    [36] = {
        name = "货车",
        command = {
            { text = "CarriageWante ", des = "通缉指定玩家ID 一个参数被通缉玩家ID" },
            { text = "Sandbox CarriageRestLoots", des = "重置掠夺次数" },
            { text = "Sandbox CarriageRestTrades", des = "重置发车次数" },
            { text = "Sandbox CarriageFinish 1", des = "货车立刻到站 货车索引（1~4）" },
            { text = "CarriageClearWante", des = "通缉玩家清0（所有玩家不再被通缉" },
            { text = "CarriageClearRoleData", des = "清除玩家数据" },
            { text = "Sandbox CarriageSetWorldGroup  2  2553 3552", des = "强制设置服务器跨在一起" },

        }
    },
    [37] = {
        name = "阵营试炼",
        command = {
            { text = "CampTrial reset", des = "重置活动" },
            { text = "CampTrial pass ID", des = "直接通关`ID`关卡" },
            { text = "CampTrial WinRate 100", des = "修改胜率(百分率), `非数字`时为关闭" },
            { text = "CampTrial clear", des = "清除玩家数据" },
        }
    },
    [38] = {
        name = "杂项",
        command = {
            { text = "DelAlliance 200", des = "删除联盟   联盟ID" },
        }
    },
    [39] = {
        name = "任务",
        command = {
            { text = "TaskPartTest_PrintTaskInfo 1001", des = "输出任务情况  任务ID" },
            { text = "TaskPartLingshi_ClearTaskValue 1001", des = "重置任务  任务ID" },
            { text = "TaskPartLingshi_ModifyTaskValue 1001 1000", des = "更新任务进度  任务ID 进度值" },

        }
    },
    [40] = {
        name = "国会管理",
        command = {
            { text = "Congress Up 1 0", des = "上任职位; Up 官职ID 玩家ID(默认自己)" },
            { text = "Congress Down ID", des = "下任; Down 官职ID" },
            { text = "Congress reset 0", des = "国会重置; reset 是否赛季重置" },
            { text = "Congress ApplyForAdd 1 ID", des = "随机添加申请; Add 数量 官职ID" },
            { text = "Congress QueueUpAdd 1 ID", des = "随机添加排队; Add 数量 官职ID" },
            { text = "Congress Finish 0", des = "国会管理结算; Finish 联盟ID" },
        }
    },
    [41] = {
        name = "橡果酒馆",
        command = {
            { text = "AcornPub Refresh", des = "普通刷新酒馆任务" },
            { text = "AcornPub ReSetAllData", des = "重置所有酒馆数据" },
            { text = "AcornPub LingShiMode 1", des = "灵石模式：1为开启；0为关闭" },
            { text = "AcornPub SendTaskPre 1", des = "派遣预处理创建沙盘实体 任务sid" },
            { text = "AcornPub SendTask 1", des = "派遣任务 任务sid" },
            { text = "AcornPub RobTask 11 1 2000", des = "抢夺任务 被抢的dbid 被抢的任务sid 被抢的worldid" },
            { text = "AcornPub HelpTask 11 1", des = "协助任务 被协助的dbid 被协助的任务sid" },
            { text = "AcornPub GetReward {1,2}", des = "批量领取奖励 任务sid" },
            { text = "AcornPub FinishTask", des = "完成所以进行中任务" },
            { text = "AcornPub ResetDailyTask", des = "每日刷新任务" },
            { text = "AcornPub ResetStarTask", des = "每日刷新红星任务" },
            { text = "AcornPub ResetTimes", des = "重置掠夺协助次数" },
            { text = "AcornPub PrintData", des = "打印玩家酒馆数据" },
            { text = "AcornPub DayReset", des = "执行跨天逻辑" },
            { text = "AcornPub LikeRecord 1", des = "点赞协助玩家：1为标记且点赞；0为只标记不点赞" },
            { text = "AcornPub RefreshFakeTask", des = "刷新补充假任务" },
            { text = "AcornPub CleanFakeTask", des = "清理假任务" },
        }
    },
    [42] = {
        name = "联盟火车",
        command = {
            { text = "AllianceTrainClear", des = "清空联盟火车" },
            { text = "AllianceTrainCreate 1", des = "创建火车：1普通；2黄金" },
            { text = "AllianceTrainAppointDriver 1", des = "任命联盟火车司机 roleid" },
            { text = "AllianceTrainStart", des = "启动联盟火车" },
            { text = "AllianceTrainGoToCity 1", des = "AllianceTrainGoToCity 参数1(站点索引)" },
            { text = "AllianceTrainCmd 1", des = "1->(火车全部路径点)" },
        }
    },
    [43] = {
        name = "沙漠风暴",
        command = {
			{ text = "https://q1doc.yuque.com/qc01ug/zm1dqk/ratnzbe5z7s44erz", des = "活动&指令详解" },
			{ text = "DesertStrom Check", des = "检测服务器判定" },
            { text = "DesertStrom whatever_sign 1 1", des = "强制报名自己联盟" },
            { text = "DesertStrom MatchForce 0", des = "强制匹配流程" },
            { text = "DesertStrom Enter", des = "强制进入沙盘", CallCmd = function(str)
                local gw_storm_mgr = require "gw_storm_mgr"
                gw_storm_mgr.EnterStorm()
            end },
			{ text = "DesertStrom SetSignEd 0", des = "修改报名结束时间点" },
			{ text = "DesertStrom SetMatchEd 0", des = "修改匹配结束时间点" },
            { text = "DesertStrom ChangeBatTime 1", des = "战斗时间延长" },
            { text = "DesertStrom OpenSign 1", des = "跳过部分个人报名判定 1:开启 0:关闭" },
            { text = "DesertStrom ClearAllianceTeam", des = "清除自己联盟报名" },
            { text = "DesertStrom ClearAllAllianceSign", des = "清除所有报名" },
            --{ text = "DesertStrom MatchTo a b", des = "(废弃)报名并将联盟a和b匹配" },
            { text = "DesertStrom ResultNow 0", des = "立即结算对局:清除并重复结算" },
            { text = "DesertStrom CreateCityAll", des = "己方参赛正式成员创建基地" },
            { text = "DesertStrom DelCityAll", des = "删除所有基地" },
            { text = "DesertStrom AddPersonScoreByType 1 1", des = "增加参赛小队个人指定积分 1:积分类型 1:增量(允许为负数)" },
            { text = "DesertStrom FreeAllianceForceSignUp 1 1 1", des = "空闲联盟直接报名 1:小分队, 1:时间段, 1:数量" },
            { text = "DesertStrom OccupyBuilding 1", des = "占领建筑 1:建筑id(适用非油井和积分宝箱类型建筑)" },
            { text = "DesertStrom CancelOccupyBuilding 1", des = "撤出建筑 1:建筑id(适用非油井和积分宝箱类型建筑)" },
            { text = "DesertStrom CreateScoreBox 1", des = "创建积分宝箱 1:建筑id(适用非油井和积分宝箱类型建筑)" },
            { text = "DesertStrom GetScoreBox 1", des = "获取积分宝箱 1:建筑sid" },
            { text = "DesertStrom GetOilWell 1", des = "采矿 1:建筑sid" },
			{ text = "DesertStrom AddRecord 1", des = "添加记录:次数" },
			{ text = "DesertStrom AddLog 1", des = "添加日志:次数" },
            { text = "DesertStrom printTime", des = "打印时间&跨服组" },
			{ text = "DesertStrom SetGroup 1 1234", des = "修改跨服组" },
			{ text = "DesertStrom printInfo", des = "打印对局信息" },
            { text = "DesertStrom SaveNow", des = "立即存库" },
            { text = "DesertStrom Guide", des = "沙漠风暴新手引导", CallCmd = function(str)
                local player_prefs = require "player_prefs"
                player_prefs.SetCacheData("CheckGuideDesert", false)
            end },
        }
    },
    [44] = {
        name = "末日降临",
        command = {
            -- { text = "SkyFall OnActivityOpen", des = "活动开启" },
            -- { text = "SkyFall OnActivityClose", des = "活动关闭" },
            { text = "SkyFall OnSkyFallThreatMonster", des = "请求怪物威胁数据" },
            { text = "SkyFall OnGmSkyFallAtkResult", des = "测试攻击游荡怪结果" },
            { text = "SkyFall OnGmRefDoomsdayWander", des = "测试刷新游荡怪" },
        }
    },
    [45] = {
        name = "自选周卡",
        command = {
            { text = "ChooseWeekCard OnBuyCheck", des = "购买周卡检查" },
            { text = "ChooseWeekCard ActiveCard", des = "激活周卡" },
            { text = "ChooseWeekCard GetReward", des = "领取周卡奖励" },
            { text = "ChooseWeekCard MailReward", des = "测试自选周卡邮件" },
        }
    },
    [46] = {
        name = "版署服命令",
        command = {
            { text = "GetGameVersionHero 1 ", des = "获得版署英雄 1:高级账号 2：中级账号 3：低级账号" },
            { text = "GetGameVersionBuilding 1 ", des = "获得版署建筑 1:高级账号 2：中级账号 3：低级账号" },
            { text = "GetGameVersionDrone 1 ", des = "获得版署无人机 1:高级账号 2：中级账号 3：低级账号" },
            { text = "GetGameVersionSoldier 1 ", des = "获得版署校场士兵 1:高级账号 2：中级账号 3：低级账号" },
            { text = "GetGameVersionFinishAllChapterTask", des = "完成所有章节任务" },
            { text = "GetGameVersionAll 1 ", des = "获得版署全部资源 1:高级账号 2：中级账号 3：低级账号" },
        }
    },
    [47] = {
        name = "点赞",
        command = {
            { text = "LikePart SetFakeLikeNum 1", des = "打开主页时增加假点赞数" },
            { text = "LikePart ZombieCancelPoisonedChat 137356 ", des = "丧尸灾变协助私聊" },
        }
    },
    [48] = {
        name = "通用存储",
        command = {
            { text = "CommonStorage DayReset", des = "执行跨天逻辑" },
        }
    },
	[49] = {
        name = "丧尸灾变",
        command = {
            { text = "ZombieApocalypse SetActivityPreView", des = "设置活动为预告期" },
			{ text = "ZombieApocalypse SetActivityOpen", des = "设置活动为正式期" },
			{ text = "ZombieApocalypse ClearAllianceCoolDown", des = "清除联盟开启冷却时间" },
			{ text = "ZombieApocalypse AddCluePoint 10000", des = "增加联盟线索点X" },
			{ text = "ZombieApocalypse UnlockDifficulty 1", des = "联盟解锁到难度X" },
			{ text = "ZombieApocalypse CancelMemberLimit", des = "取消活动开启人数限制" },
			{ text = "ZombieApocalypse PassSuccess", des = "联盟直接通关成功" },
			{ text = "ZombieApocalypse PassFail", des = "联盟直接通关失败" },
			{ text = "ZombieApocalypse SendMail", des = "直接发放攻略邮件" },
        }
    },
	[50] = {
        name = "新一键入盟",
        command = {
            { text = "AllianceAddNew PrintActivity 0", des = "活跃度缓存:目标联盟" },
			{ text = "AllianceAddNew PrintActRank", des = "活跃度排行" },
			{ text = "AllianceAddNew UpdateActivity 0", des = "更新并打印:目标联盟" },
			{ text = "AllianceAddNew AddEvent_Speak 1", des = "添加聊天积分: 次数" },
			{ text = "AllianceAddNew AddEvent_Announce 1", des = "添加公告积分: 次数" },
			{ text = "AllianceAddNew AddEvent_Kick 1", des = "添加踢人积分: 次数" },
			{ text = "AllianceAddNew ClearScore 0", des = "清除积分数据:目标联盟" },
			{ text = "AllianceAddNew SetStatistics 86400", des = "修改积分统计有效期:秒" },
			{ text = "AllianceAddNew UpdateRank", des = "更新活跃度&标签排行" },
			{ text = "AllianceAddNew PrintTag", des = "打印联盟标签分布" },
			{ text = "AllianceAddNew IsApply", des = "本区服是否应用新规则" },
			{ text = "AllianceAddNew SetApply 1", des = "设置是否开启" },
			{ text = "AllianceAddNew ReportTimer", des = "league_Active打点" },
        }
    },
    [51] = {
        name = "联盟待办",
        command = {
            { text = "AllianceTodoSetNotGet", des = "设置领取状态未领取" },
			{ text = "AllianceTodoSetTask 1 0 0", des = "设置任务状态 ID 状态 领取次数 " },
			{ text = "AllianceTodoCreateTask 0", des = "联盟待办创建一个任务 ID" },
			{ text = "ClearAllianceTask 1", des = "清理任务 ID 是否全部" },
        }
    },
    [52] = {
        name = "领主巡逻&流浪狗",
        command = {
            { text = "HomeLordActive", des = "激活领主", CallCmd = function(str)
                local gw_home_lord_util = require "gw_home_lord_util"
                gw_home_lord_util.InitLordSystem(true)
            end },
            { text = "HomeStrayDogActive", des = "激活流浪狗", CallCmd = function(str)
                local gw_home_stray_dog_util = require "gw_home_stray_dog_util"
                gw_home_stray_dog_util.OnServerNotifyStrayDogUnlocked()
            end },
            { text = "HomeLordDebug 1", des = "调试状态 1打开0关闭", CallCmd = function(str)
                local strArray = string.split(str, " ")
                local gw_home_lord_util = require "gw_home_lord_util"
                gw_home_lord_util.SetDebugMode(tonumber(strArray[2])== 1)  -- 开启DEBUG
            end },
        }
    },
}
--定义各类别包含的按钮
local CommandTable1 = {
    [1] = {
        indexbase = 1,
        name = CommandTableBase[1].name,
        command = CommandTableBase[1].command,
    },
    [2] = {
        indexbase = 2,
        name = CommandTableBase[2].name,
        command = CommandTableBase[2].command,
    },
    [3] = {
        indexbase = 4,
        name = CommandTableBase[4].name,
        command = CommandTableBase[4].command,
    },
    [4] = {
        indexbase = 16,
        name = CommandTableBase[16].name,
        command = CommandTableBase[16].command,
    },
    [5] = {
        indexbase = 21,
        name = CommandTableBase[21].name,
        command = CommandTableBase[21].command,
    },
    [6] = {
        indexbase = 23,
        name = CommandTableBase[23].name,
        command = CommandTableBase[23].command,
    },
    [7] = {
        indexbase = 28,
        name = CommandTableBase[28].name,
        command = CommandTableBase[28].command,
    },
    [8] = {
        indexbase = 29,
        name = CommandTableBase[29].name,
        command = CommandTableBase[29].command,
    },
    [9] = {
        indexbase = 30,
        name = CommandTableBase[30].name,
        command = CommandTableBase[30].command,
    },
    [10] = {
        indexbase = 32,
        name = CommandTableBase[32].name,
        command = CommandTableBase[32].command,
    },
    [11] = {
        indexbase = 34,
        name = CommandTableBase[34].name,
        command = CommandTableBase[34].command,
    },
    [12] = {
        indexbase = 36,
        name = CommandTableBase[36].name,
        command = CommandTableBase[36].command,
    },
    [13] = {
        indexbase = 33,
        name = CommandTableBase[33].name,
        command = CommandTableBase[33].command,
    },
    [14] = {
        indexbase = 37,
        name = CommandTableBase[37].name,
        command = CommandTableBase[37].command,
    },
    [15] = {
        indexbase = 39,
        name = CommandTableBase[39].name,
        command = CommandTableBase[39].command,
    },
    [16] = {
        indexbase = 40,
        name = CommandTableBase[40].name,
        command = CommandTableBase[40].command,
    },
    [17] = {
        indexbase = 41,
        name = CommandTableBase[41].name,
        command = CommandTableBase[41].command,
    },
    [18] = {
        indexbase = 42,
        name = CommandTableBase[42].name,
        command = CommandTableBase[42].command,
    },
    [19] = {
        indexbase = 43,
        name = CommandTableBase[43].name,
        command = CommandTableBase[43].command,
    },
    [20] = {
        indexbase = 44,
        name = CommandTableBase[44].name,
        command = CommandTableBase[44].command,
    },
    [21] = {
        indexbase = 45,
        name = CommandTableBase[45].name,
        command = CommandTableBase[45].command,
    },
    [22] = {
        indexbase = 46,
        name = CommandTableBase[46].name,
        command = CommandTableBase[46].command,
    },
    [23] = {
        indexbase = 47,
        name = CommandTableBase[47].name,
        command = CommandTableBase[47].command,
    },
    [24] = {
        indexbase = 48,
        name = CommandTableBase[48].name,
        command = CommandTableBase[48].command,
    },
    [25] = {
        indexbase = 49,
        name = CommandTableBase[49].name,
        command = CommandTableBase[49].command,
    },
}
local CommandTable2 = {
    [1] = {
        indexbase = 17,
        name = CommandTableBase[17].name,
        command = CommandTableBase[17].command,
    },
    [2] = {
        indexbase = 15,
        name = CommandTableBase[15].name,
        command = CommandTableBase[15].command,
    },

}
local CommandTable3 = {
    [1] = {
        indexbase = 3,
        name = CommandTableBase[3].name,
        command = CommandTableBase[3].command,
    },
    [2] = {
        indexbase = 31,
        name = CommandTableBase[31].name,
        command = CommandTableBase[31].command,
    },
    [3] = {
        indexbase = 5,
        name = CommandTableBase[5].name,
        command = CommandTableBase[5].command,
    },
    [4] = {
        indexbase = 22,
        name = CommandTableBase[22].name,
        command = CommandTableBase[22].command,
    },
}
local CommandTable4 = {
    [1] = {
        indexbase = 6,
        name = CommandTableBase[6].name,
        command = CommandTableBase[6].command,
    },
    [2] = {
        indexbase = 7,
        name = CommandTableBase[7].name,
        command = CommandTableBase[7].command,
    },
    [3] = {
        indexbase = 9,
        name = CommandTableBase[9].name,
        command = CommandTableBase[9].command,
    },
    [4] = {
        indexbase = 11,
        name = CommandTableBase[11].name,
        command = CommandTableBase[11].command,
    },
    [5] = {
        indexbase = 12,
        name = CommandTableBase[12].name,
        command = CommandTableBase[12].command,
    },
    [6] = {
        indexbase = 18,
        name = CommandTableBase[18].name,
        command = CommandTableBase[18].command,
    },
    [7] = {
        indexbase = 14,
        name = CommandTableBase[14].name,
        command = CommandTableBase[14].command,
    },
    [8] = {
        indexbase = 10,
        name = CommandTableBase[10].name,
        command = CommandTableBase[10].command,
    },
}
local CommandTable5 = {

    [1] = {
        indexbase = 24,
        name = CommandTableBase[24].name,
        command = CommandTableBase[24].command,
    },
    [2] = {
        indexbase = 25,
        name = CommandTableBase[25].name,
        command = CommandTableBase[25].command,
    },
    [3] = {
        indexbase = 26,
        name = CommandTableBase[26].name,
        command = CommandTableBase[26].command,
    },
    [4] = {
        indexbase = 27,
        name = CommandTableBase[27].name,
        command = CommandTableBase[27].command,
    },
    [5] = {
        indexbase = 35,
        name = CommandTableBase[35].name,
        command = CommandTableBase[35].command,
    },
    [6] = {
        indexbase = 38,
        name = CommandTableBase[38].name,
        command = CommandTableBase[38].command,
    },
    [7] = {
        indexbase = 8,
        name = CommandTableBase[8].name,
        command = CommandTableBase[8].command,
    },
    [8] = {
        indexbase = 20,
        name = CommandTableBase[20].name,
        command = CommandTableBase[20].command,
    },
    [9] = {
        indexbase = 50,
        name = CommandTableBase[50].name,
        command = CommandTableBase[50].command,
    },
    [10] = {
        indexbase = 51,
        name = CommandTableBase[51].name,
        command = CommandTableBase[51].command, 
    }
}
local CommandTable6 = {
    [1] = {
        indexbase = 13,
        name = CommandTableBase[13].name,
        command = CommandTableBase[13].command,
    },
    [2] = {
        indexbase = 19,
        name = CommandTableBase[19].name,
        command = CommandTableBase[19].command,
    },
    [3] = {
        indexbase = 52,
        name = CommandTableBase[52].name,
        command = CommandTableBase[52].command,
    },
}

local keyWords = {
    "ModifyleagueTecBLv",
    "Alltecbonus",
    "upgradeheroavatar",
    "Upgradestartec",
    "heroawaken",
    "ZombieTreasure",

    "superstone",
    "getAllSigils",
    "gethero",
    "GetHerosByLimit",
    "getgoods",
    "heroadvance",
    "getcoin",
    "getdiamond",
    "addexp",
    "modifylv",
    "addheroexp",
    "showheropro",
    "showheroproByID",
    "showheropower",
    "showtime",
    "ModifyOpenWorldTime",
    "addtime",
    "settime",
    "clearall",
    "DelThisAccount",
    "GetFullallotment",
    "CopyAccountNumber",
    "ScientificResearch",
    "delgoods",
    "delhero",
    "modifyTowerStage",
    "getheroteam",
    "getequipment",
    "getherostar",
    "addvipexp",
    --"resetLeagueBossCount",
    --"setLeaugeOfficer",
    "modifyviplv",
    "leagueComp",
    "preventwallow",
    "getreward",
    --"treasurerecover",
    "cleanStagePlot",
    "modifyMateLevel",
    "addMateExp",
    "modifyIdleStage",
    "modifyChinaRedStage",
    "completeTargetTask",
    "resetTargetTask",
    --"createLeague",
    --"setLeagueBoss",
    "resetMaze",
    "passMaze",
    "passMazeMN",
    "showHistoryPower",
    "pushHistoryPower",
    "passFactionStage",
    "addRelic",
    --"joinLeague",
    "AshMaxStage",
    "AshStage",
    "AshAddMedicine",
    "AshAddStore",
    "AshRestart",
    "getUStar",
    "getGStar",
    "getPassport",
    "setAutoLogin",
    "UpgradeEquip",
    "UpgradeArtifact",
    "UpgradeExclusive",
    "PeakAddRelic",
    "PeakSetLevel",
    "PeakGetSomeBox",
    "PeakDelIndependEvt",
    "PeakClearFog",
    "PeakCreateDynEvent",
    "PeakMonsterSetHp1",
    "ResetEquipEctype",
    "ModifyFarm",
    --"SetLeagueLevel",
    --"AddLeagueExp",
    --"AddLeagueActivity",
    "SetSelfCEOR5",
    "SetSelfR4",
    "PrinTimeZoneId",
    "ModifyTimeZoneId",
    "WeaponUnActive",
    "addArenaScore",
    --"ResetLeagueActivityBossCount",
    "ResetMergeServer",
    "SetNextLogtouTime",
    "ResetReturnAct",
    "simulaterecharge",
    "Recharge",
    "RechargeDelivery",
    --"ModifyLeagueTechLv",
    "ModifyAirshipLv",
    "privatemute",
    "ResetVoidArenaAll",
    "ResetVoidArenaSingle",
    "VoidArenaClearRoleCD",
    "VoidArenaSetRole",
    "VoidArenaSetUpgradeLv",
    "SetHeroHandBook",
    "SetMonthlyTask",
    "TavernTaskFinishAll",
    "GeneralSetStage",
    "ModifyIdleReward",
    "AddTreasureRare",
    "HammerTreasureRare",
    "SpaceExploreActivationAllPosition",
    "BattleshipTechAllFullLevel",
    "BattleshipTechToXLv",
    "modifyKillingTower",
    "modifyMultiKillingTower",
    "addLeagueWarActiviy",
    "SetMinGameLv",
    "ClearMinGame",
    "SetRoleCreateTime",
    "HerotestResource",
    "GetRarityHero",
    "SigilLevelUp",
    "Timetravel",
    "DecorationsLevelUp",
    "GetDecorations",
    "UpgradeWeapon",
    --"dimensionwar",

    "AddAllStarDiamond",
    "ModifyFromWorldID",
    "AwakeHeroSkill",
    "UpgradeExclusiveEquipment",
    "getmaxblankhero",
    "gettrialhero",
    "runLuaFunc",
    --新版联盟
    "AllianceHelp",
    "AllianceTechnology",
    --科技
    "ResetTechExp ",
    "FillRecommendTechExp",
    "AddDailyDonate",
    "FlushCoinDonateCnt",
    --礼物
    "SetAllianceGiftLv",
    "SetAllianceGiftExp",
    --压测
    "ClearApplyList",
    "JoinAlliance",
    "ApplyJoinAlliance",
    "InitCityTroop",
    --城建
    "AddWoundedSoldier",
    "CityDefeatBreak",
    "SetBuildingLevel",
    "AddTechEffect",
    "RemoveTechEffect",
    "GetSurvivorById",
    "GetSurvivorByBuildingTypeId",
    "AddInnerCitySoldier",
    "AddOutsideCitySoldier",
    "ClearAreaEvent",
    "ClearSingleEvent",
    "JumpNewPlayerEvent",
    --同盟boss
    "AllianceBoss",
    --沙盘
    "Sandbox",
    "CampTrial",
    "DesertStrom",
    "Congress",
    "AcornPub",
    --末日降临
    "SkyFall",
    --自选周卡
    "ChooseWeekCard",
    --点赞
    "LikePart",
    --通用存储
    "CommonStorage",
	"AllianceAddNew",
    --个性化
    "ResetAllDecoration",
    "AddAllDecoration",
    "AddLikeTimes",
    "ResetLikeTimes",

    --region 新版英雄
    "GetNewHeroByID",
    "GetAllNewHero",
    "ChangeNewHeroLv",
    "ChangeNewHeroStarLv",
    "ShowNewHeroProp",
    "DelNewHeroByID",
    "ModifyPalInfo",
    "SavePalInfo",

    --endregion
    --region 世界boss活动
    "GenWorldBoss",
    "DamgeToWorldBoss",
    --endregion
    --region 军备竞赛
    "AddItem",
    --endregion
    "TopicNums",
    "SendMail",

    "ResetLeagueAch",
    "FinishLeagueAch",
    "ChangeLeagueAch",
    "ChangeLeagueAchUnlockTime",
    "ChangeLeagueAchOverTime",
    "DelAlliance",
    "ResetAllianceFirstJoinTime",
    "AllianceAddTime",
    "INVITATION",
    "AllianceShare",
    "TaskPartTest_PrintTaskInfo",
    "TaskPartLingshi_ClearTaskValue",
    "TaskPartLingshi_ModifyTaskValue",
    "AddAllianceRecord",
    "CloseAllianceCheck",
    "AddAllianceIntelligence",
    --货车
    "Carriage",
    "DecreaseItem",
    "CloseActivity",
	"CompleteTask"
}

local SuperStoneList = {
    { text = "getgoods 101 1000", des = "获取英雄召唤券1000个" },
    { text = "getgoods 102 1000", des = "获取幸存者召唤卷1000个" },
    { text = "getgoods 801 1000", des = "获取1分钟通用加速1000个" },
    { text = "getgoods 802 1000", des = "获取5分钟通用加速1000个" },
    { text = "getgoods 803 1000", des = "获取1小时通用加速1000个" },
    { text = "getgoods 804 1000", des = "获取3小时通用加速1000个" },
    { text = "getgoods 3302 1000", des = "获取白图纸1000个" },
    { text = "getgoods 4000 1000000", des = "神兽经验" },
    { text = "getgoods 3303 1000", des = "获取红图纸1000个" },
    { text = "getgoods 4002 1000", des = "获取神兽零件1000个" },
    { text = "getgoods 4051 1000", des = "获取神兽1级宝箱1000个" },
    { text = "getgoods 4052 1000", des = "获取神兽2级宝箱1000个" },
    { text = "getgoods 4053 1000", des = "获取神兽3级宝箱1000个" },
    { text = "getgoods 4054 20000", des = "获取神兽4级宝箱20000个" },
    { text = "getgoods 4055 1000", des = "获取神兽5级宝箱1000个" },
    { text = "getgoods 1 100000000000", des = "获取金币1M个" },
    { text = "getgoods 2 100000000", des = "获取钻石100M个" },
    { text = "getgoods 3 100000000", des = "获取英雄经验100M个" },
    { text = "getgoods 35 100000000000", des = "粮食1M个" },
    { text = "getgoods 37 1000", des = "获体力1000个" },
    { text = "getgoods 36 100000000000", des = "获铁矿100M" },
    { text = "getgoods 3201 1000000", des = "白色锻造材料1000000个" },
    { text = "getgoods 3202 1000000", des = "绿色锻造材料1000000个" },
    { text = "getgoods 3203 1000000", des = "蓝色锻造材料1000000个" },
    { text = "getgoods 3204 1000000", des = "紫色锻造材料1000000个" },
    { text = "getgoods 3205 1000000", des = "橙色锻造材料1000000个" },
    { text = "getgoods 3301 1000000", des = "强化石1000000个" },
    { text = "getgoods 3001 10", des = "橙武器" },
    { text = "getgoods 3002 10", des = "橙衣服" },
    { text = "getgoods 3003 10", des = "橙头盔" },
    { text = "getgoods 3004 10", des = "橙鞋子" },
    { text = "getgoods 3005 10", des = "紫武器" },
    { text = "getgoods 3009 10", des = "蓝武器" },
    { text = "getgoods 3013 10", des = "绿武器" },
    { text = "getgoods 2100 1000000", des = "技能勋章" },
    { text = "getgoods 2101 1000000", des = "SR碎片" },
    { text = "getgoods 2102 1000000", des = "SSR碎片" },
    { text = "getgoods 2103 1000000", des = "UR碎片" },
    { text = "getgoods 5101 10", des = "装饰物" },
    { text = "getgoods 5102 10", des = "装饰物" },
    { text = "getgoods 5104 10", des = "装饰物" },
    { text = "getgoods 5201 10", des = "装饰物" },
    { text = "getgoods 5202 10", des = "装饰物" },
    { text = "getgoods 5203 10", des = "装饰物" },
    { text = "getgoods 5301 10", des = "装饰物" },
    { text = "getgoods 5304 10", des = "装饰物" },
    { text = "getgoods 5305 10", des = "装饰物" },
    { text = "getgoods 5306 10", des = "装饰物" },
    { text = "getgoods 5307 10", des = "装饰物" },
    { text = "getgoods 5308 10", des = "装饰物" },
    { text = "getgoods 5309 10", des = "装饰物" },
    { text = "getgoods 5310 10", des = "装饰物" },
    { text = "getgoods 5311 10", des = "装饰物" },
    { text = "getgoods 5314 10", des = "装饰物" },
    { text = "getgoods 5315 10", des = "装饰物" },
 { text = "getgoods 2 100", des = "获取钻石 100个" },
    { text = "getgoods 601 100", des = "获取5钻石 100个" },
    { text = "getgoods 602 100", des = "获取10钻石 100个" },
    { text = "getgoods 603 100", des = "获取20钻石 100个" },
    { text = "getgoods 604 100", des = "获取50钻石 100个" },
    { text = "getgoods 605 100", des = "获取100钻石 100个" },
    { text = "getgoods 606 100", des = "获取5K钻石 100个" },
    { text = "getgoods 607 100", des = "获取10K钻石 100个" },
    { text = "getgoods 37 100", des = "获取通用体力 100个" },
    { text = "getgoods 6 100", des = "获取10体力（道具） 100个" },
    { text = "getgoods 7 100", des = "获取50体力（道具） 100个" },
    { text = "getgoods 26 100", des = "获取每日任务积分 100个" },
    { text = "getgoods 34 100", des = "获取莫妮卡钳子 100个" },
    { text = "getgoods 5 100", des = "获取同盟贡献 100个" },
    { text = "getgoods 8 100", des = "获取VIP经验 100个" },
    { text = "getgoods 650 100", des = "获取10VIP 100个" },
    { text = "getgoods 651 100", des = "获取50VIP 100个" },
    { text = "getgoods 652 100", des = "获取100VIP 100个" },
    { text = "getgoods 653 100", des = "获取500VIP 100个" },
    { text = "getgoods 654 100", des = "获取1000VIP 100个" },
    { text = "getgoods 655 100", des = "获取2500VIP 100个" },
    { text = "getgoods 671 100", des = "获取24小时VIP（使用道具） 100个" },
    { text = "getgoods 672 100", des = "获取7天VIP（使用道具） 100个" },
    { text = "getgoods 673 100", des = "获取30天VIP（使用道具） 100个" },
    { text = "getgoods 25 100", des = "获取支付用代币(红钻) 100个" },
    { text = "getgoods 104 100", des = "获取改名卡 100个" },
    { text = "getgoods 541 100", des = "获取50%行军加速 100个" },
    { text = "getgoods 542 100", des = "获取25%行军加速 100个" },
    { text = "getgoods 11 100", des = "获取勇气勋章 100个" },
    { text = "getgoods 12 100", des = "获取远征奖章 100个" },
    { text = "getgoods 13 100", des = "获取荣誉积分 100个" },
    { text = "getgoods 14 100", des = "获取10K荣誉积分 100个" },
    { text = "getgoods 1051 100", des = "获取50同盟贡献 100个" },
    { text = "getgoods 1052 100", des = "获取500贡献点 100个" },
    { text = "getgoods 1053 100", des = "获取2500同盟贡献 100个" },
    { text = "getgoods 35 100", des = "获取粮食 100个" },
    { text = "getgoods 36 100", des = "获取铁矿 100个" },
    { text = "getgoods 1 100", des = "获取金币 100个" },
    { text = "getgoods 701 100", des = "获取1K粮食 100个" },
    { text = "getgoods 702 100", des = "获取1K铁矿 100个" },
    { text = "getgoods 703 100", des = "获取600金币 100个" },
    { text = "getgoods 704 100", des = "获取5K粮食 100个" },
    { text = "getgoods 705 100", des = "获取5K铁矿 100个" },
    { text = "getgoods 706 100", des = "获取3K金币 100个" },
    { text = "getgoods 707 100", des = "获取10K粮食 100个" },
    { text = "getgoods 708 100", des = "获取10K铁矿 100个" },
    { text = "getgoods 709 100", des = "获取6K金币 100个" },
    { text = "getgoods 710 100", des = "获取30K粮食 100个" },
    { text = "getgoods 711 100", des = "获取30K铁矿 100个" },
    { text = "getgoods 712 100", des = "获取18K金币 100个" },
    { text = "getgoods 713 100", des = "获取50K粮食 100个" },
    { text = "getgoods 714 100", des = "获取50K铁矿 100个" },
    { text = "getgoods 715 100", des = "获取30K金币 100个" },
    { text = "getgoods 716 100", des = "获取500K粮食 100个" },
    { text = "getgoods 717 100", des = "获取500K铁矿 100个" },
    { text = "getgoods 718 100", des = "获取300K金币 100个" },
    { text = "getgoods 719 100", des = "获取1M粮食 100个" },
    { text = "getgoods 720 100", des = "获取1M铁矿 100个" },
    { text = "getgoods 721 100", des = "获取1M金币 100个" },
    { text = "getgoods 751 100", des = "获取蓝色资源自选宝箱 100个" },
    { text = "getgoods 752 100", des = "获取紫色资源自选宝箱 100个" },
    { text = "getgoods 753 100", des = "获取金色资源自选宝箱 100个" },
    { text = "getgoods 754 100", des = "获取资源宝箱 100个" },
    { text = "getgoods 761 100", des = "获取食物等级宝箱 100个" },
    { text = "getgoods 762 100", des = "获取食物等级宝箱 100个" },
    { text = "getgoods 763 100", des = "获取食物等级宝箱 100个" },
    { text = "getgoods 764 100", des = "获取食物等级宝箱 100个" },
    { text = "getgoods 765 100", des = "获取钢铁等级宝箱 100个" },
    { text = "getgoods 766 100", des = "获取钢铁等级宝箱 100个" },
    { text = "getgoods 767 100", des = "获取钢铁等级宝箱 100个" },
    { text = "getgoods 768 100", des = "获取钢铁等级宝箱 100个" },
    { text = "getgoods 769 100", des = "获取金币等级宝箱 100个" },
    { text = "getgoods 770 100", des = "获取金币等级宝箱 100个" },
    { text = "getgoods 771 100", des = "获取金币等级宝箱 100个" },
    { text = "getgoods 772 100", des = "获取金币等级宝箱 100个" },
    { text = "getgoods 850 100", des = "获取5分钟加速道具宝箱 100个" },
    { text = "getgoods 801 100", des = "获取1分钟加速 100个" },
    { text = "getgoods 802 100", des = "获取5分钟加速 100个" },
    { text = "getgoods 803 100", des = "获取1小时加速 100个" },
    { text = "getgoods 804 100", des = "获取3小时加速 100个" },
    { text = "getgoods 805 100", des = "获取8小时加速 100个" },
    { text = "getgoods 806 100", des = "获取15分钟加速 100个" },
    { text = "getgoods 811 100", des = "获取1分钟建筑加速 100个" },
    { text = "getgoods 812 100", des = "获取5分钟建筑加速 100个" },
    { text = "getgoods 813 100", des = "获取1小时建筑加速 100个" },
    { text = "getgoods 814 100", des = "获取3小时建筑加速 100个" },
    { text = "getgoods 815 100", des = "获取8小时建筑加速 100个" },
    { text = "getgoods 816 100", des = "获取15分钟建筑加速 100个" },
    { text = "getgoods 821 100", des = "获取1分钟科技加速 100个" },
    { text = "getgoods 822 100", des = "获取5分钟科技加速 100个" },
    { text = "getgoods 823 100", des = "获取1小时科技加速 100个" },
    { text = "getgoods 824 100", des = "获取3小时科技加速 100个" },
    { text = "getgoods 825 100", des = "获取8小时科技加速 100个" },
    { text = "getgoods 826 100", des = "获取15分钟科技加速 100个" },
    { text = "getgoods 831 100", des = "获取1分钟训练加速 100个" },
    { text = "getgoods 832 100", des = "获取5分钟训练加速 100个" },
    { text = "getgoods 833 100", des = "获取1小时训练加速 100个" },
    { text = "getgoods 834 100", des = "获取3小时训练加速 100个" },
    { text = "getgoods 835 100", des = "获取8小时训练加速 100个" },
    { text = "getgoods 841 100", des = "获取1分钟治疗加速 100个" },
    { text = "getgoods 842 100", des = "获取5分钟治疗加速 100个" },
    { text = "getgoods 843 100", des = "获取1h治疗加速 100个" },
    { text = "getgoods 844 100", des = "获取3h治疗加速 100个" },
    { text = "getgoods 845 100", des = "获取8h治疗加速 100个" },
    { text = "getgoods 3 100", des = "获取英雄经验 100个" },
    { text = "getgoods 2003 100", des = "获取英雄经验宝箱 100个" },
    { text = "getgoods 2004 100", des = "获取英雄经验宝箱 100个" },
    { text = "getgoods 2005 100", des = "获取英雄经验宝箱 100个" },
    { text = "getgoods 101 100", des = "获取传奇招募卷 100个" },
    { text = "getgoods 8007 100", des = "获取金色英雄自选礼盒1 100个" },
    { text = "getgoods 2103 100", des = "获取金色英雄通用碎片 100个" },
    { text = "getgoods 2102 100", des = "获取紫色英雄通用碎片 100个" },
    { text = "getgoods 2101 100", des = "获取蓝色英雄通用碎片 100个" },
    { text = "getgoods 2100 100", des = "获取技能勋章 100个" },
    { text = "getgoods 3252 100", des = "获取优秀装备宝箱 100个" },
    { text = "getgoods 3253 100", des = "获取优质装备宝箱 100个" },
    { text = "getgoods 3254 100", des = "获取稀有装备宝箱 100个" },
    { text = "getgoods 3256 100", des = "获取装备幸运箱 100个" },
    { text = "getgoods 3001 100", des = "获取橙装武器 100个" },
    { text = "getgoods 3002 100", des = "获取橙装护甲 100个" },
    { text = "getgoods 3003 100", des = "获取橙装头盔 100个" },
    { text = "getgoods 3004 100", des = "获取橙装胫甲 100个" },
    { text = "getgoods 3005 100", des = "获取紫装武器 100个" },
    { text = "getgoods 3006 100", des = "获取紫装护甲 100个" },
    { text = "getgoods 3007 100", des = "获取紫装头盔 100个" },
    { text = "getgoods 3008 100", des = "获取紫装胫甲 100个" },
    { text = "getgoods 3009 100", des = "获取蓝装武器 100个" },
    { text = "getgoods 3010 100", des = "获取蓝装护甲 100个" },
    { text = "getgoods 3011 100", des = "获取蓝装头盔 100个" },
    { text = "getgoods 3012 100", des = "获取蓝装胫甲 100个" },
    { text = "getgoods 3013 100", des = "获取绿装武器 100个" },
    { text = "getgoods 3014 100", des = "获取绿装护甲 100个" },
    { text = "getgoods 3015 100", des = "获取绿装头盔 100个" },
    { text = "getgoods 3016 100", des = "获取绿装胫甲 100个" },
    { text = "getgoods 3301 100", des = "获取强化石 100个" },
    { text = "getgoods 3302 100", des = "获取装备图纸传奇 100个" },
    { text = "getgoods 3303 100", des = "获取装备图纸神话 100个" },
    { text = "getgoods 3201 100", des = "获取螺丝钉 100个" },
    { text = "getgoods 3202 100", des = "获取精铁模组 100个" },
    { text = "getgoods 3203 100", des = "获取高温合金 100个" },
    { text = "getgoods 3204 100", des = "获取合成树脂 100个" },
    { text = "getgoods 3205 100", des = "获取介电陶瓷 100个" },
    { text = "getgoods 4000 100", des = "获取战斗数据 100个" },
    { text = "getgoods 4001 100", des = "获取10K战斗数据 100个" },
    { text = "getgoods 4002 100", des = "获取无人机零件 100个" },
    { text = "getgoods 4051 100", des = "获取1级无人机宝箱 100个" },
    { text = "getgoods 4052 100", des = "获取2级无人机宝箱 100个" },
    { text = "getgoods 4053 100", des = "获取3级无人机宝箱 100个" },
    { text = "getgoods 4054 100", des = "获取4级无人机宝箱 100个" },
    { text = "getgoods 4055 100", des = "获取5级无人机宝箱 100个" },
    { text = "getgoods 4056 100", des = "获取6级无人机宝箱 100个" },
    { text = "getgoods 4057 100", des = "获取7级无人机宝箱 100个" },
    { text = "getgoods 4058 100", des = "获取8级无人机宝箱 100个" },
    { text = "getgoods 4059 100", des = "获取9级无人机宝箱 100个" },
    { text = "getgoods 4060 100", des = "获取10级无人机宝箱 100个" },
    { text = "getgoods 5000 100", des = "获取万能装饰物组件 100个" },
    { text = "getgoods 5001 100", des = "获取装饰物宝箱 100个" },
    { text = "getgoods 5002 100", des = "获取装饰物宝箱 100个" },
    { text = "getgoods 5003 100", des = "获取装饰物宝箱 100个" },
    { text = "getgoods 5004 100", des = "获取万能装饰物组件幸运箱 100个" },
    { text = "getgoods 5101 100", des = "获取【蓝色】【箱子】青铜炮车（铜剑） 100个" },
    { text = "getgoods 5102 100", des = "获取【蓝色】【箱子】青铜飞机（铜盾） 100个" },
    { text = "getgoods 5103 100", des = "获取【蓝色】【箱子】和平主义 100个" },
    { text = "getgoods 5104 100", des = "获取【蓝色】【箱子】青铜坦克（铜头盔） 100个" },
    { text = "getgoods 5201 100", des = "获取【紫色】【累充】银白战士（银剑） 100个" },
    { text = "getgoods 5202 100", des = "获取【紫色】【箱子】白银战机（银盾） 100个" },
    { text = "getgoods 5203 100", des = "获取【紫色】【箱子】白银毁灭者（银头盔） 100个" },
    { text = "getgoods 5204 100", des = "获取【紫色】【箱子】白银火箭 100个" },
    { text = "getgoods 5205 100", des = "获取【紫色】【箱子】它是传奇 100个" },
    { text = "getgoods 5301 100", des = "获取【橙色】【礼包】永恒金字塔（金字塔） 100个" },
    { text = "getgoods 5302 100", des = "获取【橙色】【隐秘宝藏】黄金机动队 100个" },
    { text = "getgoods 5303 100", des = "获取【橙色】【礼包】黄金元帅雕像 100个" },
    { text = "getgoods 5304 100", des = "获取【橙色】【箱子】黄金坦克（金头盔） 100个" },
    { text = "getgoods 5305 100", des = "获取【橙色】【箱子】黄金导弹车（金剑） 100个" },
    { text = "getgoods 5306 100", des = "获取【橙色】【箱子】黄金轰炸机（金盾） 100个" },
    { text = "getgoods 5307 100", des = "获取【橙色】【箱子】钟楼（歌剧院） 100个" },
    { text = "getgoods 5308 100", des = "获取【橙色】【箱子】铁塔复制品（斗兽场） 100个" },
    { text = "getgoods 5309 100", des = "获取【橙色】【箱子】摩天轮（泰姬陵） 100个" },
    { text = "getgoods 5310 100", des = "获取【橙色】【箱子】霓虹灯牌（天守阁） 100个" },
    { text = "getgoods 5311 100", des = "获取【橙色】【巅峰】自由女神（自由女神） 100个" },
    { text = "getgoods 5312 100", des = "获取【橙色】【雷达】勇士丰碑 100个" },
    { text = "getgoods 5313 100", des = "获取【橙色】【远征】军功纪念碑 100个" },
    { text = "getgoods 5314 100", des = "获取【橙色】【闪金集市】胜利之塔（人面像） 100个" },
    { text = "getgoods 5315 100", des = "获取【橙色】【战区对决】鲜血王座（基督山） 100个" },
    { text = "getgoods 9 100", des = "获取光荣勋章 100个" },
    { text = "getgoods 10 100", des = "获取1000光荣勋章 100个" },
    { text = "getgoods 102 100", des = "获取幸存者招募卷 100个" },
    { text = "getgoods 6000 100", des = "获取幸存者信物 100个" },
    { text = "getgoods 6006 100", des = "获取随机幸存者蓝 100个" },
    { text = "getgoods 6007 100", des = "获取随机幸存者紫 100个" },
    { text = "getgoods 501 100", des = "获取同盟迁城 100个" },
    { text = "getgoods 500 100", des = "获取高级迁城 100个" },
    { text = "getgoods 502 100", des = "获取随机迁城 100个" },
    { text = "getgoods 521 100", des = "获取8小时防护罩 100个" },
    { text = "getgoods 522 100", des = "获取12小时防护罩 100个" },
    { text = "getgoods 523 100", des = "获取24小时防护罩 100个" },
    { text = "getgoods 524 100", des = "获取3天防护罩 100个" },
    { text = "getgoods 1001 100", des = "获取普通同盟礼物 100个" },
    { text = "getgoods 1002 100", des = "获取优秀同盟礼物 100个" },
    { text = "getgoods 1003 100", des = "获取优质同盟礼物 100个" },
    { text = "getgoods 1004 100", des = "获取稀有同盟礼物 100个" },
    { text = "getgoods 1005 100", des = "获取传奇同盟礼物 100个" },
    { text = "getgoods 1006 100", des = "获取神话同盟礼物 100个" },
    { text = "getgoods 900 100", des = "获取同盟礼物 100个" },
    { text = "getgoods 950 100", des = "获取末日游荡者宝箱 100个" },
    { text = "getgoods 7003 100", des = "获取战术大师补给箱 100个" },
    { text = "getgoods 7004 100", des = "获取莫妮卡的谢礼 100个" },
    { text = "getgoods 7013 100", des = "获取神秘补给箱 100个" },
    { text = "getgoods 7014 100", des = "获取隐秘调令 100个" },
    { text = "getgoods 7015 100", des = "获取贸易合约 100个" },
    { text = "getgoods 7016 100", des = "获取建设零件 100个" },
    { text = "getgoods 7017 100", des = "获取稀有探索者宝箱 100个" },
    { text = "getgoods 7018 100", des = "获取豪华冒险者宝箱 100个" },
    { text = "getgoods 7019 100", des = "获取普通远征宝箱 100个" },
    { text = "getgoods 7020 100", des = "获取优秀远征宝箱 100个" },
    { text = "getgoods 7021 100", des = "获取稀有远征宝箱 100个" },
    { text = "getgoods 7022 100", des = "获取传奇远征宝箱 100个" },
    { text = "getgoods 7023 100", des = "获取军备竞赛进度积分 100个" },
    { text = "getgoods 7024 100", des = "获取备战奖章 100个" },
    { text = "getgoods 7025 100", des = "获取最强指挥官进度积分 100个" },
    { text = "getgoods 7027 100", des = "获取同盟对决积分道具 100个" },
    { text = "getgoods 7028 100", des = "获取隐秘宝藏-蓝 100个" },
    { text = "getgoods 7029 100", des = "获取隐秘宝藏-紫 100个" },
    { text = "getgoods 7030 100", des = "获取隐秘宝藏-橙 100个" },
    { text = "getgoods 7031 100", des = "获取国会争夺积分 100个" },
    { text = "getgoods 7032 100", des = "获取最强指挥官D1目标奖励积分 100个" },
    { text = "getgoods 7033 100", des = "获取最强指挥官D2目标奖励积分 100个" },
    { text = "getgoods 7034 100", des = "获取最强指挥官D3目标奖励积分 100个" },
    { text = "getgoods 7035 100", des = "获取最强指挥官D4目标奖励积分 100个" },
    { text = "getgoods 7036 100", des = "获取最强指挥官D5目标奖励积分 100个" },
    { text = "getgoods 7037 100", des = "获取最强指挥官D6目标奖励积分 100个" },
    { text = "getgoods 7038 100", des = "获取最强指挥官D7目标奖励积分 100个" },
    { text = "getgoods 7039 100", des = "获取城镇宝箱 100个" },
    { text = "getgoods 103 100", des = "获取无限宝箱 100个" },
    { text = "getgoods 8001 100", des = "获取强化石幸运宝箱 100个" },
    { text = "getgoods 8002 100", des = "获取超级自选宝箱 100个" },
    { text = "getgoods 8003 100", des = "获取弹药 100个" },
    { text = "getgoods 8004 100", des = "获取高级钥匙 100个" },
    { text = "getgoods 8005 100", des = "获取先锋战令积分 100个" },
    { text = "getgoods 8006 100", des = "获取墨菲勇士战令积分 100个" },
    { text = "getgoods 8007 100", des = "获取金色英雄自选礼盒1 100个" },
    { text = "getgoods 8008 100", des = "获取特训战令积分 100个" },
    { text = "getgoods 8009 100", des = "获取全面备战礼券 100个" },
    { text = "getgoods 8010 100", des = "获取通用勇士战令积分 100个" },
    { text = "getgoods 8011 100", des = "获取金色英雄自选礼盒2 100个" },
    { text = "getgoods 8012 100", des = "获取幸运转盘代币 100个" },
    { text = "getgoods 8013 100", des = "获取英雄自选宝箱3 100个" },
    { text = "getgoods 8014 100", des = "获取每日必买进度奖励积分 100个" },
    { text = "getgoods 8015 100", des = "获取联盟徽章—蓝色宝箱 100个" },
    { text = "getgoods 8016 100", des = "获取联盟徽章—紫色宝箱 100个" },
    { text = "getgoods 8017 100", des = "获取联盟徽章—橙色宝箱 100个" },
    { text = "getgoods 8018 100", des = "获取幸运转盘代币 100个" },
    { text = "getgoods 8019 100", des = "获取英雄自选宝箱2 100个" },
    { text = "getgoods 8101 100", des = "获取累充代币-新人 100个" },
    { text = "getgoods 8102 100", des = "获取累充代币-新服 100个" },
    { text = "getgoods 8103 100", des = "获取累充代币-循环-七日累充 100个" },
    { text = "getgoods 8104 100", des = "获取累充代币-循环-每日累充 100个" },
    { text = "getgoods 8105 100", des = "获取累充代币-节日 100个" },
    { text = "getgoods 9000 100", des = "获取默认城堡 100个" },
    { text = "getgoods 9001 100", des = "获取女性默认城堡 100个" },
    { text = "getgoods 9002 100", des = "获取神秘蘑菇 100个" },
    { text = "getgoods 9003 100", des = "获取翱翔之翼3天 100个" },
    { text = "getgoods 9101 100", des = "获取默认头像框 100个" },
    { text = "getgoods 9102 100", des = "获取月卡头像框 100个" },
    { text = "getgoods 9103 100", des = "获取上将头像框 100个" },
    { text = "getgoods 9104 100", des = "获取中将头像框 100个" },
    { text = "getgoods 9105 100", des = "获取少将头像框 100个" },
    { text = "getgoods 9106 100", des = "获取上校头像框 100个" },
    { text = "getgoods 9107 100", des = "获取中校头像框 100个" },
    { text = "getgoods 9108 100", des = "获取王之叹息头像框 100个" },
    { text = "getgoods 9109 100", des = "获取征服者头像框 100个" },
    { text = "getgoods 9110 100", des = "获取王者之师头像框 100个" },
    { text = "getgoods 101 100", des = "获取传奇招募卷 100个" },
    { text = "getgoods 102 100", des = "获取幸存者招募卷 100个" },
    { text = "getgoods 103 100", des = "获取无限宝箱 100个" },
    { text = "getgoods 104 100", des = "获取改名卡 100个" },
    { text = "getgoods 105 100", des = "获取【森海旅者】约德 100个" },
    { text = "getgoods 106 100", des = "获取【龙血武姬】龙咔咔 100个" },
    { text = "getgoods 107 100", des = "获取【孢子勇士】艾德瑞克 100个" },
    { text = "getgoods 108 100", des = "获取【渡魂祭司】克莉斯多 100个" },
    { text = "getgoods 109 100", des = "获取【狂电拳王】阿里 100个" },
    { text = "getgoods 110 100", des = "获取【怜悯树灵】费尔瑞 100个" },
    { text = "getgoods 111 100", des = "获取【星夜公主】维尔娜 100个" },
    { text = "getgoods 112 100", des = "获取【暴怒巨斧】托伦 100个" },
    { text = "getgoods 113 100", des = "获取【蒜头武士】茂加 100个" },
    { text = "getgoods 114 100", des = "获取【死亡黑骑】安德烈 100个" },
    { text = "getgoods 115 100", des = "获取【骁羽武神】瓦尔基里 100个" },
    { text = "getgoods 116 100", des = "获取【火羽圣凰】菲利克斯 100个" },
    { text = "getgoods 117 100", des = "获取【涟漪之灵】索菲亚 100个" },
    { text = "getgoods 118 100", des = "获取【放逐之影】阿尔瓦雷斯 100个" },
    { text = "getgoods 119 100", des = "获取【屠龙者】格鲁德 100个" },
    { text = "getgoods 120 100", des = "获取【复仇之箭】玛丽莎 100个" },
    { text = "getgoods 121 100", des = "获取【吟游诗人】丹妮丝 100个" },
    { text = "getgoods 122 100", des = "获取【圣裁者】罗马诺 100个" },
    { text = "getgoods 123 100", des = "获取【弑神剑魔】斯巴达 100个" },
    { text = "getgoods 124 100", des = "获取【鬼面修罗】卡特拉斯 100个" },
    { text = "getgoods 125 100", des = "获取【蔷薇之望】黛芬妮 100个" },
    { text = "getgoods 126 100", des = "获取【魔书邪魂】贝利尔 100个" },
    { text = "getgoods 127 100", des = "获取【幽冥罗刹】米拉娜 100个" },
    { text = "getgoods 128 100", des = "获取【啸月狼王】亚伦 100个" },
    { text = "getgoods 129 100", des = "获取【魅惑魔女】莱纳 100个" },
    { text = "getgoods 130 100", des = "获取【深海恐惧】克希拉 100个" },
    { text = "getgoods 131 100", des = "获取【铁拳酋长】哈罗德 100个" },
    { text = "getgoods 2201 100", des = "获取【电魂龙裔】龙煞碎片 100个" },
    { text = "getgoods 2202 100", des = "获取【凛冬之怒】雷克萨碎片 100个" },
    { text = "getgoods 2203 100", des = "获取【狂沙猎影】莫妮卡碎片 100个" },
    { text = "getgoods 2204 100", des = "获取【明日之弓】赛维亚拉碎片 100个" },
    { text = "getgoods 2205 100", des = "获取【森海旅者】约德碎片 100个" },
    { text = "getgoods 2206 100", des = "获取【龙血武姬】龙咔咔碎片 100个" },
    { text = "getgoods 2207 100", des = "获取【孢子勇士】艾德瑞克碎片 100个" },
    { text = "getgoods 2208 100", des = "获取【渡魂祭司】克莉斯多碎片 100个" },
    { text = "getgoods 2209 100", des = "获取【狂电拳王】阿里碎片 100个" },
    { text = "getgoods 2210 100", des = "获取【怜悯树灵】费尔瑞碎片 100个" },
    { text = "getgoods 2211 100", des = "获取【星夜公主】维尔娜碎片 100个" },
    { text = "getgoods 2212 100", des = "获取【暴怒巨斧】托伦碎片 100个" },
    { text = "getgoods 2213 100", des = "获取【蒜头武士】茂加碎片 100个" },
    { text = "getgoods 2214 100", des = "获取【死亡黑骑】安德烈碎片 100个" },
    { text = "getgoods 2215 100", des = "获取【骁羽武神】瓦尔基里碎片 100个" },
    { text = "getgoods 2216 100", des = "获取【火羽圣凰】菲利克斯碎片 100个" },
    { text = "getgoods 2217 100", des = "获取【涟漪之灵】索菲亚碎片 100个" },
    { text = "getgoods 2218 100", des = "获取【放逐之影】阿尔瓦雷斯碎片 100个" },
    { text = "getgoods 2219 100", des = "获取【屠龙者】格鲁德碎片 100个" },
    { text = "getgoods 2220 100", des = "获取【复仇之箭】玛丽莎碎片 100个" },
    { text = "getgoods 2221 100", des = "获取【吟游诗人】丹妮丝碎片 100个" },
    { text = "getgoods 2222 100", des = "获取【圣裁者】罗马诺碎片 100个" },
    { text = "getgoods 2223 100", des = "获取【弑神剑魔】斯巴达碎片 100个" },
    { text = "getgoods 2224 100", des = "获取【鬼面修罗】卡特拉斯碎片 100个" },
    { text = "getgoods 2225 100", des = "获取【蔷薇之望】黛芬妮碎片 100个" },
    { text = "getgoods 2226 100", des = "获取【魔书邪魂】贝利尔碎片 100个" },
    { text = "getgoods 2227 100", des = "获取【幽冥罗刹】米拉娜碎片 100个" },
    { text = "getgoods 2228 100", des = "获取【啸月狼王】亚伦碎片 100个" },
    { text = "getgoods 2229 100", des = "获取【魅惑魔女】莱纳碎片 100个" },
    { text = "getgoods 2230 100", des = "获取【深海恐惧】克希拉碎片 100个" },
    { text = "getgoods 2231 100", des = "获取【铁拳酋长】哈罗德碎片 100个" },
    { text = "getgoods 4101 100", des = "获取神兽印记1-1 100个" },
    { text = "getgoods 4102 100", des = "获取神兽印记1-2 100个" },
    { text = "getgoods 4103 100", des = "获取神兽印记1-3 100个" },
    { text = "getgoods 4104 100", des = "获取神兽印记1-4 100个" },
    { text = "getgoods 4105 100", des = "获取神兽印记1-5 100个" },
    { text = "getgoods 4106 100", des = "获取神兽印记1-6 100个" },
    { text = "getgoods 4107 100", des = "获取神兽印记1-7 100个" },
    { text = "getgoods 4108 100", des = "获取神兽印记1-8 100个" },
    { text = "getgoods 4109 100", des = "获取神兽印记1-9 100个" },
    { text = "getgoods 4110 100", des = "获取神兽印记1-10 100个" },
    { text = "getgoods 4111 100", des = "获取神兽印记1-11 100个" },
    { text = "getgoods 4112 100", des = "获取神兽印记1-12 100个" },
    { text = "getgoods 4113 100", des = "获取神兽印记1-13 100个" },
    { text = "getgoods 4114 100", des = "获取神兽印记1-14 100个" },
    { text = "getgoods 4115 100", des = "获取神兽印记1-15 100个" },
    { text = "getgoods 4116 100", des = "获取神兽印记1-16 100个" },
    { text = "getgoods 4117 100", des = "获取神兽印记1-17 100个" },
    { text = "getgoods 4118 100", des = "获取神兽印记1-18 100个" },
    { text = "getgoods 4119 100", des = "获取神兽印记1-19 100个" },
    { text = "getgoods 4120 100", des = "获取神兽印记1-20 100个" },
    { text = "getgoods 4121 100", des = "获取神兽印记2-1 100个" },
    { text = "getgoods 4122 100", des = "获取神兽印记2-2 100个" },
    { text = "getgoods 4123 100", des = "获取神兽印记2-3 100个" },
    { text = "getgoods 4124 100", des = "获取神兽印记2-4 100个" },
    { text = "getgoods 4125 100", des = "获取神兽印记2-5 100个" },
    { text = "getgoods 4126 100", des = "获取神兽印记2-6 100个" },
    { text = "getgoods 4127 100", des = "获取神兽印记2-7 100个" },
    { text = "getgoods 4128 100", des = "获取神兽印记2-8 100个" },
    { text = "getgoods 4129 100", des = "获取神兽印记2-9 100个" },
    { text = "getgoods 4130 100", des = "获取神兽印记2-10 100个" },
    { text = "getgoods 4131 100", des = "获取神兽印记2-11 100个" },
    { text = "getgoods 4132 100", des = "获取神兽印记2-12 100个" },
    { text = "getgoods 4133 100", des = "获取神兽印记2-13 100个" },
    { text = "getgoods 4134 100", des = "获取神兽印记2-14 100个" },
    { text = "getgoods 4135 100", des = "获取神兽印记2-15 100个" },
    { text = "getgoods 4136 100", des = "获取神兽印记2-16 100个" },
    { text = "getgoods 4137 100", des = "获取神兽印记2-17 100个" },
    { text = "getgoods 4138 100", des = "获取神兽印记2-18 100个" },
    { text = "getgoods 4139 100", des = "获取神兽印记2-19 100个" },
    { text = "getgoods 4140 100", des = "获取神兽印记2-20 100个" },
    { text = "getgoods 4141 100", des = "获取神兽印记3-1 100个" },
    { text = "getgoods 4142 100", des = "获取神兽印记3-2 100个" },
    { text = "getgoods 4143 100", des = "获取神兽印记3-3 100个" },
    { text = "getgoods 4144 100", des = "获取神兽印记3-4 100个" },
    { text = "getgoods 4145 100", des = "获取神兽印记3-5 100个" },
    { text = "getgoods 4146 100", des = "获取神兽印记3-6 100个" },
    { text = "getgoods 4147 100", des = "获取神兽印记3-7 100个" },
    { text = "getgoods 4148 100", des = "获取神兽印记3-8 100个" },
    { text = "getgoods 4149 100", des = "获取神兽印记3-9 100个" },
    { text = "getgoods 4150 100", des = "获取神兽印记3-10 100个" },
    { text = "getgoods 4151 100", des = "获取神兽印记3-11 100个" },
    { text = "getgoods 4152 100", des = "获取神兽印记3-12 100个" },
    { text = "getgoods 4153 100", des = "获取神兽印记3-13 100个" },
    { text = "getgoods 4154 100", des = "获取神兽印记3-14 100个" },
    { text = "getgoods 4155 100", des = "获取神兽印记3-15 100个" },
    { text = "getgoods 4156 100", des = "获取神兽印记3-16 100个" },
    { text = "getgoods 4157 100", des = "获取神兽印记3-17 100个" },
    { text = "getgoods 4158 100", des = "获取神兽印记3-18 100个" },
    { text = "getgoods 4159 100", des = "获取神兽印记3-19 100个" },
    { text = "getgoods 4160 100", des = "获取神兽印记3-20 100个" },
    { text = "getgoods 4161 100", des = "获取神兽印记4-1 100个" },
    { text = "getgoods 4162 100", des = "获取神兽印记4-2 100个" },
    { text = "getgoods 4163 100", des = "获取神兽印记4-3 100个" },
    { text = "getgoods 4164 100", des = "获取神兽印记4-4 100个" },
    { text = "getgoods 4165 100", des = "获取神兽印记4-5 100个" },
    { text = "getgoods 4166 100", des = "获取神兽印记4-6 100个" },
    { text = "getgoods 4167 100", des = "获取神兽印记4-7 100个" },
    { text = "getgoods 4168 100", des = "获取神兽印记4-8 100个" },
    { text = "getgoods 4169 100", des = "获取神兽印记4-9 100个" },
    { text = "getgoods 4170 100", des = "获取神兽印记4-10 100个" },
    { text = "getgoods 4171 100", des = "获取神兽印记4-11 100个" },
    { text = "getgoods 4172 100", des = "获取神兽印记4-12 100个" },
    { text = "getgoods 4173 100", des = "获取神兽印记4-13 100个" },
    { text = "getgoods 4174 100", des = "获取神兽印记4-14 100个" },
    { text = "getgoods 4175 100", des = "获取神兽印记4-15 100个" },
    { text = "getgoods 4176 100", des = "获取神兽印记4-16 100个" },
    { text = "getgoods 4177 100", des = "获取神兽印记4-17 100个" },
    { text = "getgoods 4178 100", des = "获取神兽印记4-18 100个" },
    { text = "getgoods 4179 100", des = "获取神兽印记4-19 100个" },
    { text = "getgoods 4180 100", des = "获取神兽印记4-20 100个" },
    { text = "getgoods 4181 100", des = "获取神兽印记5-1 100个" },
    { text = "getgoods 4182 100", des = "获取神兽印记5-2 100个" },
    { text = "getgoods 4183 100", des = "获取神兽印记5-3 100个" },
    { text = "getgoods 4184 100", des = "获取神兽印记5-4 100个" },
    { text = "getgoods 4185 100", des = "获取神兽印记5-5 100个" },
    { text = "getgoods 4186 100", des = "获取神兽印记5-6 100个" },
    { text = "getgoods 4187 100", des = "获取神兽印记5-7 100个" },
    { text = "getgoods 4188 100", des = "获取神兽印记5-8 100个" },
    { text = "getgoods 4189 100", des = "获取神兽印记5-9 100个" },
    { text = "getgoods 4190 100", des = "获取神兽印记5-10 100个" },
    { text = "getgoods 4191 100", des = "获取神兽印记5-11 100个" },
    { text = "getgoods 4192 100", des = "获取神兽印记5-12 100个" },
    { text = "getgoods 4193 100", des = "获取神兽印记5-13 100个" },
    { text = "getgoods 4194 100", des = "获取神兽印记5-14 100个" },
    { text = "getgoods 4195 100", des = "获取神兽印记5-15 100个" },
    { text = "getgoods 4196 100", des = "获取神兽印记5-16 100个" },
    { text = "getgoods 4197 100", des = "获取神兽印记5-17 100个" },
    { text = "getgoods 4198 100", des = "获取神兽印记5-18 100个" },
    { text = "getgoods 4199 100", des = "获取神兽印记5-19 100个" },
    { text = "getgoods 4200 100", des = "获取神兽印记5-20 100个" },
    { text = "getgoods 4201 100", des = "获取神兽印记6-1 100个" },
    { text = "getgoods 4202 100", des = "获取神兽印记6-2 100个" },
    { text = "getgoods 4203 100", des = "获取神兽印记6-3 100个" },
    { text = "getgoods 4204 100", des = "获取神兽印记6-4 100个" },
    { text = "getgoods 4205 100", des = "获取神兽印记6-5 100个" },
    { text = "getgoods 4206 100", des = "获取神兽印记6-6 100个" },
    { text = "getgoods 4207 100", des = "获取神兽印记6-7 100个" },
    { text = "getgoods 4208 100", des = "获取神兽印记6-8 100个" },
    { text = "getgoods 4209 100", des = "获取神兽印记6-9 100个" },
    { text = "getgoods 4210 100", des = "获取神兽印记6-10 100个" },
    { text = "getgoods 4211 100", des = "获取神兽印记6-11 100个" },
    { text = "getgoods 4212 100", des = "获取神兽印记6-12 100个" },
    { text = "getgoods 4213 100", des = "获取神兽印记6-13 100个" },
    { text = "getgoods 4214 100", des = "获取神兽印记6-14 100个" },
    { text = "getgoods 4215 100", des = "获取神兽印记6-15 100个" },
    { text = "getgoods 4216 100", des = "获取神兽印记6-16 100个" },
    { text = "getgoods 4217 100", des = "获取神兽印记6-17 100个" },
    { text = "getgoods 4218 100", des = "获取神兽印记6-18 100个" },
    { text = "getgoods 4219 100", des = "获取神兽印记6-19 100个" },
    { text = "getgoods 4220 100", des = "获取神兽印记6-20 100个" },
    { text = "getgoods 951 100", des = "获取食人魔宝箱1 100个" },
    { text = "getgoods 952 100", des = "获取食人魔宝箱2 100个" },
    { text = "getgoods 953 100", des = "获取食人魔宝箱3 100个" },
    { text = "getgoods 954 100", des = "获取食人魔宝箱4 100个" },
    { text = "getgoods 955 100", des = "获取食人魔宝箱5 100个" },
    { text = "getgoods 956 100", des = "获取食人魔宝箱6 100个" },
    { text = "getgoods 957 100", des = "获取食人魔宝箱7 100个" },
    { text = "getgoods 958 100", des = "获取食人魔宝箱8 100个" },
    { text = "getgoods 959 100", des = "获取食人魔宝箱9 100个" },
    { text = "getgoods 960 100", des = "获取食人魔宝箱10 100个" },
    { text = "getgoods 961 100", des = "获取食人魔宝箱11 100个" },
    { text = "getgoods 962 100", des = "获取食人魔宝箱12 100个" },
    { text = "getgoods 963 100", des = "获取食人魔宝箱13 100个" },
    { text = "getgoods 964 100", des = "获取食人魔宝箱14 100个" },
    { text = "getgoods 965 100", des = "获取食人魔宝箱15 100个" },
    { text = "getgoods 966 100", des = "获取食人魔宝箱16 100个" },
    { text = "getgoods 967 100", des = "获取食人魔宝箱17 100个" },
    { text = "getgoods 968 100", des = "获取食人魔宝箱18 100个" },
    { text = "getgoods 969 100", des = "获取食人魔宝箱19 100个" },
    { text = "getgoods 970 100", des = "获取食人魔宝箱20 100个" },
}

local heroPropList = {
    -- 英雄id, 英雄等级,英雄阶级,英雄星级, 技能id1, 技能等级1, 技能id2, 技能等级2, 宝石id1, 宝石id2, 宝石id3, 宝石id4, 宝石id5, 宝石id6, 宝石等级1, 宝石等级2, 宝石等级3, 宝石等级4, 宝石等级5, 宝石等级6
    [1] = {
        "20 180 9 8 0 0 0 0 27009 27109 27209 27309 0 0 0 0 0 0 0 0",
        "37 180 9 8 0 0 0 0 27009 27109 27209 27309 0 0 0 0 0 0 0 0",
        "56 180 9 8 0 0 0 0 27009 27109 27209 27309 0 0 0 0 0 0 0 0",
        "78 180 9 8 0 0 0 0 27009 27109 27209 27309 0 0 0 0 0 0 0 0",
        "88 180 9 8 0 0 0 0 27009 27109 27209 27309 0 0 0 0 0 0 0 0",
        "96 180 9 8 0 0 0 0 27009 27109 27209 27309 0 0 0 0 0 0 0 0",
    },
    [2] = {
        "20 240 11 11 0 0 0 0 27011 27111 27211 27311 0 0 0 0 0 0 0 0",
        "37 240 11 11 0 0 0 0 27011 27111 27211 27311 0 0 0 0 0 0 0 0",
        "56 240 11 11 0 0 0 0 27011 27111 27211 27311 0 0 0 0 0 0 0 0",
        "78 240 11 11 0 0 0 0 27011 27111 27211 27311 0 0 0 0 0 0 0 0",
        "88 240 11 11 0 0 0 0 27011 27111 27211 27311 0 0 0 0 0 0 0 0",
        "96 240 11 11 0 0 0 0 27011 27111 27211 27311 0 0 0 0 0 0 0 0",
    },
    [3] = {
        "20 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "37 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "56 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "78 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "88 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "96 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "21 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "52 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "57 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "80 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "98 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "101 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "14 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
    },
    --堕落
    [4] = {
        "3 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "4 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "11 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "12 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "14 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "15 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "16 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "17 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "20 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "95 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "101 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        --"145 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "164 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "165 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
    },
    --超人
    [5] = {
        "21 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "25 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "26 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "28 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "31 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "34 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "35 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "36 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "37 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "39 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "109 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "143 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "160 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "161 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
    },
    --科技
    [6] = {
        "42 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "50 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "52 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "53 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "55 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "56 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "57 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "58 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "59 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "60 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "108 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "142 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        --"147 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "166 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "167 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
    },
    --自然
    [7] = {
        "10 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "65 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "70 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "71 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "73 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "74 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "76 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "78 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "80 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        --"146 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "106 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "116 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "162 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "163 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
    },
    --宇宙神明
    [8] = {
        "82 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "83 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "86 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "88 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "97 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "98 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "156 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "177 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "154 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "171 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "158 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "19 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "89 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "90 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "93 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "96 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "112 180 9 8 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "168 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "85 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
    },
    [9] = {
        "20 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "37 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "56 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "78 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "88 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "96 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "21 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "52 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "57 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "80 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "98 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "101 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "14 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "71 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "37 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "42 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "59 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "60 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "65 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "86 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "88 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "98 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "25 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "73 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "78 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "142 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "10 340 16 15 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
    },
    --三星
    [10] = {
        "1 60 3 3 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "2 60 3 3 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "23 60 3 3 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "40 60 3 3 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "41 60 3 3 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "44 60 3 3 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "62 60 3 3 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "63 60 3 3 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "84 60 3 3 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
        "99 60 3 3 0 0 0 0 27012 27112 27212 27312 0 0 16 16 16 16 0 0",
    },
}

local SelfHistroyList = {}
function GetCommadList(index)
    return CommandTableBase[index]
end

function SetDecorationList()
    -- local decoration_mgr = require "decoration_mgr"
    -- local allDecorationCSV = decoration_mgr.GetAllDecorationCSV()
    -- if allDecorationCSV then
    --     for itemID, v in pairs(allDecorationCSV) do
    --         --if v.colourType == 3 then
    --         local temp = {}
    --         temp.text = string.format("getgoods %d 1", itemID)
    --         temp.des = string.format("获取1个%d饰品", itemID)
    --         -- --print("itemID",itemID,"text",temp.text,"des",temp.des)
    --         table.insert(SuperStoneList, temp)
    --         --end
    --     end
    -- end
end

--- 获取所有符文
---@return table
function GetSigilList()
    local game_scheme = require("game_scheme")
    local count = game_scheme:Sigil_nums()
    local list = {}
    for i = 0, count - 1 do
        local cfg = game_scheme:Sigil(i)
        if cfg then
            local item = {
                text = string.format("getgoods %d 1", cfg.SigilID),
                des = string.format("获取1个%d符文", cfg.SigilID)
            }
            table.insert(list, item)
        end
    end
    return list
end

--- 获取拼年华/南瓜的所有道具
function GetPumnkinItemList()
    local game_scheme = require("game_scheme")
    local cfgActivityExchange = game_scheme:ActivityExchange_0(1)
    local cmdList = {}
    for i = 0, cfgActivityExchange.ctn3.count - 1 do
        local goodsID = cfgActivityExchange.ctn3.data[i]
        local item = {
            text = string.format("getgoods %d 1", goodsID),
            des = string.format("获取1个%d符文", goodsID)
        }
        table.insert(cmdList, item)
    end
    return cmdList
end

function GetSuperStoneList()
    SetDecorationList()
    return SuperStoneList
end

--标签
function GetComandTaggles()
    if util.get_len(SelfHistroyList) == 0 then
        local localHistoryData = PlayerPrefs.GetString("lingshi_history_cmd")
        if localHistoryData and localHistoryData ~= "" then
            SelfHistroyList = json.decode(localHistoryData) or {}
        end
    end
    local toggles = {}
    for key, data in pairs(CommandTableBase) do
        toggles[key] = data.name
    end
    return toggles
end
--获取各类别按钮的名称
function GetComandTaggles1()
    local toggles = {}
    for key, data in pairs(CommandTable1) do
        toggles[key] = data.name
    end
    return toggles
end
function GetComandTaggles2()
    local toggles = {}
    for key, data in pairs(CommandTable2) do
        toggles[key] = data.name
    end
    return toggles
end
function GetComandTaggles3()
    local toggles = {}
    for key, data in pairs(CommandTable3) do
        toggles[key] = data.name
    end
    return toggles
end
function GetComandTaggles4()
    local toggles = {}
    for key, data in pairs(CommandTable4) do
        toggles[key] = data.name
    end
    return toggles
end
function GetComandTaggles5()
    local toggles = {}
    for key, data in pairs(CommandTable5) do
        toggles[key] = data.name
    end
    return toggles
end
function GetComandTaggles6()
    local toggles = {}
    for key, data in pairs(CommandTable6) do
        toggles[key] = data.name
    end
    return toggles
end
--获取各类别按钮的原指数
function GetComandTaggleID1()
    local toggleID = {}
    for key, data in pairs(CommandTable1) do
        toggleID[key] = data.indexbase
    end
    return toggleID
end
function GetComandTaggleID2()
    local toggleID = {}
    for key, data in pairs(CommandTable2) do
        toggleID[key] = data.indexbase
    end
    return toggleID
end
function GetComandTaggleID3()
    local toggleID = {}
    for key, data in pairs(CommandTable3) do
        toggleID[key] = data.indexbase
    end
    return toggleID
end
function GetComandTaggleID4()
    local toggleID = {}
    for key, data in pairs(CommandTable4) do
        toggleID[key] = data.indexbase
    end
    return toggleID
end
function GetComandTaggleID5()
    local toggleID = {}
    for key, data in pairs(CommandTable5) do
        toggleID[key] = data.indexbase
    end
    return toggleID
end
function GetComandTaggleID6()
    local toggleID = {}
    for key, data in pairs(CommandTable6) do
        toggleID[key] = data.indexbase
    end
    return toggleID
end

function GetHeroPropList(index)
    return heroPropList[index]
end

function HasKeyWord(cmdline)
    --2024/12/9 沟通，屏蔽过滤
    return true

    --[[for key, str in pairs(keyWords) do
        if string.find(cmdline, str) then
            return true
        end
    end
    return false]]
end

function SendCommandLine(cmdline)
    local data = util.SplitString(cmdline, " ")
    if data and data[1] == "getgoods" and data[2] and not tonumber(data[2]) then
        local game_scheme = require "game_scheme"
        local lang = require "lang"
        local count = game_scheme:Item_nums()
        local isItem = false
        for i = 0, count - 1 do
            local cfg = game_scheme:Item(i)
            if cfg and lang.Get(cfg.nameKey) == data[2] then
                cmdline = data[1] .. " " .. cfg.ID .. " " .. data[3]
                isItem = true
                break
            end
        end
        if not isItem then
            return
        end
    end

    local msg = goldfinger_pb.TMSG_GOLD_FINGER_NTF()

    msg.commandLine = cmdline
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_GOLD_FINGER_NTF, msg)
end

--设置自动登录开关
-- -1:自动选择是否使用自动登录
-- 0：所有平台不使用自动登录
-- 1：所有平台使用自动登录
-- return true/false ： 是否为自动登录命令，true/false : 自动登录命令使用是否成功
function SetAutoLoginSwitch(cmdline)
    if string.find(cmdline, "setAutoLogin") then
        local strArr = util.SplitString(cmdline, " ")
        if #strArr ~= 2 then
            ----             --print("自动登录开关格式异常")
            return true, false
        end
        local autologinSwitch = tonumber(strArr[2])
        if autologinSwitch ~= -1 and autologinSwitch ~= 0 and autologinSwitch ~= 1 then
            ----             --print("自动登录不支持的开关参数："..autologinSwitch)
            return true, false
        end
        PlayerPrefs.SetInt("autologinSwitch", autologinSwitch)
        PlayerPrefs.Save()
        ----         --print("设置自动登录开关："..autologinSwitch.." 完成")
        return true, true
    end
    return false, false
end



--实验性功能 >>>>>>>> 
function AddHistroyList(cmdLine)
    local has = false
    for k, v in ipairs(SelfHistroyList) do
        if cmdLine == v.text then
            has = true
            break
        end
    end
    if has == false then
        table.insert(SelfHistroyList, { text = cmdLine, isSelf = true, timeStr = string.fmtdate('%m-%d %H:%M:%S', os.server_time()) })
        local str = json.encode(SelfHistroyList)
        PlayerPrefs.SetString("lingshi_history_cmd", str)
    end
end

--获得历史记录
function GetHistroyCommand()
    local newList = {}
    if util.get_len(SelfHistroyList) > 0 then
        newList[1] = { text = "<color=#FF2929>全部清除</color>", isSelf = true, isAll = true }
    end
    local count = util.get_len(SelfHistroyList)
    for i = 1, count do
        newList[count - i + 2] = SelfHistroyList[i]
    end
    return newList
end


--删除历史记录
function DeleteHistroyCommand(cmdLine)
    local newList = {}
    for k, v in ipairs(SelfHistroyList) do
        if cmdLine ~= v.text then
            table.insert(newList, { text = v.text, isSelf = true })
        end
    end
    SelfHistroyList = newList
    local str = json.encode(SelfHistroyList)
    PlayerPrefs.SetString("lingshi_history_cmd", str)
end

function CleanAllHistory()
    local newList = {}
    SelfHistroyList = {}
    local str = json.encode(newList)
    PlayerPrefs.SetString("lingshi_history_cmd", str)
end

local allCommand = {}
function GetSearchCommand(str)
    if util.get_len(allCommand) == 0 then
        for key, tab in pairs(CommandTableBase) do
            for key_2, value in pairs(tab.command) do
                table.insert(allCommand, value)
            end
        end
    end

    local resualt = {}
    for key, value in pairs(allCommand) do
        if string.find(value.text, str) or string.find(value.des, str) then
            table.insert(resualt, value)
        end
    end

    for key, value in pairs(SelfHistroyList) do
        if string.find(value.text, str) then
            table.insert(resualt, value)
        end
    end
    return resualt
end

function EnableLingshiRecharge(value)
    enableLingshiRecharge = value
    local flow_text = require "flow_text"
    flow_text.Add("模拟购买激活：" .. (enableLingshiRecharge and "<color=#00FF00>true</color>" or "<color=#FF0000>false</color>"))
end

function GetLingshiRechargeable()
    return enableLingshiRecharge
end

function EnableRecallMessage(value)
    enableRecallMessage = value
    local flow_text = require "flow_text"
    flow_text.Add("模拟消息撤回：" .. (enableRecallMessage and "<color=#00FF00>true</color>" or "<color=#FF0000>false</color>"))
end

function GetRecallMessageeable()
    return enableRecallMessage
end

function SwitchPropCalModel()
    local const = require "const"
    local lang = require "lang"
    local ui_window_mgr = require "ui_window_mgr"
    local event = require "event"

    local content = ""
    if not const.USE_NEW_HERO_PROP_COUNT then
        content = "切换到<color=#009900>新版</color>属性计算"
    else
        content = "切换回<color=#666600>旧版</color>属性计算"
    end

    local message_box = require "message_box"
    message_box.Open(
            string.format("%s\n需要重新登录，是否确定？", content), message_box.STYLE_YESNO,
            function(data, nRet)
                if message_box.RESULT_YES == nRet then
                    --返回登录
                    const.USE_NEW_HERO_PROP_COUNT = not const.USE_NEW_HERO_PROP_COUNT
                    event.Trigger(event.RETURN_LOGIN)
                    ui_window_mgr:UnloadModule("ui_ling_shi_new")
                    local login_module = require "net_login_module"
                    login_module.ReturnLogin()
                    local flow_text = require "flow_text"
                    flow_text.Add(content)
                end
            end,
            0,
            lang.KEY_OK,
            lang.KEY_CANCEL,
            "切换计算模块"
    )
end

function EnableSendMultiMsg(value)
    enableSendMultiMsg = value
    local flow_text = require "flow_text"
    flow_text.Add("模拟多发消息激活：" .. (enableSendMultiMsg and "<color=#00FF00>true</color>" or "<color=#FF0000>false</color>"))
end

function GetSendMultiMsg()
    return enableSendMultiMsg
end

function EnableSkipClientMsgLimit(value)
    enableSkipClientMsgLimit = value
    local flow_text = require "flow_text"
    flow_text.Add("发送消息前跳过客户端限制激活：" .. (enableSkipClientMsgLimit and "<color=#00FF00>true</color>" or "<color=#FF0000>false</color>"))
end

function GetSkipClientMsgLimit()
    return enableSkipClientMsgLimit
end

local testQuickFightNum = 0
local curQuickLevel = 0
function SetQuickFightData(quickNum, curQuickLv)
    testQuickFightNum = quickNum
    curQuickLevel = curQuickLv
end

function GetCurQuickLevel()
    return curQuickLevel
end

function GetCurQuickData()
    return testQuickFightNum > 0
    -- body
end

function SetQuickFighTimesData()
    testQuickFightNum = testQuickFightNum - 1

end

function EnableFlagExternScreen(value)
    enableFlagExternScreen = value
    local flow_text = require "flow_text"
    flow_text.Add("刘海状态已更新,重新打开UI看")
end

function GetEnableFlagExternScreen()
    return enableFlagExternScreen
end

function EnableChangeLangTip(value)
    enableChangeLangTip = value
    local ui_window_mgr = require "ui_window_mgr"
    if value then
        ui_window_mgr:ShowModule("ui_lang_test")
    else
        ui_window_mgr:UnloadModule("ui_lang_test")
    end
    local flow_text = require "flow_text"
    flow_text.Add("切换语言开关激活：" .. (enableChangeLangTip and "<color=#00FF00>true</color>" or "<color=#FF0000>false</color>"))
end

function GetEnableChangeLangTip()
    if enableChangeLangTip then
        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:ShowModule("ui_lang_test")
    end
    return enableChangeLangTip
end

local RecordAssetBudleDict = {}
function GetAllRecordAB()
    return RecordAssetBudleDict
end
function GeRecordABList()
    local list = {}
    for i, v in ipairs(RecordAssetBudleDict) do
        if v ~= nil then
            table.insert(list, v)
        end
    end
    return list
end
function SetRecordAB(timestamp, recordTable)
    table.insert(RecordAssetBudleDict, {
        timestamp = timestamp,
        recordTable = recordTable
    })
end
function GetRecordAB(timestamp)
    for k, v in pairs(RecordAssetBudleDict) do
        if v.timestamp == timestamp then
            return v
        end
    end
    return nil
end
function CleanSingleRecordAB(timestamp)
    for k, v in pairs(RecordAssetBudleDict) do
        if v.timestamp == timestamp then
            RecordAssetBudleDict[k] = nil
            table.remove(RecordAssetBudleDict, k)
            break
        end
    end
end

function CheckIsToClient(str)
    if not str then
        return
    end
    if string.find(str, CmdToClient) then
        return true
    end
end

function CleanAllRecordAB()
    RecordAssetBudleDict = {}
end

function EnablePrintID(value)
    enablePrintID = value
end

function GetEnablePrintID()
    return enablePrintID
end