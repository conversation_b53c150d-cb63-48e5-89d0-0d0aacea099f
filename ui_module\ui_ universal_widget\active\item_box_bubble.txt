local require = require
local type = type
local pairs = pairs
local cc = cc
local newclass = newclass
local ipairs = ipairs
local item_data = require "item_data"
local iui_item_detail = require "iui_item_detail"
local goods_item_new = require "goods_item_new"
local card_sprite_asset = require "card_sprite_asset"
local log = require "log"
local util = require "util"
local util_widget_table = require "util_widget_table"
local binding = require "item_box_bubble_binding"
local LeanTween             = CS.LeanTween
module("item_box_bubble")
local itemView = newclass("item_land_task")
itemView.widget_table = binding.WidgetTable

function itemView:ctor()
    self.isLoaded = false
    self.gameObject = nil
    self.transform = nil
    cc(self):addComponent("base_ui_util"):exportMethods()
end

function itemView:Init(prefab)
    if prefab then
        util_widget_table.BindWidgetTableItem(self, prefab, self.widget_table)
        self.isLoaded = true
    else
        log.Error("ui_battlePass_info Init gameObject is nil")
    end
    local originScale = {x=0,y=0,z=0}
    self.rtf_rewardParent.localScale = originScale
end

function itemView:UpdateData(data)
    self.goodsItemArr = self.goodsItemArr or {}
    for i, v in ipairs(data) do
        self.goodsItemArr[i] = self.goodsItemArr[i] or goods_item_new.CGoodsItem():Init(self.rtf_rewardParent.transform, nil, 0.74)
        self.goodsItemArr[i]:SetGoods(nil, v.id, v.num, true)
        self.goodsItemArr[i]:SetCountEnable(true)
    end
    
    self.bubbleScaleTween = LeanTween.scale(self.rtf_rewardParent, {x=1,y=1,z=1}, 0.3)
end

function itemView:PlayAnim(isPlay)
    self.ator_rewardParent.enabled = isPlay
end


function itemView:Dispose()
    util_widget_table.DisposeWidgetTableManageEvent(self, self.widget_table)
    self.isLoaded = false
    self.gameObject = nil
    self.transform = nil
    if self.IData then
        for i, v in pairs(self.IData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.goodsItemArr then
        for i, v in pairs(self.goodsItemArr) do
            v:Dispose()
        end
        self.goodsItemArr = nil
    end

    if self.bubbleScaleTween then
        LeanTween.cancel(self.bubbleScaleTween.uniqueId)
    end
    
    self.IData = nil
end

--region Item Logic

--endregion
function NewItem(prefab)
    local item = itemView.new()
    item:Init(prefab)
    return item;
end
--return itemView
