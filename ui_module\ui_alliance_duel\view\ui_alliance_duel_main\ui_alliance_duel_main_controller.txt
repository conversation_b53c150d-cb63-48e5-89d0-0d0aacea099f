local require = require
local pairs = pairs     
local ipairs = ipairs
local table = table
local newClass = newclass
local type = type
local log = require "log"
local activity_preview_mgr = require "activity_preview_mgr"
local activity_preview_cfg = require "activity_preview_cfg"
local game_scheme = require "game_scheme"
local technology_data = require "technology_data"
local alliance_duel_const = require "alliance_duel_const"
local net_allianceDuel_module = require "net_allianceDuel_module"
local alliance_duel_data = require "alliance_duel_data"
local event_allianceDuel_define = require "event_allianceDuel_define"
local event         = require "event"
local controller_base = require "controller_base"
local game 			= require "game"
local ui_window_mgr = require "ui_window_mgr"
local alliance_duel_mgr = require "alliance_duel_mgr"
--region Controller Life
module("ui_alliance_duel_main_controller")
local controller = nil
local UIController = newClass("ui_alliance_duel_main_controller", controller_base)
local listPage = {
    --今日主题
    "ui_alliance_duel_theme",
    --对决战况
    "ui_duel_situation",
    --同盟突袭
    "ui_alliance_duel_raid",
    --预告界面
    "ui_alliance_duel_preview",
    --对决联赛
    "ui_alliance_matchmaking_league",
}
local curIndex = 1
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    self:InitControllerMethod()
end

function UIController:InitControllerMethod()
    --region 预告特殊处理
    --获取是否预告

    local showMatch = alliance_duel_data.GetDuelRoundID() or 0
    self:TriggerUIEvent("ShowMatchPanel",showMatch ~= 0)

    if showMatch ~= 0 then
        alliance_duel_mgr.GetBattleInfo() --打开界面时获取一次数据（因为存在不打开对决联赛主界面就打开奖励信息页的情况
        alliance_duel_mgr.GetMyRankScore() --同上
    end

    local isPreview = activity_preview_mgr.IsJudgePreview(activity_preview_cfg.ActivityPreviewID.AllianceDuel)
    if isPreview then
        self:OnTogPreviewValueChange(true)
        self:TriggerUIEvent("OnChangeTab",alliance_duel_const.Duel_PanelIndex.Preview)
        self:TriggerUIEvent("ShowPreviewPanel",true)
        self:TriggerUIEvent("SetListPages",listPage,false,0)
        return
    else
        self:TriggerUIEvent("ShowPreviewPanel",false)
    end
    --endregion

    net_allianceDuel_module.MSG_ALLIANCEDUEL_INFO_REQ()

    alliance_duel_data.RefreshScienceData()
    --获取是否展示联盟突袭
    local data = alliance_duel_data.GetDuelSituationViewData()
    --if not data then
    --    ui_window_mgr:UnloadModule("ui_alliance_duel_main")
    --    return
    --end
    local progress = alliance_duel_data.OnGetSciencePoint() or 0

    local openRaid = data and data.attend and not data.isBye
    if curIndex == alliance_duel_const.Duel_PanelIndex.Raid and not openRaid then
        curIndex = alliance_duel_const.Duel_PanelIndex.Theme
    end
    self:TriggerUIEvent( "SetListPages",listPage,openRaid,progress)
    local themeId = alliance_duel_data.GetThemeDuelID() --主题为0表示是周日，此时强制打开对决联赛
    if themeId == 0 and showMatch ~= 0 then
        curIndex = alliance_duel_const.Duel_PanelIndex.Matchmaking
        self:TriggerUIEvent("OnSetWeekendPanel")
    end
    if curIndex == alliance_duel_const.Duel_PanelIndex.Matchmaking then
        local alliance_data = require("alliance_data")
        local userAllianceData = alliance_data.GetUserAllianceData()
        if not userAllianceData or userAllianceData.allianceId == nil or userAllianceData.allianceId == 0  then
            curIndex = alliance_duel_const.Duel_PanelIndex.Theme --没有联盟，然后上一次记录的是对决联赛时，强制显示主题
        end
    end
    --记录当前打开面板，全屏界面返回时重新打开
    self.curPanelIndex = alliance_duel_const.Duel_PanelIndex.None
    self:OnInitBeginPage()
end

function UIController:OnInitBeginPage()
    if curIndex == alliance_duel_const.Duel_PanelIndex.Theme then
        self:OnTogThemeValueChange(true)
    elseif curIndex == alliance_duel_const.Duel_PanelIndex.Situation then
        self:OnTogWar_stateValueChange(true)
    elseif curIndex == alliance_duel_const.Duel_PanelIndex.Raid then
        self:OnTogRaidValueChange(true)
    elseif curIndex == alliance_duel_const.Duel_PanelIndex.Matchmaking then
        self:OnTogMatchValueChange(true)
    else
        self:OnTogThemeValueChange(true)
    end
    self:TriggerUIEvent("OnChangeTab",curIndex)
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    curIndex = self.curPanelIndex
    self.viewWnd = nil
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil

    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents()
    self.MSG_ALLIANCE_DUEL_INFO_RSP = function()
        local data = alliance_duel_data.GetDuelSituationViewData()
        local progress = alliance_duel_data.OnGetSciencePoint()
        local themeId = alliance_duel_data.GetThemeDuelID() --主题为0表示是周日，此时强制打开对决联赛
        self:TriggerUIEvent("SetListPages",listPage,data and data.attend and not data.isBye and themeId ~= 0,progress)
    end
    self:RegisterEvent(event_allianceDuel_define.TMSG_ALLIANCEDUEL_INFO_RSP, self.MSG_ALLIANCE_DUEL_INFO_RSP)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnTogThemeValueChange(state)
    self.viewWnd = self.viewWnd or  ui_window_mgr:GetWindowObj(self.view_name)
    if state then
        self.curPanelIndex = alliance_duel_const.Duel_PanelIndex.Theme
        ui_window_mgr:ShowModule("ui_alliance_duel_theme", nil, nil, {uiParent = self.viewWnd.rtf_pageRoot})
        self:TriggerUIEvent("OnToggleChange",1)
    else
        ui_window_mgr:UnloadModule("ui_alliance_duel_theme")
    end
end

--点击对决战况分页
function  UIController:OnTogWar_stateValueChange(state)
    --获取view wnd
    self.viewWnd = self.viewWnd or ui_window_mgr:GetWindowObj(self.view_name)
    if state then
        self.curPanelIndex = alliance_duel_const.Duel_PanelIndex.Situation
        local data = alliance_duel_data.GetDuelSituationViewData()
        data.uiParent = self.viewWnd.rtf_pageRoot
        ui_window_mgr:ShowModule("ui_duel_situation", nil, nil, data)
        self:TriggerUIEvent("OnToggleChange",2)
    else
        ui_window_mgr:UnloadModule("ui_duel_situation")
    end
end

function UIController:OnTogMatchValueChange(state)
    self.viewWnd = self.viewWnd or  ui_window_mgr:GetWindowObj(self.view_name)
    if state then
        self.curPanelIndex = alliance_duel_const.Duel_PanelIndex.Matchmaking
        ui_window_mgr:ShowModule("ui_alliance_matchmaking_league", nil, nil, {uiParent = self.viewWnd.rtf_pageRoot})
        self:TriggerUIEvent("OnToggleChange",5)
    else
        ui_window_mgr:UnloadModule("ui_alliance_matchmaking_league")
    end
end

function  UIController:OnTogRaidValueChange(state)
    self.viewWnd = self.viewWnd or  ui_window_mgr:GetWindowObj(self.view_name)
    if state then
        self.curPanelIndex = alliance_duel_const.Duel_PanelIndex.Raid
        ui_window_mgr:ShowModule("ui_alliance_duel_raid", nil, nil, {uiParent = self.viewWnd.rtf_pageRoot})
        self:TriggerUIEvent("OnToggleChange",3)
    else
        ui_window_mgr:UnloadModule("ui_alliance_duel_raid")
    end
end
function  UIController:OnTogLeagueValueChange(state)
end

---预告界面
function UIController:OnTogPreviewValueChange(state)
    self.viewWnd = self.viewWnd or ui_window_mgr:GetWindowObj(self.view_name)
    if state then
        ui_window_mgr:ShowModule("ui_alliance_duel_preview", nil, nil, {uiParent = self.viewWnd.rtf_pageRoot})
    else
        ui_window_mgr:UnloadModule("ui_alliance_duel_preview")
    end
end

---点击攻略按钮
function  UIController:OnBtnStrategyClickedProxy()
    alliance_duel_mgr.SetClickExplain()
    
    local ui_duel_explain_window = require "ui_duel_explain_window"
    ui_duel_explain_window.ShowExplainWindow(10041)
    event.EventReport("AllianceDuel_Enter_Strategy", {})
end

--点击科技提升tips
function  UIController:OnBtnProgressClickedProxy()
    --这里获取数据
    local tipsID = self:GetCurShowTechnologyID()
    
    ui_window_mgr:ShowModule("ui_duel_science_tips",nil,nil,{ tipsID = tipsID })
    event.EventReport("AllianceDuel_Click_Technology", {})
end

--点击排行奖励
function  UIController:OnBtnReward_rankClickedProxy()
    if self.curPanelIndex == alliance_duel_const.Duel_PanelIndex.Theme then
        self:OnTogThemeValueChange(false)
        ui_window_mgr:ShowModule("ui_duel_rank_reward_list",nil,function()  
            self:OnTogThemeValueChange(true)
        end)
    else
        ui_window_mgr:ShowModule("ui_duel_rank_reward_list")
    end
    event.EventReport("AllianceDuel_Enter_RankingReward", {})
end

--点击积分排名
function  UIController:OnBtnPoint_rankClickedProxy()
    if self.curPanelIndex == alliance_duel_const.Duel_PanelIndex.Theme then
        self:OnTogThemeValueChange(false)
        ui_window_mgr:ShowModule("ui_duel_points_rank_list",nil,function()
            self:OnTogThemeValueChange(true)
        end)
    else
        ui_window_mgr:ShowModule("ui_duel_points_rank_list")
    end
end

function  UIController:OnBtnJump_tecClickedProxy()
end


function UIController:OnBtnCloseClickedProxy()
    ui_window_mgr:UnloadModule("ui_alliance_duel_main")
end

---@public 获取要显示的科技ID
function UIController:GetCurShowTechnologyID()
    --先判断解锁没，return 0
    local scienceData = technology_data.GetScientificGroupByIndex(9)
    if not scienceData.isLock then
        local scienceArr = {}
        --在判断科技解锁到哪
        local length = game_scheme:AllianceDuelChest_nums()
        for i = 1, length - 1 do
            local cfg = game_scheme:AllianceDuelChest(i)
            if cfg.UnlockCondition ~= 0 then
                table.insert(scienceArr,cfg.UnlockCondition)
            end
        end
        --local scienceLength = #scienceArr
        for i, v in ipairs(scienceArr) do
            local id = scienceArr[i]
            local level = technology_data.GetScientificLevelByIndex(id)
            if not level or level <= 0 then
                --返回的是科技ID
                return v
            end
        end
    end
    return 0
end


--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end

--切换账号
function OnStateEnter(eventName,curState)
    if curState == game.STATE_LOGIN_TYPE then
        curIndex = 1
    end
end
event.Register(event.STATE_ENTER, OnStateEnter)
--endregion
