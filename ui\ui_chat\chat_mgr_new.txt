-- chat_mgr_new.txt ------------------------------------------
-- author:  刘志远
-- date:    2019.10.14
-- ver:     1.0
-- desc:    聊天数据模块 新
--------------------------------------------------------------
local string = string
local print = print
local require = require
local table = table
local ipairs = ipairs
local pairs = pairs
local dump = dump
local os = os
local tonumber = tonumber
local tostring = tostring
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
-- local Debug = CS.UnityEngine.Debug
local string_util = require "string_util"
local lang = require "lang"
local util = require "util"

local mq_common_pb = require "mq_common_pb"
local event = require "event"
local player_mgr = require "player_mgr"
local chat_pb = require "chat_pb"
local chat_io_mgr = require "chat_io_mgr"
local game_scheme = require "game_scheme"
local log = require "log"
local chat_monitor_mgr = require "chat_monitor_mgr"
local ReviewingUtil = require "ReviewingUtil"
local data_personalInfo = require "data_personalInfo"
local custom_avatar_data = require "custom_avatar_data"
local ui_setting_data = require "ui_setting_data"
local time_util = require "time_util"
local math = math
local flow_text = require "flow_text"

module("chat_mgr_new")

ENUM_CHANNEL = { LANG = 1, WORLD = 2, GUIDE = 3, TERRITORY = 4, PRIVATE = 5, VIP = 6, RECRUIT = 8, ALLRECRUIT = 9, SLG = 10, PEEKGAME = 11, R4R5 = 12, } --VIP仅用于屏蔽控制
enum_pState = { lang = 1, world = 2, guide = 3, territory = 4, privateList = 5, privateView = 6, recruit = 8, slg = 9, peekGame = 10, allianceNotice = 11, r4r5 = 12, } --页面状态  1语言频道 2本服 3联盟 4领地 5私聊列表 6私聊窗口 8联盟招聘 9联盟星战战场 10巅峰赛 11联盟公告 12联盟R4R5
pStateLinkCHANNEL = {
    [enum_pState.lang] = ENUM_CHANNEL.LANG,
    [enum_pState.world] = ENUM_CHANNEL.WORLD,
    [enum_pState.guide] = ENUM_CHANNEL.GUIDE,
    [enum_pState.territory] = ENUM_CHANNEL.TERRITORY,
    [enum_pState.privateList] = ENUM_CHANNEL.PRIVATE,
    [enum_pState.privateView] = ENUM_CHANNEL.PRIVATE,
    [enum_pState.recruit] = ENUM_CHANNEL.RECRUIT,
    [enum_pState.slg] = ENUM_CHANNEL.SLG,
    [enum_pState.peekGame] = ENUM_CHANNEL.PEEKGAME,
    [enum_pState.allianceNotice] =ENUM_CHANNEL.GUIDE,
    [enum_pState.r4r5] = ENUM_CHANNEL.R4R5,
}
ENUM_PRIVATE_CHAT_UPDATE_TYPE={
    None = 0,
    All = 1 ,
    Single = 2,
}
--翻译类型枚举
ENUM_TRANSLATION_TYPE = {
    ALLIANCE = "IsAutoTranslation", --联盟
    WORLD = "IsAutoTranslation_World"--世界
}
--聊天页签数据配置
E_pStateDataMap = {
    [enum_pState.guide] = {
        e_translateType = { ENUM_TRANSLATION_TYPE.ALLIANCE },
    },
    [enum_pState.allianceNotice] = {
        e_translateType = { ENUM_TRANSLATION_TYPE.ALLIANCE },
    },
    [enum_pState.r4r5] = {
        e_translateType = { ENUM_TRANSLATION_TYPE.ALLIANCE },
    },
    [enum_pState.world] = {
        e_translateType = { ENUM_TRANSLATION_TYPE.WORLD },
        --limitFunc = {
        --    function()
        --        local privilege_data = require "privilege_data"
        --        local privilege_define = require "privilege_define"
        --        return privilege_data.GetPrivilegeIsOpenByID(privilege_define.MONTH_CHAT_TRANSLATE)
        --    end,
        --}, --额外限制函数
    }
}

ViewModeEnum = {
    Mode1 = 1,--1.浏览消息
    Mode2 = 2,--2.文本输入(隐藏频道tog、适应键盘高度)
    Mode3 = 3,--3.表情输入(隐藏频道tog、显示表情选择面板) 
    Mode4 = 4,--4.+号扩展面板
}

local BlockPlayer = {}      --屏蔽玩家
local BlockChannel = 0x00   --屏蔽通道
local bHideVIP = nil        --屏蔽vip
local bAllRecruit = nil --显示所有语言招募
local endTime = 0           --被禁言结束时间
local reason = ""          --禁言原因

local isShowChatTimeMap = {} --是否显示时间戳

local worldMsg = {}     --世界消息
local recruitMsg = {}     --联盟邀请消息
local guildMsg = {}     --公会消息
local territoryMsg = {} --领地消息
local langMsg = {}      --语言频道消息
local slgMsg = {}       --战场消息
local peekGameMsg = {}       --巅峰赛消息
local allianceR4R5Msg = {} --联盟R4R5数据
local tempMsgs = {}     --临时显示的消息映射，等待后台回复，回复的是临时消息的id则替换掉临时消息

local urgentAnnounceNum = 0 --今日紧急公告次数
local urgentAnnounceTime = 0 --上次发布紧急公告时间



local privateMsg = {}   --私聊消息 key是聊天对象的id，value是与该对象的聊天消息集
local sessionProp = {}  --会话属性 key是聊天对象的id，value是{newNum=2,isTop=false}
local isInitPri = false
local isWaitForGuideData = false--等待联盟数据

--因为多处会获取聊或修改天界面的状态，顾把ui的状态数据放到本模块管理
local pageState = enum_pState.world
--@curSessionData = {sessionId=xx,toWorldid=xx,toTitleName=xx,toName=xx } --用于打开私聊窗口
local curSessionData = {}
local isClose = true

local todayGetRewardTimes = nil
local curWeekHelpTimes = nil
local nextReqTimes = nil
local todayHelpTimes = nil
local flagID = nil
local reqTimes = nil
local helpInfo = {}

local redEnvelopeInfos = {} --所有红包信息,key是红包id，value是RedPacketInfo

local chatBlockTypeTbl = nil --屏蔽类型 1屏蔽玩家 2屏蔽频道 3屏蔽玩家和频道

local at_playerID = {} -- 被@玩家的数据
--local massTeamDeleteShareInfo = {}     --所有集结队伍信息，key是集结队伍id，value是MassTeamInfo

--channel红点属性
local channelDot = {
    [ENUM_CHANNEL.LANG] = 0,
    [ENUM_CHANNEL.WORLD] = 0,
    [ENUM_CHANNEL.GUIDE] = 0,
    [ENUM_CHANNEL.TERRITORY] = 0,
    [ENUM_CHANNEL.PRIVATE] = 0,
    [ENUM_CHANNEL.RECRUIT] = 0,
    [ENUM_CHANNEL.SLG] = 0,
    [ENUM_CHANNEL.PEEKGAME] = 0,
    [ENUM_CHANNEL.R4R5] = 0,
}

--频道艾特状态 @new是否未读  @pos消息的index
local channelAtState = {
    [ENUM_CHANNEL.WORLD] = { new = false, pos = 0 },
    [ENUM_CHANNEL.GUIDE] = { new = false, pos = 0 },
    [ENUM_CHANNEL.TERRITORY] = { new = false, pos = 0 },
    [ENUM_CHANNEL.LANG] = { new = false, pos = 0 },
    [ENUM_CHANNEL.RECRUIT] = { new = false, pos = 0 },
    [ENUM_CHANNEL.SLG] = { new = false, pos = 0 },
    [ENUM_CHANNEL.PEEKGAME] = { new = false, pos = 0 },
    [ENUM_CHANNEL.R4R5] = { new = false, pos = 0 },
}

--当前语言频道info(GW为了兼容老代码)
local curLangChanneInfo = {
    id = chat_pb.Channel_Language,
    channelName = 670033,
}

--语言频道配置 key是频道id value是langChanneInfo
local langChannelConfig = {}

--翻译结果缓存
local translationTable = {}

--GM相关
GMConstRoldId = -100 --伪GM roldid，固定不变，方便gm消息的合并保存
GMLastRoldId = 0 --最新一条gm消息的roldId
local GMTalkOnline = false --gm是否在线
local mgMsgNum = 0 --GM聊天数量


local isAutoTranslation = {} --是否开启自动翻译功能（联盟模块）
local isReadLocalAutoTranslation = {} --是否已读取本地自动翻译设置

local isReceiveRedEnvelopeInfo = false --是否接收到红包信息列表
--local isReceiveMassTeamInfo = false --是否接收到集结队伍信息列表

--///data<==>ui----------------------------------------------------------------
--[[ 获取聊天消息
    @channel ENUM_CHANNEL枚举值
    @toRoldID 私聊对象id 如果是获取私聊的话 要传
]]
function GetMsgByType(channel, toRoldID)
    if channel == ENUM_CHANNEL.LANG then
        return UpdateBlockPlayer(langMsg)
    elseif channel == ENUM_CHANNEL.WORLD then
        return UpdateBlockPlayer(worldMsg)
    elseif channel == ENUM_CHANNEL.GUIDE then
        local helpGuild = {}
        local delIndex = {}

        for i = #guildMsg, 1, -1 do
            local guideMsgInfo = guildMsg[i]
            if guideMsgInfo.sType == mq_common_pb.enSpeak_LeagueMutualHelp then
                local targetTable = helpGuild
                if targetTable[guildMsg[i].roleid] and targetTable[guildMsg[i].roleid] == guildMsg[i].leagueHelp.flagID then
                    delIndex[i] = true
                else
                    targetTable[guildMsg[i].roleid] = guildMsg[i].leagueHelp.flagID
                end

                if not delIndex[i] then
                    if not helpInfo[guideMsgInfo.roleid] then
                        delIndex[i] = true
                    else
                        local cfg_help = game_scheme:LeagueHelp_0(helpInfo[guideMsgInfo.roleid].helpID)
                        if helpInfo[guideMsgInfo.roleid].flagID ~= guideMsgInfo.leagueHelp.flagID or (cfg_help and helpInfo[guideMsgInfo.roleid].prog >= cfg_help.helpNum) then
                            delIndex[i] = true
                        else
                            guideMsgInfo.leagueHelp.process = helpInfo[guideMsgInfo.roleid].prog
                        end
                    end
                end
            elseif guideMsgInfo.sType == mq_common_pb.enSpeak_ChineseRedPacket then
                if guideMsgInfo.redPacketInfo and isReceiveRedEnvelopeInfo then
                    local info = redEnvelopeInfos[guideMsgInfo.redPacketInfo.redPacketId]
                    if not info then
                        --数据以redEnvelopeInfo为准，没有的删除
                        delIndex[i] = true
                    else
                        if os.server_time() > info.expireTime then
                            --过期的删除
                            delIndex[i] = true
                        end
                    end
                else
                    --消息类型是春节红包但是没有红包信息，移除
                    delIndex[i] = true
                end
                --[[            elseif guideMsgInfo.sType == mq_common_pb.enSpeak_SandboxMass then
                                if isReceiveMassTeamInfo then
                                    if massTeamDeleteShareInfo[guideMsgInfo.sandboxmassData.massTeamId] then
                                        --移除
                                        delIndex[i] = true
                                    end
                                end]]
            end
        end

        local delFlag = false
        for k, v in pairs(delIndex) do
            delFlag = true
            break
        end

        if delFlag then
            local msg = {}
            local num = 0

            for i = 1, #guildMsg do
                if not delIndex[i] then
                    num = num + 1
                    msg[num] = guildMsg[i]
                end
            end
            guildMsg = msg
        end
        return UpdateBlockPlayer(guildMsg) 
    elseif channel == ENUM_CHANNEL.TERRITORY then
        return UpdateBlockPlayer(territoryMsg)  
    elseif channel == ENUM_CHANNEL.PRIVATE then
        LocalInitPri()
        if toRoldID then
            return UpdateBlockPlayer(privateMsg[tonumber(toRoldID)],ENUM_CHANNEL.PRIVATE,ENUM_PRIVATE_CHAT_UPDATE_TYPE.Single) 
        else
            return UpdateBlockPlayer(privateMsg,ENUM_CHANNEL.PRIVATE,ENUM_PRIVATE_CHAT_UPDATE_TYPE.All) 
        end
    elseif channel == ENUM_CHANNEL.RECRUIT then
        return UpdateBlockPlayer(recruitMsg) 
    elseif channel == ENUM_CHANNEL.PEEKGAME then
        return UpdateBlockPlayer(peekGameMsg) 
    elseif channel == ENUM_CHANNEL.R4R5 then
        return UpdateBlockPlayer(allianceR4R5Msg) 
    end
end

function UpdateBlockPlayer(data,channel,UpdateType)
    if not data then 
        return data
    end 
    local resData = {}
    if channel == ENUM_CHANNEL.PRIVATE then 
        if UpdateType == ENUM_PRIVATE_CHAT_UPDATE_TYPE.All then 
            for i,v in pairs(data) do 
                resData[i] = {}
                for index,value in ipairs(v) do 
                    if not IsPlayerChatTypeMsgBlocked(value.sType,value.roleid) then 
                        table.insert(resData[i],value)
                    end
                end
            end
        else
            for i,v in ipairs(data) do 
                if not IsPlayerChatTypeMsgBlocked(v.sType,v.roleid) then 
                    table.insert(resData,v)
                end
            end
        end
    else
        for i,v in ipairs(data) do 
            if not IsPlayerChatTypeMsgBlocked(v.sType,v.roleid) then 
                table.insert(resData,v)
            end
        end
    end
    return resData
end

function GetCfgChatTypeIsBlocked(channel)
    if chatBlockTypeTbl==nil then
		local chatCfg = game_scheme:InitBattleProp_0(1019)
        chatBlockTypeTbl = {}
        if chatCfg and chatCfg.szParam and chatCfg.szParam.data then
            for i = 0, #chatCfg.szParam.data do
                local chatType = chatCfg.szParam.data[i]
                local num = tonumber(chatType)
                if num then
                    chatBlockTypeTbl[num] = true
                    log.Warning("GetCfgChatTypeIsBlocked chatType Blocked:",num)
                else
                    log.Error("GetCfgChatTypeIsBlocked: szParam["..i.."] is not a valid number:", chatType)
                end
            end
        end
    end
    -- log.Warning("GetCfgChatTypeIsBlocked 666:",channel,chatBlockTypeTbl[channel]==true)
    return chatBlockTypeTbl[channel] == true
end

function GetPrivateMsg()
    return privateMsg
end
--会话属性
function GetSessionProp()
    LocalInitPri()
    return sessionProp
end

function GetMgMsgNum()
    return mgMsgNum
end

function SetMGMsgRead()
    for _, sProp in pairs(sessionProp) do
        if sProp.isTop then
            sProp.newNum = 0
        end
    end
    --dump(sessionProp)
    UpdateMGMsgNum()
    TriggerChannelDotEvent()
end

--获取通道栏红点属性
function GetChannelDotProp()
    return channelDot
end

--获取当前语言频道id, 全部info
function GetCurLangChannelId()
    return curLangChanneInfo and curLangChanneInfo.id, curLangChanneInfo
    --return 10001
end

--获取语言频道配置
function GetLangChannelConfig()
    return langChannelConfig

    -- return {
    --     {channelName="简体中文-频道1",languageId=1,maxNum=4000,onlineNum=1000,createTime=1571139726,id=10000},
    --     {channelName="简体中文-频道2",languageId=1,maxNum=4000,onlineNum=2000,createTime=1571139726,id=10001},
    --     {channelName="简体中文-频道3",languageId=1,maxNum=4000,onlineNum=3000,createTime=1571139726,id=10002},
    -- }
end
function GetChannelDataById(id)
    for k, v in pairs(langChannelConfig) do
        if v.id == id then
            return v
        end
    end
end

--通知聊天消息更新 data->ui @channelType通道类型 @singleMsg如果是更新单条消息需要传 @isHelp联盟互助消息更新
function TriggerChatEvent(channelType, singleMsg, isHelp)
    ------ --print("TriggerChatEvent>>>>>>>>>>>>>>>>>>>>>",channelType,singleMsg)
    event.Trigger(event.UPDATE_CHAT_MSG, channelType, singleMsg, isHelp)
end
--通知私聊消息更新 data->ui @sessionId会话id @singleMsg如果是更新单条消息需要传
function TriggerPrivateChatEvent(sessionId, singleMsg)
    event.Trigger(event.UPDATE_PRIVATE_CHAT_MSG, sessionId, singleMsg)
end
--通知会话属性更新 data->ui
function TriggerSessionPropEvent()
    event.Trigger(event.UPDATE_SESSION_PROP)
end
--通知通道栏红点更新 data->ui
function TriggerChannelDotEvent()
    ------ --print("TriggerChannelDotEvent>>>>>>>>>>>>>>>>>>>>>")
    event.Trigger(event.UPDATE_CHANNEL_DOT)
end
--当前语言频道改变
function TriggerCurChannelChange()
    event.Trigger(event.UPDATE_CUR_CHANNEL)
end
--频道配置改变
function TriggerChannelConfigChange()
    event.Trigger(event.UPDATE_CHANNEL_CONFIG)
end
--页面状态发生变化
function TriggerPageStateChange()
    event.Trigger(event.UPDATE_CHAT_PAGE_CHANGE)
end

--刷新当前页面（用于撤回）
function TiggerPageView(tMsg)
    event.Trigger(event.UPDATE_CHAT_VIEW, tMsg)
end
--@状态发生变化
function TriggerAtStateChange()
    event.Trigger(event.AT_STATE_CHANGE)
end

function UpdateMGMsgNum()
    mgMsgNum = 0
    for roleId, sProp in pairs(sessionProp) do
        if tonumber(roleId) == tonumber(GMConstRoldId) and sProp.isTop then
            mgMsgNum = mgMsgNum + sProp.newNum
        end
    end
    -- mgMsgNum = 1
end
--通知某消息已读 ui->data @sessionId如果是私聊需要传
function NtfMsgReaded(channelType, sessionId)
    ------ --print("NtfMsgReaded>>>>>>>",channelType,sessionId)
    -- body
    if channelType ~= ENUM_CHANNEL.PRIVATE then
        channelAtState[channelType].new = false --这里at状态更新并不需要通知ui，因为标签的红点提示以红点消息为准
        channelDot[channelType] = 0
        TriggerChannelDotEvent()
        chat_io_mgr.SetChannelReadTime(channelType)
    else
        if sessionId and sessionProp[tostring(sessionId)] then
            sessionProp[tostring(sessionId)].newNum = 0
            UpdatePrivateMsgRedDot()
            UpdateMGMsgNum()
            TriggerChannelDotEvent()
            TriggerSessionPropEvent()
            chat_io_mgr.SetSessionProp(sessionProp)
        end
    end
    event.Trigger(event.CHAT_MSG_READED,channelType)
end

--置顶某个会话 @toRoldId会话对象
function ToppingSession(sessionId, bool)
    if sessionProp[tostring(sessionId)] then
        sessionProp[tostring(sessionId)].isTop = bool
    end
    UpdateMGMsgNum()
    TriggerSessionPropEvent()
    chat_io_mgr.SetSessionProp(sessionProp)

end

--删除某个会话
function DeleteSession(sessionId)
    NtfMsgReaded(ENUM_CHANNEL.PRIVATE, sessionId) --标为已读

    sessionProp[tostring(sessionId)] = nil
    privateMsg[tonumber(sessionId)] = nil
    UpdateMGMsgNum()
    TriggerSessionPropEvent()
    TriggerPrivateChatEvent(sessionId)
    chat_io_mgr.SetSessionProp(sessionProp)
    chat_io_mgr.DeleteOneSession(sessionId)

end

--设置页面状态 如果pState==6 则cSession应该赋值
function SetPageState(pState, cSession, isCls, isTri)
    pageState = pState or pageState
    curSessionData = cSession or curSessionData
    isClose = isCls

    if isTri then
        TriggerPageStateChange()
    end
end
function GetPageState()
    local _pageState = pageState
    if ReviewingUtil.IsReviewing() and pageState == enum_pState.lang then
        _pageState = enum_pState.world
    end
    return _pageState, curSessionData, isClose
end

function IsPlayerChatTypeMsgBlocked(chatType, roleId)
    -- log.Warning("IsPlayerChatTypeMsgBlocked channelType:",chatType," roleId:",roleId)
    if GetCfgChatTypeIsBlocked(chatType) and IsBlockPlayer(roleId) then
        return true
    end
    return false
end

--[[判断是否是被屏蔽的玩家]]
function IsBlockPlayer(playerID)
    -- for k, v in pairs(BlockPlayer) do
    --     if playerID == k then
    --         return true
    --     end
    -- end
    -- return false
    if BlockPlayer[playerID] then 
        return true
    end

    
    return false
end

--[[是否被禁言 以及原因]]
function IsMute()
    return endTime > os.server_time(), reason
end

--获取屏蔽设置
function GetBlock()
    local bit = require "bit"
    local block = {}
    block[ENUM_CHANNEL.WORLD] = bit.band(BlockChannel, chat_pb.Channel_World) == chat_pb.Channel_World
    block[ENUM_CHANNEL.GUIDE] = bit.band(BlockChannel, chat_pb.Channel_Guild) == chat_pb.Channel_Guild
    block[ENUM_CHANNEL.TERRITORY] = bit.band(BlockChannel, chat_pb.Channel_Territory) == chat_pb.Channel_Territory
    block[ENUM_CHANNEL.LANG] = false --待定
    block[ENUM_CHANNEL.VIP] = bHideVIP
    block[ENUM_CHANNEL.ALLRECRUIT] = bAllRecruit

    return block
    -- body
end

--模拟初始化私聊数据(读取本地)、如果请求品台没有结果，则私聊数据没有初始化，需要手动初始化下
function LocalInitPri()
    if isInitPri == false then
        SetPrivateMsg({ data = {} })
    end
end

--如果数据为空则请求数据，ui打开时调用
--************gw已废弃
function ReqChatDataAfterLogin()
    local net_chat_module_new = require "net_chat_module_new"
    if curLangChanneInfo == nil then
        --net_chat_module_new.Req_ALLOT_CHANNEL()
        --net_chat_module_new. Req_PRIVATE_MSG()
    else
        if util.get_len(langMsg) == 0 then
            --net_chat_module_new.Req_LANGUAGE_CHANNEL_MSG() --请求频道聊天数据
        end
        -- if util.get_len(privateMsg)==0 then
        --     -- net_chat_module_new.Req_PRIVATE_MSG()
        -- end
    end
end

--获取@状态
function GetChannelAtState()
    return channelAtState
end
--清除掉已阅的艾特，退出聊天的时候
function ClearOldAt()
    if channelAtState[ENUM_CHANNEL.WORLD].new == false then
        channelAtState[ENUM_CHANNEL.WORLD].pos = 0
    end
    if channelAtState[ENUM_CHANNEL.GUIDE].new == false then
        channelAtState[ENUM_CHANNEL.GUIDE].pos = 0
    end
    if channelAtState[ENUM_CHANNEL.TERRITORY].new == false then
        channelAtState[ENUM_CHANNEL.TERRITORY].pos = 0
    end
    if channelAtState[ENUM_CHANNEL.LANG].new == false then
        channelAtState[ENUM_CHANNEL.LANG].pos = 0
    end
    if channelAtState[ENUM_CHANNEL.RECRUIT].new == false then
        channelAtState[ENUM_CHANNEL.RECRUIT].pos = 0
    end
    if channelAtState[ENUM_CHANNEL.PEEKGAME].new == false then
        channelAtState[ENUM_CHANNEL.PEEKGAME].pos = 0
    end
    if channelAtState[ENUM_CHANNEL.R4R5].new == false then
        channelAtState[ENUM_CHANNEL.R4R5].pos = 0
    end
end
--删除某频道艾特，点击@胶囊的时候
function ClearChannelAt(channelType)
    if channelAtState[channelType] then
        channelAtState[channelType].pos = 0
    end
end

--tool-----
function GetAtNamesByStr(str)
    local names = {}
    for nameText in string.gmatch(str, "@[^%s]+") do
        if util.GetStringByteLen(nameText) <= 20 then
            --名字最长是16
            table.insert(names, nameText)
        end
    end
    return names
end

--设置@的数组-----
function SetAtPlayerData(id, name)
    if id and name then
        at_playerID[id] = name
    end
end
-- 重置@的数组
function ResetAtPlayerData()
    at_playerID = {}
end
--获取@的数组-----
function GetAtPlayerData()
    return at_playerID or {}
end
-- 检测是否含有某个名字
function IfHasInputName(msgStr, name)
    local inputName = "@" .. name
    local atNames = GetAtNamesByStr(msgStr)
    -- log.Warning("chat atNames", Edump(atNames))
    for _, atName in pairs(atNames) do
        if atName == inputName then
            return true
        end
    end
    return false
end

--获取@的数组-----
function GetAtPlayeStr(msgStr)
    local atPlayerIDStr
    if at_playerID then
        local atPlayerIDArr = {}
        for key, value in pairs(at_playerID) do
            -- 校验
            local isHas = IfHasInputName(msgStr, value)
            -- log.Warning("chat isHas", isHas)
            if isHas then
                table.insert(atPlayerIDArr, key)
            end
        end
        if #atPlayerIDArr > 0 then
            atPlayerIDStr = table.concat(atPlayerIDArr, "@")
        end
    end
    -- log.Warning("chat atPlayerIDStr", atPlayerIDStr)
    return atPlayerIDStr
end

--获取翻译结果
function GetTranslation(str, langIso)
    if translationTable[str] then
        return translationTable[str][langIso]
    end
end

function GetGMState()
    return GMTalkOnline
end

function ClearGuildMsg()
    guildMsg = {}
    allianceR4R5Msg = {}
    TriggerChatEvent(ENUM_CHANNEL.GUIDE)
    TriggerChatEvent(ENUM_CHANNEL.R4R5)
end

local setChatMsg_sTypeFuncMap = {
    [mq_common_pb.enSpeak_Announcement] = function(msg)
        msg.context = string_util.NormalizeNewLines(msg.context)
        local copyData = CopyChatMsg(msg)
        copyData.sType = mq_common_pb.enSpeak_AnnounceForChannel
        table.insert(guildMsg, copyData)
    end,
    [mq_common_pb.enSpeak_UrgentAnnouncement] = function(msg)
        msg.context = string_util.NormalizeNewLines(msg.context)
        local copyData = CopyChatMsg(msg)
        copyData.sType = mq_common_pb.enSpeak_UrgentAnnounceForChannel
        table.insert(guildMsg, copyData)
    end,
}

--///data<==>net----------------------------------------------------------------------------
--设置聊天消息集
function SetChatMsg(data)
    ------ --print("SetChatMsg>>>>>>>>>>>>>>>>>>>>>>>>>")
    -- data = TMSG_CHAT_MSG_NTF

    local selfRoleId = player_mgr.GetPlayerRoleID()
    --刷新数据
    --巅峰赛数据会调用该方法，但只发巅峰赛数据，会导致其他聊天数据被清空
    local oldWorldMsg = worldMsg
    local oldGuildMsg = guildMsg
    local oldTerritoryMsg = territoryMsg
    local oldRecruitMsg = recruitMsg
    local oldR4R5Msg = allianceR4R5Msg
    worldMsg = {}
    guildMsg = {}
    territoryMsg = {}
    recruitMsg = {}
    allianceR4R5Msg = {}
    local num = 0
    local lastTime = chat_io_mgr.GetChannelReadTime(ENUM_CHANNEL.WORLD)

    --屏蔽控制
    bHideVIP = data.blockCtrl.bHideVip
    BlockChannel = data.blockCtrl.blockChannel
    bAllRecruit = data.blockCtrl.bRecruitMsg
    urgentAnnounceNum = data.urgentAnnounceNum
    urgentAnnounceTime = data.urgentAnnounceTime

    --屏蔽玩家数据
    -- log.Warning("data.blockPlayer 1111")
    -- log.Warning("data.blockPlayer",data.blockPlayer,data.worldMsg)
    for i, id in ipairs(data.blockPlayer or {}) do
        BlockPlayer[id] = 1
    end

    local hasNewAt = false
    if #data.worldMsg > 0 then
        for i, msg in ipairs(data.worldMsg or {}) do
            if not IsPlayerChatTypeMsgBlocked(msg.sType,msg.roleid) then
                if msg.nFlag ~= mq_common_pb.ChatFlag_Recall then
                    chat_monitor_mgr.SetRecallMessage(msg, chat_pb.Channel_World)
                    table.insert(worldMsg, msg)
                    if msg.roleid ~= selfRoleId and msg.chatTime > lastTime and (pageState ~= enum_pState.world or isClose) then
                        num = num + 1
                    end
                    if msg.sType == mq_common_pb.enSpeak_At and msg.roleid ~= selfRoleId and msg.chatTime > lastTime and IfHasSelfName(msg.context) then
                        channelAtState[ENUM_CHANNEL.WORLD].pos = util.get_len(worldMsg)
                        channelAtState[ENUM_CHANNEL.WORLD].new = pageState ~= enum_pState.world or isClose
                        hasNewAt = true
                    end
                end
            end
        end
        channelDot[ENUM_CHANNEL.WORLD] = num
    end

    if #data.GuildMsg > 0 then
        num = 0
        lastTime = chat_io_mgr.GetChannelReadTime(ENUM_CHANNEL.GUIDE)
        for i, msg in ipairs(data.GuildMsg or {}) do
            if not IsPlayerChatTypeMsgBlocked(msg.sType,msg.roleid) then
                if msg.nFlag ~= mq_common_pb.ChatFlag_Recall then
                    chat_monitor_mgr.SetRecallMessage(msg, chat_pb.Channel_Guild)
                    table.insert(guildMsg, msg)
                    if setChatMsg_sTypeFuncMap[msg.sType] then
                        setChatMsg_sTypeFuncMap[msg.sType](msg)
                    end
                    if msg.roleid ~= selfRoleId and msg.chatTime > lastTime and (pageState ~= enum_pState.guide or isClose) then
                        num = num + 1
                    end
                end
            end
        end
        table.sort(guildMsg, function(value1, value2)
            --联盟消息需要排序，因为联盟频道里会显示个人消息和联盟消息，但是这两个消息集是分开发的
            return value1.chatTime < value2.chatTime
        end)

        for i = #guildMsg, 1, -1 do
            if guildMsg[i].sType == mq_common_pb.enSpeak_At and
                    guildMsg[i].roleid ~= selfRoleId and guildMsg[i].chatTime > lastTime and
                    IfHasSelfName(guildMsg[i].context) then
                channelAtState[ENUM_CHANNEL.GUIDE].pos = i
                channelAtState[ENUM_CHANNEL.GUIDE].new = pageState ~= enum_pState.guide or isClose
                hasNewAt = true
                break
            end
        end
        channelDot[ENUM_CHANNEL.GUIDE] = num
    end

    if #data.territoryMsg > 0 then
        num = 0
        lastTime = chat_io_mgr.GetChannelReadTime(ENUM_CHANNEL.TERRITORY)
        for i, msg in ipairs(data.territoryMsg or {}) do
            if not IsPlayerChatTypeMsgBlocked(msg.sType,msg.roleid) then
                if msg.nFlag ~= mq_common_pb.ChatFlag_Recall then
                    chat_monitor_mgr.SetRecallMessage(msg, chat_pb.Channel_Territory)
                    table.insert(territoryMsg, msg)
                    if msg.roleid ~= selfRoleId and msg.chatTime > lastTime and (pageState ~= enum_pState.territory or isClose) then
                        num = num + 1
                    end
                    if msg.sType == mq_common_pb.enSpeak_At and msg.roleid ~= selfRoleId and msg.chatTime > lastTime and IfHasSelfName(msg.context) then
                        channelAtState[ENUM_CHANNEL.TERRITORY].pos = util.get_len(territoryMsg)
                        channelAtState[ENUM_CHANNEL.TERRITORY].new = pageState ~= enum_pState.territory or isClose
                        hasNewAt = true
                    end
                end
            end
        end
        channelDot[ENUM_CHANNEL.TERRITORY] = num
    end

    if #data.territoryMsg > 0 then
        num = 0
        lastTime = chat_io_mgr.GetChannelReadTime(ENUM_CHANNEL.RECRUIT)
        local net_chat_module_new = require "net_chat_module_new"
        local _, __, ___, LanguageId = net_chat_module_new.GetRoleInfo()
        for i, msg in ipairs(data.recruitMsg or {}) do
            if not IsPlayerChatTypeMsgBlocked(msg.sType,msg.roleid) then
                if msg.nFlag ~= mq_common_pb.ChatFlag_Recall then
                    chat_monitor_mgr.SetRecallMessage(msg, chat_pb.Channel_Recruit)
                    if bAllRecruit or (msg.sType ~= mq_common_pb.enSpeak_LeagueRecruit and msg.langtype == LanguageId) or (msg.sType == mq_common_pb.enSpeak_LeagueRecruit and msg.leagueRecruit.langtype == LanguageId) then
                        table.insert(recruitMsg, msg)

                        if msg.roleid ~= selfRoleId and msg.chatTime > lastTime and (pageState ~= enum_pState.recruit or isClose) then
                            num = num + 1
                        end
                        if msg.sType == mq_common_pb.enSpeak_At and msg.roleid ~= selfRoleId and msg.chatTime > lastTime and IfHasSelfName(msg.context) then
                            channelAtState[ENUM_CHANNEL.RECRUIT].pos = util.get_len(recruitMsg)
                            channelAtState[ENUM_CHANNEL.RECRUIT].new = pageState ~= enum_pState.recruit or isClose
                            hasNewAt = true
                        end
                    end
                end
            end
        end
        channelDot[ENUM_CHANNEL.RECRUIT] = num
    end

    if #data.WMTopRaceMsg > 0 then
        peekGameMsg = {}
        for i, msg in ipairs(data.WMTopRaceMsg or {}) do
            if not IsPlayerChatTypeMsgBlocked(msg.sType,msg.roleid) then
                if msg.nFlag ~= mq_common_pb.ChatFlag_Recall then
                    chat_monitor_mgr.SetRecallMessage(msg, chat_pb.Channel_WMTOPRACE)
                    local peakGame_data = require "peakGame_data"
                    peakGame_data.SetRoomMsgTime(msg.chatTime)
                    table.insert(peekGameMsg, msg)
                    --巅峰赛的数据由peakgamedata管理
                    peakGame_data.SetChatMsg(msg)
                    if msg.roleid ~= selfRoleId and msg.chatTime > lastTime and (pageState ~= enum_pState.peekGame or isClose) then
                        num = num + 1
                    end
                    if msg.sType == mq_common_pb.enSpeak_At and msg.roleid ~= selfRoleId and msg.chatTime > lastTime and IfHasSelfName(msg.context) then
                        channelAtState[ENUM_CHANNEL.PEEKGAME].pos = util.get_len(peakGame_data.GetChatMsgByRoomId())
                        channelAtState[ENUM_CHANNEL.PEEKGAME].new = pageState ~= enum_pState.peekGame or isClose
                        hasNewAt = true
                    end
                end
            end
        end
        channelDot[ENUM_CHANNEL.PEEKGAME] = num
    end

    if #data.r4r5Msg > 0 then
        num = 0
        lastTime = chat_io_mgr.GetChannelReadTime(ENUM_CHANNEL.R4R5)
        for i, msg in ipairs(data.r4r5Msg or {}) do
            if not IsPlayerChatTypeMsgBlocked(msg.sType,msg.roleid) then
                if msg.nFlag ~= mq_common_pb.ChatFlag_Recall then
                    chat_monitor_mgr.SetRecallMessage(msg, chat_pb.Channel_AllianceR4R5)
                    table.insert(allianceR4R5Msg, msg)
                    if msg.roleid ~= selfRoleId and msg.chatTime > lastTime and (pageState ~= enum_pState.r4r5 or isClose) then
                        num = num + 1
                    end
                    if msg.sType == mq_common_pb.enSpeak_At and msg.roleid ~= selfRoleId and msg.chatTime > lastTime and IfHasSelfName(msg.context) then
                        channelAtState[ENUM_CHANNEL.R4R5].pos = util.get_len(allianceR4R5Msg)
                        channelAtState[ENUM_CHANNEL.R4R5].new = pageState ~= enum_pState.r4r5 or isClose
                        hasNewAt = true
                    end
                end
            end
        end
        channelDot[ENUM_CHANNEL.R4R5] = num
    end

    --插入本地数据
    local personalAllMsg = chat_io_mgr.GetPresonalChat()
    -- local sociaty_data = require "sociaty_data"
    -- local baseData = sociaty_data.GetLeagueData()
    -- local sociatyName = baseData and baseData.strName
    -- local sociatyID = baseData and baseData.id
    for key, _data in pairs(personalAllMsg) do
        if _data.channel == chat_pb.Channel_Guild or _data.channel == chat_pb.Channel_Territory then
            -- if sociatyName and sociatyID then
            --     if _data["msg"] and (_data["msg"].guildid == sociatyID or _data["msg"].guildname == sociatyName) then
            --         AddChatNewMsg(_data)
            --     end
            --     isWaitForGuideData = false
            -- else
                isWaitForGuideData = true
            -- end
        else
            AddChatNewMsg(_data)
        end
    end

    if #worldMsg <= 0 then
        worldMsg = oldWorldMsg
    end
    if #guildMsg <= 0 then
        guildMsg = oldGuildMsg
    end
    if #territoryMsg <= 0 then
        territoryMsg = oldTerritoryMsg
    end
    if #recruitMsg <= 0 then
        recruitMsg = oldRecruitMsg
    end
    if #allianceR4R5Msg <= 0 then
        allianceR4R5Msg = oldR4R5Msg
    end
    table.sort(peekGameMsg, function(value1, value2)
        return value1.chatTime < value2.chatTime
    end)

    --排序
    table.sort(worldMsg, function(value1, value2)
        return value1.chatTime < value2.chatTime
    end)

    table.sort(territoryMsg, function(value1, value2)
        return value1.chatTime < value2.chatTime
    end)

    table.sort(recruitMsg, function(value1, value2)
        return value1.chatTime < value2.chatTime
    end)

    table.sort(allianceR4R5Msg, function(value1, value2)
        return value1.chatTime < value2.chatTime
    end)

    MarkShowChatTime(worldMsg)
    MarkShowChatTime(guildMsg)
    MarkShowChatTime(allianceR4R5Msg)
    --禁言时间
    endTime = data.muteEndTime

    --通知刷新
    UpdateMGMsgNum()
    TriggerChatEvent(ENUM_CHANNEL.WORLD)
    TriggerChatEvent(ENUM_CHANNEL.GUIDE)
    TriggerChatEvent(ENUM_CHANNEL.TERRITORY)
    TriggerChatEvent(ENUM_CHANNEL.RECRUIT)
    TriggerChatEvent(ENUM_CHANNEL.PEEKGAME)
    TriggerChatEvent(ENUM_CHANNEL.R4R5)

    TriggerChannelDotEvent()
    if hasNewAt then
        TriggerAtStateChange()
    end

    SetLangChannelMsg(data)--登录下推语言频道数据

    SetPrivateMsg(data.privateMsg)--登录下推私聊数据
end

--重置语言频道信息数据
function ResetLangChannelMsg(msg)
    langMsg = {}
    SetLangChannelMsg(msg)--设置语言频道信息
end

function IfHasSelfName(msgStr)
    local selfName = "@" .. player_mgr.GetRoleName()
    local atNames = GetAtNamesByStr(msgStr)
    for _, atName in pairs(atNames) do
        if atName == selfName then
            return true
        end
    end
    return false
end

--- 标记相邻的聊天记录是否需要显示时间 戳
---@param dataTable table 表格数据，包含 chatTime 字段，已按时间排序（升序）
---@return table 返回修改后的表格数据
function MarkShowChatTime(dataTable, startIndex)
    if not dataTable or #dataTable < 2 then
        return dataTable -- 如果表为空或少于两条数据，直接返回
    end

    local fiveMinutesInSeconds = 5 * 60 -- 定义5分钟的秒数

    -- 遍历表数据，从第二条开始
    local statIndex = startIndex or 2
    for i = statIndex, #dataTable do
        local currentData = dataTable[i]       -- 当前数据
        local prevData = dataTable[i - 1]     -- 前一条数据

        local currentTime = currentData.chatTime
        local prevTime = prevData.chatTime

        if currentTime and prevTime then
            -- 确保时间字段存在且有效
            local timeDiff = currentTime - prevTime -- 计算两条数据的时间戳差值
            if timeDiff > fiveMinutesInSeconds then
                -- 如果时间戳差值大于 5 分钟
                if currentData.szChatID ~= nil then
                    isShowChatTimeMap[currentData.szChatID] = true-- 给当前较大的时间戳数据添加标记
                end
            end
        end
    end
    --默认第一个显示时间
    isShowChatTimeMap[dataTable[1].szChatID] = true

    return dataTable -- 返回修改后的表
end


--新增消息数据时的处理函数
local addChatNewMsg_sTypeFuncMap = {
    [mq_common_pb.enSpeak_Announcement] = function(msg)
        msg.context = string_util.NormalizeNewLines(msg.context)
        local copyData = CopyChatMsg(msg)
        copyData.sType = mq_common_pb.enSpeak_AnnounceForChannel
        table.insert(guildMsg, copyData)
    end,
    [mq_common_pb.enSpeak_UrgentAnnouncement] = function(msg)
        msg.context = string_util.NormalizeNewLines(msg.context)
        local copyData = CopyChatMsg(msg)
        copyData.sType = mq_common_pb.enSpeak_UrgentAnnounceForChannel
        table.insert(guildMsg, copyData)
        AddUrgentAnnounceNum(msg)
    end,
}

function CopyChatMsg(msg)
    local tmp = {
        sType = msg.sType,
        roleid = msg.roleid,
        faceId = string.IsNullOrEmpty(msg.faceStr) and msg.faceId or msg.faceStr,
        faceStr= msg.faceStr,
        name = msg.name,
        roleLv = msg.roleLv,
        bHideVip = msg.bHideVip,
        nVipLv = msg.nVipLv,
        worldid = msg.worldid,
        guildname = msg.guildname,
        ce = msg.ce,
        chatTime = msg.chatTime,
        context = msg.context,
        szChatID = msg.szChatID,
        sex = msg.sex,
        leaguePostion = msg.leaguePostion,
        postionId = msg.postionId,
        leagueShortName = msg.leagueShortName,
    }
    return tmp
end

--增加一条新聊天消息
function AddChatNewMsg(data)
    --print(396,"<color=#00FFFF>设置 本服 消息 </color>>>>>>>>>>>>>")
    -- data = TMSG_CHAT_NEW_MSG_NTF
    if IsPlayerChatTypeMsgBlocked(data.msg.sType,data.msg.roleid) then
        return
    end

    --如果是禁言，则走禁言规则，此处return
    if data.msg.szChatID and data.msg.nFlag and data.msg.nFlag == mq_common_pb.ChatFlag_Recall then

        if data.channel ~= chat_pb.Channel_Language then
            if not chat_monitor_mgr.CheckMsgIsLoaded(data.msg.szChatID) then
                return
            end
        end
        if data.channel == chat_pb.Channel_World then
            worldMsg = chat_monitor_mgr.CheckIfRecallMessage(worldMsg, data.msg)
            if channelDot[ENUM_CHANNEL.WORLD] > 0 then
                channelDot[ENUM_CHANNEL.WORLD] = channelDot[ENUM_CHANNEL.WORLD] - 1
            end
        elseif data.channel == chat_pb.Channel_Guild then
            guildMsg = chat_monitor_mgr.CheckIfRecallMessage(guildMsg, data.msg)
            if channelDot[ENUM_CHANNEL.GUIDE] > 0 then
                channelDot[ENUM_CHANNEL.GUIDE] = channelDot[ENUM_CHANNEL.GUIDE] - 1
            end
        elseif data.channel == chat_pb.Channel_Territory then
            territoryMsg = chat_monitor_mgr.CheckIfRecallMessage(territoryMsg, data.msg)
            if channelDot[ENUM_CHANNEL.TERRITORY] > 0 then
                channelDot[ENUM_CHANNEL.TERRITORY] = channelDot[ENUM_CHANNEL.TERRITORY] - 1
            end
        elseif data.channel == chat_pb.Channel_Recruit then
            recruitMsg = chat_monitor_mgr.CheckIfRecallMessage(recruitMsg, data.msg)
            if channelDot[ENUM_CHANNEL.RECRUIT] > 0 then
                channelDot[ENUM_CHANNEL.RECRUIT] = channelDot[ENUM_CHANNEL.RECRUIT] - 1
            end
        elseif data.channel == chat_pb.Channel_WMTOPRACE then
            local lastestTime
            local peakGame_data = require "peakGame_data"
            peekGameMsg, lastestTime = chat_monitor_mgr.CheckIfRecallMessage(peekGameMsg, data.msg)
            peakGame_data.SetRoomMsgTime(lastestTime)
            if channelDot[ENUM_CHANNEL.PEEKGAME] > 0 then
                channelDot[ENUM_CHANNEL.PEEKGAME] = channelDot[ENUM_CHANNEL.PEEKGAME] - 1
            end
        elseif data.channel == chat_pb.Channel_AllianceR4R5 then
            allianceR4R5Msg = chat_monitor_mgr.CheckIfRecallMessage(allianceR4R5Msg, data.msg)
            if channelDot[ENUM_CHANNEL.R4R5] > 0 then
                channelDot[ENUM_CHANNEL.R4R5] = channelDot[ENUM_CHANNEL.R4R5] - 1
            end
        elseif data.channel == chat_pb.Channel_Language then
                langMsg = chat_monitor_mgr.CheckIfRecallMessage(langMsg, data.msg)
                if channelDot[ENUM_CHANNEL.LANG] > 0 then
                    channelDot[ENUM_CHANNEL.LANG] = channelDot[ENUM_CHANNEL.LANG] - 1
                end
        end
        TiggerPageView(data.msg)
        return
    else
        --灵石记录
        chat_monitor_mgr.SetRecallMessage(data.msg, data.channel)
    end

    if addChatNewMsg_sTypeFuncMap[data.msg.sType] then
        addChatNewMsg_sTypeFuncMap[data.msg.sType](data.msg)
    end

    local selfRoleId = player_mgr.GetPlayerRoleID()
    if data.channel == chat_pb.Channel_World then
        table.insert(worldMsg, data.msg)
        MarkShowChatTime(worldMsg, util.get_len(worldMsg))
        TriggerChatEvent(ENUM_CHANNEL.WORLD, data.msg)
        if data.msg.sType == mq_common_pb.enSpeak_At and data.msg.roleid ~= selfRoleId and IfHasSelfName(data.msg.context) then
            channelAtState[ENUM_CHANNEL.WORLD].pos = util.get_len(worldMsg)
            channelAtState[ENUM_CHANNEL.WORLD].new = pageState ~= enum_pState.world or isClose
            TriggerAtStateChange()
        end
        if data.msg.roleid ~= selfRoleId and (pageState ~= enum_pState.world or isClose) then
            channelDot[ENUM_CHANNEL.WORLD] = channelDot[ENUM_CHANNEL.WORLD] + 1
            TriggerChannelDotEvent()
        end
    elseif data.channel == chat_pb.Channel_Guild then
        --频道是联盟
        if data.msg.sandboxTreasureFinishData then
            --这里是领奖通知
            --log.Error("-------挖掘宝藏完成了！！！！")

        elseif data.msg.sandboxTreasureData then

        elseif data.msg.sandboxKastenboxMultipleRewardData then
            --这里显示双倍奖励
            --log.Error("-------有人获得了双倍奖励！！！！")
        end

        table.insert(guildMsg, data.msg)
        MarkShowChatTime(guildMsg, util.get_len(guildMsg))

        if data.msg.sType == mq_common_pb.enSpeak_LeagueMutualHelp then
            TriggerChatEvent(ENUM_CHANNEL.GUIDE)
        else
            TriggerChatEvent(ENUM_CHANNEL.GUIDE, data.msg)
        end

        if data.msg.sType == mq_common_pb.enSpeak_At and data.msg.roleid ~= selfRoleId and IfHasSelfName(data.msg.context) then
            channelAtState[ENUM_CHANNEL.GUIDE].pos = util.get_len(guildMsg)
            channelAtState[ENUM_CHANNEL.GUIDE].new = pageState ~= enum_pState.guide or isClose
            TriggerAtStateChange()
        end

        if data.msg.roleid ~= selfRoleId and (pageState ~= enum_pState.guide or isClose) then
            channelDot[ENUM_CHANNEL.GUIDE] = channelDot[ENUM_CHANNEL.GUIDE] + 1
            TriggerChannelDotEvent()
        end

        if data.msg.sType == mq_common_pb.enSpeak_SandboxTreasure then
            local radar_define = require "radar_define"
            event.Trigger(radar_define.RADAR_DIG_TREASURE_MSG_BUBBLE_UPDATE)
        end

    elseif data.channel == chat_pb.Channel_Territory then
        table.insert(territoryMsg, data.msg)
        TriggerChatEvent(ENUM_CHANNEL.TERRITORY, data.msg)
        if data.msg.sType == mq_common_pb.enSpeak_At and data.msg.roleid ~= selfRoleId and IfHasSelfName(data.msg.context) then
            channelAtState[ENUM_CHANNEL.TERRITORY].pos = util.get_len(territoryMsg)
            channelAtState[ENUM_CHANNEL.TERRITORY].new = pageState ~= enum_pState.territory or isClose
            TriggerAtStateChange()
        end
        if data.msg.roleid ~= selfRoleId and (pageState ~= enum_pState.territory or isClose) then
            channelDot[ENUM_CHANNEL.TERRITORY] = channelDot[ENUM_CHANNEL.TERRITORY] + 1
            TriggerChannelDotEvent()
        end
    elseif data.channel == chat_pb.Channel_Recruit then
        local net_chat_module_new = require "net_chat_module_new"
        local _, __, ___, LanguageId = net_chat_module_new.GetRoleInfo()
        if bAllRecruit or (data.msg.sType ~= mq_common_pb.enSpeak_LeagueRecruit and data.msg.langtype == LanguageId) or (data.msg.sType == mq_common_pb.enSpeak_LeagueRecruit and data.msg.leagueRecruit.langtype == LanguageId) then
            table.insert(recruitMsg, data.msg)
            TriggerChatEvent(ENUM_CHANNEL.RECRUIT, data.msg)
            if data.msg.sType == mq_common_pb.enSpeak_At and data.msg.roleid ~= selfRoleId and IfHasSelfName(data.msg.context) then
                channelAtState[ENUM_CHANNEL.RECRUIT].pos = util.get_len(recruitMsg)
                channelAtState[ENUM_CHANNEL.RECRUIT].new = pageState ~= enum_pState.recruit or isClose
                TriggerAtStateChange()
            end
            if data.msg.roleid ~= selfRoleId and (pageState ~= enum_pState.recruit or isClose) then
                channelDot[ENUM_CHANNEL.RECRUIT] = channelDot[ENUM_CHANNEL.RECRUIT] + 1
                TriggerChannelDotEvent()
            end
        end
    elseif data.channel == chat_pb.Channel_WMTOPRACE then
        table.insert(peekGameMsg, data.msg)
        local peakGame_data = require "peakGame_data"
        peakGame_data.SetRoomMsgTime(data.msg.chatTime)
        peakGame_data.SetChatMsg(data.msg)

        TriggerChatEvent(ENUM_CHANNEL.PEEKGAME, data.msg)
        if data.msg.sType == mq_common_pb.enSpeak_At and data.msg.roleid ~= selfRoleId and IfHasSelfName(data.msg.context) then
            channelAtState[ENUM_CHANNEL.PEEKGAME].pos = util.get_len(peakGame_data.GetChatMsgByRoomId())
            channelAtState[ENUM_CHANNEL.PEEKGAME].new = pageState ~= enum_pState.peekGame or isClose
            TriggerAtStateChange()
        end
        if data.msg.roleid ~= selfRoleId and (pageState ~= enum_pState.peekGame or isClose) then
            channelDot[ENUM_CHANNEL.PEEKGAME] = channelDot[ENUM_CHANNEL.PEEKGAME] + 1
            TriggerChannelDotEvent()
        end

    elseif data.channel == chat_pb.Channel_AllianceR4R5 then
        --下推一条联盟R4R5消息
        table.insert(allianceR4R5Msg, data.msg)
        MarkShowChatTime(allianceR4R5Msg, util.get_len(allianceR4R5Msg))
        TriggerChatEvent(ENUM_CHANNEL.R4R5, data.msg)

        if data.msg.sType == mq_common_pb.enSpeak_At and data.msg.roleid ~= selfRoleId and IfHasSelfName(data.msg.context) then
            channelAtState[ENUM_CHANNEL.R4R5].pos = util.get_len(allianceR4R5Msg)
            channelAtState[ENUM_CHANNEL.R4R5].new = pageState ~= enum_pState.r4r5 or isClose
            TriggerAtStateChange()
        end
        if data.msg.roleid ~= selfRoleId and (pageState ~= enum_pState.r4r5 or isClose) then
            channelDot[ENUM_CHANNEL.R4R5] = channelDot[ENUM_CHANNEL.R4R5] + 1
            TriggerChannelDotEvent()
        end
    elseif data.channel == chat_pb.Channel_Language then
        AddLangChannelNewMsg(data.msg)--语言频道增加一条新聊天消息
    end
    if data.msg.sType == mq_common_pb.enSpeak_GoldenEggs then
        local surprise_bag_data = require "surprise_bag_data"
        --不是自己的就触发
        if data.msg.roleid ~= player_mgr.GetPlayerRoleID() then
            if data.channel == chat_pb.Channel_Guild then
                surprise_bag_data.SetChatMsgRed(ENUM_CHANNEL.GUIDE,true)
            end
            if data.channel == chat_pb.Channel_World then
                surprise_bag_data.SetChatMsgRed(ENUM_CHANNEL.WORLD,true)
            end 
        end
    end
end
--设置联盟频道里的个人消息
function SetPersonalMsg(data)
    -- data = TMSG_PRIVATE_MSG_SET_NTF
    --print(396,"<color=#FFFF00>设置 个人消息 </color>>>>>>>>>>>>>")
    local selfRoleId = player_mgr.GetPlayerRoleID()
    local num = 0
    local lastTime = chat_io_mgr.GetChannelReadTime(ENUM_CHANNEL.GUIDE)
    for i, msg in ipairs(data.msg) do
        if not IsPlayerChatTypeMsgBlocked(msg.sType,msg.roleid) then
            table.insert(guildMsg, msg) --刷新数据
            if msg.roleid ~= selfRoleId and msg.chatTime > lastTime and (pageState ~= enum_pState.guide or isClose) then
                num = num + 1
            end
        end
    end
    table.sort(guildMsg, function(value1, value2)
        --联盟消息需要排序，因为联盟频道里会显示个人消息和联盟消息，但是这两个消息集是分开发的
        return value1.chatTime < value2.chatTime
    end)

    local hasNewAt = false
    for i = #guildMsg, 1, -1 do
        if guildMsg[i].sType == mq_common_pb.enSpeak_At and
                guildMsg[i].roleid ~= selfRoleId and guildMsg[i].chatTime > lastTime and
                IfHasSelfName(guildMsg[i].context) then
            channelAtState[ENUM_CHANNEL.GUIDE].pos = i
            channelAtState[ENUM_CHANNEL.GUIDE].new = pageState ~= enum_pState.guide or isClose
            hasNewAt = true
            break
        end
    end

    TriggerChatEvent(ENUM_CHANNEL.GUIDE) --通知更新
    if num > 0 then
        channelDot[ENUM_CHANNEL.GUIDE] = channelDot[ENUM_CHANNEL.GUIDE] + num --刷新数据
        TriggerChannelDotEvent()--通知更新
    end

    if hasNewAt then
        TriggerAtStateChange()
    end --通知更新
end
--联盟频道里增加一条个人消息
function AddPersonalNewMsg(data)
    -- data = TMSG_PRIVATE_MSG_NTF
    if IsPlayerChatTypeMsgBlocked(data.msg.sType,data.msg.roleid) then
        return
    end

    local selfRoleId = player_mgr.GetPlayerRoleID()
    if data.msg.nFlag ~= mq_common_pb.ChatFlag_Recall then
        chat_monitor_mgr.SetRecallMessage(data.msg, chat_pb.Channel_Guild)
        table.insert(guildMsg, data.msg)
        TriggerChatEvent(ENUM_CHANNEL.GUIDE, data.msg)
        if data.msg.roleid ~= selfRoleId and (pageState ~= enum_pState.guide or isClose) then
            channelDot[ENUM_CHANNEL.GUIDE] = channelDot[ENUM_CHANNEL.GUIDE] + 1
            TriggerChannelDotEvent()
        end
    end
end


--设置申请到的语言频道信息
function SetAllotChannelInfo(data)
    -- data = {"code":1,"message":"分配成功","data":{"channelName":"简体中文-频道2","languageId":1,"maxNum":4000,"onlineNum":0,"createTime":1571018986,"id":10001}}
    --重新分配频道后 语言聊天数据清空
    langMsg = {}
    TriggerChatEvent(ENUM_CHANNEL.LANG)

    curLangChanneInfo = data.data
    TriggerCurChannelChange()

    --重置lang频道红点信息
    channelAtState[ENUM_CHANNEL.LANG].new = false
    channelAtState[ENUM_CHANNEL.LANG].pos = 0
    channelDot[ENUM_CHANNEL.LANG] = 0
end
--设置语言频道配置
function SetLangChannelConfig(data)
    --{"code":1,"message":"查询成功","data":[{"channelName":"简体中文-频道1","languageId":1,"maxNum":4000,"onlineNum":1000,"createTime":1571139726,"id":10000},{"channelName":"简体中文-频道2","languageId":1,"maxNum":4000,"onlineNum":2000,"createTime":1571139826,"id":10001},{"channelName":"简体中文-频道3","languageId":1,"maxNum":4000,"onlineNum":3000,"createTime":1571139926,"id":10002}]}
    langChannelConfig = data.data
    TriggerChannelConfigChange()
end

--语言频道设置聊天消息集
function SetLangChannelMsg(data)
    local selfRoleId = player_mgr.GetPlayerRoleID()

    local oldLangMsg = langMsg

    langMsg = {}

    local num = 0
    local lastTime = chat_io_mgr.GetChannelReadTime(ENUM_CHANNEL.WORLD)

    local hasNewAt = false
    if #data.languageMsg > 0 then
        for i, msg in ipairs(data.languageMsg or {}) do
            if not IsPlayerChatTypeMsgBlocked(msg.sType,msg.roleid) then
                if msg.nFlag ~= mq_common_pb.ChatFlag_Recall then
                    chat_monitor_mgr.SetRecallMessage(msg, chat_pb.Channel_Language)
                    table.insert(langMsg, msg)
                    if msg.roleid ~= selfRoleId and msg.chatTime > lastTime and (pageState ~= enum_pState.lang or isClose) then
                        num = num + 1
                    end
                    if msg.sType == mq_common_pb.enSpeak_At and msg.roleid ~= selfRoleId and msg.chatTime > lastTime and IfHasSelfName(msg.context) then
                        channelAtState[ENUM_CHANNEL.LANG].pos = util.get_len(langMsg)
                        channelAtState[ENUM_CHANNEL.LANG].new = pageState ~= enum_pState.lang or isClose
                        hasNewAt = true
                    end
                end
            end
        end
        if ReviewingUtil.IsReviewing() then
            channelDot[ENUM_CHANNEL.LANG] = 0
        else
            channelDot[ENUM_CHANNEL.LANG] = num
        end
    end

    if #langMsg <= 0 then
        langMsg = oldLangMsg
    end

    table.sort(langMsg, function(value1, value2)
        return value1.chatTime < value2.chatTime
    end)

    MarkShowChatTime(langMsg)

    --通知刷新
    TriggerChatEvent(ENUM_CHANNEL.LANG)

    if channelDot[ENUM_CHANNEL.LANG] > 0 then
        TriggerChannelDotEvent()
    end
    if hasNewAt then
        TriggerAtStateChange()
    end
end

--语言频道增加一条新聊天消息
function AddLangChannelNewMsg(data)
    --print(396,"<color=#00AA00> 设置 全服 消息ID </color>>>>>>>>")
    --dump(data)
    --data = tCrossSvrChatMsg rebulid


    if not data or IsPlayerChatTypeMsgBlocked(data.sType,data.roleid) then
        return
    end

    --如果是禁言，则走禁言规则，此处return
    if data.nFlag == mq_common_pb.ChatFlag_Recall then
        if not chat_monitor_mgr.CheckMsgIsLoaded(data.szChatID) then
            return
        end
        langMsg = chat_monitor_mgr.CheckIfRecallMessage(langMsg, data)
        if channelDot[ENUM_CHANNEL.LANG] > 0 then
            channelDot[ENUM_CHANNEL.LANG] = channelDot[ENUM_CHANNEL.LANG] - 1
        end
        TiggerPageView(data)
        return
    else
        chat_monitor_mgr.SetRecallMessage(data, data.channelid)
    end

    local selfRoleId = player_mgr.GetPlayerRoleID()
    local msg = data
    -- if msg.sType == mq_common_pb.enSpeak_ShareLabourDayInfo then
    --     local ui_may_day_chat_notice = require "ui_may_day_chat_notice"
    --     ui_may_day_chat_notice.InitNoticeData()
    --     ui_may_day_chat_notice.CheckIsHighPrice(msg)
    --     ui_may_day_chat_notice.AddMsgToQueue(msg)
    --     ui_may_day_chat_notice.PlayMsgQueue()
    -- end
    table.insert(langMsg, msg)
    MarkShowChatTime(langMsg, util.get_len(langMsg))
    TriggerChatEvent(ENUM_CHANNEL.LANG, msg)
    if msg.sType == mq_common_pb.enSpeak_At and msg.roleid ~= selfRoleId and IfHasSelfName(msg.context) then
        channelAtState[ENUM_CHANNEL.LANG].pos = util.get_len(langMsg)
        channelAtState[ENUM_CHANNEL.LANG].new = pageState ~= enum_pState.lang or isClose
        TriggerAtStateChange()
    end
    if msg.roleid ~= selfRoleId and (pageState ~= enum_pState.lang or isClose) then
        channelDot[ENUM_CHANNEL.LANG] = channelDot[ENUM_CHANNEL.LANG] + 1
        TriggerChannelDotEvent()
    end
    if ReviewingUtil.IsReviewing() then
        channelDot[ENUM_CHANNEL.LANG] = 0
    end
end

local function deepCopy(orig)
    if type(orig) ~= 'table' then
        return nil
    end

    local copy = {}
    if orig._fields then
        if type(orig._fields) == 'table' then
            for key, value in pairs(orig._fields) do
                copy[key] = value
            end
        else
            copy._fields = orig._fields
        end
    end

    return copy
end


--设置私聊消息集 登录
function SetPrivateMsg(data)
    if not data or #data == 0 then
        return
    end
    isInitPri = true
    -- data = TChatPrivateMsg
    local selfRoleId = tonumber(player_mgr.GetPlayerRoleID())
    if selfRoleId == 0 then
        isInitPri = false
    end
    local lastGetTime = chat_io_mgr.GetChannelReadTime(ENUM_CHANNEL.PRIVATE)

    ------ --print("SetPrivateMsg::lastGetTime>>>>>>>>>>>>>>>>>>>>",lastGetTime)

    --本地读取
    privateMsg = chat_io_mgr.GetAllMsg() or {}
    sessionProp = chat_io_mgr.ReadSessionProp() or {}

    --刷新data
    local sortMsg = {}
    for k, v in ipairs(data) do

        -- if not IsPlayerChatTypeMsgBlocked(v.sType,v.roleid) then
        if v.chatData then
            for m, n in ipairs(v.chatData) do
                if n then
                    local tMsg = util.cp_pb_data(n.common, {})
                    -- if not IsPlayerChatTypeMsgBlocked(tMsg.sType,tMsg.roleid) then
                        local pb = n
                        print("SetPrivateMsg::pb>>>>>>>>>>>>>>>>>>>>", pb)
                        --tMsg.titleID = pb.common.titleID
                        if pb.common.shareInfo then
                            print("SetPrivateMsg::shareInfo>>>>>>>>>>>>>>>>>>>>", pb.common.shareInfo)
                            -- tMsg.shareInfo = pb.common.shareInfo
                            tMsg.shareInfo = {
                                nShareType = pb.common.shareInfo.nShareType,
                                nHostId = pb.common.shareInfo.nHostId,
                                nStallId = pb.common.shareInfo.nStallId,
                                nDaySellPrice = pb.common.shareInfo.nDaySellPrice,
                                nShareState = pb.common.shareInfo.nShareState,
                                nHostAreaId = pb.common.shareInfo.nHostAreaId,
                            }
                        end
                        --tMsg.nFlag = pb.common.nFlag
                        --tMsg.szChatID = pb.common.szChatID
                        --tMsg.chatTime = pb.common.chatTime
                        --tMsg.roleid = pb.common.roleid
                        tMsg.sType = tMsg.sType or 0
                        tMsg.toRoleId = n.toRoleId
                        tMsg.toWorldid = n.toWorldid
                        tMsg.langtype = n.langtype
                        tMsg.channelid = n.channelid
                        tMsg.extendinfopb2 = pb.common.extendinfopb
                        table.insert(sortMsg, tMsg)
                    -- end
                end
            end
        end
        -- end
    end

    table.sort(sortMsg, function(value1, value2)
        if value1 and value2 and value1.chatTime and value2.chatTime then
            return value1.chatTime < value2.chatTime
        else
            return false
        end
    end)

    local lastMsgTime = nil
    if #sortMsg > 0 then
        lastMsgTime = sortMsg[#sortMsg].chatTime
    end

    --替换gm消息的id,记录最新gmid
    local t_time = nil
    local t_id = nil
    for k, tMsg in ipairs(sortMsg) do
        if tMsg.bgm == true then
            if tMsg.roleid ~= selfRoleId then
                t_time = tMsg.chatTime
                t_id = tMsg.roleid
                tMsg.roleid = GMConstRoldId
            else
                tMsg.toRoleId = GMConstRoldId
            end
        end
    end
    local gmMsgInfo = chat_io_mgr.GetLastGMMsgInfo()
    GMLastRoldId = gmMsgInfo.roleId
    if t_time and t_id and t_time > gmMsgInfo.time then
        GMLastRoldId = t_id
        chat_io_mgr.SetLastGMMsgInfo(t_time, t_id)
    end
    local recallMsg = {}
    for key, value in ipairs(sortMsg) do
        if value.nFlag == mq_common_pb.ChatFlag_Recall then
            --登录时收到撤回消息
            recallMsg[value.szChatID] = true
        end
    end
    local newMsgTable = {}
    for k, tMsg in ipairs(sortMsg) do
        local sessionId = tMsg.roleid == selfRoleId and tMsg.toRoleId or tMsg.roleid

        local sessionMsg = privateMsg[tonumber(sessionId)]
        local lastMsg
        if sessionMsg ~= nil and #sessionMsg > 0 then
            lastMsg = sessionMsg[#sessionMsg]
            if tMsg.extendinfopb2 then
                for kkk, tttMsg in ipairs(sessionMsg) do
                    if tttMsg.szChatID==tMsg.szChatID then
                        tttMsg.extendinfopb2=tMsg.extendinfopb2
                    end
                end
            end
        end


        -- if lastMsg then
        --     print(tMsg.context,lastMsg.context," || ",tMsg.chatTime,lastMsg.chatTime,lastGetTime,os.server_time())
        -- else
        --     print(tMsg.context," ||| ",tMsg.chatTime,lastGetTime,os.server_time())
        -- end
        if tMsg.nFlag == mq_common_pb.ChatFlag_Recall then
            --登录时收到撤回消息
            local sessionId = chat_io_mgr.GetMsgSessionId(tMsg.szChatID)
            if sessionId then
                privateMsg[tonumber(sessionId)] = privateMsg[tonumber(sessionId)] or {}
                privateMsg[tonumber(sessionId)] = chat_monitor_mgr.CheckIfRecallMessage(privateMsg[tonumber(sessionId)], tMsg)
                chat_io_mgr.RemoveMsgInSession(sessionId, tMsg.szChatID)
                print("撤回消息", tMsg.bgm, tMsg.context, tMsg.chatTime, " || ", (lastMsg and lastMsg.context), (lastMsg and lastMsg.chatTime), " || ", lastGetTime, "szChatID", tMsg.szChatID)
            end

        elseif not recallMsg[tMsg.szChatID] and (lastMsg == nil or tMsg.chatTime > lastMsg.chatTime) and tMsg.chatTime > lastGetTime then
            if tMsg and tMsg.nFlag ~= mq_common_pb.ChatFlag_Recall then
                chat_monitor_mgr.SetRecallMessage(tMsg, sessionId)
            end
            privateMsg[tonumber(sessionId)] = privateMsg[tonumber(sessionId)] or {}
            table.insert(privateMsg[tonumber(sessionId)], tMsg)

            newMsgTable[sessionId] = newMsgTable[sessionId] or {}
            table.insert(newMsgTable[sessionId], tMsg)
            if sessionId == GMConstRoldId then
                --gm消息
                event.Trigger(event.GAME_EVENT_REPORT, "gm_service_message", {})
            end
            print(396, "添加消息", tMsg.bgm, tMsg.context, tMsg.chatTime, " || ", (lastMsg and lastMsg.context), (lastMsg and lastMsg.chatTime), " || ", lastGetTime)

        else
            --print("舍弃消息",tMsg.bgm,tMsg.context,tMsg.chatTime," || ",(lastMsg and lastMsg.context),(lastMsg and lastMsg.chatTime)," || ",lastGetTime)
        end
    end

    for sessionId, MsgSet in pairs(newMsgTable) do
        local istop = sessionId == GMConstRoldId --如果是gm消息，默认置顶
        sessionProp[tostring(sessionId)] = sessionProp[tostring(sessionId)] or { newNum = 0, isTop = istop }
        local newN = 0
        if pageState ~= enum_pState.privateView or curSessionData.sessionId ~= sessionId or isClose then
            --当前页面在该会话则不刷新红点
            for _, sMsg in ipairs(MsgSet) do
                if sMsg.roleid ~= selfRoleId then
                    --去除自己
                    newN = newN + 1
                end
            end
        end
        sessionProp[tostring(sessionId)].newNum = sessionProp[tostring(sessionId)].newNum + newN
    end

    -- 更新私聊红点
    UpdatePrivateMsgRedDot()

    --通知刷新
    UpdateMGMsgNum()
    TriggerChannelDotEvent()
    TriggerSessionPropEvent()
    for sessionId, MsgSet in pairs(newMsgTable) do
        TriggerPrivateChatEvent(sessionId)
    end

    --本地存储
    chat_io_mgr.SetSessionProp(sessionProp)
    chat_io_mgr.AddMsgToSession(newMsgTable)
    if lastMsgTime then
        chat_io_mgr.SetChannelReadTime(ENUM_CHANNEL.PRIVATE, lastMsgTime) --保存本次获取私聊消息的时间
    end
    for i, v in pairs(privateMsg) do
        MarkShowChatTime(v)
    end

end

--- 是否有私聊会话
---@param sessionId number
---@return boolean
function HasPrivateSession(sessionId)
    return (privateMsg[sessionId] ~= nil)
end

--- 更新私聊红点
function UpdatePrivateMsgRedDot()
    local num = 0
    for sessionId, prop in pairs(sessionProp) do
        -- 确认私聊列表中是否有实际的会话
        if HasPrivateSession(tonumber(sessionId)) then
            num = num + prop.newNum
        else
            log.Warning("PP*[chat_mgr_new] 无效的私聊会话！| " .. sessionId .. " > " .. util.toJsonString(prop))
        end
    end
    channelDot[ENUM_CHANNEL.PRIVATE] = num
    --log.Warning("PP*[chat_mgr_new] 私聊红点数量：" .. tostring(num))
end

function RemovePrivateMsg(szChatID)
    local sessionId = chat_io_mgr.GetMsgSessionId(szChatID)
    if not sessionId then
        print("[chat_mgr_new]>>sessionId=nil")
        return
    end
    privateMsg[tonumber(sessionId)] = privateMsg[tonumber(sessionId)] or {}

    privateMsg[tonumber(sessionId)] = chat_monitor_mgr.CheckIfRecallMessage(privateMsg[tonumber(sessionId)], { szChatID = szChatID })
    chat_io_mgr.RemoveMsgInSession(sessionId, szChatID)
    print("[chat_mgr_new]>>RemovePrivateMsg删除一条消息 szChatID", szChatID, "sessionId", sessionId, "playerid", player_mgr.GetPlayerRoleID())
    TiggerPageView()
    if sessionId ~= 0 then
        sessionProp[tostring(sessionId)] = sessionProp[tostring(sessionId)] or { newNum = 0 }
        if sessionProp[tostring(sessionId)].newNum > 0 then
            sessionProp[tostring(sessionId)].newNum = sessionProp[tostring(sessionId)].newNum - 1
            channelDot[ENUM_CHANNEL.PRIVATE] = channelDot[ENUM_CHANNEL.PRIVATE] - 1
            TriggerChannelDotEvent()
            TriggerSessionPropEvent()
        end
    end
end
--增加一条私聊消息
function AddPrivateNewMsg(tMsg)
    --data = tCrossSvrChatMsg reBuild
    if IsPlayerChatTypeMsgBlocked(tMsg.sType,tMsg.roleid) then
        return
    end
    local selfRoleId = tonumber(player_mgr.GetPlayerRoleID())

    --替换gm消息的id,记录最新gmid
    if tMsg.bgm == true then
        if tMsg.roleid ~= selfRoleId then
            GMLastRoldId = tMsg.roleid
            chat_io_mgr.SetLastGMMsgInfo(tMsg.chatTime, tMsg.roleid)
            tMsg.roleid = GMConstRoldId

            SetGMState(true)
        else
            tMsg.toRoleId = GMConstRoldId
        end
    end

    --刷新data
    local sessionId = tMsg.roleid == selfRoleId and tMsg.toRoleId or tMsg.roleid

    privateMsg[tonumber(sessionId)] = privateMsg[tonumber(sessionId)] or {}

    --如果是禁言，则走禁言规则，此处return
    if tMsg.nFlag == mq_common_pb.ChatFlag_Recall then
        local roldID = chat_monitor_mgr.CheckMsgIsLoaded(tMsg.szChatID)
        if not roldID then
            return
        end
        -- langMsg[tonumber(sessionId)] = chat_monitor_mgr.CheckIfRecallMessage(langMsg,tMsg)
        privateMsg[tonumber(roldID)] = chat_monitor_mgr.CheckIfRecallMessage(privateMsg[tonumber(roldID)], tMsg)
        chat_io_mgr.RemoveMsgInSession(roldID, tMsg.szChatID)
        TiggerPageView(tMsg)

        if roldID ~= 0 then
            sessionProp[tostring(roldID)] = sessionProp[tostring(roldID)] or { newNum = 0 }
            if sessionProp[tostring(roldID)].newNum > 0 then
                sessionProp[tostring(roldID)].newNum = sessionProp[tostring(roldID)].newNum - 1
                channelDot[ENUM_CHANNEL.PRIVATE] = channelDot[ENUM_CHANNEL.PRIVATE] - 1
                TriggerChannelDotEvent()
                TriggerSessionPropEvent()
            end
        end
        return
    else
        chat_monitor_mgr.SetRecallMessage(tMsg, sessionId)
    end

    table.insert(privateMsg[tonumber(sessionId)], tMsg)
    MarkShowChatTime(privateMsg[tonumber(sessionId)], util.get_len(privateMsg[tonumber(sessionId)]))

    local istop = sessionId == GMConstRoldId --如果是gm消息，默认置顶
    sessionProp[tostring(sessionId)] = sessionProp[tostring(sessionId)] or { newNum = 0, isTop = istop }
    if tMsg.roleid ~= selfRoleId and (pageState ~= enum_pState.privateView or curSessionData.sessionId ~= sessionId or isClose) then
        sessionProp[tostring(sessionId)].newNum = sessionProp[tostring(sessionId)].newNum + 1
        channelDot[ENUM_CHANNEL.PRIVATE] = channelDot[ENUM_CHANNEL.PRIVATE] + 1
        TriggerChannelDotEvent()
    end
    UpdateMGMsgNum()
    TriggerSessionPropEvent()
    chat_io_mgr.SetSessionProp(sessionProp)

    TriggerPrivateChatEvent(sessionId, tMsg)
    local newMsg = { [sessionId] = { tMsg } }
    chat_io_mgr.AddMsgToSession(newMsg)
    if istop then
        event.Trigger(event.GAME_EVENT_REPORT, "gm_service_message", {})
    end
end

--设置屏蔽玩家数据
function SetBlockPlayer(content)
    if content.bBlock then
        BlockPlayer[content.roleid] = 1
    else
        BlockPlayer[content.roleid] = nil
    end
    local channelType = pStateLinkCHANNEL[pageState]  
    log.Warning("UPDATE_CHAT_MSG channelType: ", channelType)
    event.Trigger(event.UPDATE_CHAT_MSG,channelType)
    event.Trigger(event.UPDATE_SESSION_PROP)
    event.Trigger(event.UPDATE_PLAYER_INFO_DETAIL)
end

--设置屏蔽控制
function SetBlockCtrl(blockChannel, bHideVip, b_AllRecruit)
    bHideVIP = bHideVip
    BlockChannel = blockChannel
    bAllRecruit = b_AllRecruit
end

--设置禁言 、原因
function SetBan(content)
    endTime = content.endTime
    reason = content.reason
end

--设置翻译结果 @sStr源文本 @tStr翻译文本 @langIso翻译文本的语言编码
function SetTranslation(sStr, tStr, langIso)
    translationTable[sStr] = translationTable[sStr] or {}
    translationTable[sStr][langIso] = tStr

    event.Trigger(event.TRANSLATE_RSP, sStr, tStr, langIso)
end

function SetGMState(bool)
    GMTalkOnline = bool
end

function CleanRedEnvelopeInfos()
    redEnvelopeInfos = {}
end

function OnSCENE_DESTROY()
    worldMsg = {}
    guildMsg = {}
    territoryMsg = {}
    langMsg = {}
    privateMsg = {}
    sessionProp = {}
    recruitMsg = {}
    peekGameMsg = {}
    allianceR4R5Msg = {}
    channelDot = {
        [ENUM_CHANNEL.WORLD] = 0,
        [ENUM_CHANNEL.GUIDE] = 0,
        [ENUM_CHANNEL.TERRITORY] = 0,
        [ENUM_CHANNEL.LANG] = 0,
        [ENUM_CHANNEL.PRIVATE] = 0,
        [ENUM_CHANNEL.RECRUIT] = 0,
        [ENUM_CHANNEL.PEEKGAME] = 0,
        [ENUM_CHANNEL.R4R5] = 0,
    }
    --curLangChanneInfo = nil
    langChannelConfig = {}

    BlockPlayer = {}
    BlockChannel = 0x00
    bHideVIP = nil
    bAllRecruit = nil
    endTime = 0
    reason = ""

    pageState = enum_pState.lang
    curSessionData = {}
    isClose = true
    isInitPri = false

    todayGetRewardTimes = nil
    curWeekHelpTimes = nil
    nextReqTimes = nil
    flagID = nil
    reqTimes = nil
    helpInfo = {}
    redEnvelopeInfos = {}
    mgMsgNum = 0
    channelAtState = {
        [ENUM_CHANNEL.WORLD] = { new = false, pos = 0 },
        [ENUM_CHANNEL.GUIDE] = { new = false, pos = 0 },
        [ENUM_CHANNEL.TERRITORY] = { new = false, pos = 0 },
        [ENUM_CHANNEL.LANG] = { new = false, pos = 0 },
        [ENUM_CHANNEL.RECRUIT] = { new = false, pos = 0 },
        [ENUM_CHANNEL.PEEKGAME] = { new = false, pos = 0 },
        [ENUM_CHANNEL.R4R5] = { new = false, pos = 0 },
    }

    isAutoTranslation = {} --是否开启自动翻译功能（联盟模块）
    isReadLocalAutoTranslation = {} --是否已读取本地自动翻译设置
    isReceiveRedEnvelopeInfo = false
end
event.Register(event.SCENE_DESTROY, OnSCENE_DESTROY)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP, OnSCENE_DESTROY)

function OnReconnection()
    ------ --print("OnReconnection>>>>>>>>>>>>")
    local windowMgr = require "ui_window_mgr"
    if windowMgr:IsModuleShown("ui_chat_main_new") then
        local net_chat_module_new = require "net_chat_module_new"
        if curLangChanneInfo == nil then
            --net_chat_module_new.Req_ALLOT_CHANNEL()
        else
            --net_chat_module_new.Req_LANGUAGE_CHANNEL_MSG() --请求频道聊天数据
        end
        --net_chat_module_new.Req_PRIVATE_MSG()
    else
        OnSCENE_DESTROY()
    end
end
event.Register(event.CREATE_PLAYER_ENTITY, OnReconnection) --断线重连的情况

function GetTodayGetRewardTimes()
    return todayGetRewardTimes
end

function SetTodayGetRewardTimes(times)
    todayGetRewardTimes = times
end

function GetCurWeekHelpTimes()
    return curWeekHelpTimes
end

function SetCurWeekHelpTimes(times)
    curWeekHelpTimes = times
end

function GetNextReqTimes()
    return nextReqTimes
end

function SetNextReqTimes(time)
    nextReqTimes = time
end

function GetTodayHelpTimes()
    return todayHelpTimes
end

function SetTodayHelpTimes(times)
    todayHelpTimes = times
end

function GetFlagID()
    return flagID
end

function SetFlagID(id)
    flagID = id
end

function GetReqTimes()
    return reqTimes
end

function SetReqTimes(times)
    reqTimes = times
end

---设置同盟红包数据
---@param info table RedPacketInfo联盟中成员发红包信息 
function SetLeagueRedPacketInfo(info)
    isReceiveRedEnvelopeInfo = true
    if info then
        for k, v in ipairs(info) do
            redEnvelopeInfos[v.redPacketId] = v
        end
    end
    event.Trigger(event.CHINESE_NEWYEAR_REDPACKET_REFRESH_RED)
end

---获取所有红包信息
function GetRedPacketInfo()
    return redEnvelopeInfos
end

---根据红包id获取红包信息
---@param id number 红包id
function GetRedPacketInfoById(id)
    return redEnvelopeInfos[id]
end

---根据红包id移除红包
---@param id number 红包id
function RemoveRedPacketInfoById(id)
    if redEnvelopeInfos[id] then
        redEnvelopeInfos[id] = nil
    end
end

--[[--region 集结分享
---@deprecated 刷新需要删除的集结队伍数据
function SetMassTeamShareInfo(teamId)
    isReceiveMassTeamInfo = true
    massTeamDeleteShareInfo[teamId] = true
end

---@deprecated 获取集结队伍数据
function GetMassTeamShareInfo()
    return massTeamDeleteShareInfo
end

---@deprecated 根据id获取集结队伍数据
function GetMassTeamShareInfoById(id)
    return massTeamDeleteShareInfo[id]
end

---@deprecated 根据id移除集结队伍数据
function RemoveMassTeamShareInfoById(id)
    if massTeamDeleteShareInfo[id] then
        massTeamDeleteShareInfo[id] = nil
    end
end]]
--endregion

function SetLeagueHelpInfo(info)
    local addNew = true
    if info then
        for _, v in ipairs(info) do

            if v.bDel == nil or v.bDel ~= 1 then
                local temp = {
                    roleID = v.roleID,
                    prog = v.prog,
                    helpID = v.helpID,
                    cbtAll = v.cbtAll,
                    flagID = v.nflagID,
                }
                temp.helpRoleID = {}
                if v.helpRoleID then
                    for _, v in ipairs(v.helpRoleID) do
                        temp.helpRoleID[v] = true
                    end
                end
                if addNew and helpInfo[v.roleID] then
                    addNew = false
                end
                helpInfo[v.roleID] = temp
            elseif v.bDel == 1 then
                helpInfo[v.roleID] = nil
                addNew = true
            end

        end
        --dump(helpInfo)
    end
    if not addNew then
        TriggerChatEvent(ENUM_CHANNEL.GUIDE, nil, true)
    end
end

function GetMyHelpInfo()
    local selfRoleId = player_mgr.GetPlayerRoleID()
    return helpInfo[selfRoleId]
end

function GetHelpInfoByID(id)
    return helpInfo[id]
end

function HasUnreadHelpMsg()
    local msg = GetMsgByType(ENUM_CHANNEL.GUIDE)
    local selfRoleId = player_mgr.GetPlayerRoleID()
    local lastTime = chat_io_mgr.GetChannelReadTime(ENUM_CHANNEL.GUIDE)
    for _, v in ipairs(msg or {}) do
        if not IsPlayerChatTypeMsgBlocked(v.sType,v.roleid) then
            if v.roleid ~= selfRoleId and v.chatTime > lastTime and (pageState ~= enum_pState.guide or isClose) then
                if helpInfo[v.roleid] then
                    local cfg_help = game_scheme:LeagueHelp_0(helpInfo[v.roleid].helpID)
                    if helpInfo[v.roleid].flagID == v.leagueHelp.flagID and cfg_help and helpInfo[v.roleid].prog < cfg_help.helpNum then
                        return true
                    end
                end
            end
        end
    end
    return false
end

local function CheckDataIsUnread(data, selfRoleId, lastTime)
    if IsPlayerChatTypeMsgBlocked(data.sType,data.roleid) then
        return false
    end
    if data.roleid ~= selfRoleId and data.chatTime > lastTime then
        return true
    end
    return false
end

local function isCheckDataType(dataType, v)
    if not dataType then
        return true
    end
    return dataType == v.sType
end

---获取最新的未读数据
---@param channelType number 频道类型
---@param dataType number 数据类型
---@param count number 获取数量
---@param toRoleId number 目标角色id(获取某个角色私聊数据时要传)
function GetNewUnreadData(channelType, dataType, count, toRoleId)
    count = count or 1
    local tmpData = {}
    local msg = GetMsgByType(channelType)
    local selfRoleId = player_mgr.GetPlayerRoleID()
    local lastTime = chat_io_mgr.GetChannelReadTime(channelType)

    if channelType == ENUM_CHANNEL.PRIVATE and not toRoleId then
        for i, v in pairs(sessionProp) do
            if v.newNum > 0 and msg[tonumber(i)] then
                for _, n in ipairs(msg[tonumber(i)]) do
                    if isCheckDataType(dataType, n) and CheckDataIsUnread(n, selfRoleId, 0) then
                        table.insert(tmpData, n)
                        if #tmpData >= count then
                            return tmpData
                        end
                    end
                end
            end
        end
        for _, v in ipairs(msg or {}) do
            for m, n in pairs(v) do
                if isCheckDataType(dataType, n) and CheckDataIsUnread(n, selfRoleId, lastTime) then
                    table.insert(tmpData, n)
                    if #tmpData >= count then
                        return tmpData
                    end
                end
            end
        end
    else
        if toRoleId then
            msg = privateMsg[tonumber(toRoleId)]
        end
        for _, v in ipairs(msg or {}) do
            if isCheckDataType(dataType, v) and CheckDataIsUnread(v, selfRoleId, lastTime) then
                table.insert(tmpData, v)
                if #tmpData >= count then
                    return tmpData
                end
            end
        end
    end

    return #tmpData > 0 and tmpData or nil
end

--聊天气泡
function CanAskAndReceive()
    local net_login_module = require "net_login_module"
    local helpInfo = GetMyHelpInfo()
    local prog = 0
    local cfg_help = nil
    if helpInfo then
        cfg_help = game_scheme:LeagueHelp_0(helpInfo.helpID)
        prog = helpInfo.prog or 0
        if cfg_help then
            if prog < cfg_help.helpNum then
                if reqTimes and reqTimes == 0 then
                    return true
                elseif nextReqTimes and nextReqTimes - net_login_module.GetServerTime() <= 0 then
                    return true
                end
            else
                return false
            end
        end
    end
    return false
end

function CanHelp()
    local msg = GetMsgByType(ENUM_CHANNEL.GUIDE)
    local selfRoleId = player_mgr.GetPlayerRoleID()
    for _, v in ipairs(msg or {}) do
        if not IsPlayerChatTypeMsgBlocked(v.sType,v.roleid) then
            if v.roleid ~= selfRoleId then
                if v.leagueHelp and helpInfo[v.roleid] then
                    local cfg_help = game_scheme:LeagueHelp_0(helpInfo[v.roleid].helpID)
                    if helpInfo[v.roleid].flagID == v.leagueHelp.flagID and cfg_help and helpInfo[v.roleid].prog < cfg_help.helpNum and not helpInfo[v.roleid].helpRoleID[selfRoleId] then
                        return true
                    end
                end
            end
        end
    end
    return false
end

---是否有红包可以抢
------@return boolean true表示有
function HaveRedEnvelopeRob()
    for i, v in pairs(redEnvelopeInfos) do
        if v then
            if os.server_time() < v.expireTime and v.hadGet == 0 then
                --红包没过期并且还没领取
                return true
            end
        end
    end
    return false
end

---是否有红包道具可以发红包
------@return boolean true表示有
function HaveRedEnvelopeSend()
    local skep_mgr = require "skep_mgr"
    local redPacket = { skep_mgr.const_id.chineseRedEnvelope, skep_mgr.const_id.anniversaryRedPackage1,
                        skep_mgr.const_id.anniversaryRedPackage2, skep_mgr.const_id.anniversaryRedPackage3 }
    for i, v in ipairs(redPacket) do
        local entity = skep_mgr.GetGoodsEntity(v)
        if entity ~= nil then
            return true
        end
    end
    return false
end

---是否可以发送发红或者有红包可以抢
---@return boolean true表示有
function HaveRedEnvelope()
    return HaveRedEnvelopeRob() or HaveRedEnvelopeSend()
end

--是否开启自动翻译功能（联盟模块）
function SetIsAutoTranslation(value, key)
    key = key and key or ENUM_TRANSLATION_TYPE.ALLIANCE
    isAutoTranslation[key] = value
    local roleID = player_mgr.GetPlayerRoleID()
    PlayerPrefs.SetInt(roleID .. key, value and 0 or -1)
end

--获取是否开启自动翻译功能（联盟模块）
function GetIsAutoTranslation(key)
    key = key and key or ENUM_TRANSLATION_TYPE.ALLIANCE
    --国内版本屏蔽翻译
    if not util.ShouldUseCustomSDKUI() then
        return false
    end

    if not isReadLocalAutoTranslation[key] then
        isReadLocalAutoTranslation[key] = true
        local roleID = player_mgr.GetPlayerRoleID()
        --默认开启
        local isOpenInt = PlayerPrefs.GetInt(roleID .. key, 0)
        isAutoTranslation[key] = isOpenInt == 0
    end
    return isAutoTranslation[key]
end

--- 是否可以自动翻译
function IsCanAutoTranslation(par_pState)
    local tmpData = TryGetPStateData(par_pState or pageState)
    if tmpData and tmpData.e_translateType and CheckPStateDataLimitFunc(tmpData.limitFunc) then
        return GetIsAutoTranslation(tmpData.e_translateType[1])
    end
    return false
end

function TryGetPStateData(pState)
    if E_pStateDataMap[pState] then
        return E_pStateDataMap[pState]
    end
    return nil
end

function CheckPStateDataLimitFunc(limitFunc)
    local isPass = true
    if limitFunc then
        for i, v in pairs(limitFunc) do
            if v then
                isPass = v()
                if not isPass then
                    break
                end
            end

        end
    end
    return isPass
end

--- 是否可以发送世界、跨服聊天
function CanSendChatWorld(showTips)
    -- 服务器按照和开服时间差来计算天数
    local openSerTime = player_mgr.GetRoleOpenSvrTime()
    local curTime = os.server_time()
    local difftime = curTime - openSerTime
    local vipLevel = player_mgr.GetPlayerVipLevel()
    local vipcfg = game_scheme:InitBattleProp_0(708).szParam.data[0]
    local cfg = nil
    if vipLevel >= vipcfg then
        cfg = game_scheme:InitBattleProp_0(707).szParam.data
    else
        cfg = game_scheme:InitBattleProp_0(705).szParam.data
    end
    local paramIdx = 2
    if difftime < 2592000 then
        -- 30天内
        paramIdx = 0
        -- 60天内
    elseif difftime < 5184000 then
        paramIdx = 1
    end
    local id = cfg[paramIdx] or 0
    local laymain_data = require "laymain_data"
    local passLevel = laymain_data.GetPassLevel()
    local enableSendChat = (passLevel >= id)

    if showTips and not enableSendChat then
        local lang = require "lang"
        local flow_text = require "flow_text"
        local hookLevelCfg = game_scheme:HookLevel_0(id)
        if hookLevelCfg then
            flow_text.Add(string.format(lang.Get(100450 + paramIdx), hookLevelCfg.Name))
        end
    end

    return enableSendChat
end

--是否可以私聊
--needFlowText是否需要飘字
function CanSendPrivateChat(needFlowText)
    local privateChatHookLevel = nil
    local cfg = game_scheme:InitBattleProp_0(306).szParam.data
    if cfg then
        local id = cfg[1]
        local hookLevelCfg = game_scheme:HookLevel_0(id)
        if hookLevelCfg then
            privateChatHookLevel = hookLevelCfg.checkPointID
            local laymain_data = require "laymain_data"
            local passLevel = laymain_data.GetPassLevel()
            if needFlowText and privateChatHookLevel >= passLevel then
                local flow_text = require "flow_text"
                local lang = require "lang"
                flow_text.Add(string.format(lang.Get(100450), hookLevelCfg.Name))
            end
            return privateChatHookLevel <= passLevel
        end
    end
    return true
end

function GuidChatDataInit()
    if isWaitForGuideData then
        --插入本地数据
        local personalAllMsg = chat_io_mgr.GetPresonalChat()
        -- local sociaty_data = require "sociaty_data"
        -- local baseData = sociaty_data.GetLeagueData()
        -- local sociatyName = baseData and baseData.strName or ""
        -- local sociatyID = baseData and baseData.id
        for key, _data in pairs(personalAllMsg) do
            if _data.channel == chat_pb.Channel_Guild or _data.channel == chat_pb.Channel_Territory then
                ------ print(396,"联盟 >>>>> ",sociatyID,"/",_data["msg"].guildid,sociatyName,"/",_data["msg"].guildname)
                -- if _data["msg"] and (_data["msg"].guildid == sociatyID or _data["msg"].guildname == sociatyName) then
                --     AddChatNewMsg(_data)
                -- end
            end
        end
        table.sort(guildMsg, function(value1, value2)
            return value1.chatTime < value2.chatTime
        end)
        isWaitForGuideData = false
    end
end
event.Register(event.UPDATE_SOCIATY_BASEDATA, GuidChatDataInit)

--- 屏蔽状态
function GetChannelBlock(channelType)
    local block = GetBlock()
    return block[channelType]
end

--- 获取今日紧急公告次数
function GetUrgentAnnounceNum()
    return urgentAnnounceNum
end

--- 增加今日紧急公告次数
function AddUrgentAnnounceNum(msg)
    urgentAnnounceNum = urgentAnnounceNum + 1
    urgentAnnounceTime = msg.chatTime
end

--- 获取今日紧急公告剩余次数
function GetUrgentRemainingNum()
    local urgentCountCur = urgentAnnounceNum
    local constCfg = game_scheme:InitBattleProp_0(8214) --紧急公告配置
    if constCfg and constCfg.szParam.data and #constCfg.szParam.data > 0 then
        local urgentCountMax = constCfg.szParam.data[0]
        return urgentCountMax - urgentCountCur
    else
        return 0
    end
end

--- 获取上次发布紧急公告时间
function GetUrgentAnnounceTime()
    return urgentAnnounceTime
end

---展示紧急公告倒计时提示
function ShowUrgentAnnounceTimeCountDown()
    local constCfg = game_scheme:InitBattleProp_0(8214) --紧急公告配置
    if urgentAnnounceTime>0 and  constCfg and constCfg.szParam.data and #constCfg.szParam.data > 0 then
        local offsetTime=urgentAnnounceTime+constCfg.szParam.data[1] * 60-time_util.GetServerTime_GW()
        if offsetTime > 0 then
            local countStr=time_util.FormatTime5(math.round(offsetTime))
            flow_text.Add(string.format2(lang.Get(130131),countStr))
        end
    else
        flow_text.Add(lang.Get(670026))
    end
end

---获取最新的公告
function GetNewAllianceNotice()
    local length = util.get_len(guildMsg)
    local sType = nil
    for i = length, 1, -1 do
        sType = guildMsg[i].sType
        if sType == mq_common_pb.enSpeak_Announcement or sType == mq_common_pb.enSpeak_UrgentAnnouncement then
            return guildMsg[i]
        end
    end
    return nil
end

---根据频道类型和发言类型，获取对应的数据
function GetMsgByChannelAndSpeakType(eChannel, eSpeak)
    local msg = GetMsgByType(eChannel)
    local result = {}
    for _, v in ipairs(msg) do
        if v.sType == eSpeak then
            table.insert(result, v)
        end
    end
    return result
end

---获取是否展示聊天时间
function GetIsShowChatTime(szChatID)
    if isShowChatTimeMap[szChatID] then
        return true
    end
    return false
end

--获取集结大作战信息
function GetGatheringChatMsg(extendinfopb)
    if not extendinfopb then
        return {}
    end
    local activity_pb = require "activity_pb"
    local pbMsg = activity_pb.TGatheringChatPushinfo()
    pbMsg:ParseFromString(extendinfopb)
    if not pbMsg then
        log.Error("GetAllianceTrainMsg 有问题 反序列化失败", pbMsg, ", extendinfopb: ", extendinfopb)
    end
    return pbMsg
end

function GetAllianceTrainMsg(extendinfopb)
    if not extendinfopb then
        return {}
    end
    local allianceTrain_pb = require "allianceTrain_pb"
    local pbMsg = allianceTrain_pb.TPbAllianceTrainChat()
    pbMsg:ParseFromString(extendinfopb)
    if not pbMsg then
        log.Error("GetAllianceTrainMsg 有问题 反序列化失败", pbMsg, ", extendinfopb: ", extendinfopb)
    end
    return pbMsg
end

-- 丧尸灾变卡片类
function GetZombieApocalypseCard(extendinfopb)
    if not extendinfopb then
        return {}
    end
    local zombieApocalypse_pb = require "ZombieApocalypse_pb"
    local pbMsg = zombieApocalypse_pb.TZombieApocalypseCardChat()
    pbMsg:ParseFromString(extendinfopb)
    if not pbMsg then
        log.Error("GetAllianceTrainMsg 有问题 反序列化失败", pbMsg, ", extendinfopb: ", extendinfopb)
    end
    return pbMsg
end

-- 丧尸灾变列表类
function GetZombieApocalypseList(extendinfopb)
    if not extendinfopb then
        return {}
    end
    local zombieApocalypse_pb = require "ZombieApocalypse_pb"
    local pbMsg = zombieApocalypse_pb.TZombieApocalypseListChat()
    pbMsg:ParseFromString(extendinfopb)
    if not pbMsg then
        log.Error("GetAllianceTrainMsg 有问题 反序列化失败", pbMsg, ", extendinfopb: ", extendinfopb)
    end
    return pbMsg
end

--丧尸灾变详情类
function GetZombieApocalypseDetail(extendinfopb)
    if not extendinfopb then
        return {}
    end
    local zombieApocalypse_pb = require "ZombieApocalypse_pb"
    local pbMsg = zombieApocalypse_pb.TZombieApocalypseDetailChat()
    pbMsg:ParseFromString(extendinfopb)
    if not pbMsg then
        log.Error("GetAllianceTrainMsg 有问题 反序列化失败", pbMsg, ", extendinfopb: ", extendinfopb)
    end
    return pbMsg
end

-- 酒馆藏宝图碎片信息
function GetTavernTreasureMsg(extendinfopb)
    if not extendinfopb then
        return {}
    end
    local activity_pb = require "activity_pb"
    local pbMsg = activity_pb.SwapOrderData()
    pbMsg:ParseFromString(extendinfopb)
    if not pbMsg then
        log.Error("GetAllianceTrainMsg 有问题 反序列化失败", pbMsg, ", extendinfopb: ", extendinfopb)
    end
    return pbMsg
end

-- 联盟信息
function GetAllianceShareMsg(extendinfopb)
    if not extendinfopb then
        return {}
    end
    local alliance_pb = require "alliance_pb"
    local pbMsg = alliance_pb.TAllianceBase()
    pbMsg:ParseFromString(extendinfopb)
    if not pbMsg then
        log.Error("GetAllianceTrainMsg 有问题 反序列化失败", pbMsg, ", extendinfopb: ", extendinfopb)
    end
    return pbMsg
end



-- 战报信息
function GetChatShareBattleReport(extendinfopb)
        if not extendinfopb then
            return {}
        end
       local pbMsg = chat_pb.ChatShareBattleReport()
        pbMsg:ParseFromString(extendinfopb)
        if not pbMsg then
            log.Error("GetAllianceTrainMsg 有问题 反序列化失败", pbMsg, ", extendinfopb: ", extendinfopb)
        end
        return pbMsg
end

--私聊聊天其他人头相信息
function GetChatRoleInfoMsg(extendinfopb)
    if not extendinfopb then
        return {}
    end
    local common_new_pb = require "common_new_pb"
    local pbMsg = common_new_pb.PlayerBasicInfo()
    pbMsg:ParseFromString(extendinfopb)
    if not pbMsg then
        log.Error("GetChatRoleInfoMsg 有问题 反序列化失败", pbMsg, ", extendinfopb: ", extendinfopb)
    end
    return pbMsg
end


function IsGM(msgData)
    return msgData.roleid == GMConstRoldId
end

function GetGMName()
    return util.SetColor(util.EnumColor.Red_Dark2, lang.Get(938))
end

function GetMsgName(msgData)
    if msgData then
        if IsGM(msgData) then
            return GetGMName()
        else
            return msgData.name
        end
    end
end

function AddTempChatMsg(channel, context, sType,languageId) 
    local tempMsg = CreateTempNormalChatMsg(channel, context, sType,languageId)
    --添加到临时消息列表中，用于与后端做判断
    --tempMsgs[tempMsg.msg.chatTime] = tempMsg

    local chat_mgr_pro = require "chat_mgr_pro"
    if chat_mgr_pro.GetIsUseChatPro() and  chat_mgr_pro.GetContentPro() then
        chat_mgr_pro.GetContentPro():AddTempChatMsg(tempMsg.msg,1)
    end


end

--组装临时聊天数据，用于服务器未返回时，客户端先显示出来，避免延迟，然后等待后端回复之后再将其替换掉
function CreateTempNormalChatMsg(channel, context, sType,languageId)
    --临时数据
    local tempMsg = {}
    tempMsg.channel = channel
    tempMsg.msg = {}
    tempMsg.msg.roleid = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleID)
    tempMsg.msg.faceId = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FaceID)


    tempMsg.msg.name = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleName)

    tempMsg.msg.roleLv = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleLevel)
    --tempMsg.msg.bHideVip = 
    tempMsg.msg.nVipLv = player_mgr.GetPlayerVipLevel()
    tempMsg.msg.ce = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RolePower);
    tempMsg.msg.chatTime = os.server_time()
    tempMsg.msg.context = context
    tempMsg.msg.avatarFrame = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FrameID)
    tempMsg.msg.titleID = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.TitleID)
    tempMsg.msg.sex = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleSex)
    tempMsg.msg.channelId = channel
    tempMsg.msg.langtype = languageId
    tempMsg.msg.deviceLangType=ui_setting_data.GetDevicePlatformLangId()
    tempMsg.msg.sType = sType
    tempMsg.msg.worldid = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.WorldId)
   local alliance_mgr = require "alliance_mgr"
    tempMsg.msg.leagueShortName=  alliance_mgr.GetUserAllianceShortName()	--联盟简称

    local customHeadData = custom_avatar_data.GetMyAvatar()
    local faceStr = player_mgr.GetRoleFaceID()
    if customHeadData then
        faceStr = customHeadData.remoteUrl
    end
    tempMsg.msg.faceStr = faceStr
    return tempMsg
end