---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by <PERSON><PERSON><PERSON>.
--- DateTime: 2025/1/3 16:10
local require = require
local pairs = pairs
local function_open_mgr = require "function_open_mgr"
local ui_window_mgr = require "ui_window_mgr"
local event = require "event"
local mq_common_pb = require "mq_common_pb"
local chat_pb = require "chat_pb"
local ui_chat_data_gw = require "ui_chat_data_gw"
local chat_mgr_new = require "chat_mgr_new"
local function_open = require "function_open"
local flow_text = require "flow_text"
local red_const = require "red_const"
local red_system = require "red_system"

---@class ui_chat_mgr_gw
local M = {}
local ePageState = chat_mgr_new.enum_pState
local eChannel = chat_mgr_new.ENUM_CHANNEL
local redDotCheck = {
    [red_const.Enum.ChatSelfServer] = eChannel.WORLD,
    [red_const.Enum.ChatLanguage] = eChannel.LANG,
    [red_const.Enum.ChatAlliance] = eChannel.GUIDE,
    [red_const.Enum.ChatR4R5] = eChannel.R4R5,
}

function M.Init()
    event.Register(event.UPDATE_CHAT_MSG, M.RefreshMailChatContent_NewChatMsgNTF_Handler)
    event.Register(event.UPDATE_CHANNEL_DOT, M.UpdateChannelDot)
    event.Register(event.LANGUAGE_SETTING_CHANGED, M.LanguageTypeChangedHandler)
    --主界面红点
    red_system.RegisterRedFunc(red_const.Enum.ChatMain, M.GetChatMainRedCount)

    red_system.RegisterRedFunc(red_const.Enum.ChatWorld, M.GetChatWorldRedCount)
    red_system.RegisterRedFunc(red_const.Enum.ChatSelfServer, M.GetChatSelfServerRedCount)
    red_system.RegisterRedFunc(red_const.Enum.ChatLanguage, M.GetChatLanguageRedCount)
    red_system.RegisterRedFunc(red_const.Enum.ChatAllianceMain, M.GetChatAllianceRedCount)
    red_system.RegisterRedFunc(red_const.Enum.ChatAlliance, M.GetChatAllianceRedCount)
    red_system.RegisterRedFunc(red_const.Enum.ChatR4R5, M.GetChatR4R5RedCount)
    event.Register(event.CHAT_SEND_SUCCESSFULLY,M.CHAT_SEND_SUCCESSFULLY_Handler)

end

--尝试发布紧急公告
function M.TryReleaseUrgentNotice(content)
    if not ui_chat_data_gw.CheckUrgentCanRelease() then
        return
    end
    --发布公告，调用聊天发言
    local net_chat_module_new = require "net_chat_module_new"
    net_chat_module_new.Send_CHAT_SPEAK(chat_pb.Channel_Guild, mq_common_pb.enSpeak_UrgentAnnouncement, content)
end


--点击主界面聊天框
function M.OnMailChatContentClick()
    --根据当前存储的主界面页签数据，展示相应的聊天界面数据
    local curMainChatPageState = ui_chat_data_gw.GetCurMainPageState()
    M.ShowChatMainPanel(curMainChatPageState)
end

--聊天主界面聊天框刷新函数（监听新聊天数据下推）
function M.RefreshMailChatContent_NewChatMsgNTF_Handler(evtName, channelType, singleMsg, isHelp)
    if ui_chat_data_gw.IsCurMainChatChanel(channelType) then
        ui_chat_data_gw.RefreshMainContent()
    end
end

--展示聊天界面
function M.ShowChatMainPanel(pState, cSession, isCls, isTri)
    local isOpen = function_open.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.Chat)
    if not isOpen then
        flow_text = flow_text.Add("聊天未解锁")
        return
    end
    chat_mgr_new.SetPageState(pState, cSession, isCls, isTri)
    if not ui_window_mgr:IsModuleShown("ui_chat_main_new") then
        ui_window_mgr:ShowModule("ui_chat_main_new")
    end
end

--获取红点数量方法

---更新频道红点
function M.UpdateChannelDot()
    for i, v in pairs(redDotCheck) do
        red_system.TriggerRed(i)
    end
end

--主界面红点数量获取--策划修改为：时间联盟和私聊红点的总和
function M.GetChatMainRedCount()
    --获取当前主界面的
    local totalRedNum = M.GetChatWorldRedCount() + M.GetChatAllianceMainRedCount() + M.GetChatPrivateRedCount()
    return totalRedNum
    -- local curMainChatPageState = ui_chat_data_gw.GetCurMainPageState()
    -- if curMainChatPageState == ePageState.world then
    --     return M.GetChatWorldRedCount()
    -- elseif curMainChatPageState == ePageState.guide then
    --     return M.GetChatAllianceMainRedCount()
    -- end
end

--获取世界频道的红点数量
function M.GetChatWorldRedCount()
    return M.GetChatLanguageRedCount() + M.GetChatSelfServerRedCount()
end

--获取本服频道的红点数量
function M.GetChatSelfServerRedCount()
    local channelDot = chat_mgr_new.GetChannelDotProp()
    return channelDot[eChannel.WORLD]
end

--获取语言频道的红点数量
function M.GetChatLanguageRedCount()
    local channelDot = chat_mgr_new.GetChannelDotProp()
    return channelDot[eChannel.LANG]
end

--获取私聊频道的红点数量
function M.GetChatPrivateRedCount()
    local channelDot = chat_mgr_new.GetChannelDotProp()
    return channelDot[eChannel.PRIVATE]
end

--联盟频道红点数量获取
function M.GetChatAllianceMainRedCount()
    local alliance_mgr = require "alliance_mgr"
    if alliance_mgr.IsSelfR4R5() then
        return M.GetChatAllianceRedCount() + M.GetChatR4R5RedCount()
    end
    return M.GetChatAllianceRedCount()
end

--获取联盟频道的红点数量
function M.GetChatAllianceRedCount()
    local channelDot = chat_mgr_new.GetChannelDotProp()
    return channelDot[eChannel.GUIDE]
end

--获取R4R5频道的红点数量
function M.GetChatR4R5RedCount()
    local channelDot = chat_mgr_new.GetChannelDotProp()
    return channelDot[eChannel.R4R5]
end

--设置聊天界面的顶部提示数据
--data={
--    txtInfo = "这是一个测试的文本",
--    clickFunc=nil,
--    closeFuncCB=nil,
--}
function M.SetChatTopTipsData(data)
    ui_chat_data_gw.SetChatTopTipsData(data)
end

function M.SetChatTopTipsHide()
    ui_chat_data_gw.SetChatTopTipsDataShow(false)
    event.Trigger(event.CHAT_TOP_TIPS_HIDE)
end

---监听语言切换
function M.LanguageTypeChangedHandler()
    local net_chat_module_new = require "net_chat_module_new"
    local _, __, ___, LanguageId = net_chat_module_new.GetRoleInfo()
    --重新请求语言频道数据
    net_chat_module_new.C2S_TMSG_CHAT_LANGUAGE_MSG_REQ_Handler(LanguageId)
    ui_chat_data_gw.RefreshMainContent()
end

--联盟聊天公告，紧急公告打点
function M.CHAT_SEND_SUCCESSFULLY_Handler(evt,msg)
    if not msg or msg.err~=0 or (msg.sType~=mq_common_pb.enSpeak_Announcement and msg.sType~=mq_common_pb.enSpeak_UrgentAnnouncement) then
        return
    end
    local AllianceMgr = require "alliance_mgr"
    
    local announcementType=msg.sType==mq_common_pb.enSpeak_Announcement and 1 or 2
    local Member_Level= AllianceMgr.GetPlayerAuthority()

    event.EventReport("league_ChatAnnouncement", { AnnouncementType = announcementType, Member_Level = Member_Level, Robot = 0 })--打点-	查看玩家战报分享的情况
end

return M