﻿--region FileHead
--- ui_bs_buildbar.txt
-- author:  author
-- ver:     1.0
-- desc:    
-------------------------------------------------
--endregion 

--region Require
local require = require
local type = type
local pairs = pairs
local string = string
local tostring = tostring
local Common_Util = CS.Common_Util.UIUtil
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local GWConst = require "gw_const"
local gw_home_card_sprite_asset_mgr = require "gw_home_card_sprite_asset_mgr"
local red_const = require "red_const"
local enum_define = require "enum_define"
local class = require "class"
local ui_base = require "ui_base"
local module_scroll_list = require "scroll_list"
local lang = require "lang"
local game_scheme = require "game_scheme"
local card_assets = require "card_sprite_asset"
local tonumber = tonumber
local RectTransform = CS.UnityEngine.RectTransform
local UIUtil = UIUtil
local util = require "util"
local GWAssetMgr = require "gw_asset_mgr"
local GWG = GWG
--endregion 

--region ModuleDeclare
module("ui_bs_buildbar")
local ui_path = "ui/prefabs/gw/buildsystem/uibsbuildbar.prefab"
local window = nil
local UIView = {}
local spriteAsset = nil;
local gwhomebuildingiconAsset = nil;
local scrollToIndex = nil
--endregion 

--region WidgetTable
UIView.widget_table = {
    ScrollRect = { path = 'panel/ScrollRect', type = "ScrollRect" },
    rect_table = { path = 'panel/ScrollRect/Mask/List', type = ScrollRectTable },
    rect_table_tran = { path = 'panel/ScrollRect/Mask/List', type = RectTransform },

    TogEco = { path = "panel/BtnGroup/TogEco", type = "Toggle", value_changed_event = "OnTogEcoValueChanged" },
    TogEcoTex = { path = "panel/BtnGroup/TogEco/Text", type = "Text" },
    TogEcoNotSelectTex = { path = "panel/BtnGroup/TogEco/notSelectText", type = "Text" },
    TogAffair = { path = "panel/BtnGroup/TogAffair", type = "Toggle", value_changed_event = "OnTogAffairValueChanged" },
    TogAffairTex = { path = "panel/BtnGroup/TogAffair/Text", type = "Text" },
    TogAffairNotSelectTex = { path = "panel/BtnGroup/TogAffair/notSelectText", type = "Text" },
    TogDecor = { path = "panel/BtnGroup/TogDecor", type = "Toggle", value_changed_event = "OnTogDecorValueChanged" },
    TogDecorTex = { path = "panel/BtnGroup/TogDecor/Text", type = "Text" },
    TogDecorNotSelectTex = { path = "panel/BtnGroup/TogDecor/notSelectText", type = "Text" },

    CloseBg = { path = "panel/close", type = "Button", event_name = "OnCloseBg" },
}
--endregion 

--function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end
--endregion 

--region WindowInit
--[[窗口初始化]]
function UIView:Init()
    if not gwhomebuildingiconAsset then
        gwhomebuildingiconAsset = gw_home_card_sprite_asset_mgr.GetOrCreateCardSpriteAsset("gwhomebuildingicon")
    end
    self:BindUIRed(self.TogEco.transform, red_const.Enum.BuildListTogEco, nil, { redPath = red_const.Type.Num })
    self:BindUIRed(self.TogAffair.transform, red_const.Enum.BuildListTogAffair, nil, { redPath = red_const.Type.Num })
    self:BindUIRed(self.TogDecor.transform, red_const.Enum.BuildListTogDecor, nil, { redPath = red_const.Type.Num })
    self:SetVCTypeUI()
    self.__base:Init(self)
    self:SubscribeEvents()
    self:InitScrollRectTable()
    spriteAsset = spriteAsset or card_assets.CreateSpriteAsset()
    --region User
    --endregion 
end --///<<< function

--endregion 


--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    self.__base:OnShow()
    local force_guide_system = require "force_guide_system"
    local force_guide_event = require "force_guide_event"
    local guideId = force_guide_system.GetCurStep()
    if guideId and (guideId == 827 or guideId == 886) then
        if not util.IsObjNull(self.ScrollRect) then
            self.ScrollRect.horizontal = false
        end
        force_guide_system.TriEnterEvent(force_guide_event.tEventBuildPlaneClick)
    else
        if not util.IsObjNull(self.ScrollRect) then
            self.ScrollRect.horizontal = true
        end
    end
end --///<<< function

--endregion 

--region WindowOnHide
--[[界面隐藏时调用]]
function UIView:OnHide()
    self.__base:OnHide()
end --///<<< function

--endregion 


--region WindowClose
function UIView:Close()
    scrollToIndex = nil
    gwhomebuildingiconAsset = nil
    self.__base:Close()
    window = nil
    if spriteAsset then
        spriteAsset:Dispose()
        spriteAsset = nil
    end
    --region User
    --endregion 
end --///<<< function
--endregion 

function UIView:SubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了    
end

---********************功能函数区**********---
function UIView:InitView(initType)
    --默认进入经济
    if initType == GWConst.BuildType.Military then
        self.TogAffair.isOn = true
    elseif initType == GWConst.BuildType.Decoration then
        self.TogDecor.isOn = true
    else
        self.TogEco.isOn = true
    end

    self.TogEcoTex.text = lang.Get(601241)
    self.TogEcoNotSelectTex.text = self.TogEcoTex.text
    self.TogAffairTex.text = lang.Get(601242)
    self.TogAffairNotSelectTex.text = self.TogAffairTex.text
    self.TogDecorTex.text = lang.Get(601243)
    self.TogDecorNotSelectTex.text = self.TogDecorTex.text
end
function UIView:ScrollToPosition(index)
    scrollToIndex = index
    self.rect_table:ScrollTo(index)
end

function UIView:UpdateToggleList(data, value, type)
    if type == 1 then
        UIUtil.SetActive(self.TogEcoTex, true)
        UIUtil.SetActive(self.TogEcoNotSelectTex, false)
        UIUtil.SetActive(self.TogAffairTex, false)
        UIUtil.SetActive(self.TogAffairNotSelectTex, true)
        UIUtil.SetActive(self.TogDecorTex, false)
        UIUtil.SetActive(self.TogDecorNotSelectTex, true)
    elseif type == 2 then
        UIUtil.SetActive(self.TogEcoTex, false)
        UIUtil.SetActive(self.TogEcoNotSelectTex, true)
        UIUtil.SetActive(self.TogAffairTex, true)
        UIUtil.SetActive(self.TogAffairNotSelectTex, false)
        UIUtil.SetActive(self.TogDecorTex, false)
        UIUtil.SetActive(self.TogDecorNotSelectTex, true)
    elseif type == 3 then
        UIUtil.SetActive(self.TogEcoTex, false)
        UIUtil.SetActive(self.TogEcoNotSelectTex, true)
        UIUtil.SetActive(self.TogAffairTex, false)
        UIUtil.SetActive(self.TogAffairNotSelectTex, true)
        UIUtil.SetActive(self.TogDecorTex, true)
        UIUtil.SetActive(self.TogDecorNotSelectTex, false)
    end

    if value then
        if not data then
            self.rect_table.pageSize = 0
            self.rect_table.data = nil
            self.rect_table:Refresh(-1, -1)
            return
        end

        -- 装饰建筑只有获得后才显示
        local filter_data = data
        if type == 3 then
            filter_data = {}
            for i = 1, #data do
                local unlock = GWG.GWHomeMgr.buildingData.IsDecorateBuildUnlock(data[i].buildId, data[i].type)
                if unlock then
                    filter_data[#filter_data + 1] = data[i]
                end
            end
        end

        self.rect_table.pageSize = #filter_data
        self.rect_table.renderPerFrames = #filter_data
        self.rect_table.data = filter_data
        self.rect_table:Refresh(-1, -1)
    end
end

--初始化列表
function UIView:InitScrollRectTable()
    --如果需要可以设置动画
    --local scrollAnim = require "scroll_rect_anim"
    --scrollAnim.SetAnimCfg(self.UIModuleName, self.rect_table, OnItemRender, nil, 0.1, 0.2, true, 0, true, 3, true, 0.9)
    --self.rect_table.onItemRender = scrollAnim.OnRenderItem

    self.rect_table.onItemRender = OnItemRender
    --一定在UI 销毁时处理rect_table的Dispose  eg： self.rect_table:ItemsDispose()
    self.rect_table.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            --rect_table Dispose时 Item上相关的资源是否需要Dispose           
        end
    end
end

function OnItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    --开始查找item 内部的属性 并进行相应的设置
    local SkipBtn = scroll_rect_item:Get("SkipBtn")
    local ImgName = scroll_rect_item:Get("ImgName")
    local FuncTex = scroll_rect_item:Get("FuncTex")
    local Info = scroll_rect_item:Get("Info")
    local Consume = scroll_rect_item:Get("Consume")
    local time = scroll_rect_item:Get("time")
    local build = scroll_rect_item:Get("build")
    local InfoTex = scroll_rect_item:Get("InfoTex")
    local IronTex = scroll_rect_item:Get("IronTex")
    local BreadTex = scroll_rect_item:Get("BreadTex")
    local Seconds = scroll_rect_item:Get("seconds")
    local Count = scroll_rect_item:Get("count")
    local expandSwitch = scroll_rect_item:Get("switch")
    local new = scroll_rect_item:Get("new")
    local IconIcon = scroll_rect_item:Get("IconIcon")
    local breadIcon = scroll_rect_item:Get("breadIcon")
    local finger = scroll_rect_item:Get("finger")
    
    --装饰物
    local decorateInfo = scroll_rect_item:Get("DecorateInfo")
    local decorateLevelTxt = scroll_rect_item:Get("DecorateLevel")
    local decorateNameTxt = scroll_rect_item:Get("DecorateName")
    local decoratePropTxt = scroll_rect_item:Get("DecorateProp")
    local decoratePropValTxt = scroll_rect_item:Get("DecoratePropVal")
    local decorateLevelOutline = scroll_rect_item:Get("DecorateLevelOutline")
    local decorateNameOutline = scroll_rect_item:Get("DecorateNameOutline")
    local matGo = Consume.transform:Find("material").gameObject
    
    Common_Util.SetActive(decorateInfo, false)
    Common_Util.SetActive(FuncTex.gameObject,true)
    Common_Util.SetActive(ImgName.gameObject,true)
    Common_Util.SetActive(matGo,true)
    
    if scrollToIndex and scrollToIndex == index then
        Common_Util.SetActive(finger, true)
    else
        Common_Util.SetActive(finger, false)
    end

    GWAssetMgr:LoadGoodsIcon(200036, function(sprite)
        IconIcon.sprite = sprite
    end)
    GWAssetMgr:LoadGoodsIcon(200035, function(sprite)
        breadIcon.sprite = sprite
    end)
    local Cfg_Build_Type = game_scheme:BuildingType_0(dataItem.type)
    local Cfg_Build = game_scheme:Building_0(dataItem.buildId, 1)
    local Arr = string.split(Cfg_Build_Type.buildable, ";")
    local nextBuild, preBuildLevel
    for _, v in pairs(Arr) do
        local arr = string.split(v, "#")
        if tonumber(arr[1]) > dataItem.level then
            preBuildLevel = tonumber(arr[1])
            nextBuild = tonumber(arr[2]) --下一级建造数量
            break
        end
    end
    SkipBtn.onClick:RemoveAllListeners()
    if gwhomebuildingiconAsset then
        gwhomebuildingiconAsset:GetSprite(Cfg_Build.BuildIcon, function(sp)
            scroll_rect_item:Get("Image").sprite = sp
        end)        
    end

    ImgName.text = lang.Get(Cfg_Build_Type.NameID)
    FuncTex.text = lang.Get(Cfg_Build_Type.BuildingDes)
    time.text = lang.Get(602029)
    build.text = lang.Get(602030)
    Common_Util.SetActive(new, dataItem.isNew)

    if dataItem.maxBuild and dataItem.curBuild < dataItem.maxBuild then
        --当前建造数量 小于 可建造数量 则可建造
        Common_Util.SetActive(Info, false)
        Common_Util.SetActive(Consume, true)
        IronTex.text = Cfg_Build.iron
        BreadTex.text = Cfg_Build.food
        Common_Util.SetText(Seconds, util.FormatTime4(Cfg_Build.time))
        Count.text = dataItem.curBuild .. "/" .. dataItem.maxBuild
        expandSwitch:Switch(0)
        --Common_Util.SetColor(FuncTex, "#25385B") 
        Common_Util.SetColor(IronTex, Cfg_Build.iron <= dataItem.iron and "#FFFFFF" or "#e45763")
        Common_Util.SetColor(BreadTex, Cfg_Build.food <= dataItem.food and "#FFFFFF" or "#e45763")
    else
        if Cfg_Build_Type.BuildingSubtype ~= 3 then
            Common_Util.SetActive(Info, true)
            Common_Util.SetActive(Consume, false)
            local preBuildName = lang.Get(game_scheme:BuildingType_0(Cfg_Build_Type.Preconditions).NameID)
            if nextBuild and preBuildLevel then
                if nextBuild == 1 then
                    InfoTex.text = string.format2(lang.Get(601286), preBuildName, preBuildLevel)
                else
                    InfoTex.text = string.format2(lang.Get(601244), preBuildName, preBuildLevel, nextBuild)
                end
            else
                InfoTex.text = lang.Get(601289) --下一级可建造数量为nil，说明已达上限
            end
            expandSwitch:Switch(1)
        else
            Common_Util.SetActive(Info, true)
            InfoTex.text = lang.Get(601289) --下一级可建造数量为nil，说明已达上限
            Common_Util.SetActive(Consume, false)
            expandSwitch:Switch(1)
        end
    end

    if dataItem.mapId and (dataItem.curBuild <= 0 or dataItem.buildLevel <= 0) then
        local fixCfgData = game_scheme:BuildMaincityMap_0(dataItem.mapId)
        local fixCondition = fixCfgData.FixCondition
        local fixLangId = fixCfgData.FixDes
        local res = string.split(fixCondition, "#")
        local type = tonumber(res[1])

        if type == 1 then
            preBuildLevel = tonumber(res[3])
            preBuildType = game_scheme:Building_0(tonumber(res[2]), tonumber(res[3])).TypeID
            preBuildName = lang.Get(game_scheme:BuildingType_0(preBuildType).NameID)
            InfoTex.text = string.format2(lang.Get(fixLangId), preBuildName, preBuildLevel)
        end
        if type == 2 then
            InfoTex.text = string.format2(lang.Get(fixLangId), res[2])
        end
        if type == 4 then
            InfoTex.text = lang.Get(fixLangId)
        end

        SkipBtn.onClick:AddListener(
                function()
                    dataItem["module"](index, dataItem)
                end
        )
        return
    end
    SkipBtn.onClick:AddListener(
            function()
                dataItem["module"](index, dataItem)
            end
    )

    --稀有度处理
    --子类型 ==3 装饰建筑
    --绿蓝紫橙
    local rarity_color = {
        [1] = "#ABFF34",
        [2] = "#50ECFF",
        [3] = "#D585FF",
        [4] = "#FFB239",
    }
    
    local outline_rarity_color = 
    {
        [1] = {r = 39/255, g = 82/255, b = 18/255, a = 1},
        [2] =  {r = 0/255, g = 38/255, b = 95/255, a = 1},
        [3] =  {r = 53/255, g = 0/255, b = 107/255, a = 1},
        [4] =  {r = 95/255, g = 13/255, b = 2/255, a = 1},
    }
    
    if Cfg_Build_Type.BuildingSubtype == 3 then
        Common_Util.SetActive(decorateInfo, true)
        Common_Util.SetActive(ImgName.gameObject,false)
        Common_Util.SetActive(FuncTex.gameObject,false)
        local rarity = Cfg_Build_Type.Rarity or 1
        decorateNameTxt.text = "<color=" .. rarity_color[rarity] .. ">" .. ImgName.text .. "</color>"
        decorateNameOutline.effectColor =  outline_rarity_color[rarity]
        local showLevel = dataItem.buildLevel >0 and dataItem.buildLevel or 1
        decorateLevelTxt.text = "<color=" .. rarity_color[rarity] .. ">Lv."..showLevel.. "</color>"
        decorateLevelOutline.effectColor = outline_rarity_color[rarity]
        Count.text = dataItem.curBuild .. "/" .. dataItem.maxBuild
        decoratePropTxt.text = ""
        decoratePropValTxt.text = ""
        for i = 1, 5 do
            local prop_id = Cfg_Build["BuildingPara" .. tostring(i)]
            if prop_id and prop_id > 0 then
                local map_effect = game_scheme:GWMapEffect_0(prop_id)
                local proType = map_effect.nGroupID
                local proCfg = game_scheme:ProToLang_0(proType)
                local isPercentage = proCfg.isPercentage and proCfg.isPercentage==1
                local percentage = isPercentage and "%" or ""
                local proTable =  GWG.GWHomeMgr.buildingData.GetDecorateProTable(proType)
                local proLang = proTable.proLang
                local nameID = proLang
                local value = map_effect.strParam[0]
                decoratePropTxt.supportRichText = true
                local newRow =  i>1 and "\n" or ""
                decoratePropTxt.text = decoratePropTxt.text..newRow.."<color=#25385B>"..lang.Get(nameID).."</color>"
                decoratePropValTxt.text = decoratePropValTxt.text..newRow.."<color=#87F425>" .. "+" ..(isPercentage and (value / 100) or value)..percentage .. "</color>"
                if rarity > 1 then
                    Common_Util.SetActive(matGo,false)
                    local new_get = GWG.GWHomeMgr.buildingData.IsDecorateNewGet(dataItem.buildId)
                    if new_get then
                        new:SetActive(true)
                    else
                        new:SetActive(false)
                    end
                end
            end
        end
    end
end
---********************end功能函数区**********---

--region WindowInherited
local CUIView = class(ui_base, nil, UIView)
--endregion 

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
    if data and data["uipath"] then
        ui_path = data["uipath"];
    end
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window.delayOpenMain = 0
        window.delayCloseMain = 0
        window.isOpenOrginIsStretch = false
        
        window:LoadUIResource(ui_path, nil, nil, nil)
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--endregion
