local require = require
local setmetatable = setmetatable
local split_server_res_base_task = require "split_server_res_base_task"
local split_define = require "split_server_res_define"
local Warning = split_define.logger.Warning

local ClassTable = {}
setmetatable(ClassTable, {
    __index = split_server_res_base_task
})

local VerTaskType = split_define.VerTaskType
local VerTaskTypeName = split_define.VerTaskTypeName
local DownloadErrorCode = split_define.DownloadErrorCode
local VerTaskState = split_define.VerTaskState
local TaskState = split_define.TaskState
local reportData = split_define.reportData

function ClassTable.New(...)
    local newObj = {}
    setmetatable(newObj, {
        __index = ClassTable
    })
    newObj:ctor(...)
    return newObj
end

function ClassTable:ctor(version, serverId)
    self.verTaskType = VerTaskType.SwitchServerResDownload
    split_server_res_base_task.ctor(self, version, serverId)
end

function ClassTable:InitDownloadTaskList(versionTask)
    --可控制下载列表，暂时没有差异直接用基类的
    split_server_res_base_task.InitDownloadTaskList(self, versionTask)
end

function ClassTable:GetServerListStr()
    local serverList = self.serverList
    local str = "server "
    local sepChar = " "
    for i = 1, #serverList do
        str = str..serverList[i].."S"
        if i < #serverList then
            str = str..sepChar 
        end
    end
    return str
end

function ClassTable:OnFinish()
    local content = "res download finish of "
    local str = self:GetServerListStr()
    content = content..str
    --todo 提示
end

function ClassTable:GetTaskName()
    local content = "res download of "
    local str = self:GetServerListStr()
    content = content..str
    return content
end

return ClassTable